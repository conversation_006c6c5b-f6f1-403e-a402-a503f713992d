package cn.feiying.med.clinics_wm.service.impl;

import cn.feiying.med.clinics_wm.dao.ArtSnTrackDao;
import cn.feiying.med.clinics_wm.entity.ArtSnTrackEntity;
import cn.feiying.med.clinics_wm.model.ArtSnId;
import cn.feiying.med.clinics_wm.service.ArtSnTrackService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 追溯码分发明细表
 *
 * <AUTHOR>
 * 2025-05-20 11:13:14
 */
@Slf4j
@Service("artSnTrackService")
public class ArtSnTrackServiceImpl extends ServiceImpl<ArtSnTrackDao, ArtSnTrackEntity> implements ArtSnTrackService {

    @Override
    public List<ArtSnTrackEntity> findLsByArtSnIdLs(List<ArtSnId> artSnIdLs) {
        if (ObjectUtil.isEmpty(artSnIdLs)) {
            return new ArrayList<>();
        }
        return baseMapper.findLsByArtSnIdLs(artSnIdLs);
    }
}