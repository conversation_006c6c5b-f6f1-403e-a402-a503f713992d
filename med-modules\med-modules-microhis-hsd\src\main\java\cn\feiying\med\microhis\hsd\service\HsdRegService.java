package cn.feiying.med.microhis.hsd.service;

import cn.feiying.med.hip.enums.ImMsgType;
import cn.feiying.med.microhis.hsd.dto.RegDto;
import cn.feiying.med.microhis.hsd.dto.SimplePatientDto;
import cn.feiying.med.microhis.hsd.dto.VisitDto;
import cn.feiying.med.microhis.hsd.entity.RegEntity;
import cn.feiying.med.microhis.hsd.vo.EmergencyRegVo;
import cn.feiying.med.microhis.hsd.vo.RegRefundData;
import cn.feiying.med.microhis.hsd.vo.RegTriageVo;
import cn.feiying.med.microhis.hsd.vo.RegVo;

import java.util.List;
import java.util.Map;

public interface HsdRegService {

    /**
     * 获取挂号记录
     * @param orgId         机构ID
     * @param clinicianId   医师ID
     * @param clinicDate    就诊日期
     * @param deptCode      科室
     * @param patientName   患者名称
     * @param status        状态 0-待接诊 1-诊查中 2-已结诊
     * @param forClinic     是否诊所一体化
     * @param myPatient     是否只查询当前医生患者
     * @return              返回挂号记录列表
     */
    List<RegDto> findRegClinicDateLs(Long orgId, Long clinicianId, Integer clinicDate, String deptCode, String patientName,
                                     Integer status, Integer forClinic, Integer myPatient);

    /**
     * 根据挂号ID获取患者相关信息
     * @param regId         挂号ID
     * @return              返回患者信息
     */
    SimplePatientDto findSimplePatientById(Long regId);

    /**
     * 获取患者待接诊挂号
     * @param orgId         机构ID
     * @param userId        用户ID
     * @param patientId     患者ID
     * @param clinicianId   医生ID
     * @param clinicDate    就诊日期
     * @param deptCode      科室
     * @return              返回挂号ID
     */
    VisitDto findPatientPendingReg(Long orgId, Long userId, Long patientId, Long clinicianId, Integer clinicDate, String deptCode);

    /**
     * 保存挂号信息
     * @param orgId         机构ID
     * @param userId        用户ID
     * @param reg           挂号信息
     */
    void saveReg(Long orgId, Long userId, RegVo reg);

    /**
     * 保存线上挂号记录
     * @param orgId     机构ID
     * @param userId    用户ID
     * @param regVo     挂号记录信息
     * @return          返回诊疗信息
     */
    VisitDto saveOcReg(Long orgId, Long userId, RegVo regVo);

    /**
     * 绑定患者ID
     *
     * @param orgId      机构ID
     * @param userId     用户ID
     * @param regId      挂号ID
     * @param patientId  患者ID
     */
    void bindPatientId(Long orgId, long userId, Long regId, Long patientId);

    /**
     * 接诊
     * @param userId        用户ID
     * @param regId         挂号ID
     * @param clinicianId   医生ID
     */
    void receiveReg(Long userId, Long regId, Long clinicianId);

    /**
     * 保存诊间挂号记录
     * @param orgId     机构ID
     * @param userId    用户ID
     * @param regVo     挂号记录信息
     * @return          返回挂号ID
     */
    Map<String, Object> saveAppointmentReg(Long orgId, Long userId, RegVo regVo);

    /**
     * 保存挂号诊疗记录
     * @param orgId     机构ID
     * @param userId    用户ID
     * @param regVo     挂号记录信息
     * @return          返回诊疗信息
     */
    VisitDto saveRegVisit(Long orgId, Long userId, RegVo regVo);

    /**
     * 保存门诊挂号记录
     * @param orgId         机构ID
     * @param userId        用户ID
     * @param regVo         挂号记录信息
     * @return              返回挂号ID
     */
    Long saveClinicReg(Long orgId, Long userId, RegVo regVo);

    /**
     * 保存加号挂号记录
     * @param orgId         机构ID
     * @param userId        用户ID
     * @param clinicianId   医师ID
     * @param regLs         挂号记录信息
     */
    void saveAdditionReg(Long orgId, Long userId, Long clinicianId, List<RegVo> regLs);

    /**
     * 保存急诊挂号记录
     * @param orgId         机构ID
     * @param userId        用户ID
     * @param regVo         挂号记录信息
     */
    void saveEmergency(Long orgId, Long userId, EmergencyRegVo regVo);

    /**
     * 复诊
     * @param orgId         机构ID
     * @param userId        用户ID
     * @param regId         挂号ID
     * @param visitId       诊疗ID
     * @param clinicianId   医师ID
     * @param deptCode      科室代号
     * @param clinicDate    就诊日期
     */
    Map<String, Object> revisitReg(Long orgId, Long userId, Long regId, Long visitId, Long clinicianId, String deptCode, Integer clinicDate);

    /**
     * 分诊
     * @param orgId         机构ID
     * @param userId        用户ID
     * @param regTriage     分诊信息
     */
    void triageReg(Long orgId, Long userId, RegTriageVo regTriage);

    /**
     * 转诊
     * @param userId        用户ID
     * @param regId         挂号ID
     * @param clinicianId   转入医师ID
     * @param deptCode      转入科室编码
     * @param transferCid   转出医师ID
     * @param periodId      午别代号
     * @param aptNo         预约号
     * @param referralNotes 转诊说明
     */
    void rolloutReg(Long userId, Long regId, Long clinicianId, String deptCode, Long transferCid, Integer periodId, Integer aptNo, String referralNotes);

    void newRolloutReg(Long userId, Long regId, Long clinicianId, String deptCode, Long transferCid, Integer periodId, Integer aptNo, String referralNotes);

    /**
     * 消息推送
     */
    void afterReg(Long userId, Long regId, Long orgId, Long clinicianId, String deptCode, ImMsgType imMsgType);
    /**
     * 过号
     * @param regId     挂号ID
     */
    void skipReg(Long regId);

    /**
     * 拒诊
     * @param userId        用户ID
     * @param regId         挂号ID
     * @param rejectedNotes 拒诊原因
     */
    RegRefundData rejectedReg(Long userId, Long regId, String rejectedNotes);

    /**
     * 退诊
     * @param userId        用户ID
     * @param regId         挂号ID
     * @param returnNotes   退诊原因
     */
    void returnReg(Long userId, Long regId, String returnNotes);

    /**
     * 取消拒诊
     * @param regId 挂号ID
     */
    void canceledRejectedReg(Long regId);

    /**
     * 暂挂
     * @param regId     挂号ID
     */
    void suspendReg(Long regId);

    /**
     * 诊结
     * @param userId        用户ID
     * @param regId         挂号ID
     * @param autoFinished 是否自动结诊
     */
    void finishedReg(Long userId, Long regId, boolean autoFinished);

    /**
     * 批量诊结
     * @param userId    用户ID
     * @param regIds  挂号ID列表
     */
    void batchFinishedReg(Long userId, List<Long> regIds);

    /**
     * 召回
     * @param regId 挂号ID
     */
    void recallReg(Long regId);

    /**
     * 创建账单
     *
     * @param orgId     机构ID
     * @param userId    用户ID
     * @param ageOfYears 年龄
     * @param medTypeId 医疗类别代号
     * @param entity    挂号信息
     * @return          返回账单ID
     */
    Long createBill(Long orgId, Long userId, Integer ageOfYears, Integer medTypeId, RegEntity entity);

    /**
     * 获取支付参数
     * @param visitId     挂号ID
     * @return            返回支付参数
     */
    Map<String, Object> getPayCodeParams(Long visitId);

    /**
     * 关闭未完成挂号
     */
    void closeUnFinishReg();

    /**
     * 关闭24小时未完成的线上挂号
     */
    void closeUnFinishOcRegAfter24H();
}
