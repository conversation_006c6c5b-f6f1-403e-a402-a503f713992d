package cn.feiying.med.clinics_wm.dao;

import cn.feiying.med.clinics_wm.entity.ArtSnTrackEntity;
import cn.feiying.med.clinics_wm.model.ArtSnId;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 追溯码分发明细表
 *
 * <AUTHOR>
 * 2025-05-20 11:13:14
 */
@Mapper
public interface ArtSnTrackDao extends BaseMapper<ArtSnTrackEntity> {

    List<ArtSnTrackEntity> findLsByArtSnIdLs(List<ArtSnId> artSnIdLs);
}
