package cn.feiying.med.his.api.model.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目价格查询响应模型
 */
@Data
public class ItemPriceRespModel {
    
    /**
     * 申请单号
     */
    private String orderId;

    /**
     * 项目明细列表
     */
    private List<Item> itemLs;

    /**
     * 项目价格查询明细项
     */
    @Data
    public static class Item {

        /**
         * 行号
         */
        private Integer lineNo;

        /**
         * 项目编码
         */
        private String artCode;

        /**
         * 项目名称
         */
        private String artName;

        /**
         * 规格
         */
        private String artSpec;

        /**
         * 单位
         */
        private String unit;

        /**
         * 单位类型 1-制剂单位,2-包装单位,3-剂量单位
         */
        private Integer unitType;

        /**
         * 数量
         */
        private BigDecimal total;

        /**
         * 单价
         */
        private BigDecimal price;

        /**
         * 减免金额
         */
        private BigDecimal derated;

        /**
         * 折让金额
         */
        private BigDecimal discounted;

        /**
         * 总金额
         */
        private BigDecimal amount;

        /**
         * 项目类别编码
         */
        private String artTypeCode;

        /**
         * 项目类别名称
         */
        private String artTypeName;

        /**
         * 费用类别编码
         */
        private String feeTypeCode;

        /**
         * 费用类别名称
         */
        private String feeTypeName;
    }
} 