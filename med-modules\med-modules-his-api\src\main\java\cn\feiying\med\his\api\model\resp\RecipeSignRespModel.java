package cn.feiying.med.his.api.model.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 处方签名响应模型
 */
@Data
public class RecipeSignRespModel {
    
    /**
     * 诊疗ID
     */
    private Long visitId;
    
    /**
     * 处方签名结果列表
     */
    private List<RecipeSignResult> resultList;
    
    /**
     * 处方签名结果
     */
    @Data
    public static class RecipeSignResult {
        /**
         * 处方ID
         */
        private Long recipeId;
        /**
         * 处方号
         */
        private String rxNo;
        /**
         * 治疗单列表
         */
        private List<TreatmentItem> treatmentList;
    }
    
    /**
     * 治疗单项
     */
    @Data
    public static class TreatmentItem {
        /**
         * 项目编码
         */
        private String artCode;
        
        /**
         * 项目名称
         */
        private String artName;

        /**
         * 规格箱号
         */
        private String artSpec;

        /**
         * 数量
         */
        private BigDecimal total;
        
        /**
         * 单位
         */
        private String unit;
        
        /**
         * 单位类型 1-制剂单位,2-包装单位,3-剂量单位
         */
        private Integer unitType;
    }
} 