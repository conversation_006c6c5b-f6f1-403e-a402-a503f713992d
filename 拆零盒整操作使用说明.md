# 拆零盒整操作使用说明

## 功能概述

拆零盒整操作用于处理药品库存的拆零和整包转换计算，将JSON格式的库存记录转换为Map对象，并执行拆零盒整计算，最后更新仓库和机构的库存数据。

## 接口信息

**接口地址：** `POST /clinics_wm/wmbill/performSplitPack`

**权限要求：** `clinics_wm:wmbill:performSplitPack`

## 请求参数

### 请求体格式
```json
{
  "orgId": 370811001,  // 可选，机构ID，如果不提供则从请求头获取
  "record": "{\"artId\":1084374,\"totalPacks\":23,\"totalCells\":80,\"orgId\":370811001,\"deptCode\":\"000013\",\"stockNo\":1,\"batchNo\":\"BM24111603\",\"dateManufactured\":20241116,\"expiry\":20261115,\"packPrice\":35,\"cellPrice\":0.35,\"salePackPrice\":35,\"saleCellPrice\":0.35,\"timeCreated\":\"2025-03-24 09:35:47\",\"custName\":\"华润济宁医药有限公司\",\"lastBuyPrice\":35,\"basePackPrice\":35,\"baseCellPrice\":0.35,\"splittable\":1,\"deptTotalPacks\":123,\"deptTotalCells\":80,\"expiryDays\":534,\"costAmount\":833,\"saleAmount\":833}"
}
```

### record字段说明
record是一个JSON字符串，包含以下关键字段：

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| artId | Long | 是 | 商品ID |
| deptCode | String | 是 | 仓库编码 |
| deptTotalPacks | Integer | 否 | 仓库总库存整包数 |
| deptTotalCells | BigDecimal | 否 | 仓库总库存拆零数 |
| totalPacks | Integer | 否 | 批次库存整包数 |
| totalCells | BigDecimal | 否 | 批次库存拆零数 |

## 计算逻辑

### 拆零盒整计算公式

1. **总制剂数计算：**
   ```
   总制剂数 = (仓库总库存拆零数 + 批次库存拆零数) + (仓库总库存整包数 + 批次库存整包数) × 拆零系数
   ```

2. **最终整包数（取整）：**
   ```
   最终整包数 = 总制剂数 ÷ 拆零系数（向下取整）
   ```

3. **最终拆零数（取余）：**
   ```
   最终拆零数 = 总制剂数 - (最终整包数 × 拆零系数)
   ```

### 示例计算

假设输入数据：
- 仓库总库存：123整包 + 80拆零
- 批次库存：23整包 + 80拆零  
- 拆零系数：100

计算过程：
1. 总制剂数 = (80 + 80) + (123 + 23) × 100 = 160 + 14600 = 14760
2. 最终整包数 = 14760 ÷ 100 = 147（向下取整）
3. 最终拆零数 = 14760 - (147 × 100) = 60

## 响应格式

### 成功响应
```json
{
  "code": 0,
  "msg": "success",
  "data": "拆零盒整操作执行成功"
}
```

### 失败响应
```json
{
  "code": 500,
  "msg": "拆零盒整操作失败: 具体错误信息"
}
```

## 业务流程

1. **参数验证：** 检查record参数是否为空，JSON格式是否正确
2. **JSON转换：** 将record的JSON字符串转换为Map<String,Object>
3. **数据提取：** 从Map中提取artId、deptCode等关键字段
4. **商品信息获取：** 根据artId获取商品信息，特别是拆零系数
5. **拆零盒整计算：** 执行拆零盒整操作计算
6. **库存更新：** 调用deptArtService.remakeDeptArt和orgArtService.remakeOrgArt更新库存

## 注意事项

1. **拆零系数：** 如果商品的拆零系数为空或小于等于0，默认使用1
2. **数据默认值：** 如果库存数量字段为空，会使用默认值（整包数为0，拆零数为BigDecimal.ZERO）
3. **事务处理：** 整个操作在事务中执行，如果任何步骤失败，会回滚所有更改
4. **权限控制：** 需要相应的权限才能执行此操作
5. **日志记录：** 操作会记录详细的日志信息，便于问题排查

## 错误处理

常见错误及解决方案：

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| record参数不能为空 | 未提供record参数 | 确保请求体包含record字段 |
| artId不能为空 | record中缺少artId | 确保record JSON包含有效的artId |
| deptCode不能为空 | record中缺少deptCode | 确保record JSON包含有效的deptCode |
| 未找到商品信息 | artId对应的商品不存在 | 检查artId是否正确 |
| JSON格式错误 | record不是有效的JSON | 检查record的JSON格式 |

## 测试用例

可以使用提供的测试类 `WmBillServiceImplTest` 进行单元测试，验证计算逻辑的正确性。
