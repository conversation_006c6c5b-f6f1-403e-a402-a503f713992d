package cn.feiying.med.microhis.hsd.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 诊疗记录诊断描述表
 *
 * <AUTHOR> 2025-05-29 10:30:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("microhis_hsd.t_visit_diag_desc")
public class VisitDiagDescEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 诊疗ID
	 */
	@TableId(value = "Visit_ID", type = IdType.INPUT)
	private Long visitId;
	/**
	 * 门诊诊断编码
	 */
	private String opcDiagcode;
	/**
	 * 门诊诊断名称
	 */
	private String opcDiagname;
	/**
	 * 入院诊断编码
	 */
	private String admDiagcode;
	/**
	 * 入院诊断名称
	 */
	private String admDiagname;
	/**
	 * 出院诊断编码
	 */
	private String dscDiagcode;
	/**
	 * 出院诊断名称
	 */
	private String dscDiagname;
	/**
	 * 主诊断编码
	 */
	private String mainDiagcode;
	/**
	 * 主诊断名称
	 */
	private String mainDiagname;
	/**
	 * 术前诊断编码
	 */
	private String preopDiagcode;
	/**
	 * 术前诊断名称
	 */
	private String preopDiagname;
	/**
	 * 术后诊断编码
	 */
	private String postopDiagcode;
	/**
	 * 术后诊断名称
	 */
	private String postopDiagname;
} 