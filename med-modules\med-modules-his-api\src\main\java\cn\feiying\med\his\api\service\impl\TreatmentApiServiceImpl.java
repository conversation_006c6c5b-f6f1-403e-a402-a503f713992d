package cn.feiying.med.his.api.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.his.api.model.req.treatment.TreatmentDetailModel;
import cn.feiying.med.his.api.model.req.treatment.TreatmentModel;
import cn.feiying.med.his.api.model.req.treatment.TreatmentSaveReqModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.ApplyRespModel;
import cn.feiying.med.his.api.service.TreatmentApiService;
import cn.feiying.med.saas.api.service.RemoteHsdService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 治疗申请服务接口实现类
 */
@Slf4j
@Service
public class TreatmentApiServiceImpl implements TreatmentApiService {

    @Resource
    private ClinicianService clinicianService;
    @Resource
    private RemoteHsdService remoteHsdService;

    @Override
    public ApiResultModel<ApplyRespModel> saveOrUpdateTreatment(TreatmentSaveReqModel treatmentReq) {
        log.info("治疗申请保存及修改开始处理");

        ApiResultModel<ApplyRespModel> result = new ApiResultModel<>();
        
        try {
            // 实现治疗申请保存及修改逻辑
            List<ApplyRespModel.ApplyItem> applyItemList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(treatmentReq.getApplyList())) {
                // 根据申请单号查询已存在的申请单
                List<String> accessionNoLs = treatmentReq.getApplyList().stream()
                        .map(TreatmentModel::getAccessionNo)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
                JSONArray orderArr = findOrderLs(accessionNoLs);
                
                // 如果已存在的申请单先做校验
                for (TreatmentModel treatment : treatmentReq.getApplyList()) {
                    validateTreatment(orderArr, treatment.getAccessionNo());
                }
                
                // 先调用撤销订单签名
                withdrawSignedOrder(orderArr);
                
                // 构造治疗申请参数
                JSONArray saveOrderArr = toJSONObject(orderArr, treatmentReq.getApplyList());
                log.info("构造的治疗申请参数: {}", saveOrderArr);
                
                // 调用远程服务保存申请单
                Map<String, Object> orderMap = new HashMap<>();
                orderMap.put("orderLs", saveOrderArr);
                JSONArray saveOrderLs = remoteHsdService.saveOrder(orderMap);
                
                for (JSONObject orderJson : saveOrderLs.toList(JSONObject.class)) {
                    ApplyRespModel.ApplyItem applyItem = new ApplyRespModel.ApplyItem();
                    applyItem.setAccessionNo(orderJson.getStr("accessionNo"));
                    applyItem.setOrderId(orderJson.getLong("orderId"));
                    applyItemList.add(applyItem);
                    
                    // 申请单签名
//                    remoteHsdService.signOrder(orderJson.getLong("orgId"), orderJson.getLong("clinicianId"),
//                            null, null, Collections.singletonList(orderJson.getLong("orderId")));
                }
            } else {
                throw new SaveFailureException("治疗申请信息列表不能为空");
            }
            
            ApplyRespModel responseModel = new ApplyRespModel();
            responseModel.setVisitId(treatmentReq.getVisitId());
            responseModel.setApplyList(applyItemList);
            result.setCode(0);
            result.setMsg("操作成功");
            result.setData(responseModel);
        } catch (Exception e) {
            log.error("治疗申请保存及修改处理异常", e);
            result.setCode(1);
            result.setMsg("操作失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 将TreatmentModel列表转换为OrderVo参数列表
     * @param orderArr 已存在的申请单数组
     * @param treatmentModels 治疗申请模型列表
     * @return 治疗申请对象JSON数组
     */
    private JSONArray toJSONObject(JSONArray orderArr, List<TreatmentModel> treatmentModels) {
        JSONArray orderArray = new JSONArray();

        if (ObjectUtil.isNotEmpty(treatmentModels)) {
            // 医师信息查询
            List<String> clinicianNoLs = treatmentModels.stream()
                    .map(TreatmentModel::getClinicianCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            List<ClinicianEntity> clinicianLs = new ArrayList<>();
            if (!clinicianNoLs.isEmpty()) {
                clinicianLs = clinicianService.list(Wrappers.lambdaQuery(ClinicianEntity.class)
                        .in(ClinicianEntity::getClinicianNo, clinicianNoLs));
            }
            for (TreatmentModel treatmentModel : treatmentModels) {
                JSONObject orderJson = convertToTreatmentOrderParams(orderArr, treatmentModel, clinicianLs);
                orderArray.set(orderJson);
            }
        }

        return orderArray;
    }

    /**
     * 将TreatmentModel转换为OrderVo参数
     * @param orderArr 已存在的申请单数组
     * @param treatmentModel 治疗申请模型
     * @param clinicianLs 医师列表
     * @return 治疗申请对象JSON
     */
    private JSONObject convertToTreatmentOrderParams(JSONArray orderArr, TreatmentModel treatmentModel, List<ClinicianEntity> clinicianLs) {
        JSONObject orderJson = new JSONObject();
        
        // 设置基本信息
        orderJson.set("visitId", treatmentModel.getVisitId());
        
        // 设置科室及医师信息
        orderJson.set("applyDeptcode", treatmentModel.getApplyDeptcode());
        orderJson.set("exceDeptcode", treatmentModel.getExceDeptcode());
        
        // 设置医师ID
        if (ObjectUtil.isNotEmpty(clinicianLs)) {
            clinicianLs.stream()
                    .filter(c -> c.getClinicianNo().equals(treatmentModel.getClinicianCode()))
                    .findFirst().ifPresent(clinician -> orderJson.set("clinicianId", clinician.getClinicianId()));
        }
        
        // 处方分类
        orderJson.set("recipeTypeId", RecipeType.zl.getValue());
        orderJson.set("recipeCatId", RecipeCat.commonRecipe.getValue());
        
        // 设置医疗信息
        orderJson.set("medicalHistory", treatmentModel.getMedicalHistory());
        orderJson.set("purposeDesc", treatmentModel.getPurposeDesc());
        orderJson.set("isSecret", 0);
        
        // 从已存在的申请单中获取orderId和recipeId
        Long orderId = getOrderId(orderArr, treatmentModel.getAccessionNo());
        if (orderId != null) {
            orderJson.set("orderId", orderId);
        }
        Long recipeId = getRecipeId(orderArr, treatmentModel.getAccessionNo());
        if (recipeId != null) {
            orderJson.set("recipeId", recipeId);
        }
        
        // 创建治疗申请组
        JSONArray recipeGroupArray = new JSONArray();
        
        // 如果有明细列表，则根据明细创建组
        if (treatmentModel.getApplyDetailList() != null && !treatmentModel.getApplyDetailList().isEmpty()) {
            JSONObject groupJson = new JSONObject();
            groupJson.set("groupNo", 1); // 默认组号
            
            // 创建明细列表
            JSONArray recipeDetailArray = new JSONArray();
            int i = 1;
            for (TreatmentDetailModel detailModel : treatmentModel.getApplyDetailList()) {
                JSONObject detailJson = new JSONObject();
                
                // 设置明细信息
                detailJson.set("lineNo", i);  // 行号从1开始
                detailJson.set("artId", detailModel.getArtCode());
                detailJson.set("artName", detailModel.getArtName());
                detailJson.set("total", detailModel.getTotal());
                detailJson.set("unit", detailModel.getUnit());
                detailJson.set("unitType", detailModel.getUnitType());
                detailJson.set("freqCode", detailModel.getFreqCode());
                detailJson.set("freqName", detailModel.getFreqName());
                detailJson.set("remarks", detailModel.getRemarks());
                
                recipeDetailArray.set(detailJson);
                i++;
            }
            
            groupJson.set("recipeDetailList", recipeDetailArray);
            recipeGroupArray.set(groupJson);
        }
        
        orderJson.set("recipeGroupLs", recipeGroupArray);
        
        return orderJson;
    }

    /**
     * 校验治疗申请信息
     */
    private void validateTreatment(JSONArray orderArr, String accessionNo) {
        if (ObjectUtil.isNotEmpty(orderArr)) {
            for (int i = 0; i < orderArr.size(); i++) {
                JSONObject orderJson = orderArr.getJSONObject(i);
                if (StrUtil.equals(accessionNo, orderJson.getStr("accessionNo"))) {
                    Integer paidStatus = orderJson.getInt("paidStatus");
                    Integer execStatus = orderJson.getInt("execStatus");
                    if (paidStatus != null && paidStatus.equals(PaidStatus.paid.getValue())) {
                        throw new SaveFailureException("治疗申请【" + accessionNo + "】已支付，不能修改");
                    }
                    if (execStatus != null && execStatus.equals(ExecStatus.cancel.getValue())) {
                        throw new SaveFailureException("治疗申请【" + accessionNo + "】已作废，不能修改");
                    }
                    if (execStatus != null && execStatus.equals(ExecStatus.finish.getValue())) {
                        throw new SaveFailureException("治疗申请【" + accessionNo + "】已完成，不能修改");
                    }
                }
            }
        }
    }

    /**
     * 撤销已签名的订单
     */
    private void withdrawSignedOrder(JSONArray orderArr) {
        if (ObjectUtil.isNotEmpty(orderArr)) {
            List<Long> orderIdLs = getOrderIdLs(orderArr);
            if (ObjectUtil.isNotEmpty(orderIdLs)) {
                remoteHsdService.withdrawSignedOrder(orderIdLs);
            }
        }
    }

    /**
     * 根据申请单号列表查询订单列表
     */
    private JSONArray findOrderLs(List<String> accessionNoLs) {
        return remoteHsdService.findOrderByAccessionNo(accessionNoLs);
    }

    private List<Long> getOrderIdLs(JSONArray orderLs) {
        List<Long> orderIdLs = new ArrayList<>();
        if (orderLs != null && !orderLs.isEmpty()) {
            for (int i = 0; i < orderLs.size(); i++) {
                JSONObject jsonItem = orderLs.getJSONObject(i);
                Long orderId = jsonItem.getLong("orderId");
                if (orderId != null && !orderIdLs.contains(orderId)) {
                    orderIdLs.add(orderId);
                }
            }
        }
        return orderIdLs;
    }

    private Long getOrderId(JSONArray orderLs, String accessionNo) {
        Long orderId = null;
        if (orderLs != null && !orderLs.isEmpty()) {
            for (int i = 0; i < orderLs.size(); i++) {
                JSONObject jsonItem = orderLs.getJSONObject(i);
                String accessionNoItem = jsonItem.getStr("accessionNo");
                if (StrUtil.equals(accessionNo, accessionNoItem)) {
                    orderId = jsonItem.getLong("orderId");
                    break;
                }
            }
        }
        return orderId;
    }

    private Long getRecipeId(JSONArray orderLs, String accessionNo) {
        Long recipeId = null;
        if (orderLs != null && !orderLs.isEmpty()) {
            for (int i = 0; i < orderLs.size(); i++) {
                JSONObject jsonItem = orderLs.getJSONObject(i);
                String accessionNoItem = jsonItem.getStr("accessionNo");
                if (StrUtil.equals(accessionNo, accessionNoItem)) {
                    recipeId = jsonItem.getLong("recipeId");
                    break;
                }
            }
        }
        return recipeId;
    }
} 