package cn.feiying.med.microhis.hsd.service;

import cn.feiying.med.microhis.hsd.dto.VisitDiagDto;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.microhis.hsd.entity.VisitDiagEntity;

import java.util.List;
import java.util.Map;

/**
 * 诊疗记录诊断表
 *
 * <AUTHOR> @email 
 * @date 2023-09-21 15:15:55
 * 2023-09-21 15:15:55
 */
public interface VisitDiagService extends IService<VisitDiagEntity> {

    PageUtils<VisitDiagDto> queryPage(Map<String, Object> params);

    List<VisitDiagDto> findByWrapper(QueryWrapper<VisitDiagDto> wrapper);

    VisitDiagDto findById(Long visitId, Integer diagNo);

    void saveEntity(VisitDiagEntity entity);

    List<VisitDiagDto> saveBatchEntity(Long visitId, Long userId, Long clinicianId, String deptCode, List<VisitDiagEntity> visitDiagLs);

    void delVisitDiagById(Long visitId, List<Integer> diagNoLs);

    void delBatchVisitDiag(List<VisitDiagEntity> list);

    void delete(Integer... idLs);
}


