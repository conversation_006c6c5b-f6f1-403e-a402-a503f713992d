package cn.feiying.med.clinics_wm.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArtSnId {
    /**
     * 商品代号
     */
    @Schema(type = "Integer", description = "商品代号")
    private Integer artId;
    /**
     * 追溯序列码
     */
    @Schema(type = "Long", description = "追溯序列码")
    private Long snNo;
}
