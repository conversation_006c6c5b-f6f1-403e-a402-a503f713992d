package cn.feiying.med.microhis.hsd.vo;

import cn.feiying.med.common.annotation.TrimStr;
import cn.feiying.med.common.validator.group.DefaultGroup;
import cn.feiying.med.common.validator.group.EditGroup;
import cn.feiying.med.common.validator.group.SaveGroup;
import cn.feiying.med.microhis.hsd.entity.VisitDiagEntity;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@Data
public class VisitVo {

    /**
     * 诊疗ID
     */
    @Null(message = "诊疗ID必须为空", groups = SaveGroup.class)
    @NotNull(message = "诊疗ID不能为空", groups = EditGroup.class)
    private Long visitId;
    /**
     * 挂号ID
     */
    private Long regId;
    /**
     * 机构ID
     */
//    @NotNull(message = "机构ID不能为空", groups = DefaultGroup.class)
    private Long orgId;
    /**
     * 患者ID
     */
//    @NotNull(message = "患者ID不能为空", groups = DefaultGroup.class)
    private Long patientId;
    /**
     * 患者姓名
     */
    @NotBlank(message = "患者姓名不能为空", groups = DefaultGroup.class)
    @TrimStr
    private String patientName;
    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空", groups = DefaultGroup.class)
    private Long clinicianId;
    /**
     * 就诊日期
     */
    private Integer clinicDate;
    /**
     * 主诉
     */
    @NotEmpty(message = "主诉不能为空", groups = DefaultGroup.class)
    @Size(message = "请输入60字以内的主诉", max = 60, groups = DefaultGroup.class)
    @TrimStr
    private String complainedAs;
    /**
     * 临床病种代号
     */
    private Integer diseaseId;
    /**
     * 诊疗类型
     */
    private Integer clinicTypeId;
    /**
     * 看诊科室代码
     */
    @NotBlank(message = "科室代码不能为空", groups = DefaultGroup.class)
    private String deptCode;
    /**
     * 现病史
     */
    @Size(message = "请输入1024字以内的现病史", max = 1024, groups = DefaultGroup.class)
    @TrimStr
    private String hpiDesc;
    /**
     * 既往史
     */
    @Size(message = "请输入1024字以内的既往史", max = 1024, groups = DefaultGroup.class)
    @TrimStr
    private String pastHistory;
    /**
     * 家族史
     */
    @Size(message = "请输入1024字以内的家族史", max = 1024, groups = DefaultGroup.class)
    @TrimStr
    private String familyHistory;
    /**
     * 过敏史
     */
    @Size(message = "请输入255字以内的过敏史", max = 255, groups = DefaultGroup.class)
    @TrimStr
    private String allergicHistory;
    /**
     * 一般检查
     */
    @Size(message = "请输入1024字以内的一般检查", max = 1024, groups = DefaultGroup.class)
    @TrimStr
    private String generalInspection;
    /**
     * 鉴别诊断
     */
    @Size(message = "请输入1024字以内的鉴别诊断", max = 1024, groups = DefaultGroup.class)
    @TrimStr
    private String differentialDiag;
    /**
     * 治疗方案
     */
    @Size(message = "请输入255字以内的治疗方案", max = 255, groups = DefaultGroup.class)
    @TrimStr
    private String treatAbstract;
    /**
     * 病情陈述
     */
    @Size(message = "请输入1024字以内的病情陈述", max = 1024, groups = DefaultGroup.class)
    @TrimStr
    private String patientStatement;
    /**
     * 健康宣教
     */
    @Size(message = "请输入255字以内的健康宣教", max = 255, groups = DefaultGroup.class)
    @TrimStr
    private String healthEducation;
    /**
     * 婚姻史
     */
    @Size(message = "请输入255字以内的婚姻史", max = 255, groups = DefaultGroup.class)
    @TrimStr
    private String maritalHistory;
    /**
     * 生育史
     */
    @Size(message = "请输入255字以内的生育史", max = 255, groups = DefaultGroup.class)
    @TrimStr
    private String childbearingHistory;
    /**
     * 月经史
     */
    @Size(message = "请输入255字以内的月经史", max = 255, groups = DefaultGroup.class)
    @TrimStr
    private String menstrualHistory;
    /**
     * 辅助检查
     */
    @Size(message = "请输入1024字以内的辅助检查", max = 1024, groups = DefaultGroup.class)
    @TrimStr
    private String auxiliaryInspection;
    /**
     * 病程记录
     */
    @Size(message = "请输入255字以内的病程记录", max = 255, groups = DefaultGroup.class)
    @TrimStr
    private String progressNotes;
    /**
     * 医疗类别代号
     */
    private Integer medTypeId;
    /**
     * 体温
     */
    private BigDecimal temperature;
    /**
     * 身高cm
     */
    private Integer heightCm;
    /**
     * 体重Kg
     */
    private BigDecimal weightKg;
    /**
     * 舒张压
     */
    private Integer dbp;
    /**
     * 收缩压
     */
    private Integer sbp;
    /**
     * 脉搏率
     */
    private Integer pulse;
    /**
     * 呼吸率
     */
    private Integer rr;
    /**
     * 复诊标志位
     */
    private Integer revisitFlag;
    /**
     * 预约挂号渠道代号
     */
    private Integer sourceId;
    /**
     * 性别
     */
    @NotNull(message = "性别不能为空", groups = DefaultGroup.class)
    private Integer genderId;
    /**
     * 年龄
     */
    private Integer ageOfYears;
    /**
     * 日龄
     */
    private BigDecimal ageOfDays;
    /**
     * 年龄类型
     */
    private Integer agesTypeId;
    /**
     * 出生日期
     */
    private Integer birthDate;
    /**
     * 出生时间
     */
    private Integer birthTime;
    /**
     * 证件类型代号
     */
    private Integer certTypeId;
    /**
     * 险种类型代号
     */
    private Integer insuranceTypeId;
    /**
     * 患者类型代号
     */
    private Integer patientTypeId;
    /**
     * 人群类型代号
     */
    private Integer psnTypeId;
    private String psnTypeCode;
    /**
     * 公务员标志位
     */
    private Integer civilServantFlag;
    /**
     * 参保单位名称
     */
    private String employerName;
    /**
     * 就诊凭证类型代号
     */
    private Integer mdtrtCertTypeId;
    /**
     * 就诊凭证内容
     */
    private String mdtrtCertText;
    /**
     * 电话号码
     */
    @TrimStr
    private String telNo;
    /**
     * 证件号码
     */
    @Size(message = "请输入20字以内的证件号码", max = 20, groups = DefaultGroup.class)
    @TrimStr
    private String idcertNo;
    /**
     * 现居地区代号
     */
    private String livingZonecode;
    /**
     * 现居详细地址
     */
    @Size(message = "请输入80字以内的详细地址", max = 80, groups = DefaultGroup.class)
    @TrimStr
    private String livingAddr;
    /**
     * 联系人姓名
     */
    @Size(message = "请输入50字以内的陪诊人姓名", max = 50, groups = DefaultGroup.class)
    @TrimStr
    private String companionName;
    /**
     * 关系代号
     */
    private Integer relationshipId;
    /**
     * 联系人电话
     */
    @Size(message = "请输入40字以内的联系人电话", max = 40, groups = DefaultGroup.class)
    @TrimStr
    private String contactTel;
    /**
     * 联系人身份证号码
     */
    @Size(message = "请输入20字以内的联系人身份证号码", max = 20, groups = DefaultGroup.class)
    @TrimStr
    private String companionIdno;
    /**
     * 陪诊人地址
     */
    @Size(message = "请输入80字以内的陪诊人地址", max = 80, groups = DefaultGroup.class)
    @TrimStr
    private String companionAddr;
    /**
     * 是否外伤(0-否,1-是)
     */
    private Integer isTraumatic;
    /**
     * 是否涉及第三方(0-否,1-是)
     */
    private Integer isTpRelative;
    /**
     * 诊断列表
     */
    private List<VisitDiagEntity> visitDiagLs;
    /**
     * 诊疗记录过敏史
     */
    private List<Integer> allergyTypeIdLs;
    /**
     * 诊疗记录病史
     */
    private List<Integer> illnessTypeIdLs;
}
