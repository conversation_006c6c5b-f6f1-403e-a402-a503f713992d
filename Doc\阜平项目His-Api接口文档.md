# 阜平项目His-<PERSON><PERSON>接口文档

## 目录
- [1. 用户接口](#1-用户接口)
  - [1.1 用户token获取](#11-用户token获取)
  - [1.2 用户机构列表](#12-用户机构列表)
  - [1.3 机构切换](#13-机构切换)
- [2. 处方接口](#2-处方接口)
  - [2.1 处方保存及修改](#21-处方保存及修改)
  - [2.2 处方签名](#22-处方签名)
  - [2.3 处方作废](#23-处方作废)
  - [2.4 处方状态回写](#24-处方状态回写)
- [3. 医嘱接口](#3-医嘱接口)
  - [3.1 医嘱保存及修改](#31-医嘱保存及修改)
  - [3.2 医嘱作废](#32-医嘱作废)
  - [3.3 医嘱状态回写](#33-医嘱状态回写)
- [4. 申请单接口](#4-申请单接口)
  - [4.1 保存申请单](#41-保存申请单)
  - [4.2 手术申请](#42-手术申请)
  - [4.3 治疗申请](#43-治疗申请)
  - [4.4 申请单删除](#44-申请单删除)
  - [4.5 申请单作废](#45-申请单作废)
  - [4.6 申请单状态回写](#46-申请单状态回写)
  - [4.7 手术状态回写](#47-手术状态回写)
- [5. 诊疗接口](#5-诊疗接口)
  - [5.1 诊疗信息回写](#51-诊疗信息回写)
  - [5.2 诊断回写](#52-诊断回写)
  - [5.3 诊断删除](#53-诊断删除)
  - [5.4 电子住院证申请](#54-电子住院证申请)
  - [5.5 电子住院证作废](#55-电子住院证作废)
- [6. 其他接口](#6-其他接口)
  - [6.1 项目价格查询](#61-项目价格查询)

## 接口概述
本接口文档适用于阜平项目His系统，涵盖处方、医嘱、申请单、诊疗、诊断、住院证等核心业务的API接口。支持处方、医嘱、申请单的新增、修改、删除、作废、状态回写等操作。

## 基础规范
- **接口协议**：HTTP/HTTPS，推荐使用POST方式提交数据。
- **数据格式**：请求和响应均采用JSON格式，字符集为UTF-8。
- **时间格式**：所有时间字段统一采用`yyyy-MM-dd HH:mm:ss`格式，日期字段采用`yyyy-MM-dd`格式。
- **编码规范**：所有编码字段（如科室编码、医生编码等）均为字符串类型，区分大小写。
- **必填项**：表格中"是否必填"为"是"的参数必须传递，否则接口将返回错误。
- **返回结构**：所有接口返回统一结构，包含code、msg、data字段。

### 状态码规范

| code值 | 说明   |
| ------ | ------ |
| 0      | 成功   |
| 其它   | 失败，具体见msg |

### 通用返回结构

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {}
}
```

## 接口安全规范

### 加密加签机制

为保证接口调用的安全性，所有接口均采用加密加签机制进行安全认证。

#### 1. 公共参数

| 参数名    | 类型   | 是否必填 | 说明                                        |
| --------- | ------ | -------- | ------------------------------------------- |
| timestamp | String | 是       | 请求时间戳，格式为yyyyMMddHHmmss            |
| nonce     | String | 是       | 随机字符串，用于防止重放攻击                |
| sign      | String | 是       | 签名，算法详见下文                          |
| data      | String | 是       | 加密后的业务数据，详见数据加密部分          |

#### 1.1 请求头参数

安全参数需要放置在HTTP请求头中。

| 请求头名称         | 是否必填 | 说明                                  |
| ----------------- | -------- | ------------------------------------- |
| X-HIS-APP-ID      | 是       | 应用标识，由系统分配                   |
| X-HIS-TIMESTAMP   | 是       | 请求时间戳，格式为yyyyMMddHHmmss       |
| X-HIS-NONCE       | 是       | 随机字符串，用于防止重放攻击           |
| X-HIS-SIGN        | 是       | 签名，算法详见签名算法部分             |
| X-HIS-ENCRYPT-TYPE| 是       | 加密类型，固定值"AES"                  |
| Content-Type      | 是       | 固定值"application/json;charset=UTF-8" |

#### 2. 签名算法

1. 将业务参数(如visitId、recipeCount等)转为JSON字符串，并使用AES算法加密得到data字段值。
2. 将公共参数(appId、timestamp、nonce、encryptType、data)按照参数名的ASCII码从小到大排序。
3. 将排序后的参数按照key=value的格式拼接成字符串，并在末尾拼接上key=appSecret。
4. 对得到的字符串进行MD5加密（32位大写），得到sign值。

##### 2.1 签名参数说明

1. 业务参数需先使用AES加密算法加密为data字段，然后data字段参与签名。
2. 签名使用的参数包括所有公共参数：appId、timestamp、nonce、encryptType、data。
3. 签名时如果参数值为空，则不参与签名。
4. appSecret由系统分配，与appId一一对应。
5. 签名结果需转为大写字母。

```java
// 签名算法示例（Java）
public static String generateSign(Map<String, String> params, String appSecret) {
    // 1. 将所有参数按照键名ASCII码从小到大排序
    List<String> keys = new ArrayList<>(params.keySet());
    Collections.sort(keys);
    // 2. 将排序后的参数拼接成key=value&key=value的形式
    StringBuilder sb = new StringBuilder();
    for (String key : keys) {
        String value = params.get(key);
        if (StrUtil.isNotEmpty(value)) {
            sb.append(key).append("=").append(value).append("&");
        }
    }
    // 3. 去掉最后一个&符号，并拼接appSecret
    String stringToSign = sb.substring(0, sb.length() - 1) + "&key=" + appSecret;
    // 4. 对拼接后的字符串进行MD5加密
    return DigestUtils.md5Hex(stringToSign).toUpperCase();
}
```

#### 3. 数据加密

为保证接口数据传输的安全性，业务参数需要进行加密处理。加密采用AES算法，模式为ECB，填充方式为PKCS7Padding。

##### 3.1 加密流程

1. 将业务数据转换为JSON字符串。
2. 使用AES算法对JSON字符串进行加密。
3. 将加密后的字节数组转为Base64编码的字符串。
4. 将加密后的字符串赋值给data参数。

```java
// AES加密示例（Java）
public static String aesEncrypt(String content, String encryptKey) throws Exception {
    Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
    SecretKeySpec keySpec = new SecretKeySpec(encryptKey.getBytes(StandardCharsets.UTF_8), "AES");
    cipher.init(Cipher.ENCRYPT_MODE, keySpec);
    byte[] encrypted = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
    return Base64.getEncoder().encodeToString(encrypted);
}
```

##### 3.2 解密流程

1. 将Base64编码的加密字符串解码为字节数组。
2. 使用AES算法对字节数组进行解密。
3. 将解密后的字节数组转为JSON字符串。
4. 解析JSON字符串得到业务数据。

```java
// AES解密示例（Java）
public static String aesDecrypt(String encryptedContent, String encryptKey) throws Exception {
    Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
    SecretKeySpec keySpec = new SecretKeySpec(encryptKey.getBytes(StandardCharsets.UTF_8), "AES");
    cipher.init(Cipher.DECRYPT_MODE, keySpec);
    byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedContent));
    return new String(decrypted, StandardCharsets.UTF_8);
}
```

##### 3.3 密钥

- AES密钥(encryptKey)：16字节长度的字符串，由系统分配

#### 4. 请求示例

```
POST /his/api/recipe/saveOrUpdate HTTP/1.1

X-HIS-APP-ID: test123456
X-HIS-TIMESTAMP: 20250512101225
X-HIS-NONCE: a1b2c3d4e5
X-HIS-SIGN: 9DFC2073F53C2C7E92E438AFCC6BA86D
X-HIS-ENCRYPT-TYPE: AES
Content-Type: application/json;charset=UTF-8

{
  "timestamp": 20250512101225,
  "nonce": "a1b2c3d4e5",
  "encryptType": "AES",
  "sign": "9DFC2073F53C2C7E92E438AFCC6BA86D",
  "data": "Base64编码的加密业务数据"
}
```

#### 5. 接口调用流程

1. 调用方将业务参数转为JSON字符串，并使用AES加密得到data字段。
2. 组装请求头参数（X-HIS-APP-ID、X-HIS-TIMESTAMP、X-HIS-NONCE、X-HIS-ENCRYPT-TYPE）。
3. 根据签名算法计算sign值，并添加到请求头中（X-HIS-SIGN）。
4. 将加密后的业务数据放入请求体的data字段。
5. 发送HTTP请求到服务端。
6. 服务端验证请求头中的签名，通过后解密业务数据并处理业务逻辑。
7. 服务端将处理结果返回。

## 接口说明

### 1. 用户接口

#### 1.1 用户token获取

##### 接口说明
此接口用于获取用户token信息。

**接口Url**  
POST /his/api/user/getToken

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | userId   | 用户ID   | Long     |          | Y        |      |
| 2    | orgId    | 机构ID   | Long     |          | Y        |      |

**请求示例**
```json
{
  "userId": 0,
  "orgId": 0
}
```

##### 返回参数
| 序号 | 参数代码  | 参数名称   | 参数类型 | 是否必填 | 说明 |
| ---- | --------- | ---------- | -------- | -------- | ---- |
| 1    | code      | 返回码     | Integer  | Y        | 0表示成功，非0表示失败 |
| 2    | msg       | 返回信息   | String   | Y        |      |
| 3    | data      | 返回数据   | Object   | Y        |      |
| 3.1  | userId    | 用户ID     | Long     | Y        | 用户ID |  
| 3.2  | token     | 访问令牌   | String   | Y        | 访问系统的令牌信息 |
| 3.3  | expiresIn | 过期时间   | Long     | Y        | 令牌过期时间(毫秒) |
| 3.4  | refToken  | 刷新令牌   | String   | Y        | 刷新令牌 |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "userId": 0,
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJVU0VSMDAxIiwiaWF0IjoxNjE5NjI0MDAwLCJleHAiOjE2MTk3MTA0MDB9.s",
    "expiresIn": 7200,
    "refToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJVU0VSMDAxIiwiaWF0IjoxNjE5NjI0MDAwLCJleHAiOjE2MTk3MTA0MDB9.s"
  }
}
```

#### 1.2 用户机构列表

##### 接口说明
此接口用于获取用户可访问的机构列表。

**接口Url**  
POST /his/api/user/getOrgList

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | userId   | 用户ID   | Long     |          | Y        |      |

**请求示例**
```json
{
  "userId": 0
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称  | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | --------- | -------- | -------- | ---- |
| 1    | code     | 返回码    | Integer  | Y        | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息  | String   | Y        |      |
| 3    | data     | 返回数据  | Array    | Y        | 机构列表 |
| 3.1  | orgId    | 机构ID    | Long     | Y        |      |
| 3.2  | orgName  | 机构名称  | String   | Y        |      |
| 3.3  | orgCode  | 机构编码  | String   | Y        |      |
| 3.4  | nickNam  | 机构简称  | String   | N        |      |
| 3.5  | fullName | 机构全称  | String   | Y        |      |
| 3.6  | displayOrder| 显示顺序  | Integer   | Y        |      |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": [
    {
      "orgId": 1,
      "orgName": "阜平县医院",
      "orgCode": "10001",
      "nickNam": "阜平县医院",
      "fullName": "阜平县医院",
      displayOrder: 1
    }
  ]
}
```

#### 1.3 机构切换

##### 接口说明
此接口用于切换用户当前的操作机构。

**接口Url**  
POST /his/api/user/chooseOrg

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | userId   | 用户ID   | Long     |          | Y        |      |
| 2    | orgId    | 机构ID   | Long     |          | Y        | 要切换到的目标机构ID |

**请求示例**
```json
{
  "userId": 0,
  "orgId": 1
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y        | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y        |      |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

### 2. 处方接口

#### 2.1 处方保存及修改

##### 接口说明
此接口用于处方的保存及修改操作。

**接口Url**
POST /his/api/recipe/saveOrUpdate

##### 请求参数
| 序号        | 参数代码          | 参数名称     | 参数类型 | 参数长度  | 是否必填  | 说明 |
| ----------- | ---------------- | ----------- | -------- | -------- | -------- | ---- |
| 1           | visitId          | 诊疗ID      | Long     |          | Y        |      |
| 2           | recipeCount      | 处方数       | Integer  |          | Y        |      |
| 3           | recipeList       | 处方列表     | Array    |          | Y        | 处方信息列表，结构如下： |
| 3.1         | recipeTypeId     | 处方类型代号 | Integer  |          | Y        | 1-西药处方，2-中药处方 |
| 3.2         | rxNo             | 处方号       | String   | 20       | Y        |      |
| 3.3         | clinicianCode    | 开单医师编码 | String   | 20       | Y        |      |
| 3.4         | clinicianName    | 开单医师名称 | String   | 50       | Y        |      |
| 3.5         | applyDeptcode    | 开单科室编码 | String   | 10       | Y        |      |
| 3.6         | applyDeptName    | 开单科室名称 | String   | 60       | Y        |      |
| 3.7         | exceDeptcode     | 执行科室编码 | String   | 10       | Y        |      |
| 3.8         | exceDeptName     | 执行科室名称 | String   | 60       | Y        |      |
| 3.9         | times        | 中药付数     | Integer    |          | N        | 默认为1 |
| 3.10        | storeCode        | 发药药房编码 | String   | 10       | Y        |      |
| 3.11        | storeName        | 发药药房名称 | String   | 60       | Y        |      |
| 3.12        | diseaseCode      | 病种编码     | String   | 10       | N        |      |
| 3.13        | diseaseName      | 病种名称     | String   | 60       | N        |      |
| 3.14        | recipeGroupList  | 处方组列表   | Array    |          | Y        | 处方组信息列表，结构如下： |
| 3.14.1      | groupNo          | 组号         | Integer  |          | Y        |      |
| 3.14.2      | periodCycles | 天数         | Integer    |          | N        |      |
| 3.14.3      | freqCode     | 频次代码     | String     | 10       | N        | 西药处方为必填 |
| 3.14.4      | freqName     | 频次名称     | String     | 20       | N        |      |
| 3.14.5      | routeCode    | 给药途径编码 | String     | 10       | N        | 西药处方为必填 |
| 3.14.6      | routeName    | 给药途径名称 | String     | 20       | N        |      |
| 3.14.7      | notice       | 嘱托         | String     | 60       | N        |      |
| 3.14.8      | recipeDetailList | 处方组明细列表 | Array    |          | Y        | 处方组明细信息列表，结构如下： |
| ********    | lineNo       | 处方明细序号     | Long    |          | Y        |      |
| ********    | artCode      | 项目编码     | String     | 20       | Y        |      |
| ********    | artName      | 项目名称     | String     | 200      | Y        |      |
| ********    | artSpec      | 规格         | String     | 100      | N        |      |
| ********    | total        | 数量         | BigDecimal | 12,4     | Y        |      |
| ********    | unit         | 单位         | String     | 20       | Y        |      |
| ********    | unitType     | 单位类型     | Integer    |          | Y        | 1-制剂单位,2-包装单位,3-剂量单位 |
| ********    | mealCells    | 每次制剂数量 | BigDecimal | 12,4     | N        | 即每次小单位用量 |
| ********    | cellUnit     | 制剂单位     | String     | 20       | N        | 即每次小单位用量单位 |
| ********0   | mealDoses    | 每次使用剂量 | BigDecimal | 12,4     | N        | 即每次用量 |
| ********1   | doseUnit     | 剂量单位     | String     | 10       | N        | 即每次用量单位 |
| ********2   | processMethod| 中药制法     | String     | 60       | N        |      |
| ********3   | stRequired   | 是否需要皮试 | Integer    |          | N        | 0-不需要皮试 1-需要皮试 2-免皮试 |
| ********4   | price        | 单价         | BigDecimal | 12,2     | N        |      |
| ********5   | amount       | 金额         | BigDecimal | 12,2     | N        |      |
| ********6   | displayOrder | 显示顺序     | Integer   |         | N        |      |

**请求示例**
```json
{
  "visitId": 12546,
  "recipeCount": 1,
  "recipeList": [
    {
      "recipeTypeId": 1,
      "rxNo": "CF202505080001",
      "clinicianCode": "DOC001",
      "clinicianName": "张医生",
      "applyDeptcode": "DEPT001",
      "applyDeptName": "内科",
      "exceDeptcode": "DEPT002",
      "exceDeptName": "药剂科",
      "times": 1,
      "storeCode": "STORE001",
      "storeName": "门诊药房",
      "diseaseCode": "M03900",
      "diseaseName": "高血压",
      "recipeGroupList": [
        {
          "groupNo": 1,
          "periodCycles": 3,
          "freqCode": "tid",
          "freqName": "每日三次",
          "routeCode": "1",
          "routeName": "口服",
          "notice": "饭后服用",
          "recipeDetailList": [
            {
              "lineNo": 1,
              "displayOrder": 1,
              "artCode": "1006041",
              "artName": "阿莫西林胶囊",
              "artSpec": "0.25g*24粒",
              "total": 2,
              "unit": "盒",
              "unitType": 2,
              "mealCells": 1,
              "cellUnit": "粒",
              "mealDoses": 0.25,
              "doseUnit": "g",
              "stRequired": 0,
              "price": 15.5,
              "amount": 31.0
            }
          ]
        }
      ]
    }
  ]
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ------ | -------- | -------- | -------- | -------- | ---- |
| 1      | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2      | msg      | 返回信息 | String   | Y |  |
| 3      | data     | 返回数据 | Array   | Y | 返回数据列表，结构如下： |
| 3.1    | visitId  | 诊疗ID   | Long   | Y | |
| 3.2    | recipeList | 处方列表 | Array | Y  | 处方信息列表，结构如下： |
| 3.2.1  | recipeId | 处方ID   | Long   | Y | His系统生成的处方ID |
| 3.2.2  | rxNo     | 处方号   | String   | Y |          |
| 3.2.3  | recipeDetailList | 处方明细列表   | Array   | Y | 处方明细信息列表，结构如下：  |
| *******| lineNo   | 处方明细序号 | Long   | Y  |      |
| *******| hisLineNo | His明细序号    | Long    | Y | His系统生成的处方明细序号  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "visitId": 12546,
    "recipeList": [
      {
        "recipeId": 12545,
        "rxNo": "CF202505080001",
        "recipeDetailList": [{
          "lineNo": 1,
          "hisLineNo": 1
        }]
      }
    ]
  }
}
```

#### 2.2 处方签名

##### 接口说明
此接口用于对处方进行签名。

**接口Url**
POST /his/api/recipe/sign

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | visitId | 诊疗ID | Long   |  | Y |  |
| 2    | signDoctorCode | 签名医师编码 | String | 20 | Y |  |
| 3    | signDoctorName | 签名医师名称 | String | 50 | Y |  |
| 4    | storeCode | 发药药房编码 | String | 10 | Y |  |
| 5    | storeName | 发药药房名称 | String | 60 | Y |  |
| 6    | recipeList | 处方列表 | Array |  | Y | 需要签名的处方列表，结构如下： |
| 6.1  | rxNo    | 处方号 | String   | 20 | Y |  |

**请求示例**
```json
{
  "visitId": 12546,
  "signDoctorCode": "DOC001",
  "signDoctorName": "张医生",
  "storeCode": "STORE001",
  "storeName": "门诊药房",
  "recipeList": [
    {
      "rxNo": "CF202505080001"
    },
    {
      "rxNo": "CF202505080002"
    },
    {
      "rxNo": "CF202505080003"
    }
  ]
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |
| 3    | data     | 返回数据 | Object   | Y | 治疗单信息 |
| 3.1  | visitId  | 诊疗ID   | Long     | Y |  |
| 3.2  | resultList | 处方签名结果列表 | Array | Y | 处方签名结果信息列表，结构如下： |
| 3.2.1 | recipeId  | 处方流水号   | String   | Y |  |
| 3.2.2 | rxNo    | 处方号 | String   | Y |  | 
| 3.2.3 | treatmentList | 治疗单列表 | Array | N | 签名成功时返回处方对应的治疗单信息列表，结构如下： |
| ******* | artCode | 项目编码 | String | Y |  |
| ******* | artName | 项目名称 | String | Y |  |
| ******* | artSpec | 规格箱号 | String | Y |  |
| ******* | total | 数量 | BigDecimal | Y |  |
| ******* | unit | 单位 | String | Y | 1-制剂单位,2-包装单位,3-剂量单位 |
| ******* | unitType | 单位类型 | Integer | Y | 1-制剂单位,2-包装单位,3-剂量单位 |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "visitId": 12546,
    "resultList": [
      {
        "recipeId": 12546,
        "rxNo": "CF202505080001",
        "treatmentList": [
          {
            "artCode": "1006041",
            "artName": "静脉输液",
            "artSpec": "",
            "total": 2,
            "unit": "次",
            "unitType": 2
          }
        ]
      }
    ]
  }
}
```

#### 2.3 处方作废

##### 接口说明
此接口用于作废已保存的处方信息。

**接口Url**
POST /his/api/recipe/cancel

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | visitId | 诊疗ID | Long   |  | Y |  |
| 2    | rxNo    | 处方号 | String   | 20 | Y |  |
| 3    | userCode | 操作人编码 | String |  | Y |  |
| 4    | userName | 操作人名称 | String |  | Y |  |
| 5    | cancelTime | 作废时间 | String   |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 6    | sourceId | 数据来源 | Integer   |  | Y | 1-内部系统，2-外部系统 |

**请求示例**
```json
[{
  "visitId": 12546,
  "rxNo": "CF202505080001",
  "userCode": "USER001",
  "userName": "李操作员",
  "cancelTime": "2025-05-02 14:30:00",
  "sourceId": 1
}]
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

#### 2.4 处方状态回写

##### 接口说明
此接口用于更新处方的执行状态。

**接口Url**
POST /his/api/recipe/updateStatus

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | visitId | 诊疗ID | Long   |  | Y |  |
| 2    | rxNo    | 处方号 | String   | 20 | Y |  |
| 3    | status   | 处方状态 | Integer   | | Y | 1-已收费，2-已完成，3-已撤销 |
| 4    | operCode | 操作人编码 | String |  | Y |  |
| 5    | operName | 操作人名称 | String |  | Y |  |
| 6    | operTime | 操作时间 | String   |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 7    | sourceId | 数据来源 | Integer   |  | Y | 1-内部系统，2-外部系统 |

**请求示例**
```json
[{
  "visitId": 12546,
  "rxNo": "CF202505080001",
  "status": 1,
  "operCode": "USER001",
  "operName": "李操作员",
  "operTime": "2025-05-02 15:30:00",
  "sourceId": 1
}]
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

### 3. 医嘱接口

#### 3.1 医嘱保存及修改

##### 接口说明
此接口用于医嘱的保存及修改操作。

**接口Url**
POST /his/api/orderentry/saveOrUpdate

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ------ | ---- |
| 1    | visitId  | 诊疗ID   | Long     |          | Y      |      |
| 2    | deptCode | 科室代码  | String  |          | Y      |      |
| 3    | oeNo     | 医嘱号   | Integer  |          | Y      |      |
| 4    | oeTypeCode | 医嘱类型编码  | String | 10   | Y     | 1-临时,2-长期,3-备用 |
| 5    | oeTypeName | 医嘱类型名称  | String | 20   | Y     |       |
| 6    | appliedCode | 立嘱医生编码 | String | 20   | Y     |       |
| 7    | appliedName | 立嘱医生名称 | String | 50   | Y     |       |
| 8    | applyDeptcode | 开立科室编码 | String | 10 | Y     |       |
| 9    | applyDeptName | 开立科室名称 | String | 60 | Y     |       |
| 10   | execDeptcode | 执行科室编码 | String | 10  | N     |       |
| 11   | execDeptName | 执行科室名称 | String | 60  | N     |       |
| 12   | timeStarted | 医嘱时间     | String |      | N     | 格式：yyyy-MM-dd HH:mm:ss |
| 13   | artCode     | 项目编码     | String | 20   | Y     | 医嘱类型oeCat=8文字医嘱的artCode=0 |
| 14   | artName     | 项目名称     | String | 200  | Y     | 医嘱类型oeCat=8文字医嘱的artCode='文字医嘱' |
| 15   | artSpec     | 规格         | String | 100  | N     |       |
| 16   | total       | 总量         | BigDecimal | 12,4 | N |       |
| 17   | unit        | 计量单位     | String |      | N      |       |
| 18   | unitType    | 单位类型     | Integer |     | N      | 1-制剂单位,2-包装单位,3-剂量单位 |
| 19   | mealCells   | 每次制剂数量 | BigDecimal | 12,4 | N  | 即每次小单位用量 |
| 20   | cellUnit    | 制剂单位     | String |      | N      | 即每次小单位用量单位 |
| 21   | mealDoses   | 每次使用剂量 | BigDecimal | 12,4 | N  | 即每次用量 |
| 22   | doseUnit    | 剂量单位     | String |      | N     | 即每次用量单位 |
| 23   | groupNo     | 组号        | Integer |      | N     |         |
| 24   | groupMark   | 组标记      | Integer |      | N     | 0-不分组,1-组开始,2-组中间,3-组结束 |
| 25   | periodCycles| 天数        | Integer |      | N     |       |
| 26   | routeCode   | 给药途径编码 | String | 10    | N     |       |
| 27   | routeName   | 给药途径名称 | String | 20    | N     |       |
| 28   | freqCode    | 频次代码    | String | 10    | N      |       |
| 29   | freqName    | 频次名称    | String | 20    | N      |       |
| 30   | stFlag      | 皮试标志位  | Integer |      | N      |       |
| 31   | oeText      | 医嘱内容    | String | 2000  | N      |       |
| 32   | notice      | 嘱托       | String | 60     | N      |      |
| 33   | status      | 医嘱状态   | Integer |       | N      | 0-新开，1-待核对，2-执行中，3-待停止，4-已停止，5-已作废，6-质疑 |
| 34   | oeCat       | 医嘱类别   | Integer |       | N      | 1-药疗，2-治疗，3-护理，4-检验，5-检查，6-手术，7-膳食，8-文字，9-其他 |
| 35   | displayOrder| 显示顺序   | Integer |       | N      |       |
| 36   | isPatientProvided | 是否患者自备药   | Integer |     | N     | 1-患者自备药, 0-2-非患者自备药 |
| 37   | accessionNo | 申请单号   | String | 20     | N      |       |
| 38   | packageCode | 组套编码   | String | 20     | N      |       |
| 39   | packageName | 组套名称   | String | 200    | N      |       |
| 40   | specimenTypeCode | 样本类型编码 | String | 10  | N   |       |
| 41   | specimenTypeName | 样本类型名称 | String | 20  | N   |       |
| 42   | bodypartCode | 检查部位编码 | String |     | N      | 多个部位用英文逗号分隔 |
| 43   | bodypartName | 检查部位名称 | String |     | N      | 多个部位用英文逗号分隔 |
| 44   | recipe       | 处方信息     | Object |     | N      | 中药处方和出院带药，处方信息必填。处方参数，结构如下： |
| 44.1 | recipeTypeId | 处方类型代号 | Integer |    | Y      | 1-西药处方，2-中药处方 |
| 44.2 | rxNo         | 处方号      | String  | 20  | Y     |      |
| 44.3 | times        | 中药付数     | Integer    |          | N        | 默认为1 |
| 44.4 | recipeGroupList | 处方组列表   | Array    |          | Y        | 处方组信息列表，结构如下： |
| 44.4.1 | groupNo    | 组号         | Integer  |          | Y        |      |
| 44.4.2 | periodCycles | 天数         | Integer    |          | N        |      |
| 44.4.3 | freqCode     | 频次代码     | String     | 10       | N        | 西药处方为必填 |
| 44.4.4 | freqName     | 频次名称     | String     | 20       | N        |      |
| 44.4.5 | routeCode    | 给药途径编码 | String     | 10       | N        | 西药处方为必填 |
| 44.4.6 | routeName    | 给药途径名称 | String     | 20       | N        |      |
| 44.4.7 | notice       | 嘱托         | String     | 60       | N        |      |
| 44.4.8 | recipeDetailList | 处方组明细列表 | Array    |          | Y        | 处方组明细信息列表，结构如下： |
| ******** | lineNo       | 处方明细序号     | Long    |          | Y        |      |
| ******** | artCode      | 项目编码     | String     | 20       | Y        |      |
| ******** | artName      | 项目名称     | String     | 200      | Y        |      |
| ******** | artSpec      | 规格         | String     | 100      | N        |      |
| ******** | total        | 数量         | BigDecimal | 12,4     | Y        |      |
| ******** | unit         | 单位         | String     | 20       | Y        |      |
| ******** | unitType     | 单位类型     | Integer    |          | Y        | 1-制剂单位,2-包装单位,3-剂量单位 |
| ******** | mealCells    | 每次制剂数量 | BigDecimal | 12,4     | N        | 即每次小单位用量 |
| ******** | cellUnit     | 制剂单位     | String     | 20       | N        | 即每次小单位用量单位 |
| ********0 | mealDoses    | 每次使用剂量 | BigDecimal | 12,4     | N        | 即每次用量 |
| ********1 | doseUnit     | 剂量单位     | String     | 10       | N        | 即每次用量单位 |
| ********2 | processMethod| 中药制法     | String     | 60       | N        |      |
| ********3 | stRequired   | 是否需要皮试 | Integer    |          | N        | 0-不需要皮试 1-需要皮试 2-免皮试 |
| ********4 | displayOrder | 显示顺序     | Integer    |        | N |                       |

**请求示例**
```json
[{
  "visitId": 1245,
  "deptCode": "DEPT001",
  "oeNo": 1,
  "oeTypeCode": "1",
  "oeTypeName": "临时医嘱",
  "appliedCode": "DOC001",
  "appliedName": "张医生",
  "applyDeptcode": "DEPT001",
  "applyDeptName": "内科",
  "execDeptcode": "DEPT002",
  "execDeptName": "药剂科",
  "timeStarted": "2025-05-01 10:00:00",
  "artCode": "1006041",
  "artName": "阿莫西林胶囊",
  "artSpec": "0.25g*24粒",
  "total": 48,
  "unit": "粒",
  "unitType": 1,
  "mealCells": 2,
  "cellUnit": "粒",
  "mealDoses": 0.5,
  "doseUnit": "g",
  "groupNo": 1,
  "groupMark": 0,
  "periodCycles": 3,
  "routeCode": "1",
  "routeName": "口服",
  "freqCode": "tid",
  "freqName": "每日三次",
  "stFlag": 0,
  "oeText": "阿莫西林胶囊 0.25g*24粒 2粒/次 tid 口服 3天",
  "notice": "饭后服用",
  "status": 0,
  "oeCat": 1,
  "displayOrder": 1,
  "isPatientProvided": 0,
  "recipe": {
    "recipeTypeId": 1,
    "rxNo": "CF202505080001",
    "times": 1,
    "recipeGroupList": [
      {
        "groupNo": 1,
        "periodCycles": 3,
        "freqCode": "tid",
        "freqName": "每日三次",
        "routeCode": "1",
        "routeName": "口服",
        "notice": "饭后服用",
        "recipeDetailList": [
          {
            "lineNo": 1,
            "displayOrder": 1,
            "artCode": "1006041",
            "artName": "阿莫西林胶囊",
            "artSpec": "0.25g*24粒",
            "total": 2,
            "unit": "盒",
            "unitType": 2,
            "mealCells": 1,
            "cellUnit": "粒",
            "mealDoses": 0.25,
            "doseUnit": "g",
            "stRequired": 0
          }
        ]
      }
    ]
  }
}]
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |
| 3    | data     | 返回数据 | Array   | Y | |
| 3.1  | visitId  | 诊疗ID   | Long   | Y | |
| 3.2  | oeNo     | 医嘱号   | Long   | Y |   |
| 3.3  | hisOeNo  | His医嘱号 | Integer | Y | His系统生成的医嘱号  |
| 3.4  | recipe   | 处方信息 | Object   | N | 中药处方和出院带药会有返回值  |
| 3.4.1 | recipeId | 处方ID  | Long    | Y | His系统生成的处方ID |
| 3.4.2 | rxNo     | 处方号  | String   | Y |   |
| 3.4.3 | recipeDetailList | 处方明细列表   | Array   | Y | 处方明细信息列表，结构如下：  |
| *******| lineNo   | 处方明细序号 | Long   | Y  |      |
| *******| hisLineNo | His明细序号    | Long    | Y | His系统生成的处方明细序号  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": [{
    "visitId": 1245,
    "oeNo": 1,
    "hisOeNo": 1,
    "recipe":  {
      "recipeId": 1,
      "rxNo": "CF202505080001",
      "recipeDetailList": [
        {
          "lineNo": 1,
          "hisLineNo": 1
        }
      ]
    }
  }]
}
```

#### 3.2 医嘱作废

##### 接口说明
此接口用于作废已保存的医嘱信息。

**接口Url**
POST /his/api/orderentry/cancel

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | visitId  | 诊疗ID   | Long   |  | Y |  |
| 2    | oeNo     | 医嘱号   | Long   |  | Y |  |
| 3    | userCode | 作废人编码 | String |  | Y |  |
| 4    | userName | 作废人名称 | String |  | Y |  |
| 5    | cancelTime | 作废时间 | String   |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 6    | sourceId | 数据来源 | Integer   |  | Y | 1-内部系统，2-外部系统 |

**请求示例**
```json
[{
  "visitId": 1245,
  "oeNo": 1,
  "userCode": "USER001",
  "userName": "李操作员",
  "cancelTime": "2025-05-02 14:30:00",
  "sourceId": 1
}]
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

#### 3.3 医嘱状态回写

##### 接口说明
此接口用于更新医嘱的执行状态。

**接口Url**
POST /his/api/orderentry/updateStatus

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | operCode | 操作人编码 | String |  | Y |  |
| 2    | operName | 操作人名称 | String |  | Y |  |
| 3    | operTime | 操作时间 | String   |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 4    | sourceId | 数据来源 | Integer   |  | Y | 1-内部系统，2-外部系统 |
| 5    | orderEntryLs | 医嘱列表 | Array |  | Y | 医嘱信息列表，结构如下： |
| 5.1  | visitId  | 诊疗ID   | Long   |  | Y |  |
| 5.2  | oeNo     | 医嘱号   | Long   |  | Y |  |
| 5.3  | status   | 医嘱状态 | Integer   |  | Y | 0-新开，1-待核对，2-执行中，3-待停止，4-已停止，5-已作废，6-质疑 |
| 5.4  | cause    | 原因 | String   | 60 | Y |  |
| 5.5  | clinicianCode | 医师编码 | String |  | Y |  |
| 5.6  | clinicianName | 医师名称 | String |  | Y |  |
| 5.7  | oeTime   | 处理时间 | String |  | Y | 格式：yyyy-MM-dd HH:mm:ss |

**请求示例**
```json
{
  "operCode": "USER001",
  "operName": "李操作员",
  "operTime": "2025-05-02 11:05:00",
  "sourceId": 1,
  "orderEntryLs": [
    {
      "visitId": 1245,
      "oeNo": 1,
      "status": 2,
      "cause": "",
      "clinicianCode": "DOC001",
      "clinicianName": "张医生",
      "oeTime": "2025-05-02 11:00:00"
    }
  ]
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

### 4. 申请单接口

#### 4.1 保存申请单

##### 接口说明
此接口用于检查、检验等非手术类申请单的保存及修改操作。

**接口Url**
POST /his/api/apply/saveOrUpdate

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ------- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1       | visitId  | 诊疗ID   | Long     |    | Y |  |
| 2       | orderCount | 申请单数量 | Integer |  | Y |  |
| 3       | applyList | 申请单列表 | Array |  | Y | 申请单列表，结构如下： |
| 3.1     | clinicType | 诊疗类别 | Integer |  | Y | 1-门诊，2-住院 |
| 3.2     | accessionNo | 申请单号 | String | 20 | Y |  |
| 3.3     | oeNo    | 医嘱号   | Long |  | N | clinicType=2时必填 |
| 3.4     | displayOrder  | 显示顺序  | Integer | | N | clinicType=2时必填 |
| 3.5     | orderTypeCode | 申请类型代码 | String | 10 | Y |  |
| 3.6     | orderTypeName | 申请类型名称 | String | 20 | N |  |
| 3.7     | clinicianCode | 申请医师编码 | String | 20 | Y |  |
| 3.8     | clinicianName | 申请医师名称 | String | 50 | Y |  |
| 3.9     | applyDeptcode | 申请科室编码 | String | 10 | Y |  |
| 3.10    | applyDeptName | 申请科室名称 | String | 20 | Y |  |
| 3.11    | exceDeptcode | 执行科室编码 | String | 10 | Y |  |
| 3.12    | exceDeptName | 执行科室名称 | String | 20 | Y |  |
| 3.13    | applyDate | 申请时间 | String |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 3.14    | medicalHistory | 病史摘要 | String | 2000 | N |  |
| 3.15    | purposeDesc | 申请目的 | String | 255 | N |  |
| 3.16    | applyDetailList | 申请单明细列表 | Array |  | Y | 检查检验申请必填，结构如下： |
| 3.17.1  | artCode | 项目编码 | String | 20 | N | |
| 3.17.2  | artName | 项目名称 | String | 200 | N | |
| 3.17.3  | total | 总量 | BigDecimal | 12,4 | N | |
| 3.17.4  | unit | 计量单位 | String | | N | |
| 3.17.5  | unitType | 单位类型 | Integer | | N | 1-制剂单位,2-包装单位,3-剂量单位 |
| 3.17.6  | specimenTypeCode | 标本类型代码 | String | 10 | N | |
| 3.17.7  | specimenTypeName | 标本类型名称 | String | 20 | N | |
| 3.17.8  | bodypartCode | 检查部位编码 | String | | N | 多个部位用英文逗号分隔 |
| 3.17.9  | bodypartName | 检查部位名称 | String | | N | 多个部位用英文逗号分隔 |
| 3.17.10 | remarks | 备注 | String | 60 | N | |

**请求示例**
```json
{
  "visitId": 2541,
  "orderCount": 1,
  "applyList": [
    "clinicType": 1,
    "accessionNo": "LIS202505080001",
    "orderTypeCode": "LIS",
    "orderTypeName": "检验申请",
    "clinicianCode": "DOC001",
    "clinicianName": "张医生",
    "applyDeptcode": "DEPT001",
    "applyDeptName": "内科",
    "exceDeptcode": "DEPT003",
    "exceDeptName": "检验科",
    "applyDate": "2025-05-01 10:30:00",
    "medicalHistory": "患者有咳嗽症状三天",
    "purposeDesc": "检查血常规",
    "applyDetailList": [
      {
        "artCode": "LIS001",
        "artName": "血常规检验",
        "total": 1,
        "unit": "次",
        "unitType": 1,
        "specimenTypeCode": "BLOOD",
        "specimenTypeName": "血液",
        "remarks": "空腹采血"
      },
      {
        "artCode": "LIS002",
        "artName": "血糖检验",
        "total": 1,
        "unit": "次",
        "unitType": 1,
        "specimenTypeCode": "BLOOD",
        "specimenTypeName": "血液",
        "remarks": "空腹采血"
      }
    ]
  ]
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ------ | -------- | -------- | -------- | -------- | ---- |
| 1      | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2      | msg      | 返回信息 | String   | Y |  |
| 3      | data     | 返回数据 | Array   | Y | |
| 3.1    | visitId     | 诊疗ID   | Long   | Y | |
| 3.2    | applyList | 申请单列表 | Array | Y | 申请单列表，结构如下： |
| 3.2.1  | accessionNo | 申请单号 | String   | Y | |
| 3.2.2  | orderId  | 申请单流水号 | Long   | Y | |
| 3.2.3  | oeNo     | 医嘱号   | Long   | N | clinicType=2住院类型时有返回值  |
| 3.3.4  | hisOeNo  | His医嘱号 | Integer | N | His系统生成的医嘱号  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "visitId": 2541,
    "applyList": [
      {
        "accessionNo": "LIS202505080001",
        "orderId": 2142
      }
    ]
  }
}
```

#### 4.2 手术申请

##### 接口说明
此接口用于手术申请单的保存及修改操作。

**接口Url**
POST /his/api/surgery/saveOrUpdate

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1     | visitId | 诊疗ID | Long |  | Y |  |
| 2     | clinicType | 诊疗类别 | Integer |  | Y | 1-门诊，2-住院 |
| 3     | accessionNo | 申请单号 | String | 20 | Y |  |
| 4     | oeNo    | 医嘱号   | Long |  | N | clinicType=2时必填 |
| 5     | displayOrder  | 显示顺序  | Integer | | N | clinicType=2时必填 |
| 6     | clinicianCode | 申请医师编码 | String | 20 | Y |  |
| 7     | clinicianName | 申请医师名称 | String | 50 | Y |  |
| 8     | applyDeptcode | 申请科室编码 | String | 10 | Y |  |
| 9     | applyDeptName | 申请科室名称 | String | 20 | Y |  |
| 10    | exceDeptcode | 执行科室编码 | String | 10 | Y |  |
| 11    | exceDeptName | 执行科室名称 | String | 20 | Y |  |
| 12    | applyDate | 申请时间 | String |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 13    | medicalHistory | 病史摘要 | String | 2000 | N |  |
| 14    | purposeDesc | 申请目的 | String | 255 | N |  |
| 15    | operatorCode | 手术医师编码 | String | 20 | Y | |
| 16    | operatorName | 手术医师名称 | String | 50 | Y | |
| 17    | supervisorName | 指导医生姓名 | String | 50 | N | |
| 18    | assistant1Code | 一助医师编码 | String | 20 | N | |
| 19    | assistant1Name | 一助医师姓名 | String | 50 | N | |
| 20    | assistant2Code | 二助医师编码 | String | 20 | N | |
| 21    | assistant2Name | 二助医师姓名 | String | 50 | N | |
| 22    | assistant3Code | 三助医师编码 | String | 20 | N | |
| 23    | assistant3Name | 三助医师姓名 | String | 50 | N | |
| 24    | isEmergency | 是否紧急手术 | Integer |  | N | 0-否，1-是 |
| 25    | isAmbulatorySurgery | 是否日间手术 | Integer |  | N | 0-否，1-是 |
| 26    | estStarted | 计划手术时间 | String |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 27    | positionDesc | 手术部位 | String | 60 | Y | |
| 28    | incisionTypeId | 切口类型代号 | Integer |  | Y | 1-Ⅰ类（清洁）切口，2-Ⅱ类（清洁-污染）切口，3-Ⅲ类（污染）切口，4-Ⅳ类（污秽-感染）切口，5-0类切口 |
| 29    | artCode | 手术项目编码  | String | 20 | Y | |
| 30    | artName | 手术项目名称  | String | 200 | Y | |
| 31    | surgeryCat | 手术术式 | String | 100 | Y | |
| 32    | estHours | 预计用时(小时) | BigDecimal | 5,2 | Y | |
| 33    | opLevelId | 手术级别 | Integer |  | Y | 1-一级，2-二级，3-三级，4-四级 |
| 34    | anstDeptcode | 麻醉科室编码 | String | 10 | Y | |
| 35    | anstDeptNme | 麻醉科室名称 | String | 20 | Y | |

**请求示例**
```json
{
  "visitId": 2541,
  "clinicType": 1,
  "accessionNo": "SURG202505080001",
  "clinicianCode": "DOC002",
  "clinicianName": "李医生",
  "applyDeptcode": "DEPT002",
  "applyDeptName": "外科",
  "exceDeptcode": "DEPT004",
  "exceDeptName": "手术室",
  "applyDate": "2025-05-02 09:00:00",
  "medicalHistory": "患者腹痛三天，拟行阑尾切除术。",
  "purposeDesc": "手术治疗",
  "operatorCode": "DOC003",
  "operatorName": "王主刀",
  "supervisorName": "张主任",
  "assistant1Code": "DOC004",
  "assistant1Name": "赵一助",
  "assistant2Code": "DOC005",
  "assistant2Name": "钱二助",
  "assistant3Code": "DOC006",
  "assistant3Name": "孙三助",
  "isEmergency": 1,
  "isAmbulatorySurgery": 0,
  "estStarted": "2025-05-03 08:00:00",
  "positionDesc": "右下腹",
  "incisionTypeId": 1,
  "artCode": "SURG001",
  "artName": "阑尾切除术",
  "surgeryCat": "腹部手术",
  "estHours": 2.5,
  "opLevelId": 2,
  "anstDeptcode": "DEPT005",
  "anstDeptNme": "麻醉科"
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |
| 3    | data     | 返回数据 | Object   | Y | |
| 3.1  | visitId     | 诊疗ID   | Long   | Y | |
| 3.2  | accessionNo | 申请单号 | String   | Y | |
| 3.3  | orderId | 申请单流水号 | Long   | Y | |
| 3.4  | oeNo     | 医嘱号   | Long   | N | clinicType=2住院类型时有返回值  |
| 3.5  | hisOeNo  | His医嘱号 | Integer | N | His系统生成的医嘱号  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "visitId": 2541,
    "accessionNo": "SURG202505080001",
    "orderId": 3142
  }
}
```

#### 4.3 治疗申请

##### 接口说明
此接口用于非手术类治疗申请单的保存及修改操作。

**接口Url**
POST /his/api/treatment/saveOrUpdate

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ------- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1       | visitId | 诊疗ID | Long |  | Y |  |
| 2       | orderCount | 申请单数量 | Integer |  | Y |  |
| 3       | applyList | 申请单列表 | Array |  | Y | 申请单列表，结构如下： |
| 3.1     | clinicType | 诊疗类别 | Integer |  | Y | 1-门诊，2-住院 |
| 3.2     | accessionNo | 申请单号 | String | 20 | Y |  |
| 3.3     | oeNo    | 医嘱号   | Long |  | N | clinicType=2时必填 |
| 3.4     | displayOrder  | 显示顺序  | Integer | | N | clinicType=2时必填 |
| 3.5     | clinicianCode | 申请医师编码 | String | 20 | Y |  |
| 3.6     | clinicianName | 申请医师名称 | String | 50 | Y |  |
| 3.7     | applyDeptcode | 申请科室编码 | String | 10 | Y |  |
| 3.8     | applyDeptName | 申请科室名称 | String | 20 | Y |  |
| 3.9     | exceDeptcode | 执行科室编码 | String | 10 | Y |  |
| 3.10    | exceDeptName | 执行科室名称 | String | 20 | Y |  |
| 3.11    | applyDate | 申请时间 | String |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 3.12    | medicalHistory | 病史摘要 | String | 2000 | N |  |
| 3.13    | purposeDesc | 申请目的 | String | 255 | N |  |
| 3.14    | applyDetailList | 申请单明细列表 | Array |  | Y | 治疗项目明细列表，结构如下： |
| 3.14.1  | artCode | 项目编码 | String | 20 | Y | |
| 3.14.2  | artName | 项目名称 | String | 200 | Y | |
| 3.14.3  | total | 总量 | BigDecimal | 12,4 | Y | |
| 3.14.4  | unit | 计量单位 | String | 20 | Y | |
| 3.14.5  | unitType | 单位类型 | Integer | | N | 1-制剂单位,2-包装单位,3-剂量单位 |
| 3.14.6  | freqCode | 频次代码 | String | 10 | N | |
| 3.14.7  | freqName | 频次名称 | String | 20 | N | |
| 3.14.8  | remarks | 备注 | String | 60 | N | |

**请求示例**
```json
{
  "visitId": 2541,
  "orderCount": 1,
  "applyList": [
    {
      "clinicType": 1,
      "accessionNo": "TREAT202505080001",
      "clinicianCode": "DOC001",
      "clinicianName": "张医生",
      "applyDeptcode": "DEPT001",
      "applyDeptName": "内科",
      "exceDeptcode": "DEPT006",
      "exceDeptName": "康复科",
      "applyDate": "2025-05-01 10:30:00",
      "medicalHistory": "患者右膝关节疼痛两周",
      "purposeDesc": "进行康复理疗",
      "applyDetailList": [
        {
          "artCode": "TREAT001",
          "artName": "红外线治疗",
          "total": 6,
          "unit": "次",
          "unitType": 1,
          "freqCode": "bid",
          "freqName": "每日两次",
          "remarks": "疼痛区域重点照射"
        }
      ]
    }
  ]
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ------ | -------- | -------- | -------- | -------- | ---- |
| 1      | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2      | msg      | 返回信息 | String   | Y |  |
| 3      | data     | 返回数据 | Object   | Y | |
| 3.1    | visitId     | 诊疗ID   | Long   | Y | |
| 3.2    | applyList | 申请单列表 | Array | Y | 申请单列表，结构如下： |
| 3.2.1  | accessionNo | 申请单号 | String   | Y | |
| 3.2.2  | orderId | 申请单流水号 | Long   | Y | |
| 3.2.3  | oeNo     | 医嘱号   | Long   | N | clinicType=2住院类型时有返回值  |
| 3.3.4  | hisOeNo  | His医嘱号 | Integer | N | His系统生成的医嘱号  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "visitId": 2541,
    "applyList": [
      {
        "accessionNo": "TREAT202505080001",
        "orderId": 2143
      }
    ]
  }
}
```

#### 4.4 申请单删除

##### 接口说明
此接口用于删除已保存的申请单信息。

**接口Url**
POST /his/api/apply/delete

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | visitId     | 诊疗ID | Long   |  | Y |  |
| 2    | accessionNo | 申请单号 | String   | 20 | Y |  |
| 3    | clinicType | 诊疗类别 | Integer |  | Y | 1-门诊，2-住院 |
| 4    | oeNo     | 医嘱号   | Long |  | N | clinicType=2时必填 |
| 5    | sourceId | 数据来源 | Integer   |  | Y | 1-内部系统，2-外部系统 |

**请求示例**
```json
[{
  "visitId": 2541,
  "accessionNo": "LIS202505080001",
  "clinicType": 1,
  "sourceId": 1
}]
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

#### 4.5 申请单作废

##### 接口说明
此接口用于作废已保存的申请单信息。

**接口Url**
POST /his/api/apply/cancel

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | visitId     | 诊疗ID | Long   |  | Y |  |
| 2    | accessionNo | 申请单号 | String   | 20 | Y |  |
| 3    | clinicType | 诊疗类别 | Integer |  | Y | 1-门诊，2-住院 |
| 4    | oeNo     | 医嘱号   | Long |  | N | clinicType=2时必填 |
| 4    | userCode | 作废人编码 | String |  | Y |  |
| 5    | userName | 作废人名称 | String |  | Y |  |
| 6    | cancelTime | 作废时间 | String   |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 7    | sourceId | 数据来源 | Integer   |  | Y | 1-内部系统，2-外部系统 |

**请求示例**
```json
[{
  "visitId": 2541,
  "accessionNo": "LIS202505080001",
  "clinicType": 1,
  "userCode": "USER001",
  "userName": "李操作员",
  "cancelTime": "2025-05-02 14:30:00",
  "sourceId": 1
}]
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

#### 4.6 申请单状态回写

##### 接口说明
此接口用于更新申请单的执行状态。

**接口Url**
POST /his/api/apply/updateStatus

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ------- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1       | visitId     | 诊疗ID | Long   |  | Y |  |
| 2       | accessionNo | 申请单号 | String   | 20 | Y |  |
| 3       | clinicType  | 诊疗类别 | Integer |  | Y | 1-门诊，2-住院 |
| 4       | oeNo        | 医嘱号   | Long |  | N | clinicType=2时必填 |
| 5       | orderTypeCode | 申请类型代码 | String | 10 | Y |  |
| 6       | orderTypeName | 申请类型名称 | String | 20 | N |  |
| 7       | status   | 申请单状态 | Integer   |  | Y | 0-新开，2-审核，3-执行，4-完成，5-取消，6-作废 |
| 8       | operCode | 操作人编码 | String |  | Y |  |
| 9       | operName | 操作人名称 | String |  | Y |  |
| 10      | operTime | 操作时间 | String   |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 11      | sourceId | 数据来源 | Integer   |  | Y | 1-内部系统，2-外部系统 |
| 12      | pacsList | 检查报告列表 | Array |  | N | 检查完成时必填，结构如下： |
| 12.1    | modality | 检查类型 | String | 20 | N | MR/CT/DX/RF/NM/PET/BMD等 |
| 12.2    | studydate | 检查时间 | String |  | N | 格式：yyyy-MM-dd HH:mm:ss |
| 12.3    | reportdate | 报告时间 | String |  | N | 格式：yyyy-MM-dd HH:mm:ss |
| 12.4    | assessdate | 审核时间 | String |  | N | 格式：yyyy-MM-dd HH:mm:ss |
| 12.5    | oper | 检查医师编码 | String | 20 | N | |
| 12.6    | reporter | 报告医师编码 | String | 20 | N | |
| 12.7    | assessor | 审核医师编码 | String | 20 | N | |
| 12.8    | positive | 是否阳性 | String | 1 | N | Y-是，N-否 |
| 12.9    | examreport | 检查所见 | String | 2000 | N | |
| 12.10   | conclusion | 诊断结论 | String | 2000 | N | |
| 12.11   | reportUrl | 报告地址 | String | 500 | N | |
| 12.12   | imageUrl | 影像地址 | String | 500 | N | |
| 13      | lisResultList | 检验报告列表 | Array |  | N | 检验完成时必填，结构如下： |
| 13.1    | patInfoId | 报告单唯一主键 | Integer |  | N | |
| 13.2    | artName | 项目名称 | String | 200 | N | |
| 13.3    | msg | 报告信息 | String | 500 | N | |
| 13.4    | testDate | 检验日期 | String |  | N | 格式：yyyy-MM-dd |
| 13.5    | technicianName | 检验者 | String | 50 | N | |
| 13.6    | testUserCode | 检验者编码 | String | 20 | N | |
| 13.7    | qcCode | 审核者编码 | String | 20 | N | |
| 13.8    | qcName | 审核者 | String | 50 | N | |
| 13.9    | timeTest | 检查时间 | String |  | N | 格式：yyyy-MM-dd HH:mm:ss |
| 13.10   | timeChecked | 报告审核时间 | String |  | N | 格式：yyyy-MM-dd HH:mm:ss |
| 13.11   | remark | 报告备注 | String | 500 | N | |
| 13.12   | comment | 评语 | String | 500 | N | |
| 13.13   | pdfPathLs | 检验报告路径 | Array |  | N | |
| 13.14   | results | 检验结果列表 | Array |  | N | 结构如下： |
| 13.14.1 | itemCode | 检验项目编码 | String | 20 | N | |
| 13.14.2 | itemName | 检验项目中文名称 | String | 100 | N | |
| 13.14.3 | itemEname | 检验项目英文名称 | String | 100 | N | |
| 13.14.4 | result | 检验结果 | String | 100 | N | |
| 13.14.5 | resultType | 结果类型 | Integer |  | N | 不一定有值，没有的去state取结果状态 |
| 13.14.6 | unit | 单位 | String | 20 | N | |
| 13.14.7 | state | 结果状态 | String | 20 | N | |
| 13.14.8 | limit | 参考范围 | String | 100 | N | |

**请求示例**
```json
[{
  "visitId": 2541,
  "accessionNo": "LIS202505080001",
  "clinicType": 1,
  "orderTypeCode": "LIS",
  "orderTypeName": "检验申请",
  "status": 4,
  "operCode": "USER001",
  "operName": "李操作员",
  "operTime": "2025-05-02 09:35:00",
  "sourceId": 1,
  "pacsList": [],
  "lisResultList": [
    {
      "patInfoId": 123456,
      "artName": "血常规检验",
      "msg": "正常检验结果",
      "testDate": "2025-05-02",
      "technicianName": "王技师",
      "testUserCode": "TECH001",
      "qcCode": "QC001",
      "qcName": "张审核",
      "timeTest": "2025-05-02 09:00:00",
      "timeChecked": "2025-05-02 09:30:00",
      "remark": "",
      "comment": "",
      "pdfPathLs": ["/report/lis/20230502/12345678.pdf"],
      "results": [
        {
          "itemCode": "WBC",
          "itemName": "白细胞",
          "itemEname": "WBC",
          "result": "5.2",
          "resultType": 1,
          "unit": "10^9/L",
          "state": "normal",
          "limit": "4.0-10.0"
        },
        {
          "itemCode": "RBC",
          "itemName": "红细胞",
          "itemEname": "RBC",
          "result": "4.8",
          "resultType": 1,
          "unit": "10^12/L",
          "state": "normal",
          "limit": "3.5-5.5"
        }
      ]
    }
  ]
}]
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

#### 3.7 手术申请状态回写

##### 接口说明
此接口用于批量回写手术申请单的执行状态。

**接口Url**  
POST /his/api/surgery/updateStatus

##### 请求参数
| 序号 | 参数代码     | 参数名称     | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | ------------- | ------------ | -------- | -------- | -------- | ---- |
| 1    | operCode      | 操作人编码   | String   |          | Y        |      |
| 2    | operName      | 操作人名称   | String   |          | Y        |      |
| 3    | operTime      | 操作时间     | String   |          | Y        | 格式：yyyy-MM-dd HH:mm:ss |
| 4    | sourceId      | 数据来源     | Integer  |          | Y        | 1-内部系统，2-外部系统 |
| 5    | surgeryLs     | 手术列表     | Array    |          | Y        | 手术状态回写列表，结构如下： |
| 5.1  | visitId       | 诊疗ID       | Long     |          | Y        |       |
| 5.2  | accessionNo   | 申请单号     | String   | 20       | Y        |       |
| 5.3  | clinicType    | 诊疗类别     | Integer  |          | Y        | 1-门诊，2-住院 |
| 5.4  | oeNo          | 医嘱号       | Long     |          | N        | clinicType=2时必填 |
| 5.3  | status        | 手术状态     | Integer  |          | Y        | 0-待安排，1-已安排，2-已访视，3-已进入，4-已核查，5-麻醉开始，6-手术开始，7-麻醉结束，8-手术结束，9-复苏结束，10-已送出，11-已完成，12-已取消 |
| 5.4  | clinicianCode | 医师编码     | String   | 20       | N        |       |
| 5.5  | clinicianName | 医师名称     | String   | 50       | N        |       |
| 5.6  | startDate     | 开始时间     | String   |          | Y        | 格式：yyyy-MM-dd HH:mm:ss |
| 5.7  | endDate       | 结束时间     | String   |          | Y        | 格式：yyyy-MM-dd HH:mm:ss |

**请求示例**
```json
{
  "operCode": "USER001",
  "operName": "李操作员",
  "operTime": "2025-05-03 12:00:00",
  "sourceId": 1,
  "surgeryLs": [
    {
      "visitId": 2541,
      "accessionNo": "SURG202505080001",
      "status": 6,
      "clinicianCode": "CLIN001",
      "clinicianName": "王医师",
      "startDate": "2025-05-03 12:00:00",
      "endDate": "2025-05-03 13:00:00"
    }
  ]
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y        | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y        |      |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

### 5. 诊疗接口

#### 5.1 诊疗信息回写

##### 接口说明
此接口用于回写或保存诊疗信息。

**接口Url**  
POST /his/api/visit/saveOrUpdate

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | ------ | -------- | -------- | -------- | -------- | ---- |
| 1    | visitId | 诊疗ID | Long |  | Y |  |
| 2    | patientId | 患者ID | Long |  | N |  |
| 3    | patientName | 患者姓名 | String | 50 | Y |  |
| 4    | clinicianCode | 接诊医生编码 | String | 20 | Y |  |
| 5    | clinicianName | 接诊医生名称 | String | 50 | Y |  |
| 6    | clinicType | 诊疗类别 | Integer |  | Y | 1-门诊，2-住院 |
| 7    | clinicDate | 就诊日期 | Integer |  | N |  |
| 8    | deptCode | 科室代码 | String | 10 | Y |  |
| 9    | deptName | 科室名称 | String | 20 | Y |  |
| 10   | genderId | 性别 | Integer |  | Y | 0-未知性别，1-男，2-女 |
| 11   | ageOfYears | 年龄 | Integer |  | N |  |
| 12   | ageOfDays | 日龄 | Integer |  | N |  |
| 13   | birthDate | 出生日期 | Integer |  | N |  |
| 14   | certTypeId | 证件类型代号 | Integer |  | N |  |
| 15   | idcertNo | 证件号码 | String | 20 | N |  |
| 16   | insuranceTypeId | 险种类型代号 | Integer |  | N |  |
| 17   | patientTypeId | 患者类型代号 | Integer |  | N |  |
| 18   | diseaseCode | 病种编码 | String | 20 | N |  |
| 19   | diseaseName | 病种名称 | String | 60 | N |  |
| 20   | civilServantFlag | 公务员标志位 | Integer |  | N |  |
| 21   | employerName | 参保单位名称 | String | 100 | N |  |
| 22   | telNo | 电话号码 | String | 40 | N |  |
| 23   | livingZonecode | 现居地区代号 | String | 10 | N |  |
| 24   | livingAddr | 现居详细地址 | String | 80 | N |  |
| 25   | companionName | 陪诊人姓名 | String | 50 | N |  |
| 26   | relationshipId | 关系代号 | Integer |  | N |  |
| 27   | contactTel | 联系人电话 | String | 40 | N |  |
| 28   | companionIdno | 联系人身份证号码 | String | 20 | N |  |
| 29   | companionAddr | 陪诊人地址 | String | 80 | N |  |
| 30   | isTraumatic | 是否外伤(0-否,1-是) | Integer |  | N |  |
| 31   | isTpRelative | 是否涉及第三方(0-否,1-是) | Integer |  | N |  |
| 32   | temperature | 体温 | BigDecimal |  | N |  |
| 33   | heightCm | 身高cm | Integer |  | N |  |
| 34   | weightKg | 体重Kg | BigDecimal |  | N |  |
| 35   | dbp | 舒张压 | Integer |  | N |  |
| 36   | sbp | 收缩压 | Integer |  | N |  |
| 37   | pulse | 脉搏率 | Integer |  | N |  |
| 38   | rr | 呼吸率 | Integer |  | N |  |
| 39   | complainedAs | 主诉 | String | 60 | Y |  |
| 40   | hpiDesc | 现病史 | String | 1024 | N |  |
| 41   | pastHistory | 既往史 | String | 1024 | N |  |
| 42   | familyHistory | 家族史 | String | 1024 | N |  |
| 43   | allergicHistory | 过敏史 | String | 255 | N |  |
| 44   | generalInspection | 一般检查 | String | 1024 | N |  |
| 45   | differentialDiag | 鉴别诊断 | String | 1024 | N |  |
| 46   | treatAbstract | 治疗方案 | String | 255 | N |  |
| 47   | progressNotes | 病程记录 | String | 255 | N |  |
| 48   | auxiliaryInspection | 辅助检查 | String | 1024 | N |  |
| 49   | maritalHistory | 婚姻史 | String | 255 | N |  |
| 50   | childbearingHistory | 生育史 | String | 255 | N |  |
| 51   | menstrualHistory | 月经史 | String | 255 | N |  |
| 52   | healthEducation | 健康宣教 | String | 255 | N |  |

**请求示例**
```json
{
  "visitId": 123456,
  "patientId": 2001,
  "patientName": "张三",
  "clinicianCode": "DOC001",
  "clinicianName": "李医生",
  "clinicType": 1,
  "clinicDate": 20250512,
  "deptCode": "DEPT001",
  "deptName": "内科",
  "genderId": 1,
  "ageOfYears": 35,
  "ageOfDays": 0,
  "birthDate": 19900101,
  "certTypeId": 1,
  "idcertNo": "123456789012345678",
  "insuranceTypeId": 2,
  "patientTypeId": 1,
  "diseaseCode": "A001",
  "diseaseName": "高血压",
  "civilServantFlag": 0,
  "employerName": "某公司",
  "telNo": "13800000000",
  "livingZonecode": "130600",
  "livingAddr": "河北省保定市阜平县",
  "companionName": "王五",
  "relationshipId": 2,
  "contactTel": "13900000000",
  "companionIdno": "987654321098765432",
  "companionAddr": "河北省保定市阜平县",
  "isTraumatic": 0,
  "isTpRelative": 0,
  "temperature": 36.5,
  "heightCm": 170,
  "weightKg": 65.5,
  "dbp": 80,
  "sbp": 120,
  "pulse": 75,
  "rr": 18,
  "complainedAs": "咳嗽三天，伴有发热",
  "hpiDesc": "患者三天前出现咳嗽，逐渐加重，伴有低热。",
  "pastHistory": "既往体健。",
  "familyHistory": "无家族遗传病史。",
  "allergicHistory": "无药物过敏史。",
  "generalInspection": "生命体征平稳。",
  "differentialDiag": "需排除支气管炎。",
  "treatAbstract": "建议多饮水，休息，必要时服药。",
  "progressNotes": "病情稳定。",
  "auxiliaryInspection": "血常规、胸片未见异常。",
  "maritalHistory": "已婚。",
  "childbearingHistory": "二胎。",
  "menstrualHistory": "正常。",
  "healthEducation": "注意休息，预防感冒。"
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y        | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y        |      |
| 3    | data     | 返回数据 | Object   | Y        | 返回诊疗ID等信息 |
| 3.1  | visitId  | 诊疗ID   | Long | Y |  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "visitId": 123456
  }
}
```

#### 5.2 诊断回写

##### 接口说明
此接口用于回写患者诊断信息。

**接口Url**
POST /his/api/diagnosis/saveOrUpdate

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | visitId | 诊疗ID | Long   |  | Y |  |
| 3    | diagList | 诊断列表 | Array |  | Y | 诊断对象数组，结构如下： |
| 3.2  | diagCode | 诊断编码 | String | 30 | Y |  |
| 3.3  | diagName | 诊断名称 | String | 60 | Y |  |
| 3.4  | diagType | 诊断类型 | Integer |  | Y | 1-西医诊断，2-中医主病，3-中医主症 |
| 3.5  | diagCatId | 诊断类别 | Integer |  | Y | 1-门诊诊断，2-入院诊断，3-修正诊断，4-出院诊断，5-院内感染，6-损伤中毒，7-术前诊断，8-术后诊断，9-并发症，10-影像学诊断，11-病原学诊断，12-病理学诊断 |
| 3.6  | clinicianCode | 诊断医师编码 | String | 20 | Y |  |
| 3.7  | clinicianName | 诊断医师姓名 | String | 50 | Y |  |
| 3.8  | diagDate | 诊断日期 | String |  | Y | 格式：yyyy-MM-dd HH:mm:ss |

**请求示例**
```json
[{
  "visitId": 1256,
  "diagList": [
    {
      "diagCode": "J00.x00",
      "diagName": "急性上呼吸道感染",
      "diagType": 1,
      "diagCatId": 1,
      "clinicianCode": "DOC001",
      "clinicianName": "张医生",
      "diagDate": "2025-05-01 10:00:00"
    },
    {
      "diagCode": "TCM001",
      "diagName": "风寒感冒",
      "diagType": 1,
      "diagCatId": 1,
      "clinicianCode": "DOC001",
      "clinicianName": "张医生",
      "diagDate": "2025-05-01 10:00:00"
    }
  ]
}]
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |
| 3    | data     | 返回数据 | Array   | Y | |
| 3.1  | visitId  | 诊疗ID   | Long   | Y | |
| 3.2  | diagCode | 诊断编码 | String   | Y | |
| 3.3  | diagNo   | 诊断序号 | Integer   | Y | |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": [{
    "visitId": 1256,
    "diagCode": "J00.x00",
    "diagNo": 1
  }, {
    "visitId": 1256,
    "diagCode": "TCM001",
    "diagNo": 2
  }]
}
```

#### 5.3 诊断删除

##### 接口说明
此接口用于删除患者诊断信息。

**接口Url**
POST /his/api/diagnosis/delete

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | visitId | 诊疗ID | Long   |  | Y |  |
| 2    | diagList | 诊断删除列表 | Array |  | Y | 诊断删除信息列表，结构如下： |
| 2.1  | diagCode | 诊断编码 | String | 30 | Y |  |

**请求示例**
```json
[{
  "visitId": 1256,
  "diagList": [
    {
      "diagCode": "J00.x00"
    },
    {
      "diagCode": "TCM001"
    }
  ]
}, {
  "visitId": 1257,
  "diagList": [
    {
      "diagCode": "I10.x00"
    }
  ]
}]
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

#### 5.4 电子住院证申请

##### 接口说明
此接口用于新增和更新电子住院证数据。

**接口Url**
POST /his/api/ipr/saveOrUpdate

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | iprId | 住院申请流水号 | Long |  | N | 修改时必填，新增时为空 |
| 2    | visitId | 诊疗ID | Long |  | N |  |
| 3    | orgId | 机构ID | Long |  | N | 诊疗ID为空时,机构ID为必填项 |
| 4    | patientId | 患者ID | Long |  | N | |
| 5    | patientNo | 患者号 | Long |  | N | 门诊患者号 |
| 6    | patientName | 患者姓名 | String |  | Y |  |
| 7    | genderId | 性别 | Integer |  | Y | 0-未知性别，1-男，2-女 |
| 8    | ageOfYears | 年龄 | Integer |  | N |  |
| 9    | ageOfDays | 日龄 | Integer |  | N |  |
| 10   | certTypeId | 证件类型代号 | Integer |  | Y |  |
| 11   | idcertNo | 证件号码 | String | 20 | Y |  |
| 12   | telNo | 电话号码 | String | 40 | N |  |
| 13   | timeAdmission | 申请时间 | String |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 14   | deptCode | 入院科室代码 | String | 10 | Y |  |
| 15   | deptName | 入院科室名称 | String | 20 | N |  |
| 16   | clinicianCode | 申请医师编码 | String | 20 | Y |  |
| 17   | clinicianName | 申请医师名称 | String | 50 | Y |  |
| 18   | clinicDate | 计划入院日期 | Integer |  | Y |  |
| 19   | isEmergency | 是否紧急 | Integer |  | N |  |
| 20   | notes | 说明备注 | String | 60 | N |  |
| 21   | opDiseaseCode | 门诊西医诊断编码 | String |  | N |  |
| 22   | opDiseaseName | 门诊西医诊断名称 | String |  | N |  |
| 23   | opTcmDiseaseCode | 门诊中医疾病编码 | String |  | N |  |
| 24   | opTcmDiseaseName | 门诊中医疾病名称 | String |  | N |  |
| 25   | opTcmSymptomCode | 门诊中医证候编码 | String |  | N |  |
| 26   | opTcmSymptomName | 门诊中医证候名称 | String |  | N |  |
| 27   | hospitalizedCause | 入院原因 | String | 60 | N |  |

**请求示例**
```json
{
  "iprId": null,
  "visitId": null,
  "patientId": 123456,
  "patientNo": 87654321,
  "patientName": "张三",
  "genderId": 1,
  "ageOfYears": 30,
  "ageOfDays": 5, 
  "certTypeId": 1,
  "idcertNo": "123456789012345678",
  "telNo": "13800000000", 
  "timeAdmission": "2025-05-10 14:00:00",
  "deptCode": "DEPT001",
  "deptName": "内科",
  "clinicianCode": "DOC001",
  "clinicianName": "张医生",
  "clinicDate": 20230512,
  "isEmergency": 0,
  "notes": "患者需要住院观察",
  "opDiseaseCode": "J00.x00",
  "opDiseaseName": "急性上呼吸道感染",
  "hospitalizedCause": "需要进一步检查治疗"
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |
| 3    | data     | 返回数据 | Object   | Y | |
| 3.1  | iprId    | 住院申请流水号 | Long | Y | |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "iprId": 12658
  }
}
```

#### 5.5 电子住院证作废

##### 接口说明
此接口用于作废已保存的电子住院证申请。

**接口Url**
POST /his/api/ipr/cancel

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | userCode | 作废人编码 | String |  | Y |  |
| 2    | userName | 作废人名称 | String |  | Y |  |
| 3    | cancelTime | 作废时间 | String   |  | Y | 格式：yyyy-MM-dd HH:mm:ss |
| 4    | sourceId | 数据来源 | Integer   |  | Y | 1-内部系统，2-外部系统 |
| 5    | iprList | 作废住院证申请列表 | Array |  | Y | 作废住院证申请信息列表，结构如下： |
| 5.1  | iprId | 住院申请流水号 | Long |  | Y | |
| 5.2  | cancelCause | 作废原因 | String | 200 | N |  |

**请求示例**
```json
{
  "userCode": "USER001",
  "userName": "李操作员",
  "cancelTime": "2025-05-10 16:30:00",
  "sourceId": 1,
  "iprList": [
    {
      "iprId": 12658,
      "cancelCause": "患者取消住院申请"
    },
    {
      "iprId": 12659,
      "cancelCause": "医师建议门诊治疗"
    }
  ]
}
```

##### 返回参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | ---- |
| 1    | code     | 返回码   | Integer  | Y | 0表示成功，非0表示失败 |
| 2    | msg      | 返回信息 | String   | Y |  |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

### 6. 其他接口

#### 6.1 项目价格查询

##### 接口说明
此接口用于查询医疗项目的价格信息。

**接口Url**
POST /his/api/item/queryPrice

##### 请求参数
| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
| ---- | -------- | -------- | -------- | -------- | -------- | ---- |
| 1    | orderId  | 申请单号 | String   | 20       | N        | 申请单号 |
| 2    | orgId    | 机构代码 | Long     |          | N        | 机构代码 |
| 3    | itemLs   | 项目明细列表 | Array  |          | Y        | 项目明细信息列表，结构如下： |
| 3.1  | lineNo   | 行号     | Integer  |          | N        | 明细行号 |
| 3.2  | artCode  | 项目编码 | String   | 20       | N        | 项目编码 |
| 3.3  | artName  | 项目名称 | String   | 200      | N        | 项目名称 |
| 3.4  | total    | 数量     | BigDecimal | 12,4   | N        | 查询数量 |
| 3.5  | clinicType | 诊疗类型 | Integer  |          | N        | 1-门诊，2-住院 |

**请求示例**
```json
{
  "orderId": "LIS202505080001",
  "orgId":  12002,
  "itemLs": [
    {
      "lineNo": 1,
      "artCode": "",
      "artName": "阿莫西林",
      "total": 2.0,
      "clinicType": 1
    },
    {
      "lineNo": 2,
      "artCode": "1006042",
      "artName": "阿莫西林颗粒",
      "total": 1.0,
      "clinicType": 1
    }
  ]
}
```

##### 返回参数
| 序号   | 参数代码 | 参数名称 | 参数类型 | 是否必填 | 说明 |
| ------ | -------- | -------- | -------- | -------- | ---- |
| 1      | code     | 返回码   | Integer  | Y        | 0表示成功，非0表示失败 |
| 2      | msg      | 返回信息 | String   | Y        |      |
| 3      | data     | 返回数据 | Array    | Y        | 项目价格信息列表，结构如下： |
| 3.1    | orderId  | 申请单号 | String   | N        | 对应请求的申请单号 |
| 3.2    | itemList | 项目明细列表 | Array | Y        | 项目明细信息列表，结构如下： |
| 3.2.1  | lineNo   | 行号     | Integer  | N        | 对应请求的明细行号 |
| 3.2.2  | artCode  | 项目编码 | String   | Y        |      |
| 3.2.3  | artName  | 项目名称 | String   | Y        |      |
| 3.2.4  | artSpec  | 规格     | String   | N        |      |
| 3.2.5  | unit     | 单位     | String   | Y        |      |
| 3.2.6  | unitType | 单位类型 | Integer  | Y        | 1-制剂单位,2-包装单位,3-剂量单位 |
| 3.2.7  | total    | 数量     | BigDecimal | N      |  |
| 3.2.8  | price    | 单价     | BigDecimal| Y       |  |
| 3.2.9  | derated  | 减免金额 | BigDecimal | N      | 减免金额 |
| 3.2.10 | discounted | 折让金额 | BigDecimal | N    | 折让金额 |
| 3.2.11 | amount   | 总金额   | BigDecimal | N      | 单价*数量 |
| 3.2.12 | artTypeCode | 项目类别编码 | String | Y      | 项目类别编码 |
| 3.2.13 | artTypeName | 项目类别名称 | String | Y      | 项目类别名称 |
| 3.2.14 | feeTypeCode | 费用类别编码 | String | Y      | 费用类别编码 |
| 3.2.15 | feeTypeName | 费用类别名称 | String | Y      | 费用类别名称 |

**返回示例**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "orderId": "LIS202505080001",
    "itemList": [
      {
        "lineNo": 1,
        "artCode": "1006041",
        "artName": "阿莫西林胶囊",
        "artSpec": "0.25g*24粒",
        "unit": "盒",
        "unitType": 2,
        "total": 2.0,
        "price": 15.50,
        "derated": 0.00,
        "discounted": 1.50,
        "amount": 31.00,
        "artTypeCode": "DRUG001",
        "artTypeName": "西药",
        "feeTypeCode": "FEE001",
        "feeTypeName": "药品费"
      },
      {
        "lineNo": 2,
        "artCode": "1006042",
        "artName": "阿莫西林颗粒",
        "artSpec": "0.125g*12袋",
        "unit": "盒",
        "unitType": 2,
        "total": 1.0,
        "price": 12.80,
        "derated": 0.00,
        "discounted": 0.00,
        "amount": 12.80,
        "artTypeCode": "DRUG001",
        "artTypeName": "西药",
        "feeTypeCode": "FEE001",
        "feeTypeName": "药品费"
      }
    ]
  }
}
```
