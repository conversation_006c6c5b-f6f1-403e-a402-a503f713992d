package cn.feiying.med.his.api.service;

import cn.feiying.med.his.api.model.req.diagnosis.DiagnosisModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.DiagnosisRespModel;

import java.util.List;

/**
 * 诊断相关服务接口
 */
public interface DiagnosisApiService {
    
    /**
     * 诊断回写
     * 
     * @param diagnosisList 诊断回写请求列表
     * @return 诊断回写结果
     */
    ApiResultModel<List<DiagnosisRespModel>> saveOrUpdateDiagnosis(List<DiagnosisModel> diagnosisList);
    
    /**
     * 诊断删除
     * 
     * @param diagnosisList 诊断删除请求列表
     * @return 诊断删除结果
     */
    ApiResultModel<?> deleteDiagnosis(List<DiagnosisModel> diagnosisList);
} 