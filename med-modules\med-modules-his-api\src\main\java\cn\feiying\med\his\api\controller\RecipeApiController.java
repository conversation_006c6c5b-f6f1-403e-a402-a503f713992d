package cn.feiying.med.his.api.controller;

import cn.feiying.med.his.api.model.req.ApiReqModel;
import cn.feiying.med.his.api.model.req.recipe.RecipeCancelModel;
import cn.feiying.med.his.api.model.req.recipe.RecipeSaveReqModel;
import cn.feiying.med.his.api.model.req.recipe.RecipeSignReqModel;
import cn.feiying.med.his.api.model.req.recipe.RecipeStatusUpdateModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.RecipeRespModel;
import cn.feiying.med.his.api.model.resp.RecipeSignRespModel;
import cn.feiying.med.his.api.service.RecipeApiService;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 处方相关API控制器
 */
@Slf4j
@RestController
@RequestMapping("/his/api/recipe")
public class RecipeApiController extends BaseApiController {
    
    @Resource
    private RecipeApiService recipeApiService;
    
    /**
     * 处方保存及修改
     */
    @PostMapping("/saveOrUpdate")
    public ApiResultModel<RecipeRespModel> save(HttpServletRequest request, @RequestBody ApiReqModel apiReq) {
        try {
            // 解密请求数据
            RecipeSaveReqModel recipeSaveReq = decryptRequest(request, apiReq, RecipeSaveReqModel.class);
            log.info("处方保存及修改请求: {}", recipeSaveReq);
            
            // 调用服务处理业务逻辑
            return recipeApiService.saveOrUpdateRecipe(recipeSaveReq);
        } catch (Exception e) {
            log.error("处方保存及修改失败", e);
            return ApiResultModel.error("处方保存及修改失败: " + e.getMessage());
        }
    }

    /**
     * 处方签名
     */
    @PostMapping("/sign")
    public ApiResultModel<RecipeSignRespModel> signRecipe(HttpServletRequest request, @RequestBody ApiReqModel apiReq) {
        try {
            // 解密请求数据
            RecipeSignReqModel recipeSignReq = decryptRequest(request, apiReq, RecipeSignReqModel.class);
            log.info("处方签名请求: {}", recipeSignReq);
            
            // 调用服务处理业务逻辑
            return recipeApiService.signRecipe(recipeSignReq);
        } catch (Exception e) {
            log.error("处方签名失败", e);
            return ApiResultModel.error("处方签名失败: " + e.getMessage());
        }
    }

    /**
     * 处方作废
     */
    @PostMapping("/cancel")
    public ApiResultModel<?> cancelRecipe(HttpServletRequest request, @RequestBody ApiReqModel apiReq) {
        try {
            // 解密请求数据
            List<RecipeCancelModel> recipeCancelList = decryptRequest(request, apiReq, new TypeReference<List<RecipeCancelModel>>() {});
            log.info("处方作废请求: {}", recipeCancelList);
            
            // 调用服务处理业务逻辑
            return recipeApiService.cancelRecipe(recipeCancelList);
        } catch (Exception e) {
            log.error("处方作废失败", e);
            return ApiResultModel.error("处方作废失败: " + e.getMessage());
        }
    }
    
    /**
     * 处方状态回写
     */
    @PostMapping("/updateStatus")
    public ApiResultModel<?> updateRecipeStatus(HttpServletRequest request, @RequestBody ApiReqModel apiReq) {
        try {
            // 解密请求数据
            List<RecipeStatusUpdateModel> recipeStatusUpdateList = decryptRequest(request, apiReq, new TypeReference<List<RecipeStatusUpdateModel>>() {});
            log.info("处方状态回写请求: {}", recipeStatusUpdateList);
            
            // 调用服务处理业务逻辑
            return recipeApiService.updateRecipeStatus(recipeStatusUpdateList);
        } catch (Exception e) {
            log.error("处方状态回写失败", e);
            return ApiResultModel.error("处方状态回写失败: " + e.getMessage());
        }
    }
} 