<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.feiying.med.clinics_wm.dao.WmBillDetailDao">

    <update id="resetPriceCostAmount">
        UPDATE microhis_clinics_wm.t_wm_bill_detail
            inner JOIN microhis_clinics_wm.t_art_stockno ON t_wm_bill_detail.Art_ID = t_art_stockno.Art_ID AND
                                                            t_wm_bill_detail.Stock_No = t_art_stockno.Stock_No
        SET t_wm_bill_detail.Pack_Price = t_art_stockno.Pack_Price,
            t_wm_bill_detail.Cell_Price = t_art_stockno.Cell_Price,
            t_wm_bill_detail.Cost       = IFNULL(t_wm_bill_detail.Total_Packs, 0) * IFNULL(t_art_stockno.Pack_Price, 0) +
                                          IFNULL(t_wm_bill_detail.Total_Cells, 0) * IFNULL(t_art_stockno.Cell_Price, 0),
            t_wm_bill_detail.Amount     = IFNULL(t_wm_bill_detail.Total_Packs, 0) * IFNULL(t_art_stockno.Pack_Price, 0) +
                                          IFNULL(t_wm_bill_detail.Total_Cells, 0) * IFNULL(t_art_stockno.Cell_Price, 0)
        WHERE t_wm_bill_detail.WB_SeqID = #{wbSeqId}
    </update>

    <update id="resetBillDetailExpiry">
        UPDATE microhis_clinics_wm.t_wm_bill_detail
            inner JOIN microhis_clinics_wm.t_art_batch ON t_wm_bill_detail.Art_ID = t_art_batch.Art_ID AND
                                                          t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
        SET t_wm_bill_detail.Expiry = t_art_batch.Expiry,
            t_wm_bill_detail.Date_Manufactured = t_art_batch.Date_Manufactured
        WHERE t_wm_bill_detail.WB_SeqID = #{wbSeqId}
    </update>

    <select id="queryDtoPageSumAmount" resultType="java.math.BigDecimal">
        select sum(t_wm_bill_detail.Amount) as amount
        from microhis_clinics_wm.t_wm_bill_detail
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_batch on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_dept_art on t_wm_bill.Org_ID = t_dept_art.Org_ID and t_wm_bill.Dept_Code = t_dept_art.Dept_Code and t_wm_bill_detail.Art_ID = t_dept_art.Art_ID
            ${ew.customSqlSegment}
    </select>
    <!-- 查询t_wm_bill_detail总amount -->
    <select id="queryDtoAndStockNoPageSumAmount" resultType="java.math.BigDecimal">
        select sum(t_wm_bill_detail.Amount) as amount
        from microhis_clinics_wm.t_wm_bill_detail
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_batch on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_dept_art on t_wm_bill.Org_ID = t_dept_art.Org_ID and t_wm_bill.Dept_Code = t_dept_art.Dept_Code and t_wm_bill_detail.Art_ID = t_dept_art.Art_ID
                 left join microhis_clinics_wm.t_dept_stock on t_wm_bill.Org_ID=t_dept_stock.Org_ID and t_wm_bill.Dept_Code=t_dept_stock.Dept_Code
                     and t_wm_bill_detail.Art_ID=t_dept_stock.Art_ID and t_wm_bill_detail.Stock_No=t_dept_stock.Stock_No
            ${ew.customSqlSegment}
    </select>

    <select id="queryDtoPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t_wm_bill_detail.line_no,
               t_wm_bill_detail.art_id,
               t_wm_bill_detail.batch_no,
               t_wm_bill_detail.total_packs,
               t_wm_bill_detail.notes,
               t_wm_bill_detail.wb_seqid,
               t_wm_bill_detail.track_code,
               t_wm_bill_detail.amount,
               t_wm_bill_detail.total_cells,
               t_wm_bill_detail.pack_price,
               t_wm_bill_detail.cell_price,
               ifnull(t_wm_bill_detail.expiry, t_art_batch.Expiry)                       as Expiry,
               t_wm_bill_detail.cost,
               t_wm_bill_detail.stock_no,
               t_wm_bill_detail.substitute_art_id,
               ifnull(t_wm_bill_detail.date_manufactured, t_art_batch.Date_Manufactured) as Date_Manufactured,
               t_wm_bill_detail.last_total_packs,
               t_wm_bill_detail.last_total_cells,
               t_wm_bill_detail.return_total_packs,
               t_wm_bill_detail.return_total_cells,
               t_wm_bill_detail.wm_reqid,
               t_wm_bill_detail.wm_req_detail_line_no,
               t_wm_bill_detail.origin_place,
               t_article.art_code,
               t_article.art_name,
               t_article.art_spec,
               t_article.pack_unit,
               t_article.producer,
               t_article.cell_unit,
               t_article.pack_cells,
               t_article.mi_code,
               t_article.YPID_Code,
               t_article.No_Track_Code,
               t_article.is_Disassembled as art_is_Disassembled,
               t_article.Approval_No,
               t_dept_art.Total_Packs                                                    as Dept_Total_Packs,
               t_dept_art.Total_Cells                                                    as Dept_Total_Cells,
               t_org_art.Pack_Price,
               t_org_art.Cell_Price,
               ifnull(t_wm_bill_detail.Pack_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) / 100  as pct_Price,
               ifnull(t_wm_bill_detail.Cell_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) / 100  as pct_Cell_Price,
               ifnull(t_wm_bill_detail.Total_Packs, 0) * ifnull(t_org_art.Pack_Price, 0) +
               ifnull(t_wm_bill_detail.Total_Cells, 0) * ifnull(t_org_art.Cell_Price, 0) as Rated_Amount,
               (ifnull(t_wm_bill_detail.Total_Packs, 0) * ifnull(t_wm_bill_detail.Pack_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) /
                100)
                   +
               (ifnull(t_wm_bill_detail.Total_Cells, 0) * ifnull(t_wm_bill_detail.Cell_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) /
                100)                                                                     as Pct_Amount
        from microhis_clinics_wm.t_wm_bill_detail
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_batch
                           on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_dept_art
                           on t_wm_bill.Org_ID = t_dept_art.Org_ID and t_wm_bill.Dept_Code = t_dept_art.Dept_Code and
                              t_wm_bill_detail.Art_ID = t_dept_art.Art_ID
                 left join microhis_clinics_wm.t_org_art
                           on t_wm_bill.Org_ID = t_org_art.Org_ID and t_wm_bill_detail.Art_ID = t_org_art.Art_ID
        ${ew.customSqlSegment}
    </select>
    <!--  查询t_wm_bill_detail  -->
    <select id="detailAndStockNoPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t_wm_bill_detail.line_no,
               t_wm_bill_detail.art_id,
               t_wm_bill_detail.batch_no,
               t_wm_bill_detail.total_packs,
               t_wm_bill_detail.notes,
               t_wm_bill_detail.wb_seqid,
               t_wm_bill_detail.track_code,
               t_wm_bill_detail.amount,
               t_wm_bill_detail.total_cells,
               t_wm_bill_detail.pack_price,
               t_wm_bill_detail.cell_price,
               ifnull(t_wm_bill_detail.expiry, t_art_batch.Expiry) as Expiry,
               t_wm_bill_detail.cost,
               t_wm_bill_detail.stock_no,
               t_wm_bill_detail.substitute_art_id,
               ifnull(t_wm_bill_detail.date_manufactured, t_art_batch.Date_Manufactured) as Date_Manufactured,
               t_wm_bill_detail.last_total_packs,
               t_wm_bill_detail.last_total_cells,
               t_wm_bill_detail.return_total_packs,
               t_wm_bill_detail.return_total_cells,
               t_wm_bill_detail.wm_reqid,
               t_wm_bill_detail.wm_req_detail_line_no,
               t_wm_bill_detail.origin_place,
               t_article.art_code,
               t_article.art_name,
               t_article.art_spec,
               t_article.pack_unit,
               t_article.producer,
               t_article.cell_unit,
               t_article.pack_cells,
               t_article.mi_code,
               t_article.YPID_Code,
               t_article.Approval_No,
               t_dept_art.Total_Packs as Dept_Total_Packs,
               t_dept_art.Total_Cells as Dept_Total_Cells
               ,t_dept_stock.Total_Packs as Dept_Stock_Total_Packs
               ,t_dept_stock.Total_Cells as Dept_Stock_Total_Cells
        from microhis_clinics_wm.t_wm_bill_detail
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_batch on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_dept_art on t_wm_bill.Org_ID = t_dept_art.Org_ID and t_wm_bill.Dept_Code = t_dept_art.Dept_Code and t_wm_bill_detail.Art_ID = t_dept_art.Art_ID
                 left join microhis_clinics_wm.t_dept_stock on t_wm_bill.Org_ID=t_dept_stock.Org_ID and t_wm_bill.Dept_Code=t_dept_stock.Dept_Code
                    and t_wm_bill_detail.Art_ID=t_dept_stock.Art_ID and t_wm_bill_detail.Stock_No=t_dept_stock.Stock_No
            ${ew.customSqlSegment}
    </select>

    <select id="queryGroupByArtPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select min(t_wm_bill_detail.line_no) as line_no,
               t_wm_bill_detail.art_id,
               t_wm_bill_detail.batch_no,
               sum(t_wm_bill_detail.total_packs) as total_packs,
               sum(t_wm_bill_detail.amount) as amount,
               sum(t_wm_bill_detail.total_cells) as total_cells,
               t_wm_bill_detail.pack_price,
               sum(t_wm_bill_detail.cost) as cost,
               min(t_article.art_code) as art_code,
               min(t_article.art_name) as art_name,
               min(t_article.art_spec) as art_spec,
               min(t_article.pack_unit) as pack_unit,
               min(t_article.producer) as producer,
               min(t_article.cell_unit) as cell_unit,
               min(t_article.pack_cells) as pack_cells,
               min(t_article.mi_code) as mi_code,
               min(t_article.YPID_Code) as YPID_Code,
               min(t_article.Approval_No) as Approval_No,
               min(t_dept_art.Total_Packs) as Dept_Total_Packs,
               min(t_dept_art.Total_Cells) as Dept_Total_Cells
        from microhis_clinics_wm.t_wm_bill_detail
         left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
         left join microhis_clinics_wm.t_art_batch on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         left join microhis_clinics_wm.t_dept_art on t_wm_bill.Org_ID = t_dept_art.Org_ID and t_wm_bill.Dept_Code = t_dept_art.Dept_Code and t_wm_bill_detail.Art_ID = t_dept_art.Art_ID
        ${ew.customSqlSegment}
    </select>
    <select id="queryDtoList" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t_wm_bill_detail.line_no
             , t_wm_bill_detail.art_id
             , t_wm_bill_detail.batch_no
             , t_wm_bill_detail.total_packs
             , t_wm_bill_detail.notes
             , t_wm_bill_detail.wb_seqid
             , t_wm_bill_detail.track_code
             , t_wm_bill_detail.amount
             , t_wm_bill_detail.total_cells
             , t_wm_bill_detail.pack_price
             , t_wm_bill_detail.cell_price
             , ifnull(t_wm_bill_detail.expiry, t_art_batch.Expiry)                       as Expiry
             , t_wm_bill_detail.cost
             , t_wm_bill_detail.stock_no
             , t_wm_bill_detail.substitute_art_id
             , ifnull(t_wm_bill_detail.date_manufactured, t_art_batch.Date_Manufactured) as Date_Manufactured
             , t_wm_bill_detail.origin_place
             , t_article.art_code
             , t_article.art_name
             , t_article.art_spec
             , t_article.pack_unit
             , t_article.producer
             , t_article.cell_unit
             , t_article.pack_cells
             , t_article.mi_code
             , t_article.Approval_No
             , t_org_art.Pack_Price                                                      as org_Pack_Price
             , t_org_art.Cell_Price                                                      as org_Cell_Price
             , t_org_art.PCT_ADD                                                         as sale_Pct_Add
             , t_wm_req_detail.Visit_ID
             , t_wm_req_detail.OE_No
             , t_wm_req.Drug_ReqID
             , t_art_stockno.Pack_Price                                                  as cost_Price
             , t_art_stockno.Cell_Price                                                  as cost_Cell_Price
             , ifnull(t_art_stockno.Pack_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) / 100     as pct_Price
             , ifnull(t_art_stockno.Cell_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) / 100     as pct_Cell_Price
             , ifnull(t_wm_bill_detail.Total_Packs, 0) * ifnull(t_org_art.Pack_Price, 0) +
               ifnull(t_wm_bill_detail.Total_Cells, 0) * ifnull(t_org_art.Cell_Price, 0) as Rated_Amount
             , (ifnull(t_wm_bill_detail.Total_Packs, 0) * ifnull(t_wm_bill_detail.Pack_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) /
                100)
            +
               (ifnull(t_wm_bill_detail.Total_Cells, 0) * ifnull(t_wm_bill_detail.Cell_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) /
                100)                                                                     as Pct_Amount
        from microhis_clinics_wm.t_wm_bill_detail
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_batch
                           on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_org_art
                           on t_wm_bill.Org_ID = t_org_art.Org_ID and t_wm_bill_detail.Art_ID = t_org_art.Art_ID
                 left join microhis_clinics_wm.t_wm_req_detail on t_wm_bill_detail.WM_ReqID = t_wm_req_detail.WM_ReqID and
                                                                  t_wm_bill_detail.WM_Req_Detail_Line_No =
                                                                  t_wm_req_detail.Line_No
                 left join microhis_clinics_wm.t_wm_req on t_wm_bill_detail.WM_ReqID = t_wm_req.WM_ReqID
                 left join microhis_clinics_wm.t_art_stockno on t_wm_bill_detail.Art_ID = t_art_stockno.Art_ID and
                                                                t_wm_bill_detail.Stock_No = t_art_stockno.Stock_No
        where t_wm_bill_detail.wb_seqid = #{wbSeqid}
    </select>
    <!-- 不关联t_wm_req表的查询   -->
    <select id="queryDtoListNotLinkReq" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t_wm_bill_detail.line_no
             , t_wm_bill_detail.art_id
             , t_wm_bill_detail.batch_no
             , t_wm_bill_detail.total_packs
             , t_wm_bill_detail.notes
             , t_wm_bill_detail.wb_seqid
             , t_wm_bill_detail.track_code
             , t_wm_bill_detail.amount
             , t_wm_bill_detail.total_cells
             , t_wm_bill_detail.pack_price
             , t_wm_bill_detail.cell_price
             , ifnull(t_wm_bill_detail.expiry, t_art_batch.Expiry)                       as Expiry
             , t_wm_bill_detail.cost
             , t_wm_bill_detail.stock_no
             , t_wm_bill_detail.substitute_art_id
             , ifnull(t_wm_bill_detail.date_manufactured, t_art_batch.Date_Manufactured) as Date_Manufactured
             , t_wm_bill_detail.origin_place
             , t_article.art_code
             , t_article.art_name
             , t_article.art_spec
             , t_article.pack_unit
             , t_article.producer
             , t_article.cell_unit
             , t_article.pack_cells
             , t_article.mi_code
             , t_article.Approval_No
             , t_org_art.Pack_Price                                                      as org_Pack_Price
             , t_org_art.Cell_Price                                                      as org_Cell_Price
             , t_org_art.PCT_ADD                                                         as sale_Pct_Add
             , t_art_stockno.Pack_Price                                                  as cost_Price
             , t_art_stockno.Cell_Price                                                  as cost_Cell_Price
             , ifnull(t_art_stockno.Pack_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) / 100     as pct_Price
             , ifnull(t_art_stockno.Cell_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) / 100     as pct_Cell_Price
             , ifnull(t_wm_bill_detail.Total_Packs, 0) * ifnull(t_org_art.Pack_Price, 0) +
               ifnull(t_wm_bill_detail.Total_Cells, 0) * ifnull(t_org_art.Cell_Price, 0) as Rated_Amount
             , (ifnull(t_wm_bill_detail.Total_Packs, 0) * ifnull(t_wm_bill_detail.Pack_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) /
                100)
            +
               (ifnull(t_wm_bill_detail.Total_Cells, 0) * ifnull(t_wm_bill_detail.Cell_Price, 0) * (100 + ifnull(t_org_art.PCT_ADD, 0)) /
                100)                                                                     as Pct_Amount
        from microhis_clinics_wm.t_wm_bill_detail
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_batch
                           on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_org_art
                           on t_wm_bill.Org_ID = t_org_art.Org_ID and t_wm_bill_detail.Art_ID = t_org_art.Art_ID
                 left join microhis_clinics_wm.t_art_stockno on t_wm_bill_detail.Art_ID = t_art_stockno.Art_ID and
                                                                t_wm_bill_detail.Stock_No = t_art_stockno.Stock_No
        where t_wm_bill_detail.wb_seqid = #{wbSeqid}
    </select>

    <select id="queryRecipePage" resultType="cn.feiying.med.clinics_wm.dto.RecipeDto">
        SELECT t_recipe.Recipe_ID,
               t_recipe.RX_No,
               t_recipe_type.Recipe_Type_Name,
               t_bill.Patient_Name,
               t_clinician.Clinician_No,
               t_clinician.Clinician_Name,
               t_bill.BSeqID,
               t_bill.Times,
               t_recipe_extra.Time_Signed
        FROM microhis_hsd.t_recipe
         left join microhis_hsd.t_recipe_extra on t_recipe.Recipe_ID = t_recipe_extra.Recipe_ID
         LEFT JOIN hip_mdi.t_recipe_type ON t_recipe.Recipe_Type_ID = t_recipe_type.Recipe_Type_ID
         LEFT join microhis_bcs.t_bill on t_recipe.BSeqID = t_bill.BSeqID
         inner JOIN hip_mpi.t_patient ON t_bill.Patient_ID = t_patient.Patient_ID
         LEFT JOIN hip_mdi.t_clinician ON t_recipe.Clinician_ID = t_clinician.Clinician_ID
        ${ew.customSqlSegment}
    </select>
    <select id="queryRecipeDeliverPage" resultType="cn.feiying.med.clinics_wm.dto.RecipeDetailDto">
        select t_recipe.Recipe_ID
             , t_bill_detail.bseqid
             , t_bill_detail.line_no
             , t_recipe.RX_No
             , t_bill.Patient_Name
             , t_clinician.Clinician_Name
             , t_user_code.User_Name as Deliverer_Uname
             , t_recipe.Time_Delivered
             , t_article.Art_Code
             , t_article.Art_Name
             , t_article.Art_Spec
             , t_article.Producer
             , t_bill_detail.Total
             , t_bill.Times
             , t_bill_detail.Total * ifnull(t_bill.Times, 1) as actual_qty
             , t_bill_detail.Price
             , t_bill_detail.Amount
        from microhis_bcs.t_bill_detail
         left join microhis_bcs.t_bill on t_bill_detail.BSeqID = t_bill.BSeqID
         inner join microhis_hsd.t_recipe on t_bill.Recipe_ID = t_recipe.Recipe_ID
         left join hip_mdi.t_article on t_bill_detail.Art_ID = t_article.Art_ID
         left join hip_mdi.t_clinician on t_recipe.Clinician_ID = t_clinician.Clinician_ID
         left join hip_mdi.t_user_code on t_recipe.Deliverer_UID = t_user_code.User_ID
        ${ew.customSqlSegment}
    </select>


    <select id="queryTrackCodePage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t_wm_bill_detail.*
             , t_article.art_code
             , t_article.art_name
             , t_article.art_spec
             , t_article.pack_unit
             , t_article.producer
             , t_article.cell_unit
             , t_article.pack_cells
             , t_article.mi_code
             , t_article.YPID_Code
             , t_article.Approval_No
             , t_article.No_Track_Code
             , t_article.is_Disassembled as art_is_Disassembled
             , ifnull(t_wm_bill_detail.Total_Packs, 0) + ifnull(t_wm_bill_detail.Total_Cells, 0) as un_collected_count
             , t.collected_count
        from microhis_clinics_wm.t_wm_bill_detail
         left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
         left join (select WB_SeqID, Line_No, count(*) collected_count from microhis_clinics_wm.t_wm_bill_trackcode group by WB_SeqID, Line_No) t
                   on t_wm_bill_detail.WB_SeqID = t.WB_SeqID and t_wm_bill_detail.Line_No = t.Line_No
            ${ew.customSqlSegment}
    </select>
    <select id="queryRecipePendingDtoPageByVisitId" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t_wm_bill_detail.*
             , t_article.art_code
             , t_article.art_name
             , t_article.art_spec
             , t_article.pack_unit
             , t_article.producer
             , t_article.cell_unit
             , t_article.pack_cells
             , t_article.mi_code
             , t_article.YPID_Code
             , t_article.Approval_No
             , t_art_batch.Date_Manufactured
             , t_art_batch.Expiry
             , t_dept_art.Total_Packs as Dept_Total_Packs
             , t_dept_art.Total_Cells as Dept_Total_Cells
             , t_wm_bill.Recipe_ID
             , t_art_subtype.Subtype_Name as Art_Sub_Type_Name
        from microhis_clinics_wm.t_wm_bill_detail
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_batch
                           on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_dept_art
                           on t_wm_bill.Org_ID = t_dept_art.Org_ID and t_wm_bill.Dept_Code = t_dept_art.Dept_Code and
                              t_wm_bill_detail.Art_ID = t_dept_art.Art_ID
                 left join microhis_hsd.t_recipe on t_wm_bill.Recipe_ID = t_recipe.Recipe_ID
                 inner join microhis_clinics_wm.t_wm_req on t_wm_bill.WB_SeqID = t_wm_req.WB_SeqID
                 inner join microhis_clinics_wm.t_wm_req_pending on t_wm_req.WM_ReqID = t_wm_req_pending.WM_ReqID
                 left join hip_mdi.t_art_subtype on t_article.Subtype_ID = t_art_subtype.Subtype_ID
        ${ew.customSqlSegment}
    </select>
    <select id="queryRecipePendingSumAmountByVisitId" resultType="java.math.BigDecimal">
        select sum(t_wm_bill_detail.Amount) as amount
        from microhis_clinics_wm.t_wm_bill_detail
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_batch
                           on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_dept_art
                           on t_wm_bill.Org_ID = t_dept_art.Org_ID and t_wm_bill.Dept_Code = t_dept_art.Dept_Code and
                              t_wm_bill_detail.Art_ID = t_dept_art.Art_ID
                 left join microhis_hsd.t_recipe on t_wm_bill.Recipe_ID = t_recipe.Recipe_ID
                 inner join microhis_clinics_wm.t_wm_req on t_wm_bill.WB_SeqID = t_wm_req.WB_SeqID
                 inner join microhis_clinics_wm.t_wm_req_pending on t_wm_req.WM_ReqID = t_wm_req_pending.WM_ReqID
            ${ew.customSqlSegment}
    </select>
    <select id="queryRecipeDtoPageByVisitId" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t_wm_bill_detail.*
             , t_article.art_code
             , t_article.art_name
             , t_article.art_spec
             , t_article.pack_unit
             , t_article.producer
             , t_article.cell_unit
             , t_article.pack_cells
             , t_article.mi_code
             , t_article.YPID_Code
             , t_article.Approval_No
             , t_art_batch.Date_Manufactured as Art_Date_Manufactured
             , t_art_batch.Expiry as Art_Expiry
             , t_dept_art.Total_Packs as Dept_Total_Packs
             , t_dept_art.Total_Cells as Dept_Total_Cells
             , t_wm_bill.Recipe_ID
             , t_wm_bill.Status       as bill_status
             , t_wm_bill.Relative_WBSeqID
             , t_art_subtype.Subtype_Name as Art_Sub_Type_Name
        from microhis_clinics_wm.t_wm_bill_detail
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_batch on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_dept_art on t_wm_bill.Org_ID = t_dept_art.Org_ID and t_wm_bill.Dept_Code = t_dept_art.Dept_Code and t_wm_bill_detail.Art_ID = t_dept_art.Art_ID
                 left join microhis_hsd.t_recipe on t_wm_bill.Recipe_ID = t_recipe.Recipe_ID
                 inner join microhis_clinics_wm.t_wm_req on t_wm_bill.WB_SeqID = t_wm_req.WB_SeqID
                 left join hip_mdi.t_art_subtype on t_article.Subtype_ID = t_art_subtype.Subtype_ID
            ${ew.customSqlSegment}
    </select>
    <select id="queryRecipeSumAmountByVisitId" resultType="java.math.BigDecimal">
        select sum(t_wm_bill_detail.Amount) as amount
        from microhis_clinics_wm.t_wm_bill_detail
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_batch on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_dept_art on t_wm_bill.Org_ID = t_dept_art.Org_ID and t_wm_bill.Dept_Code = t_dept_art.Dept_Code and t_wm_bill_detail.Art_ID = t_dept_art.Art_ID
                 left join microhis_hsd.t_recipe on t_wm_bill.Recipe_ID = t_recipe.Recipe_ID
                 inner join microhis_clinics_wm.t_wm_req on t_wm_bill.WB_SeqID = t_wm_req.WB_SeqID
            ${ew.customSqlSegment}
    </select>
    <select id="querySectionDeliveredDetailPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select
            t_wm_bill_detail.WB_SeqID,
            t_wm_bill_detail.Line_No,
            t_visit.Patient_ID,
            t_visit.Patient_Name,
            t_visit.Gender_ID,
            t_gender.Gender_Name,
            t_visit.Age_of_Years,
            t_visit.Age_of_Days,
            t_wm_req_detail.Bed_No,
            t_wm_bill_detail.WM_ReqID,
            t_wm_bill_detail.WM_Req_Detail_Line_No,
            t_wm_req_detail.Short_Cells,
            t_wm_req_detail.Cells_Delivered,
            t_wm_req_detail.Cells_Reserved,
            t_wm_bill_detail.Total_Packs,
            t_wm_bill_detail.Total_Cells,
            t_wm_bill_detail.Pack_Price,
            t_wm_bill_detail.Cell_Price,
            t_wm_bill_detail.Cost,
            t_wm_bill_detail.Amount,
            t_wm_bill_detail.Art_ID,
            t_article.art_code,
            t_article.art_name,
            t_article.art_spec,
            t_article.pack_unit,
            t_article.producer,
            t_article.cell_unit,
            t_article.pack_cells,
            t_article.mi_code,
            t_article.YPID_Code,
            t_article.Approval_No,
            t_art_stockno.Batch_No,
            t_art_batch.Date_Manufactured,
            t_art_batch.Expiry
        from microhis_clinics_wm.t_wm_bill_detail
         left join microhis_clinics_wm.t_wm_req_detail on t_wm_bill_detail.WM_ReqID = t_wm_req_detail.WM_ReqID and t_wm_bill_detail.WM_Req_Detail_Line_No = t_wm_req_detail.Line_No
         left join microhis_hsd.t_visit on t_wm_req_detail.Visit_ID = t_visit.Visit_ID
         left join hip_mdi.t_gender on t_visit.Gender_ID = t_gender.Gender_ID
         left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
         left join microhis_clinics_wm.t_art_stockno on t_wm_bill_detail.Art_ID = t_art_stockno.Art_ID and t_wm_bill_detail.Stock_No = t_art_stockno.Stock_No
         left join microhis_clinics_wm.t_art_batch on t_art_stockno.Art_ID = t_art_batch.Art_ID and t_art_stockno.Batch_No = t_art_batch.Batch_No
            ${ew.customSqlSegment}
    </select>
    <select id="querySectionDeliveredDetailPageBySection"
            resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select
            t_wm_bill_detail.Art_ID,
            sum(t_wm_bill_detail.Total_Packs) as Total_Packs,
            sum(t_wm_bill_detail.Total_Cells) as Total_Cells,
            sum(t_wm_bill_detail.Cost) as Cost,
            sum(t_wm_bill_detail.Amount) as Amount,
            min(t_article.art_code) as Art_Code,
            min(t_article.art_name) as Art_Name,
            min(t_article.art_spec) as Art_Spec,
            min(t_article.pack_unit) as Pack_Unit,
            min(t_article.producer) as Producer,
            min(t_article.cell_unit) as Cell_Unit,
            min(t_article.pack_cells) as Pack_Cells,
            min(t_article.mi_code) as MI_Code,
            min(t_article.YPID_Code) as YPID_Code,
            min(t_article.Approval_No) as Approval_No,
            t_art_stockno.Batch_No,
            min(t_art_batch.Date_Manufactured) as Date_Manufactured,
            min(t_art_batch.Expiry) as Expiry
        from microhis_clinics_wm.t_wm_bill_detail
         left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
         left join microhis_clinics_wm.t_art_stockno on t_wm_bill_detail.Art_ID = t_art_stockno.Art_ID and t_wm_bill_detail.Stock_No = t_art_stockno.Stock_No
         left join microhis_clinics_wm.t_art_batch on t_art_stockno.Art_ID = t_art_batch.Art_ID and t_art_stockno.Batch_No = t_art_batch.Batch_No
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
            ${ew.customSqlSegment}
    </select>
    <select id="querySectionDeliveredDetailPageByPatient"
            resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select
            t_wm_bill_detail.Art_ID,
            sum(t_wm_bill_detail.Total_Packs) as Total_Packs,
            sum(t_wm_bill_detail.Total_Cells) as Total_Cells,
            sum(t_wm_bill_detail.Cost) as Cost,
            sum(t_wm_bill_detail.Amount) as Amount,
            min(t_article.art_code) as Art_Code,
            min(t_article.art_name) as Art_Name,
            min(t_article.art_spec) as Art_Spec,
            min(t_article.pack_unit) as Pack_Unit,
            min(t_article.producer) as Producer,
            min(t_article.cell_unit) as Cell_Unit,
            min(t_article.pack_cells) as Pack_Cells,
            min(t_article.mi_code) as MI_Code,
            min(t_article.YPID_Code) as YPID_Code,
            min(t_article.Approval_No) as Approval_No,
            t_art_stockno.Batch_No,
            min(t_art_batch.Date_Manufactured) as Date_Manufactured,
            min(t_art_batch.Expiry) as Expiry
        from microhis_clinics_wm.t_wm_bill_detail
                 left join microhis_clinics_wm.t_wm_req_detail on t_wm_bill_detail.WM_ReqID = t_wm_req_detail.WM_ReqID and t_wm_bill_detail.WM_Req_Detail_Line_No = t_wm_req_detail.Line_No
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_stockno on t_wm_bill_detail.Art_ID = t_art_stockno.Art_ID and t_wm_bill_detail.Stock_No = t_art_stockno.Stock_No
                 left join microhis_clinics_wm.t_art_batch on t_art_stockno.Art_ID = t_art_batch.Art_ID and t_art_stockno.Batch_No = t_art_batch.Batch_No
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
            ${ew.customSqlSegment}
    </select>
    <select id="querySectionDeliveredReqDetailBySection"
            resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t.Art_ID, sum(t.Short_Cells) as Short_Cells
        from (select distinct t_wm_req_detail.*
        from microhis_clinics_wm.t_wm_bill_detail
        left join microhis_clinics_wm.t_wm_req_detail
        on t_wm_bill_detail.WM_ReqID = t_wm_req_detail.WM_ReqID and
        t_wm_bill_detail.WM_Req_Detail_Line_No = t_wm_req_detail.Line_No
        left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
            ${ew.customSqlSegment}
        ) t
        left join hip_mdi.t_article on t.Art_ID = t_article.Art_ID
        group by t_article.Art_ID
    </select>
    <select id="querySectionDeliveredDetailPageGroupByArt"
            resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select
            t_wm_req_detail.Short_Cells as Short_Cells,
            t_wm_req_detail.Cells_Delivered as Cells_Delivered,
            t_wm_req_detail.Cells_Reserved as Cells_Reserved,
            t_wm_bill_detail.Total_Packs as Total_Packs,
            t_wm_bill_detail.Total_Cells as Total_Cells,
            t_wm_bill_detail.Pack_Price as Pack_Price,
            t_wm_bill_detail.Cell_Price as Cell_Price,
            t_wm_bill_detail.Cost as Cost,
            t_wm_bill_detail.Amount as Amount,
            t_wm_bill_detail.Art_ID,
            t_article.art_code as Art_Code,
            t_article.art_name as Art_Name,
            t_article.art_spec as Art_Spec,
            t_article.pack_unit as Pack_Unit,
            t_article.producer as Producer,
            t_article.cell_unit as Cell_Unit,
            t_article.pack_cells as Pack_Cells,
            t_article.mi_code as MI_Code,
            t_article.YPID_Code as YPID_Code,
            t_article.Approval_No as Approval_No,
            t_wm_bill_detail.WM_ReqID,
            t_wm_bill_detail.WM_Req_Detail_Line_No,
            t_wm_bill_detail.Batch_No
        from microhis_clinics_wm.t_wm_bill_detail
                 left join microhis_clinics_wm.t_wm_req_detail on t_wm_bill_detail.WM_ReqID = t_wm_req_detail.WM_ReqID and t_wm_bill_detail.WM_Req_Detail_Line_No = t_wm_req_detail.Line_No
                 left join microhis_hsd.t_visit on t_wm_req_detail.Visit_ID = t_visit.Visit_ID
                 left join hip_mdi.t_gender on t_visit.Gender_ID = t_gender.Gender_ID
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
            ${ew.customSqlSegment}
    </select>
    <select id="querySectionDeliveredDetailPageGroupByPatient"
            resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select
            t_wm_req_detail.Visit_ID,
            min(t_visit.Patient_ID) as Patient_ID,
            min(t_visit.Patient_Name) as Patient_Name,
            min(t_visit.Gender_ID) as Gender_ID,
            min(t_gender.Gender_Name) as Gender_Name,
            min(t_visit.Age_of_Years) as Age_of_Years,
            min(t_visit.Age_of_Days) as Age_of_Days,
            min(t_wm_req_detail.Bed_No) as Bed_No,
            sum(t_wm_req_detail.Short_Cells) as Short_Cells,
            sum(t_wm_req_detail.Cells_Delivered) as Cells_Delivered,
            sum(t_wm_req_detail.Cells_Reserved) as Cells_Reserved,
            sum(t_wm_bill_detail.Total_Packs) as Total_Packs,
            sum(t_wm_bill_detail.Total_Cells) as Total_Cells,
            min(t_wm_bill_detail.Pack_Price) as Pack_Price,
            min(t_wm_bill_detail.Cell_Price) as Cell_Price,
            sum(t_wm_bill_detail.Cost) as Cost,
            sum(t_wm_bill_detail.Amount) as Amount,
            t_wm_bill_detail.Art_ID,
            min(t_article.art_code) as Art_Code,
            min(t_article.art_name) as Art_Name,
            min(t_article.art_spec) as Art_Spec,
            min(t_article.pack_unit) as Pack_Unit,
            min(t_article.producer) as Producer,
            min(t_article.cell_unit) as Cell_Unit,
            min(t_article.pack_cells) as Pack_Cells,
            min(t_article.mi_code) as MI_Code,
            min(t_article.YPID_Code) as YPID_Code,
            min(t_article.Approval_No) as Approval_No
        from microhis_clinics_wm.t_wm_bill_detail
                 left join microhis_clinics_wm.t_wm_req_detail on t_wm_bill_detail.WM_ReqID = t_wm_req_detail.WM_ReqID and t_wm_bill_detail.WM_Req_Detail_Line_No = t_wm_req_detail.Line_No
                 left join microhis_hsd.t_visit on t_wm_req_detail.Visit_ID = t_visit.Visit_ID
                 left join hip_mdi.t_gender on t_visit.Gender_ID = t_gender.Gender_ID
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join microhis_clinics_wm.t_art_stockno on t_wm_bill_detail.Art_ID = t_art_stockno.Art_ID and t_wm_bill_detail.Stock_No = t_art_stockno.Stock_No
                 left join microhis_clinics_wm.t_art_batch on t_art_stockno.Art_ID = t_art_batch.Art_ID and t_art_stockno.Batch_No = t_art_batch.Batch_No
            ${ew.customSqlSegment}
    </select>

    <select id="findPendingLsByWrapper" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t_wm_bill.Bsn_Type,
               t_wm_bill.WMBill_Type_ID,
               t_wm_bill.Status,
               t_wm_bill.Bsn_Abstract,
               t_wm_bill.Recipe_ID,
               t_wm_bill.Clinic_Type_ID,
               t_wm_bill_detail.*,
               t_article.Art_Code,
               t_article.Art_Name,
               t_article.Art_Spec,
               t_article.Producer,
               t_article.Pack_Unit,
               t_article.Cell_Unit
        from microhis_clinics_wm.t_wm_bill_pending
                 inner join microhis_clinics_wm.t_wm_bill_detail on t_wm_bill_pending.WB_SeqID = t_wm_bill_detail.WB_SeqID
                 inner join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 inner join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
        ${ew.customSqlSegment}
    </select>

    <select id="findLsByWrapper" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t_wm_bill.Bsn_Type,
               t_wm_bill.WMBill_Type_ID,
               t_wm_bill.Status,
               t_wm_bill.Bsn_Abstract,
               t_wm_bill.Recipe_ID,
               t_wm_bill.Clinic_Type_ID,
               t_wm_bill_detail.*,
               t_article.Art_Code,
               t_article.Art_Name,
               t_article.Art_Spec,
               t_article.Producer,
               t_article.Art_Type_ID,
               t_article.Pack_Unit,
               t_article.Cell_Unit,
               t_article.Pack_Cells,
               t_article.QS_Code1,
               t_article.QS_Code2,
               t_article.No_Track_Code,
               t_article.is_Disassembled as art_is_Disassembled
        from microhis_clinics_wm.t_wm_bill_detail
                 inner join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 inner join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
            ${ew.customSqlSegment}
    </select>
</mapper>