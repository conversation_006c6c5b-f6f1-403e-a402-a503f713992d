package cn.feiying.med.microhis.hsd.vo;

import cn.feiying.med.common.annotation.TrimStr;
import cn.feiying.med.common.validator.group.DefaultGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 电子住院证数据Vo对象
 */
@Data
public class IprVo {
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 诊疗ID
     */
    private Long visitId;
    /**
     * 电子住院证ID
     */
    private Long iprId;
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者号
     * 门诊患者号
     */
    private Long patientNo;
    
    /**
     * 患者姓名
     */
    @NotNull(message = "患者姓名不能为空", groups = DefaultGroup.class)
    @Size(message = "患者姓名不能超过50个字符", max = 50, groups = DefaultGroup.class)
    @TrimStr
    private String patientName;
    
    /**
     * 性别
     * 0-未知性别，1-男，2-女
     */
    @NotNull(message = "性别不能为空", groups = DefaultGroup.class)
    private Integer genderId;
    
    /**
     * 年龄
     */
    private Integer ageOfYears;
    
    /**
     * 日龄
     */
    private Integer ageOfDays;
    
    /**
     * 证件类型代号
     */
    @NotNull(message = "证件类型不能为空", groups = DefaultGroup.class)
    private Integer certTypeId;
    
    /**
     * 证件号码
     */
    @NotNull(message = "证件号码不能为空", groups = DefaultGroup.class)
    @Size(message = "证件号码不能超过64个字符", max = 64, groups = DefaultGroup.class)
    @TrimStr
    private String idcertNo;
    
    /**
     * 电话号码
     */
    @Size(message = "电话号码不能超过64个字符", max = 64, groups = DefaultGroup.class)
    @TrimStr
    private String telNo;
    
    /**
     * 申请时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private String timeAdmission;
    
    /**
     * 入院科室代码
     */
    @NotNull(message = "入院科室不能为空", groups = DefaultGroup.class)
    @Size(message = "入院科室代码不能超过10个字符", max = 10, groups = DefaultGroup.class)
    private String deptCode;
    
    /**
     * 入院科室名称
     */
    @Size(message = "入院科室名称不能超过60个字符", max = 60, groups = DefaultGroup.class)
    private String deptName;
    
    /**
     * 申请医师编码
     */
    @NotNull(message = "申请医师ID不能为空", groups = DefaultGroup.class)
    private Long clinicianId;

    /**
     * 计划入院日期
     */
    @NotNull(message = "计划入院日期不能为空", groups = DefaultGroup.class)
    private Integer clinicDate;
    
    /**
     * 是否紧急
     */
    private Integer isEmergency;
    
    /**
     * 说明备注
     */
    @Size(message = "说明备注不能超过60个字符", max = 60, groups = DefaultGroup.class)
    private String notes;
    
    /**
     * 门诊西医诊断编码
     */
    private String opDiseaseCode;
    
    /**
     * 门诊西医诊断名称
     */
    private String opDiseaseName;
    
    /**
     * 门诊中医疾病编码
     */
    private String opTcmDiseaseCode;
    
    /**
     * 门诊中医疾病名称
     */
    private String opTcmDiseaseName;
    
    /**
     * 门诊中医证候编码
     */
    private String opTcmSymptomCode;
    
    /**
     * 门诊中医证候名称
     */
    private String opTcmSymptomName;
    
    /**
     * 入院原因
     */
    @Size(message = "入院原因不能超过60个字符", max = 60, groups = DefaultGroup.class)
    private String hospitalizedCause;
} 