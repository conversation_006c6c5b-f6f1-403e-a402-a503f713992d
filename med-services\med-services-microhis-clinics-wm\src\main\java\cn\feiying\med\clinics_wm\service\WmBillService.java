package cn.feiying.med.clinics_wm.service;

import cn.feiying.med.clinics_wm.dto.WmBillDetailDto;
import cn.feiying.med.clinics_wm.dto.WmBillDto;
import cn.feiying.med.clinics_wm.entity.WmBillDetailEntity;
import cn.feiying.med.clinics_wm.entity.WmBillEntity;
import cn.feiying.med.clinics_wm.entity.WmReqArtEntity;
import cn.feiying.med.clinics_wm.entity.WmReqDetailEntity;
import cn.feiying.med.clinics_wm.model.*;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.hip.mdi.entity.ArticleEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 出入库单表
 *
 * <AUTHOR> @email
 * @date 2024-06-04 18:48:02
 * 2024-06-04 18:48:02
 */
public interface WmBillService extends IService<WmBillEntity> {
    boolean getDeptStockEnabled(long orgId);

    /**
     * 机构内部消耗是否以成本计价 true 成本价 false 非成本
     *
     * @param orgId
     * @return
     */
    boolean getIsCostConsumeTrue(long orgId);

    WmBillDto findDtoById(Long wbSeqid);

    PageUtils queryPage(long orgId, Map<String, Object> params);

    PageUtils<WmBillDto> queryReservedPage(long orgId, Map<String, Object> params);

    void saveEntity(WmBillEntity entity);

    Long saveBill(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details);

    void inStoreSaveBill(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details);

    Long submitBill(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean reserve);

    /**
     * 采购冲红
     *
     * @param orgId
     * @param userId
     * @param wmBill  冲红主表信息
     * @param details 冲红明细表信息
     * @return
     */
    void inStoreSubmitBill(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details);

    void cancelBill(long orgId, long userId, Long wbSeqid);

    void validateBill(long orgId, long userId, Long wbSeqid, boolean isPass);

    /**
     * 采购冲红自动审核
     *
     * @param orgId
     * @param userId
     * @param wbSeqid
     * @param isPass  暂时默认为true
     */
    void inStoreValidateBill(long orgId, long userId, Long wbSeqid, boolean isPass);

    void batchValidateBill(long orgId, long userId, List<Long> wbdSeqids, boolean isPass);

    void changeBatchNo(long orgId, long userId, String deptCode, Long artId, Integer oldStockNo, Integer newStockNo, Integer totalPacks, BigDecimal totalCells);

    /**
     * 出入库单库存预留
     *
     * @param wbSeqid
     * @param orgId
     * @param deptCode
     * @param reqArtList
     * @param allReserved  需要全部预留
     * @param useCostPrice
     * @param errorMsgPre
     * @return
     */
    List<WmBillDetailEntity> wmBillArtStockReserve(Long wbSeqid, Long orgId, String deptCode, List<WmReqArtEntity> reqArtList, boolean allReserved, boolean useCostPrice, String errorMsgPre);

    List<WmBillDetailEntity> wmBillDetailStockReserve(Long wbSeqid, Long orgId, String deptCode, List<WmReqDetailEntity> reqArtList, boolean allReserved, boolean useCostPrice, String errorMsgPre);

    /**
     * 扣减库存
     *
     * @param wbSeqid
     * @param orgId
     * @param deptCode
     * @param wmBillDetailDtoList
     * @param allReserved
     * @param useCostPrice
     * @param errorMsgPre
     * @return
     */
    List<WmBillDetailEntity> wmBillArtStockNoTransfer(Long wbSeqid, Long orgId, String deptCode, List<WmBillDetailDto> wmBillDetailDtoList, boolean allReserved, boolean useCostPrice, String errorMsgPre);

    /**
     * 品种库存预留
     *
     * @param orgId
     * @param deptCode
     * @param articleEntity
     * @param deptStockEnabled
     * @param batchNo
     * @param stockNo
     * @param totalPacks
     * @param totalCells
     * @param checkSplittable  检查是否允许拆零
     * @param allReserved      需要全部预留
     * @param useCostPrice
     * @param errorMsgPre
     * @return
     */
    ArtStockReserveResult artStockReserve(Long orgId, String deptCode, ArticleEntity articleEntity, boolean deptStockEnabled,
                                          @Nullable String batchNo, @Nullable Integer stockNo, Integer totalPacks, BigDecimal totalCells, boolean checkSplittable, boolean allReserved, boolean useCostPrice, String errorMsgPre);

    /**
     * 发药确认
     *
     * @param orgId
     * @param userId
     * @param wbSeqid
     * @return
     */
    List<WmBillDetailEntity> deliverBill(long orgId, long userId, Long wbSeqid, Date date, Integer clinicTypeId);

    /**
     * 扣减库存
     *
     * @param orgId
     * @param userId
     * @param wbSeqid
     * @param date
     * @param clinicTypeId
     * @return
     */
    List<WmBillDetailEntity> deliverBillTransfer(long orgId, long userId, Long wbSeqid, Date date, Integer clinicTypeId);


    void updateTrackcodeCollected(Long wbSeqid);

    /**
     * 往来单位业务统计查询
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils queryCustBillCountPage(long orgId, Map<String, Object> params);

    /**
     * 往来单位品种列表查询
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils queryCustBillArtPage(long orgId, Map<String, Object> params);

    List<CustBillArtModel> queryCustBillArtList(long orgId, Map<String, Object> params);

    /**
     * 商品业务统计查询
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils queryArtBillCountPage(long orgId, Map<String, Object> params);

    /**
     * 商品业务统计查询-列表，不分页
     *
     * @param orgId
     * @param params
     * @return
     */
    List<ArtBillCountModel> queryArtBillCountList(long orgId, Map<String, Object> params);


    /**
     * 商品业务票据详情查询
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils queryArtBillCountByBillPage(long orgId, Map<String, Object> params);

    /**
     * 商品业务往来汇总查询
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils queryArtBillCountByCustPage(long orgId, Map<String, Object> params);

    /**
     * 仓库业务统计查询
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils queryStoreBillCountPage(long orgId, Map<String, Object> params);

    /**
     * 仓库品种列表查询
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils queryStoreBillArtPage(long orgId, Map<String, Object> params);

    List<StoreBillArtModel> queryStoreBillArtList(long orgId, Map<String, Object> params);

    /**
     * 病区业务统计查询
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils querySectionBillCountPage(long orgId, Map<String, Object> params);

    /**
     * 病区品种列表查询
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils querySectionBillArtPage(long orgId, Map<String, Object> params);

    List<SectionBillArtModel> querySectionBillArtList(long orgId, Map<String, Object> params);

    /**
     * 当日处方发药列表
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils queryTodayRecipeDeliverPage(long orgId, Map<String, Object> params);

    /**
     * 根据明细更新主表金额
     *
     * @param wbSeqid
     */
    void updateAmount(Long wbSeqid);

    /**
     * 根据明细更新主表成本
     *
     * @param wbSeqid
     */
    void updateCost(Long wbSeqid);

    /**
     * 药品消耗情况统计
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils queryArtDeliverSumPage(long orgId, Map<String, Object> params);

    /**
     * 药品消耗情况列表-不分页
     *
     * @param orgId
     * @param params
     * @return
     */
    List<WmBillDetailDto> queryArtDeliverSumList(long orgId, Map<String, Object> params);


    /**
     * 药品消耗情况统计-明细
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils queryArtDeliverSumDetailPage(long orgId, Map<String, Object> params);


    /**
     * 病区已发药列表
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils querySectionDeliveredPage(long orgId, Map<String, Object> params);

    /**
     * 病区已发药列表-按发药记录-汇总
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils querySectionDeliveredSummaryPage(long orgId, Map<String, Object> params);

    PageUtils querySectionDeliveredPageBySection(long orgId, Map<String, Object> params);

    PageUtils querySectionDeliveredPageByPatient(long orgId, Map<String, Object> params);

    /**
     * 病区-待退药-退药列表汇总
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils returingMedicationSumPage(long orgId, Map<String, Object> params);

    /**
     * 病区-待退药/已退药-退药列表
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils returnBillPage(long orgId, Map<String, Object> params);

    /**
     * 病区-已退药-退药记录-按病区汇总
     *
     * @param orgId
     * @param params
     * @return
     */
    PageUtils returnedBillBySectionPage(long orgId, Map<String, Object> params);

    /**
     * 处理拆零盒整操作
     * 将record的JSON对象转换为Map并执行拆零盒整计算
     *
     * @param orgId 机构ID
     * @param params 参数，包含record的JSON字符串
     */
    void performSplitPack(long orgId, Map<String, Object> params);

}


