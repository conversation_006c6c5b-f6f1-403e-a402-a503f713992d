package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.common.utils.R;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.mdi.dto.*;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.hip.model.*;
import cn.feiying.med.hip.model.clinicsWm.WmBillDetailModel;
import cn.feiying.med.hip.model.clinicsWm.WmReqDetailModel;
import cn.feiying.med.hip.model.clinicsWm.WmReqModel;
import cn.feiying.med.hip.model.spd.*;
import cn.feiying.med.his.moe.entity.*;
import cn.feiying.med.his.moe.service.*;
import cn.feiying.med.microhis.bcs.entity.*;
import cn.feiying.med.microhis.bcs.service.*;
import cn.feiying.med.microhis.hsd.dto.*;
import cn.feiying.med.microhis.hsd.entity.*;
import cn.feiying.med.microhis.hsd.event.HsdEventPublisher;
import cn.feiying.med.microhis.hsd.service.*;
import cn.feiying.med.microhis.hsd.vo.*;
import cn.feiying.med.saas.api.service.*;
import cn.feiying.med.saas.api.vo.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class HsdRecipeServiceImpl implements HsdRecipeService {

    @Resource
    private VisitService visitService;
    @Resource
    private RecipeService recipeService;
    @Resource
    private RecipeExtraService recipeExtraService;
    @Resource
    private RecipeDetailService recipeDetailService;
    @Resource
    private RecipeBillService recipeBillService;
    @Resource
    private ArticleService articleService;
    @Resource
    private BillService billService;
    @Resource
    private RecipeIndexService recipeIndexService;
    @Resource
    private BillDetailService billDetailService;
    @Resource
    private BillUnpaidService billUnpaidService;
    @Resource
    private RemoteRscService remoteRscService;
    @Resource
    private RemoteSpdService remoteSpdService;
    @Resource
    private RecipeTemplateService recipeTemplateService;
    @Resource
    private SchedService schedService;
    @Resource
    private PackageDetailService packageDetailService;
    @Resource
    private HsdEventPublisher hsdEventPublisher;
    @Resource
    private RemoteAcService remoteAcService;
    @Resource
    private RemoteEhrService remoteEhrService;
    @Resource
    private RemoteClinicsWmService remoteClinicsWmService;
    @Resource
    private SignatureService signatureService;
    @Resource
    private SectionArtService sectionArtService;
    @Resource
    private SectionRouteConsumableService sectionRouteConsumableService;
    @Resource
    private InsuranceTypeService insuranceTypeService;
    @Resource
    private RemoteMcispService remoteMcispService;
    @Resource
    private OrgService orgService;
    @Resource
    private OrgSettingService orgSettingService;
    @Resource
    private HsdRecipeSignService hsdRecipeSignService;
    @Resource
    private OrgDeptService orgDeptService;
    @Resource
    private MoeOrderService moeOrderService;
    @Resource
    private OrderService orderService;
    @Resource
    private TreatmentService treatmentService;
    @Resource
    private SectionService sectionService;

    @Override
    public PageUtils<RecipeDto> findRecipePage(Map<String, Object> params) {
        PageUtils<RecipeDto> pageUtils = recipeService.queryPage(params);
        setRecipeAmount(pageUtils.getList());
        setSignature(pageUtils.getList());
        return pageUtils;
    }

    @Override
    public List<RecipeDto> findRecipeLs(Long orgId, Map<String, Object> params) {
        List<RecipeDto> recipeLs = recipeService.findLs(params);
        setRecipeAmount(recipeLs);
        setSignature(recipeLs);
        return recipeLs;
    }

    @Override
    public RecipeDto findById(Long recipeId) {
        RecipeDto recipeDto = recipeService.findById(recipeId);
        if (recipeDto != null) {
            setRecipeAmount(Collections.singletonList(recipeDto));
            // 获取发药明细
            try {
                List<WmBillDetailModel> wmBillDetailLs = remoteClinicsWmService.getBillDetailByRecipeId(recipeDto.getOrgId(), recipeDto.getRecipeId());
                if (ObjectUtil.isNotEmpty(wmBillDetailLs)) {
                    setTotalPacks(recipeDto.getRecipeTypeId(), recipeDto.getRecipeDetailLs(), wmBillDetailLs);
                    for (RecipeGroupDto recipeGroupLs : recipeDto.getRecipeGroupLs()) {
                        setTotalPacks(recipeDto.getRecipeTypeId(), recipeGroupLs.getRecipeDetailLs(), wmBillDetailLs);
                    }
                }
            } catch (Exception e) {
                log.error("获取发药明细失败", e);
            }
        }
        return recipeDto;
    }

    void setTotalPacks(Integer recipeTypeId, List<RecipeDetailDto> recipeDetailLs, List<WmBillDetailModel> wmBillDetailLs) {
        if (ObjectUtil.isNotEmpty(recipeDetailLs)) {
            for (RecipeDetailDto recipeDetail : recipeDetailLs) {
                if (recipeTypeId.equals(RecipeType.xy.getValue())) {
                    if (!recipeDetail.getUnitType().equals(UnitType.Pack_Unit.getValue())) {
                        // 给根据总量total，换算系数packCells计算总包装数totalPacks
                        Integer totalPacks = Convert.toInt(recipeDetail.getTotal().divide(BigDecimal.valueOf(recipeDetail.getPackCells()), 2, RoundingMode.HALF_UP));
                        if (totalPacks > 0) {
                            recipeDetail.setTotalPacks(totalPacks);
                            // 计算剩余的制剂数量
                            BigDecimal totalCells = recipeDetail.getTotal().subtract(Convert.toBigDecimal(totalPacks * recipeDetail.getPackCells()));
                            if (totalCells.compareTo(BigDecimal.ZERO) > 0) {
                                recipeDetail.setTotalCells(totalCells);
                            } else {
                                recipeDetail.setTotalCells(BigDecimal.ZERO);
                            }
                        } else {
                            recipeDetail.setTotalPacks(0);
                            recipeDetail.setTotalCells(recipeDetail.getTotal());
                        }
                    } else if (recipeDetail.getUnitType().equals(UnitType.Pack_Unit.getValue())) {
                        recipeDetail.setTotalPacks(recipeDetail.getTotal().intValue());
                        recipeDetail.setTotalCells(BigDecimal.ZERO);
                    } else if (recipeDetail.getUnitType().equals(UnitType.Cell_Unit.getValue())) {
                        recipeDetail.setTotalPacks(0);
                        recipeDetail.setTotalCells(recipeDetail.getTotal());
                    }
                } else {
                    List<WmBillDetailModel> billDetailLs = wmBillDetailLs.stream().filter(p -> p.getArtId().equals(recipeDetail.getArtId())).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(billDetailLs)) {
                        // 获取Total_Packs汇总和Total_Cells汇总
                        Integer totalPacks = billDetailLs.stream().map(WmBillDetailModel::getTotalPacks).filter(Objects::nonNull).reduce(0, Integer::sum);
                        BigDecimal totalCells = billDetailLs.stream().map(WmBillDetailModel::getTotalCells).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        recipeDetail.setTotalPacks(totalPacks);
                        recipeDetail.setTotalCells(totalCells);
                    }
                }
            }
        }
    }

    @Override
    public RecipeDto refRecipe(Long recipeId) {
        RecipeDto recipeDto = recipeService.refRecipe(recipeId);
        if (recipeDto != null) {
            recipeDto.setBseqid(null);
            setRecipeAmount(Collections.singletonList(recipeDto));
        }
        return recipeDto;
    }

    private void setSignature(List<RecipeDto> recipeLs) {
        if (ObjectUtil.isNotEmpty(recipeLs)) {
            List<Long> signatureIdLs = recipeLs.stream().filter(p -> p.getTimeSigned() != null).map(RecipeDto::getSignatureId).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(signatureIdLs)) {
                List<SignatureEntity> signatureLs = signatureService.list(Wrappers.lambdaQuery(SignatureEntity.class)
                        .in(SignatureEntity::getSignatureId, signatureIdLs));
                if (ObjectUtil.isNotEmpty(signatureLs)) {
                    recipeLs.forEach(recipeDto -> {
                        if (recipeDto.getTimeSigned() != null) {
                            Optional<SignatureEntity> signatureEntity = signatureLs.stream().filter(p -> p.getSignatureId() != null && p.getSignatureId().equals(recipeDto.getSignatureId())).findFirst();
                            signatureEntity.ifPresent(entity -> recipeDto.setSignatureUrl(entity.getSignatureUrl()));
                        }
                    });
                }
            }
        }
    }

    private void setRecipeAmount(List<RecipeDto> recipeLs) {
        if (ObjectUtil.isNotEmpty(recipeLs)) {
            // 已生成划价单的处方，设置金额为划价单金额
            List<Long> bseqIdLs = new ArrayList<>();
            recipeLs.forEach(recipeDto -> {
                if (recipeDto.getBseqid() != null && !bseqIdLs.contains(recipeDto.getBseqid())) {
                    bseqIdLs.add(recipeDto.getBseqid());
                }
            });
            if (ObjectUtil.isNotEmpty(bseqIdLs)) {
                List<BillDetailEntity> billDetailLs = billDetailService.list(Wrappers.lambdaQuery(BillDetailEntity.class).in(BillDetailEntity::getBseqid, bseqIdLs));
                if (ObjectUtil.isNotEmpty(billDetailLs)) {
                    recipeLs.forEach(recipeDto -> {
                        BigDecimal amount = BigDecimal.ZERO;
                        if (recipeDto.getBseqid() != null) {
                            List<BillDetailEntity> detailLs = billDetailLs.stream().filter(p -> p.getBseqid().equals(recipeDto.getBseqid())).collect(Collectors.toList());
                            amount = detailLs.stream().map(BillDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//                            recipeDto.getRecipeGroupLs().forEach(recipeGroupDto -> recipeGroupDto.getRecipeDetailLs().forEach(recipeDetailDto -> {
//                                List<BillDetailEntity> detailLs;
//                                if (recipeDetailDto.getIsPackage() != null && recipeDetailDto.getIsPackage() == 1) {
//                                    detailLs = billDetailLs.stream().filter(p -> p.getBseqid().equals(recipeDto.getBseqid()) && p.getPackageId() != null && p.getPackageId().equals(recipeDetailDto.getArtId())).collect(Collectors.toList());
//                                } else {
//                                    detailLs = billDetailLs.stream().filter(p -> p.getBseqid().equals(recipeDto.getBseqid()) && p.getArtId().equals(recipeDetailDto.getArtId())).collect(Collectors.toList());
//                                }
//                                if (ObjectUtil.isNotEmpty(detailLs)) {
//                                    price = detailLs.stream().map(BillDetailEntity::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                                    amount = price.multiply(recipeDetailDto.getTotal());
//                                }
//                                recipeDetailDto.setAmount(amount);
//                            }));
                        }
                        recipeDto.setAmount(amount);
                    });
                }
            }
            // 未生成划价单的处方，从仓库中获取价格和数量
            List<Integer> sectionIdLs = recipeLs.stream().map(RecipeDto::getSectionId).distinct().collect(Collectors.toList());
            List<Integer> recipeTypeIdLs = ListUtil.of(RecipeType.xy.getValue(), RecipeType.zy.getValue());
            if (ObjectUtil.isNotEmpty(sectionIdLs)) {
                List<SectionEntity> sectionLs = sectionService.list(Wrappers.lambdaQuery(SectionEntity.class).in(SectionEntity::getSectionId, sectionIdLs));
                recipeLs.forEach(recipeDto -> {
                    if (recipeDto.getBseqid() == null && recipeDto.getSectionId() != null
                            && recipeTypeIdLs.contains(recipeDto.getRecipeTypeId())
                            && !recipeDto.getExecStatus().equals(ExecStatus.cancel.getValue())) {
                        Optional<SectionEntity> sectionEntity = sectionLs.stream().filter(p -> p.getSectionId().equals(recipeDto.getSectionId())).findFirst();
                        if (sectionEntity.isPresent()) {
                            String storeId = "";
                            if (recipeDto.getRecipeTypeId().equals(RecipeType.xy.getValue())) {
                                storeId = sectionEntity.get().getWtmDeptCode();
                            } else if (recipeDto.getRecipeTypeId().equals(RecipeType.zy.getValue())) {
                                storeId = sectionEntity.get().getCtmDeptCode();
                            }
                            List<ArtDetailVo> artDetailLs = new ArrayList<>();
                            recipeDto.getRecipeGroupLs().forEach(recipeGroupDto -> recipeGroupDto.getRecipeDetailLs().forEach(recipeDetailDto -> {
                                ArtDetailVo artDetailVo = new ArtDetailVo();
                                artDetailVo.setArtId(recipeDetailDto.getArtId());
                                artDetailVo.setUnitType(recipeDetailDto.getUnitType());
                                artDetailVo.setTotal(recipeDetailDto.getTotal());
                                artDetailLs.add(artDetailVo);
                            }));
                            if (ObjectUtil.isNotEmpty(artDetailLs)) {
                                try {
                                    List<WmBillDetailModel> wmBillDetailLs = findWmArtPriceLs(recipeDto.getOrgId(), storeId, recipeDto.getTimes(), false, artDetailLs);
                                    if (ObjectUtil.isNotEmpty(wmBillDetailLs)) {
                                        BigDecimal totalAmount = BigDecimal.ZERO;
                                        for (RecipeGroupDto recipeGroupDto : recipeDto.getRecipeGroupLs()) {
                                            for (RecipeDetailDto recipeDetailDto : recipeGroupDto.getRecipeDetailLs()) {
                                                WmBillDetailModel wmBillDetail = wmBillDetailLs.stream().filter(p -> p.getArtId().equals(recipeDetailDto.getArtId())).findFirst().orElse(null);
                                                if (wmBillDetail!= null) {
                                                    recipeDetailDto.setCellPrice(wmBillDetail.getCellPrice());
                                                    recipeDetailDto.setPackPrice(wmBillDetail.getPackPrice());
                                                    BigDecimal amount = getRecipeDetailAmount(recipeDetailDto.getUnitType(), recipeDetailDto.getTotal(), Convert.toBigDecimal(recipeDetailDto.getPackCells(), BigDecimal.ONE), wmBillDetail);
                                                    recipeDetailDto.setAmount(amount);
                                                    totalAmount = totalAmount.add(amount);
                                                }
                                            }
                                        }
                                        recipeDto.getRecipeDetailLs().forEach(recipeDetailDto -> {
                                            WmBillDetailModel wmBillDetail = wmBillDetailLs.stream().filter(p -> p.getArtId().equals(recipeDetailDto.getArtId())).findFirst().orElse(null);
                                            if (wmBillDetail!= null) {
                                                recipeDetailDto.setCellPrice(wmBillDetail.getCellPrice());
                                                recipeDetailDto.setPackPrice(wmBillDetail.getPackPrice());
                                                BigDecimal amount = getRecipeDetailAmount(recipeDetailDto.getUnitType(), recipeDetailDto.getTotal(), Convert.toBigDecimal(recipeDetailDto.getPackCells(), BigDecimal.ONE), wmBillDetail);
                                                recipeDetailDto.setAmount(amount);
                                            }
                                        });
                                        recipeDto.setAmount(totalAmount);
                                    }
                                } catch (Exception e) {
                                    log.error("获取药品价格明细失败", e);
                                }
                            }
                        }
                    }
                });
            }
        }
    }

    private BigDecimal getRecipeDetailAmount(Integer unitType, BigDecimal detailTotal, BigDecimal packCells,  WmBillDetailModel wmBillDetail) {
        int totalPacks;
        BigDecimal totalCells = BigDecimal.ZERO;
        if (Convert.toInt(unitType, UnitType.Pack_Unit.getValue()) == UnitType.Pack_Unit.getValue()) { // 包装
            // 如果有小数这里做了向上取整
            BigDecimal total = Convert.toBigDecimal(detailTotal, BigDecimal.ONE).setScale(0, RoundingMode.CEILING);
            totalPacks = total.intValue();
            // 有小数
            if (Convert.toBigDecimal(detailTotal, BigDecimal.ONE).remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) != 0) {

                if (NumberUtil.isGreater(packCells, BigDecimal.ONE)) {
                    // 包装单位有小数，且包装制剂数大于1换算成制剂数量做申请
                    totalCells = detailTotal.multiply(packCells).setScale(0, RoundingMode.CEILING);
                    totalPacks = 0;
                }
            }
        } else {
            totalPacks = 0;
            totalCells = detailTotal;
        }
        BigDecimal amount = BigDecimal.ZERO;
        if (totalPacks != 0) {
            amount = amount.add(BigDecimal.valueOf(totalPacks).multiply(wmBillDetail.getPackPrice()).setScale(2, RoundingMode.HALF_UP));
        }
        if (totalCells != null && totalCells.compareTo(BigDecimal.ZERO) != 0) {
            amount = amount.add(totalCells.multiply(wmBillDetail.getCellPrice()).setScale(2, RoundingMode.HALF_UP));
        }
        return amount;
    }

    @Override
    @Transactional
    public R<?> clinicianSign(Long orgId, Long userId, Long certId, String storeId, Integer deliverType, Integer sectionId, Integer isSelfPaid,
                              boolean ignoreRoutePackageFeeCheck, boolean ignoreRouteConsumableFeeCheck, List<Long> recipeIdLs,
                              List<RecipeSignVo> treatmentRecipeLs) {
        log.info("处方审核 userId:{} certId:{} storeId:{} deliverType:{} sectionId:{} recipeIdLs:{} recipeLs:{}",
                userId, certId, storeId, deliverType, sectionId, JSONUtil.toJsonStr(recipeIdLs), JSONUtil.toJsonStr(treatmentRecipeLs));
        if (recipeIdLs == null || recipeIdLs.isEmpty()) {
            throw new SaveFailureException("请选择要签名的处方");
        }
        List<RecipeDto> recipeLs = recipeService.findLsByIdLs(recipeIdLs);
        Optional<RecipeDto> signedRecipe = recipeLs.stream().filter(recipeDto -> recipeDto.getTimeSigned() != null).findFirst();
        // 检查是否有已签名的处方
        if (signedRecipe.isPresent()) {
            // 添加日志记录
            log.debug("已签名的处方不能再次签名: {}", signedRecipe.get());
            throw new SaveFailureException("已签名的处方不能再次签名");
        }
        // 判断已作废的处方不能再签名
        Optional<RecipeDto> invalidRecipe = recipeLs.stream().filter(recipeDto -> recipeDto.getExecStatus() != null && recipeDto.getExecStatus() > ExecStatus.processing.getValue()).findFirst();
        if (invalidRecipe.isPresent()) {
            // 添加日志记录
            log.debug("已作废的处方不能签名: {}", invalidRecipe.get());
            throw new SaveFailureException("已作废的处方不能签名");
        }
        String msg = "";
        for (Long recipeId : recipeIdLs) {
            try {
                log.info("开始审核处方: {}", recipeId);
                hsdRecipeSignService.processSingleRecipe(orgId, userId, certId, storeId, deliverType, sectionId, isSelfPaid, ignoreRoutePackageFeeCheck,
                        ignoreRouteConsumableFeeCheck, recipeId, treatmentRecipeLs, recipeLs);
                /*// 如果不是治疗处方，查询处方是否有对应的治疗处方单，有就直接签名
                RecipeDto operateRecipe = recipeLs.stream().filter(recipeDto -> recipeDto.getRecipeId().equals(recipeId)).findFirst().orElse(null);
                if (operateRecipe != null && !operateRecipe.getRecipeTypeId().equals(RecipeType.zl.getValue())) {
                    RecipeEntity recipe = recipeService.getById(recipeId);
                    // 在处理关联治疗处方前先刷新事务，确保数据已写入数据库
                    TransactionAspectSupport.currentTransactionStatus().flush();
                    log.info("处理关联治疗处方，原处方ID: {}, 关联治疗处方ID: {}", recipeId, recipe.getCureRecipeId());

                    if (recipe.getCureRecipeId() != null) {
                        RecipeDto cureRecipe = recipeService.findById(recipe.getCureRecipeId());
                        if (cureRecipe != null && cureRecipe.getTimeSigned() == null && (cureRecipe.getExecStatus() == null || cureRecipe.getExecStatus() < ExecStatus.processing.getValue())) {
                            // 查询科室对应的病区和病区默认耗材仓库
                            Pair<Integer, String> sectionAndStorage = findSectionAndStorage(orgId, cureRecipe.getExceDeptcode(), cureRecipe.getApplyDeptcode());
                            Integer cureSectionId = sectionAndStorage.getKey();
                            String cureStoreId = sectionAndStorage.getValue();
                            hsdRecipeSignService.processSingleRecipe(orgId, userId, certId, cureStoreId, null, cureSectionId, isSelfPaid, ignoreRoutePackageFeeCheck,
                                    ignoreRouteConsumableFeeCheck, cureRecipe.getRecipeId(), null, Collections.singletonList(cureRecipe));
                        }
                    }
                }*/
            } catch (Exception e) {
                log.error("审核处方失败，recipeId: {}", recipeId, e);
                msg = StrUtil.isNotBlank(e.getMessage()) ? e.getMessage() : "空指针异常";
            }
        }
        if (StrUtil.isNotBlank(msg)) {
            return R.error(msg);
        }
        return R.ok();
    }

    @Override
    @Transactional
    public void cureRecipeSign(Long orgId, Long userId, Long certId, List<Long> recipeIdLs, List<Long> orderIdLs) {
        log.info("对应的治疗处方审核 userId:{} certId:{} recipeIdLs:{} orderIdLs:{}", userId, certId, JSONUtil.toJsonStr(recipeIdLs), JSONUtil.toJsonStr(orderIdLs));
        List<Long> cureRecipeIdLs = recipeService.getCureRecipeIdLs(recipeIdLs, orderIdLs);
        if (ObjectUtil.isNotEmpty(cureRecipeIdLs)) {
            List<RecipeDto> recipeLs = recipeService.findLsByIdLs(cureRecipeIdLs);
            for (RecipeDto cureRecipe : recipeLs) {
                if (cureRecipe != null && cureRecipe.getTimeSigned() == null && (cureRecipe.getExecStatus() == null || cureRecipe.getExecStatus() < ExecStatus.processing.getValue())) {
                    // 查询科室对应的病区和病区默认耗材仓库
                    Pair<Integer, String> sectionAndStorage = findSectionAndStorage(orgId, cureRecipe.getExceDeptcode(), cureRecipe.getApplyDeptcode());
                    Integer cureSectionId = sectionAndStorage.getKey();
                    String cureStoreId = sectionAndStorage.getValue();
                    hsdRecipeSignService.processSingleRecipe(orgId, userId, certId, cureStoreId, null, cureSectionId, null, false,
                            false, cureRecipe.getRecipeId(), null, Collections.singletonList(cureRecipe));
                }
            }
        }
    }

    // 辅助方法：查询病区和耗材仓库 先根据执行科室查询科室对应的病区和病区默认耗材仓库，如果没有就查询申请科室对应的病区和病区默认耗材仓库
    private Pair<Integer, String> findSectionAndStorage(Long orgId, String exceDeptcode, String applyDeptcode) {
        OrgDeptEntity orgDept = orgDeptService.findById(orgId, exceDeptcode);
        Integer sectionId = null;
        String storeId = null;

        if (orgDept != null && orgDept.getSectionId() != null) {
            sectionId = orgDept.getSectionId();
            SectionEntity section = sectionService.getById(sectionId);
            if (section != null) {
                storeId = section.getConsumableDeptCode();
            }
        }

        if (StrUtil.isBlank(storeId)) {
            OrgDeptEntity applyDept = orgDeptService.findById(orgId, applyDeptcode);
            if (applyDept != null && applyDept.getSectionId() != null) {
                sectionId = applyDept.getSectionId();
                SectionEntity section = sectionService.getById(sectionId);
                if (section != null) {
                    storeId = section.getConsumableDeptCode();
                }
            }
        }

        return Pair.of(sectionId, storeId);
    }

    private Long getPatientId(Long orgId, Long patientId) {
        MdiPatientVo mdiPatient = remoteAcService.findPatientById(schedService.getHospitalNo(orgId), null, Convert.toStr(patientId));
        if (mdiPatient != null) {
            return mdiPatient.getPatientId();
        } else {
            throw new SaveFailureException("未找到患者信息或患者未与院内绑定关系。");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawRecipeSign(List<Long> recipeIdLs) {
        if (ObjectUtil.isNotEmpty(recipeIdLs)) {
            List<RecipeEntity> recipeLs = recipeService.listByIds(recipeIdLs);
            List<RecipeExtraEntity> recipeExtraLs = recipeExtraService.list(Wrappers.<RecipeExtraEntity>lambdaQuery().in(RecipeExtraEntity::getRecipeId, recipeIdLs));
            List<OrderEntity> orderLs = orderService.list(Wrappers.<OrderEntity>lambdaQuery().in(OrderEntity::getRecipeId, recipeIdLs));
            for (RecipeEntity recipe : recipeLs) {
                if (recipe.getExecStatus().equals(ExecStatus.finish.getValue())) {
                    throw new SaveFailureException("处方【" + recipe.getRxNo() + "】已完成，不能撤销签名。");
                }
                if (recipe.getPaidStatus().equals(PaidStatus.paid.getValue())) {
                    throw new SaveFailureException("处方【" + recipe.getRxNo() + "】已支付，不能撤销签名。");
                }
                if (recipe.getPaidStatus().equals(PaidStatus.inPayment.getValue())) {
                    throw new SaveFailureException("处方【" + recipe.getRxNo() + "】正在支付中，不能撤销签名。");
                }
                if (recipe.getExecStatus().equals(ExecStatus.cancel.getValue())) {
                    throw new SaveFailureException("处方【" + recipe.getRxNo() + "】已取消，不能撤销签名。");
                }
                RecipeExtraEntity recipeExtra = recipeExtraLs.stream().filter(r -> r.getRecipeId().equals(recipe.getRecipeId())).findFirst().orElse(new RecipeExtraEntity());
                if (recipeExtra.getTimeSigned() == null) {
                    throw new SaveFailureException("处方【" + recipe.getRxNo() + "】未签名，不能撤销签名。");
                }
                if (!recipe.getExecStatus().equals(ExecStatus.waiting.getValue()) && !recipe.getExecStatus().equals(ExecStatus.rejected.getValue())
                        && recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.flow.getValue())
                        && (recipe.getRecipeTypeId().equals(RecipeType.xy.getValue()) || recipe.getRecipeTypeId().equals(RecipeType.zy.getValue()))) {
                    remoteRscService.recipe_cancel(recipe.getOrgId(), Convert.toStr(recipe.getRecipeId()));
                } else {
                    cancelRecipeIfUnpaid(recipe.getUserId(), recipe);
                }
                // 如果是申请单，则先撤销处置单
                orderLs.stream().filter(o -> o.getRecipeId().equals(recipe.getRecipeId())).findFirst().ifPresent(order -> moeOrderService.stopOrderExec(order.getOrderId()));
                // 修改处方状态
                LambdaUpdateWrapper<RecipeEntity> wrapper = Wrappers.lambdaUpdate(RecipeEntity.class);
                wrapper.eq(RecipeEntity::getRecipeId, recipe.getRecipeId());
                wrapper.set(RecipeEntity::getExecStatus, ExecStatus.waiting.getValue());
                wrapper.set(RecipeEntity::getPaidStatus, PaidStatus.waiting.getValue());
                recipeService.update(wrapper);
                // 修改处方其他信息
                LambdaUpdateWrapper<RecipeExtraEntity> recipeExtraWrapper = Wrappers.lambdaUpdate(RecipeExtraEntity.class);
                recipeExtraWrapper.eq(RecipeExtraEntity::getRecipeId, recipe.getRecipeId());
                recipeExtraWrapper.set(RecipeExtraEntity::getTimeSigned, null);
                recipeExtraWrapper.set(RecipeExtraEntity::getDigitalSignature, null);
                recipeExtraWrapper.set(RecipeExtraEntity::getSignatureType, null);
                recipeExtraWrapper.set(RecipeExtraEntity::getCertId, null);
                recipeExtraService.update(recipeExtraWrapper);
            }
        }
    }

    @Override
    @Transactional
    public void updateRecipeStatus(Integer paidStatus, Integer execStatus, List<Long> recipeIdLs) {
        if (ObjectUtil.isNotEmpty(recipeIdLs) && (paidStatus != null || execStatus != null)) {
            // 如果是已支付状态
            if (paidStatus != null && paidStatus.equals(PaidStatus.paid.getValue())) {
                List<RecipeEntity> recipeLs = recipeService.listByIds(recipeIdLs);
                List<RecipePaidVo> recipePaidLs = recipeLs.stream().map(r -> {
                    RecipePaidVo recipePaid = new RecipePaidVo();
                    recipePaid.setRecipeId(r.getRecipeId());
                    recipePaid.setBseqid(r.getBseqid());
                    return recipePaid;
                }).collect(Collectors.toList());
                recipeService.paidRecipe(recipePaidLs);
            } else {
                LambdaUpdateWrapper<RecipeEntity> wrapper = Wrappers.lambdaUpdate(RecipeEntity.class);
                wrapper.in(RecipeEntity::getRecipeId, recipeIdLs);
                wrapper.set(paidStatus != null, RecipeEntity::getPaidStatus, paidStatus);
                wrapper.set(execStatus != null, RecipeEntity::getExecStatus, execStatus);
                recipeService.update(wrapper);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkedRecipe(Long recipeId, Integer inspectionStatus, String inspectionNotes) {
        if (recipeId != null) {
            RecipeEntity entity = recipeService.getById(recipeId);
            if (entity == null) {
                throw new SaveFailureException("处方不存在");
            }
            if (inspectionStatus != null && !inspectionStatus.equals(2) && !inspectionStatus.equals(3)) {
                throw new SaveFailureException("审方状态不是正确的值");
            }
            LambdaUpdateWrapper<RecipeEntity> recipeWrapper = Wrappers.lambdaUpdate(RecipeEntity.class);
            recipeWrapper.eq(RecipeEntity::getRecipeId, recipeId);
            if (inspectionStatus != null && inspectionStatus.equals(3)) {
                recipeWrapper.set(RecipeEntity::getExecStatus, ExecStatus.rejected.getValue());
                recipeWrapper.set(RecipeEntity::getInspectionFlag, 2);
            } else {
                recipeWrapper.set(RecipeEntity::getExecStatus, ExecStatus.processing.getValue());
                recipeWrapper.set(RecipeEntity::getInspectionFlag, 1);
            }
            recipeService.update(recipeWrapper);
            // 修改处方说明备注
            if (StrUtil.isNotBlank(inspectionNotes)) {
                RecipeExtraEntity recipeExtra = recipeExtraService.getById(recipeId);
                if (recipeExtra == null) {
                    recipeExtraService.save(RecipeExtraEntity.builder().recipeId(recipeId).inspectionNotes(inspectionNotes).build());
                } else {
                    recipeExtraService.update(Wrappers.lambdaUpdate(RecipeExtraEntity.class).eq(RecipeExtraEntity::getRecipeId, recipeId)
                            .set(RecipeExtraEntity::getInspectionNotes, inspectionNotes));
                }
            }
            if (inspectionStatus != null && inspectionStatus.equals(3)) {
                // TODO 如果已付费则退费
                if (entity.getBseqid() != null) {
                    if (billService.getPaidStatus(entity.getBseqid()) != PaidStatus.paid.getValue()) {
                        List<Long> unpaidBseqids = Collections.singletonList(entity.getBseqid()); // Use singletonList only if bseqid is not null
                        LambdaUpdateWrapper<BillEntity> billWrapper = Wrappers.lambdaUpdate(BillEntity.class);
                        billWrapper.in(BillEntity::getBseqid, unpaidBseqids);
                        billWrapper.set(BillEntity::getPaidStatus, PaidStatus.cancel.getValue());
                        billService.update(billWrapper);
                        billUnpaidService.removeBatchByIds(unpaidBseqids);
                    } else {
                        createRedBill(entity.getRecipeId());
                    }
                }
                hsdEventPublisher.recipePublisher(RecipeEventType.RSC_REJECT.getCode(), entity.getOrgId(), recipeId, entity.getRxNo());
            } else if (inspectionStatus != null && inspectionStatus.equals(2)) {
//                try {
//                    saveEhrVisit(entity.getVisitId(), entity.getRecipeId());
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    log.error("推送EHR失败", e);
//                }
                hsdEventPublisher.recipePublisher(RecipeEventType.RSC_PASS.getCode(), entity.getOrgId(), recipeId, entity.getRxNo());
            }
        }
    }

    private void saveEhrVisit(Long visitId, Long recipeId) {
        VisitDto visit = visitService.findById(visitId, false);
        // 诊疗信息
        EhrVisitData ehrVisit = EhrVisitData.builder()
                .pid(visit.getPatientId())
                .admissionTime(visit.getTimeAdmission())
                .ageOfYears(visit.getAgeOfYears())
                .ageOfDays(Convert.toInt(visit.getAgeOfDays()))
                .genderId(visit.getGenderId())
                .clinicianCode(visit.getClinicianNo())
                .clinicianName(visit.getClinicianName())
                .weightKg(visit.getWeightKg())
                .heightCm(visit.getHeightCm() != null ? BigDecimal.valueOf(visit.getHeightCm()) : null)
                .dischargeTime(visit.getTimeDischarged())
                .status(visit.getVisitStatus().equals(VisitStatus.outHospital.getValue()) ? 3 : 2)
                .visitTypeId(visit.getOcFlag() != null && visit.getOcFlag() == 1 ? VisitType.visit.getValue() : VisitType.apt.getValue())
                .orgId(visit.getOrgId())
                .deptCode(visit.getDeptCode())
                .orgDeptCode(visit.getDeptCode())
                .deptName(visit.getDeptName())
                .srcId(visit.getVisitId() + "")
                .build();
        // 诊疗其他信息
        EhrVisitExtraData ehrVisitExtra = EhrVisitExtraData.builder()
                .complainedAs(visit.getComplainedAs())
                .accompanyBy(visit.getCompanionName())
                .contactTel(visit.getContactTel())
                .livingAddr(visit.getLivingAddr())
                .treatAbstract(visit.getTreatAbstract())
                .orgCode(visit.getOrgCode())
                .orgName(visit.getOrgName())
                .build();
        // 诊疗诊断记录
        List<EhrVisitDiagData> ehrVisitDiagLs = new ArrayList<>();
        for (VisitDiagDto diagEntity : visit.getVisitDiagLs()) {
            EhrVisitDiagData ehrVisitDiag = EhrVisitDiagData.builder()
                    .diagNo(diagEntity.getDiagNo())
                    .diagCode(diagEntity.getDiagCode())
                    .diagName(diagEntity.getDiagName())
                    .diagTypeId(diagEntity.getDiagTypeId().equals(DiagType.tcmMainDiag.getValue()) ? 3 : diagEntity.getDiagTypeId().equals(DiagType.tcmOtherDiag.getValue()) ? 4 : 1)
                    .diagStatus(diagEntity.getDiagStatus())
                    .diagTime(diagEntity.getTimeDiagnosed())
                    .removeTime(diagEntity.getTimeDenied())
                    .build();
            ehrVisitDiagLs.add(ehrVisitDiag);
        }
        // 保存服务记录处方
        RecipeDto recipe = recipeService.findById(recipeId);
        EhrVisitRecipeData visitRecipe = EhrVisitRecipeData.builder()
                .recipeNo(recipe.getRxNo())
                .clinicianCode(visit.getClinicianNo())
                .clinicianName(visit.getClinicianName())
                .recipeType(recipe.getRecipeTypeId())
                .diagCode(recipe.getDiseaseDiagCode())
                .diagName(recipe.getDiseaseDiagName())
                .build();
        List<EhrVisitRecipeData> recipeModelList = new ArrayList<>();
        recipeModelList.add(visitRecipe);
        // 处方明细
        List<EhrOrderEntryData> entryModelList = new ArrayList<>();
        List<RecipeGroupDto> recipeGroupLs = recipe.getRecipeGroupLs();
        if (recipeGroupLs != null && !recipeGroupLs.isEmpty()) {
            for (RecipeGroupDto recipeGroup : recipeGroupLs) {
                List<RecipeDetailDto> recipeDetailLs = recipeGroup.getRecipeDetailLs();
                for (RecipeDetailDto recipeDetail : recipeDetailLs) {
                    EhrOrderEntryData orderEntry = EhrOrderEntryData.builder()
                            .cycleCount(recipeGroup.getPeriodCycles())
                            .mealDoses(recipeDetail.getMealDoses())
                            .doseUnit(recipeDetail.getDoseUnit())
                            .mealCells(recipeDetail.getMealCells())
                            .cellUnit(recipeDetail.getCellUnit())
                            .oeTypeId(1)
                            .routeId(recipeGroup.getRouteId())
                            .freqCode(recipeGroup.getFreqCode())
                            .drops(Convert.toBigDecimal(recipeGroup.getDpm()))
                            .recipeNo(recipe.getRxNo())
                            .recipeGroupno(recipeGroup.getGroupNo())
                            .total(recipeDetail.getTotal())
                            .unit(recipeDetail.getUnit())
                            .unitType(recipeDetail.getUnitType())
                            .clinicianCode(visit.getClinicianNo())
                            .clinicianName(visit.getClinicianName())
                            .oeText(recipeDetail.getArtName() + "(" + recipeDetail.getArtSpec() + ")")
                            .build();
                    entryModelList.add(orderEntry);
                }
            }
        }

        EhrVisitFullData fullModel = new EhrVisitFullData();
        fullModel.setEhrVisitModel(ehrVisit);
        fullModel.setEhrVisitExtraModel(ehrVisitExtra);
        fullModel.setDiagModelList(ehrVisitDiagLs);
        fullModel.setRecipeModelList(recipeModelList);
        fullModel.setEntryModelList(entryModelList);
        remoteEhrService.saveEhrVisit(fullModel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRedBill(Long recipeId) {
        RecipeEntity recipe = recipeService.getById(recipeId);
        List<RecipeBillEntity> recipeBillLs = recipeBillService.list(Wrappers.lambdaQuery(RecipeBillEntity.class).eq(RecipeBillEntity::getRecipeId, recipeId));
        List<Long> bseqidLs = new ArrayList<>();
        if (recipe != null && recipe.getBseqid() != null) {
            bseqidLs.add(recipe.getBseqid());
        }
        if (ObjectUtil.isNotEmpty(recipeBillLs)) {
            recipeBillLs.forEach(recipeBill -> {
                if (recipeBill.getBseqid() != null && !bseqidLs.contains(recipeBill.getBseqid())) {
                    bseqidLs.add(recipeBill.getBseqid());
                }
            });
        }
        // 生成红冲
        if (ObjectUtil.isNotEmpty(bseqidLs)) {
            List<BillEntity> billLs = billService.listByIds(bseqidLs);
            billLs.forEach(bill -> redOrCancelBill(recipeId, bill));
        }
    }

    private void redOrCancelBill(Long recipeId, BillEntity bill) {
        log.info("开始生成红冲单");
        if (bill != null && bill.getPaidStatus().equals(PaidStatus.paid.getValue())) {
            // 判断是已生成了红单
            List<BillEntity> redBillLs = billService.list(Wrappers.lambdaQuery(BillEntity.class).eq(BillEntity::getRelativeBseqid, bill.getBseqid())
                    .ne(BillEntity::getPaidStatus, PaidStatus.cancel.getValue()));
            boolean canAdd = true;
            if (ObjectUtil.isNotEmpty(redBillLs)) {
                BigDecimal redAmt = redBillLs.stream().map(BillEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).abs();
                if (redAmt.compareTo(bill.getAmount()) >= 0) {
                    canAdd = false;
                }
            }
            if (canAdd) {
                List<BillDetailEntity> billDetailLs = billDetailService.list(Wrappers.lambdaQuery(BillDetailEntity.class).eq(BillDetailEntity::getBseqid, bill.getBseqid()));
                BillEntity redBill = new BillEntity();
                BeanUtils.copyProperties(bill, redBill);
                redBill.setBseqid(null);
                redBill.setCashId(null);
                redBill.setRelativeBseqid(bill.getBseqid());
                redBill.setPaidStatus(PaidStatus.toPaid.getValue());
                redBill.setTimeCreated(new Date());
                redBill.setNonMedicalAmt(bill.getNonMedicalAmt() != null ? bill.getNonMedicalAmt().multiply(BigDecimal.valueOf(-1)) : BigDecimal.ZERO);
                redBill.setAmount(bill.getAmount() != null ? bill.getAmount().multiply(BigDecimal.valueOf(-1)) : BigDecimal.ZERO);
                redBill.setDerated(bill.getDerated() != null ? bill.getDerated().multiply(BigDecimal.valueOf(-1)) : BigDecimal.ZERO);
                redBill.setDiscounted(bill.getDiscounted() != null ? bill.getDiscounted().multiply(BigDecimal.valueOf(-1)) : BigDecimal.ZERO);
                redBill.setTotalAmount(bill.getTotalAmount() != null ? bill.getTotalAmount().multiply(BigDecimal.valueOf(-1)) : BigDecimal.ZERO);
                redBill.setBillAbstract(redBill.getBillAbstract() + "红冲单。");
                // 明细
                if (ObjectUtil.isNotEmpty(billDetailLs)) {
                    for (BillDetailEntity billDetail : billDetailLs) {
                        billDetail.setBseqid(null);
                        billDetail.setTotal(billDetail.getTotal() != null ? billDetail.getTotal().multiply(BigDecimal.valueOf(-1)) : null);
                        billDetail.setCellTotal(billDetail.getCellTotal() != null ? billDetail.getCellTotal().multiply(BigDecimal.valueOf(-1)) : null);
                        billDetail.setPackTotal(billDetail.getPackTotal() != null ? billDetail.getPackTotal().multiply(BigDecimal.valueOf(-1)) : null);
//                    billDetail.setCellPrice(billDetail.getCellPrice() != null ? billDetail.getCellPrice().multiply(BigDecimal.valueOf(-1)) : null);
//                    billDetail.setPackPrice(billDetail.getPackPrice() != null ? billDetail.getPackPrice().multiply(BigDecimal.valueOf(-1)) : null);
//                    billDetail.setPrice(billDetail.getPrice() != null ? billDetail.getPrice().multiply(BigDecimal.valueOf(-1)) : null);
                        billDetail.setDerated(billDetail.getDerated() != null ? billDetail.getDerated().multiply(BigDecimal.valueOf(-1)) : null);
                        billDetail.setDiscounted(billDetail.getDiscounted() != null ? billDetail.getDiscounted().multiply(BigDecimal.valueOf(-1)) : null);
                        billDetail.setAmount(billDetail.getAmount() != null ? billDetail.getAmount().multiply(BigDecimal.valueOf(-1)) : null);
                        billDetail.setCost(billDetail.getCost() != null ? billDetail.getCost().multiply(BigDecimal.valueOf(-1)) : null);
                    }
                }
                billService.saveToPaidBill(redBill, billDetailLs);
            }
        } else if (bill != null && bill.getPaidStatus() < PaidStatus.inPayment.getValue()) {
            LambdaUpdateWrapper<BillEntity> billWrapper = Wrappers.lambdaUpdate(BillEntity.class);
            billWrapper.eq(BillEntity::getBseqid, bill.getBseqid());
            billWrapper.in(BillEntity::getPaidStatus, PaidStatus.waiting.getValue(), PaidStatus.toPaid.getValue());
            billWrapper.set(BillEntity::getPaidStatus, PaidStatus.cancel.getValue());
            boolean updateBool = billService.update(billWrapper);
            if (!updateBool) {
                throw new SaveFailureException("划价单作废失败，划价单不是待支付状态。");
            }
            // 处方主索引撤销
            LambdaUpdateWrapper<RecipeIndexEntity> recipeIndexWrapper = Wrappers.lambdaUpdate(RecipeIndexEntity.class);
            recipeIndexWrapper.eq(RecipeIndexEntity::getRecipeId, recipeId);
            recipeIndexWrapper.set(RecipeIndexEntity::getTimeDropped, new Date());
            recipeIndexService.update(recipeIndexWrapper);
            billUnpaidService.removeById(bill.getBseqid());
        } else if (bill != null && bill.getPaidStatus().equals(PaidStatus.inPayment.getValue())) {
            throw new SaveFailureException("正在支付中的处方冲红。");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long userId, Long recipeId) {
        log.debug("作废处方：userId:{} recipeId:{}", userId, recipeId);
        RecipeEntity recipe = recipeService.getById(recipeId);
        if (isRecipeFinished(recipe)) {
            throw new SaveFailureException("处方已完成不能作废。");
        }
        if (userId == null) {
            userId = recipe.getUserId();
        }
        if (!recipe.getExecStatus().equals(ExecStatus.cancel.getValue())) {
            log.debug("开始作废处方：{}", JSONUtil.toJsonStr(recipe));
            // 药房作废
            log.debug("RecipeEntity:{}", recipe);
            if (!recipe.getExecStatus().equals(ExecStatus.waiting.getValue()) && !recipe.getExecStatus().equals(ExecStatus.rejected.getValue())
                    && recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.flow.getValue())
                    && (recipe.getRecipeTypeId().equals(RecipeType.xy.getValue()) || recipe.getRecipeTypeId().equals(RecipeType.zy.getValue()))) {
                RecipeExtraEntity recipeExtra = recipeExtraService.getById(recipeId);
                log.debug("recipeExtra:{}", recipeExtra);
                if (recipeExtra.getTimeSigned() != null) {
                    remoteRscService.recipe_cancel(recipe.getOrgId(), Convert.toStr(recipe.getRecipeId()));
                }
            } else {
                cancelRecipeIfUnpaid(userId, recipe);
            }
            LambdaUpdateWrapper<RecipeEntity> wrapper = Wrappers.lambdaUpdate(RecipeEntity.class);
            wrapper.eq(RecipeEntity::getRecipeId, recipeId);
            wrapper.set(RecipeEntity::getExecStatus, ExecStatus.cancel.getValue());
            wrapper.set(RecipeEntity::getPaidStatus, PaidStatus.cancel.getValue());
            recipeService.update(wrapper);
            // 修改处方其他信息
            LambdaUpdateWrapper<RecipeExtraEntity> recipeExtraWrapper = Wrappers.lambdaUpdate(RecipeExtraEntity.class);
            recipeExtraWrapper.eq(RecipeExtraEntity::getRecipeId, recipeId);
            recipeExtraWrapper.set(RecipeExtraEntity::getTimeSigned, null);
            recipeExtraWrapper.set(RecipeExtraEntity::getDigitalSignature, null);
            recipeExtraWrapper.set(RecipeExtraEntity::getSignatureType, null);
            recipeExtraWrapper.set(RecipeExtraEntity::getCertId, null);
            recipeExtraService.update(recipeExtraWrapper);
            // 修改诊疗耗材处方ID
            if (recipe.getRecipeTypeId().equals(RecipeType.zl.getValue())) {
                LambdaUpdateWrapper<VisitEntity> updateWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
                updateWrapper.eq(VisitEntity::getVisitId, recipe.getVisitId());
                updateWrapper.eq(VisitEntity::getCureRecipeId, recipe.getRecipeId());
                updateWrapper.set(VisitEntity::getCureRecipeId, null);
                visitService.update(updateWrapper);
            }
        }
        // 如果不是治疗处方，查询处方是否有对应的治疗处方单，有就直接作废则删除对应的
        if (!recipe.getRecipeTypeId().equals(RecipeType.zl.getValue()) && recipe.getCureRecipeId() != null) {
            cancel(userId, recipe.getCureRecipeId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCancelRecipe(Long userId, List<Long> recipeIdLs)  {
        if (ObjectUtil.isNotEmpty(recipeIdLs)) {
            for (Long recipeId : recipeIdLs) {
                cancel(userId, recipeId);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long reeditRecipe(Long orgId, Long userId, Long recipeId) {
        // 作废
        cancel(userId, recipeId);

        return recipeService.saveRecipeByRecipeId(orgId, userId, recipeId, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRecipeSelfPaidPct(Long recipeId, Integer lineNo, Long artId, Integer limitedFlag, BigDecimal selfpaidPct) {
        if (recipeId != null) {
            RecipeEntity recipe = recipeService.getById(recipeId);
            if (isRecipeFinished(recipe)) {
                throw new SaveFailureException("处方已完成不能修改。");
            }
            if (recipe.getExecStatus().equals(ExecStatus.cancel.getValue()) || recipe.getExecStatus().equals(ExecStatus.rejected.getValue())) {
                throw new SaveFailureException("处方在" + ExecStatus.getName(recipe.getExecStatus()) + "状态，不能修改。");
            }
            if (!recipe.getRecipeTypeId().equals(RecipeType.zy.getValue()) && !recipe.getRecipeTypeId().equals(RecipeType.xy.getValue())) {
                throw new SaveFailureException("西药处方或中药处方不能修改。");
            }
            long paidStatus = billService.getPaidStatus(recipe.getBseqid());
            if (paidStatus == PaidStatus.paid.getValue()) {
                throw new SaveFailureException("处方已支付，不能修改。");
            }
            if (paidStatus == PaidStatus.inPayment.getValue()) {
                throw new SaveFailureException("处方在支付中，不能修改。");
            }
            if (recipe.getBseqid() != null && paidStatus < PaidStatus.inPayment.getValue()) {
                // 修改划价单的自付比例
                LambdaUpdateWrapper<BillDetailEntity> billWrapper = Wrappers.lambdaUpdate(BillDetailEntity.class);
                billWrapper.eq(BillDetailEntity::getBseqid, recipe.getBseqid());
                billWrapper.eq(BillDetailEntity::getArtId, artId);
                billWrapper.set(BillDetailEntity::getLimitedFlag, limitedFlag);
                billWrapper.set(BillDetailEntity::getSelfpaidPct, selfpaidPct);
                billDetailService.update(billWrapper);
                // 修改处方明细的自付比例
                LambdaUpdateWrapper<RecipeDetailEntity> wrapper = Wrappers.lambdaUpdate(RecipeDetailEntity.class);
                wrapper.eq(RecipeDetailEntity::getRecipeId, recipeId);
                wrapper.eq(RecipeDetailEntity::getLineNo, lineNo);
                wrapper.eq(RecipeDetailEntity::getArtId, artId);
                wrapper.set(RecipeDetailEntity::getSelfpaidPct, selfpaidPct);
                wrapper.set(RecipeDetailEntity::getLimitedFlag, limitedFlag);
                recipeDetailService.update(wrapper);
            }
        }
    }

    private boolean isRecipeFinished(RecipeEntity recipe) {
        return recipe.getExecStatus().equals(ExecStatus.finish.getValue());
    }

    private void cancelRecipeIfUnpaid(Long userId, RecipeEntity recipe) {
        // Check if the recipe has been paid
        log.info("已生成划价单的处方生成红冲单: {}", JSONUtil.toJsonStr(recipe));
        // 治疗处方判断是否库存管理
        boolean stockReq = false;
        if (recipe.getRecipeTypeId().equals(RecipeType.zl.getValue())
                && recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.self.getValue())) {
            List<RecipeDetailDto> recipeDetailLs = recipeDetailService.findLsByRecipeIdLs(ListUtil.of(recipe.getRecipeId()));
            List<Long> artIdLs = new ArrayList<>();
            recipeDetailLs.forEach(recipeDetail -> {
                if (recipeDetail.getIsPackage() != null && recipeDetail.getIsPackage().equals(1)) {
                    artIdLs.addAll(recipeDetail.getPackageDetailLs().stream().map(PackageDetailDto::getArtId).collect(Collectors.toList()));
                } else {
                    artIdLs.add(recipeDetail.getArtId());
                }
            });
            if (ObjectUtil.isNotEmpty(artIdLs)) {
                List<ArticleEntity> articleLs = articleService.listByIds(artIdLs);
                List<Long> stockLs = articleLs.stream().filter(article -> article.getStockReq() != null && article.getStockReq().equals(1)).map(ArticleEntity::getArtId).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(stockLs)) {
                    stockReq = true;
                }
            }
        }

        if (recipe.getBseqid() != null) {
            // If the recipe has not been paid, cancel it
            long paidStatus = billService.getPaidStatus(recipe.getBseqid());
            OrgSettingEntity orgSetting = orgSettingService.getById(recipe.getOrgId());
            if (paidStatus == PaidStatus.paid.getValue()
                    && (recipe.getRecipeTypeId().equals(RecipeType.xy.getValue()) || recipe.getRecipeTypeId().equals(RecipeType.zy.getValue())
                    || ( recipe.getRecipeTypeId().equals(RecipeType.zl.getValue()) && stockReq))
                    && orgSetting != null && orgSetting.getCashierBackoutEnabled() != null && orgSetting.getCashierBackoutEnabled() == 1) {
                throw new SaveFailureException("处方已支付，不能作废，只能去发药药房撤销或收费处撤销。");
            }
            if (paidStatus == PaidStatus.inPayment.getValue()) {
                throw new SaveFailureException("处方在支付中状态不能作废，若要作废，需要在收费处把处方账单撤销。");
            }
            if (paidStatus < PaidStatus.inPayment.getValue()) {
                log.debug("直接把划价单取消：recipeId:{} bseqId:{}", recipe.getRecipeId(), recipe.getBseqid());
                List<Long> unpaidBseqids = Collections.singletonList(recipe.getBseqid()); // Use singletonList only if bseqid is not null
                LambdaUpdateWrapper<BillEntity> billWrapper = Wrappers.lambdaUpdate(BillEntity.class);
                billWrapper.in(BillEntity::getBseqid, unpaidBseqids);
                billWrapper.in(BillEntity::getPaidStatus, PaidStatus.waiting.getValue(), PaidStatus.toPaid.getValue());
                billWrapper.set(BillEntity::getPaidStatus, PaidStatus.cancel.getValue());
                boolean update = billService.update(billWrapper);
                if (!update) {
                    throw new SaveFailureException("划价单作废失败，划价单状态不能是" + PaidStatus.waiting.getName() + "或" + PaidStatus.toPaid.getName() + "状态，不能作废，若要作废，需要在收费处把处方账单撤销。");
                }
                billUnpaidService.removeBatchByIds(unpaidBseqids);
            } else if (paidStatus == PaidStatus.paid.getValue()) {
                log.debug("划价单生成红冲单：recipeId:{} bseqId:{}", recipe.getRecipeId(), recipe.getBseqid());
                createRedBill(recipe.getRecipeId());
                // 取消处置
                if (recipe.getRecipeTypeId().equals(RecipeType.xy.getValue())) {
                    treatmentService.refundTreatmentByRecipeId(recipe.getRecipeId());
                }
            }
        }
        // 调用远程服务取消处方预占 如果是治疗单，耗材要调仓库释放库存
        log.debug("释放库存：recipeId:{} recipeTypeId:{}", recipe.getRecipeId(), recipe.getRecipeTypeId());
        if (recipe.getRecipeTypeId().equals(RecipeType.xy.getValue()) || recipe.getRecipeTypeId().equals(RecipeType.zy.getValue())) {
            try {
                if (recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.spd.getValue())) {
                    remoteSpdService.cancelRecipe(recipe.getOrgId(), recipe.getRxNo());
                } else if (recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.self.getValue())) {
                    remoteClinicsWmService.recipeCancel(recipe.getRecipeId());
                }
            } catch (Exception e) {
                log.error("调仓库释放库存失败：recipeId:{}", recipe.getRecipeId(), e);
            }
        } else if (recipe.getRecipeTypeId().equals(RecipeType.zl.getValue())
                && recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.self.getValue()) && stockReq) {
            OrgDeptEntity orgDept = orgDeptService.findById(recipe.getOrgId(), recipe.getExceDeptcode());
            if (orgDept != null && Convert.toInt(orgDept.getWmMode(), DeptWmMode.WAREHOUSE_MANAGEMENT.getCode()).equals(DeptWmMode.DEPT_SECTION.getCode())) {
                List<OrderEntity> orderLs = orderService.list(Wrappers.lambdaQuery(OrderEntity.class).eq(OrderEntity::getRecipeId, recipe.getRecipeId()));
                if (ObjectUtil.isNotEmpty(orderLs)) {
                    try {
                        moeOrderService.cancelTherapyOrderMaterial(userId, orderLs.get(0).getOrderId());
                    } catch (Exception e) {
                        log.error("调仓库释放库存失败：recipeId:{}", recipe.getRecipeId(), e);
                        throw new SaveFailureException("调病区仓库释放库存失败：recipeId:" + recipe.getRecipeId() + ";失败原因：" + e.getMessage());
                    }
                }
            } else {
                try {
                    remoteClinicsWmService.recipeCancel(recipe.getRecipeId());
                } catch (Exception e) {
                    log.error("调仓库释放库存失败：recipeId:{}", recipe.getRecipeId(), e);
                }
            }
        }
    }

    @Override
    public List<SectionStoreModel> findDeptStoreLs(Integer sectionId) {
//        RecipeEntity entity = recipeService.getById(recipeId);
//        if (entity == null) {
//            throw new SaveFailureException("未找到处方信息。");
//        }
        return remoteSpdService.getSectionStore(sectionId);
    }

    @Override
    public List<WmStoreModel> findWmStoreLs(Long orgId) {
        return remoteClinicsWmService.recipeDispensingStoreList(orgId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRecipeTemplate(List<Long> recipeIdLs) {
        if (recipeIdLs != null && !recipeIdLs.isEmpty()) {
            recipeTemplateService.delete(recipeIdLs);
            recipeService.update(Wrappers.lambdaUpdate(RecipeEntity.class).set(RecipeEntity::getIsTemplate, 0).in(RecipeEntity::getRecipeId, recipeIdLs));
        }
    }

    @Override
    public String payCodeUrl(List<Long> recipeIdLs) {
        if (!recipeIdLs.isEmpty()) {
            List<RecipeEntity> list = recipeService.list(Wrappers.lambdaQuery(RecipeEntity.class).in(RecipeEntity::getRecipeId, recipeIdLs)
                    .isNotNull(RecipeEntity::getBseqid));
            if (!list.isEmpty()) {
                List<Long> bseqidLs = list.stream().map(RecipeEntity::getBseqid).distinct().collect(Collectors.toList());

            }
        }
        return null;
    }

    @Override
    public JSONArray findSelfPaidLs(Long visitId, Long artId, BigDecimal price) {
        VisitEntity visit = visitService.getById(visitId);
        if (visit != null && visit.getInsuranceTypeId() != null) {
            OrgEntity org = orgService.getById(visit.getOrgId());
            InsuranceTypeEntity insuranceType = insuranceTypeService.getById(visit.getInsuranceTypeId());
            if (artId != null) {
                ArticleEntity article = articleService.getById(artId);
                if (article != null && article.getMiUsageLimited() != null && article.getMiUsageLimited() == 1) {
                    return remoteMcispService.findSelfPaidLs(artId, org.getLevelId(), price, insuranceType.getBusinessCode(), insuranceType.getInsuredCode(),
                            null, null, null);
                }
            } else {
                return remoteMcispService.findSelfPaidLs(artId, org.getLevelId(), price, insuranceType.getBusinessCode(), insuranceType.getInsuredCode(),
                        null, null, null);
            }
        }
        return null;
    }

    @Override
    public JSONArray findSelfPaidLsByArtIdLs(Long visitId, List<Long> artIdLs) {
        if (ObjectUtil.isNotEmpty(artIdLs)) {
            VisitEntity visit = visitService.getById(visitId);
            if (visit != null && visit.getInsuranceTypeId() != null) {
                OrgEntity org = orgService.getById(visit.getOrgId());
                InsuranceTypeEntity insuranceType = insuranceTypeService.getById(visit.getInsuranceTypeId());
                List<ArticleEntity> articleLs = articleService.listByIds(artIdLs);
                List<Long> packageArtId = articleLs.stream().filter(p -> p.getIsPackage() != null && p.getIsPackage() == 1).map(ArticleEntity::getArtId).collect(Collectors.toList());
                List<PackageDetailEntity> packageDetailLs = new ArrayList<>();
                List<ArticleEntity> packageArtLs = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(packageArtId)) {
                    packageDetailLs = packageDetailService.list(Wrappers.lambdaQuery(PackageDetailEntity.class).in(PackageDetailEntity::getPackageId, packageArtId));
                    if (ObjectUtil.isNotEmpty(packageDetailLs)) {
                        List<Long> detailArtIdLs = packageDetailLs.stream().map(PackageDetailEntity::getArtId).distinct().collect(Collectors.toList());
                        packageArtLs = articleService.listByIds(detailArtIdLs);
                    }
                }
                JSONArray array = new JSONArray();
                for (Long artId : artIdLs) {
                    ArticleEntity article = articleLs.stream().filter(a -> a.getArtId().equals(artId)).findFirst().orElse(new ArticleEntity());
                    JSONObject json = new JSONObject();
                    json.set("artId", artId);
                    if (article.getIsPackage() != null && article.getIsPackage() == 1) {
                        json.set("isPackage", 1);
                        List<PackageDetailEntity> detailLs = packageDetailLs.stream().filter(p -> p.getPackageId().equals(artId)).collect(Collectors.toList());
                        JSONArray detailArray = new JSONArray();
                        if (ObjectUtil.isNotEmpty(detailLs)) {
                            for (PackageDetailEntity detail : detailLs) {
                                ArticleEntity detailArt = packageArtLs.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(new ArticleEntity());
                                if (detailArt.getMiUsageLimited() != null && detailArt.getMiUsageLimited() == 1) {
                                    JSONObject detailObj = new JSONObject();
                                    detailObj.set("artId", detail.getArtId());
                                    detailObj.set("artName", detailArt.getArtName());
                                    detailObj.set("selfPaidLs", remoteMcispService.findSelfPaidLs(detail.getArtId(), org.getLevelId(), null, insuranceType.getBusinessCode(),
                                            insuranceType.getInsuredCode(), null, null, null));
                                    detailArray.add(detailObj);
                                }
                            }
                        }
                        json.set("detailLs", detailArray);
                    } else if (article.getMiUsageLimited() != null && article.getMiUsageLimited() == 1) {
                        json.set("selfPaidLs", remoteMcispService.findSelfPaidLs(artId, org.getLevelId(), null, insuranceType.getBusinessCode(),
                                insuranceType.getInsuredCode(), null, null, null));
                    }
                    array.add(json);
                }
                return array;
            }
        }
        return null;
    }

    @Override
    public List<RecipeSignVo> findConsumableSelfPaidLs(Long visitId, Integer sectionId, List<RecipeSignVo> recipeLs) {
        if (ObjectUtil.isNotEmpty(recipeLs)) {
            List<Integer> routeIdLs = recipeLs.stream().map(RecipeSignVo::getRouteId).distinct().collect(Collectors.toList());
            List<SectionRouteConsumableEntity> sectionRouteConsumableLs = sectionRouteConsumableService.list(new LambdaQueryWrapper<SectionRouteConsumableEntity>()
                    .eq(SectionRouteConsumableEntity::getSectionId, sectionId).in(SectionRouteConsumableEntity::getRouteId, routeIdLs));
            if (ObjectUtil.isNotEmpty(sectionRouteConsumableLs)) {
                List<Long> artIdLs = sectionRouteConsumableLs.stream().map(SectionRouteConsumableEntity::getArtId).distinct().collect(Collectors.toList());
                List<ArticleEntity> articleLs = articleService.listByIds(artIdLs);
                List<SectionArtEntity> sectionArtLs = sectionArtService.list(new LambdaQueryWrapper<SectionArtEntity>().eq(SectionArtEntity::getSectionId, sectionId).in(SectionArtEntity::getArtId, artIdLs));
                for (SectionRouteConsumableEntity sectionRouteConsumable : sectionRouteConsumableLs) {
                    SectionArtEntity sectionArt = sectionArtLs.stream().filter(a -> a.getArtId().equals(sectionRouteConsumable.getArtId())).findFirst().orElse(null);
                    if (sectionArt != null) {
                        ArticleEntity article = articleLs.stream().filter(a -> a.getArtId().equals(sectionRouteConsumable.getArtId())).findFirst().orElse(new ArticleEntity());
                        BigDecimal price = sectionRouteConsumable.getUnitType() == null || sectionRouteConsumable.getUnitType().equals(UnitType.Pack_Unit.getValue())
                                ? sectionArt.getLastPackPrice() : sectionArt.getLastCellPrice();
                        JSONArray selfPaidLs = new JSONArray();
                        if (article.getMiUsageLimited() != null && article.getMiUsageLimited() == 1) {
                            selfPaidLs = findSelfPaidLs(visitId, null, price);
                        }
                        List<RecipeSignVo> list = recipeLs.stream().filter(r -> r.getRouteId().equals(sectionRouteConsumable.getRouteId())).collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(list)) {
                            for (RecipeSignVo recipe : list) {
                                List<SectionRouteConsumableVo> consumableLs = recipe.getSectionRouteConsumableLs();
                                if (consumableLs == null) {
                                    consumableLs = new ArrayList<>();
                                }
                                SectionRouteConsumableVo consumable = new SectionRouteConsumableVo();
                                consumable.setSectionId(sectionId);
                                consumable.setRouteId(sectionRouteConsumable.getRouteId());
                                consumable.setArtId(sectionRouteConsumable.getArtId());
                                consumable.setArtCode(article.getArtCode());
                                consumable.setArtName(article.getArtName());
                                consumable.setArtSpec(article.getArtSpec());
                                consumable.setPrice(price);
                                consumable.setSelfPaidLs(selfPaidLs);
                                consumableLs.add(consumable);
                                recipe.setSectionRouteConsumableLs(consumableLs);
                            }
                        }
                    }
                }
            }
        }
        return recipeLs;
    }

    @Override
    public List<WmBillDetailModel> findWmArtPriceLs(Long orgId, String storeId, Integer times, Boolean isCheckInventory, List<ArtDetailVo> artDetailLs) {
        if (StrUtil.isNotBlank(storeId) && ObjectUtil.isNotEmpty(artDetailLs)) {
            WmReqModel wmReq = new WmReqModel();
            wmReq.setOrgId(orgId);
            wmReq.setDeptCode(storeId);
            wmReq.setTimes(times != null ? times : 1);
            wmReq.setIsCheckInventory(Convert.toBool(isCheckInventory, false));
            List<WmReqDetailModel> wmReqDetailLs = new ArrayList<>();
            for (ArtDetailVo detail : artDetailLs) {
                WmReqDetailModel wmReqDetail = new WmReqDetailModel();
                wmReqDetail.setArtId(detail.getArtId());
                if (Convert.toInt(detail.getUnitType(), UnitType.Pack_Unit.getValue()) == UnitType.Pack_Unit.getValue()) { // 包装
                    // 如果有小数这里做了向上取整
                    BigDecimal total = Convert.toBigDecimal(detail.getTotal(), BigDecimal.ONE).setScale(0, RoundingMode.CEILING);
                    wmReqDetail.setTotalPacks(total.intValue());
                    wmReqDetail.setTotalCells(BigDecimal.ZERO);
                    // 有小数
                    if (Convert.toBigDecimal(detail.getTotal(), BigDecimal.ONE).remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) != 0) {
                        ArticleEntity article = articleService.getById(detail.getArtId());
                        BigDecimal packCells = Convert.toBigDecimal(article.getPackCells(), BigDecimal.ONE);
                        if (NumberUtil.isGreater(packCells, BigDecimal.ONE)) {
                            // 包装单位有小数，且包装制剂数大于1换算成制剂数量做申请
                            BigDecimal totalCells = detail.getTotal().multiply(packCells).setScale(0, RoundingMode.CEILING);
                            wmReqDetail.setTotalPacks(0);
                            wmReqDetail.setTotalCells(totalCells);
                        }
                    }
                } else {
                    wmReqDetail.setTotalPacks(0);
                    wmReqDetail.setTotalCells(detail.getTotal());
                }
                wmReqDetailLs.add(wmReqDetail);
            }
            wmReq.setDetails(wmReqDetailLs);
            return remoteClinicsWmService.queryInventoryAndPrice(wmReq);
        }
        return Collections.emptyList();
    }
}
