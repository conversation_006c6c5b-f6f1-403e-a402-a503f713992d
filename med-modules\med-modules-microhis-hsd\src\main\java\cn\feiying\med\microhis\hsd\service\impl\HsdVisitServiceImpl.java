package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.mysql.AESEncryptHandler;
import cn.feiying.med.common.utils.DateUtil;
import cn.feiying.med.common.utils.ValidatorUtil;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.dto.DeptPostpaidItemDto;
import cn.feiying.med.hip.mdi.dto.PackagePriceDto;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.hip.mdi.vo.PackageDetailVo;
import cn.feiying.med.hip.mpi.entity.*;
import cn.feiying.med.hip.mpi.service.*;
import cn.feiying.med.his.moe.entity.*;
import cn.feiying.med.his.moe.service.*;
import cn.feiying.med.microhis.bcs.entity.*;
import cn.feiying.med.microhis.bcs.service.*;
import cn.feiying.med.microhis.hsd.dto.*;
import cn.feiying.med.microhis.hsd.emr.service.IKZhuyBlZidnrService;
import cn.feiying.med.microhis.hsd.entity.*;
import cn.feiying.med.microhis.hsd.service.*;
import cn.feiying.med.microhis.hsd.vo.*;
import cn.feiying.med.saas.api.service.RemoteAcService;
import cn.feiying.med.saas.api.service.RemoteEhrService;
import cn.feiying.med.saas.api.vo.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HsdVisitServiceImpl implements HsdVisitService {

    private static final String DATE_FORMAT_YMD_INT = "yyyyMMdd";

    @Resource
    private VisitService visitService;
    @Resource
    private VisitExtraService visitExtraService;
    @Resource
    private OutpatientService outpatientService;
    @Resource
    private VisitDiagService visitDiagService;
    @Resource
    private VisitStateService visitStateService;
    @Resource
    private VisitPendingService visitPendingService;
    @Resource
    private OpcDiagService opcDiagService;
    @Resource
    private RegService regService;
    @Resource
    private RegPendingService regPendingService;
    @Resource
    private RegAcctService regAcctService;
    @Resource
    private PatientStateService patientStateService;
    @Resource
    private PatientVisitStatService patientVisitStatService;
    @Resource
    private MedicalTypeService medicalTypeService;
    @Resource
    private BillService billService;
    @Resource
    private RemoteEhrService remoteEhrService;
    @Lazy
    @Resource
    private HsdRegService hsdRegService;
    @Resource
    private VisitAllergyService visitAllergyService;
    @Resource
    private VisitIllnessService visitIllnessService;
    @Resource
    private PatientService patientService;
    @Resource
    private ActiveClinicianService activeClinicianService;
    @Resource
    private OrderEntryService orderEntryService;
    @Resource
    private OeStateService oeStateService;
    @Resource
    private OeExecService oeExecService;
    @Resource
    private OrgDeptService orgDeptService;
    @Resource
    private UserCodeService userCodeService;
    @Resource
    private SkintestService skintestService;
    @Resource
    private HsdPatientService hsdPatientService;
    @Resource
    private RemoteAcService remoteAcService;
    @Resource
    private DiseaseService diseaseService;
    @Resource
    private PackageDetailService packageDetailService;
    @Resource
    private ArticleService articleService;
    @Resource
    private OrgNonmedicalArtService orgNonmedicalArtService;
    @Resource
    private DeptPostpaidItemService deptPostpaidItemService;
    @Resource
    private MpiPatientMapService mpiPatientMapService;
    @Resource
    private MpiPatientnoService mpiPatientnoService;
    @Resource
    private CommonService commonService;
    @Resource
    private IdService idService;
    @Resource
    private CdaTemplateService cdaTemplateService;
    @Resource
    private OrgSettingService orgSettingService;
    @Resource
    private HsdBaseService hsdBaseService;
    @Resource
    private PersonTypeService personTypeService;
    @Resource
    private OrderService orderService;
    @Resource
    private MoeOrderService moeOrderService;
    @Resource
    private TreatmentService treatmentService;
    @Resource
    private CdaService cdaService;
    @Resource
    private IKZhuyBlZidnrService kZhuyBlZidnrService;
    @Resource
    private IprRecordService iprRecordService;

    @Override
    public VisitDto findVisitById(Long visitId, boolean isDesensitized) {
        VisitDto visit = visitService.findById(visitId, isDesensitized);
        if (visit != null && visit.getOcFlag() != null && visit.getOcFlag() == 1) {
            JSONArray jsonArray = remoteAcService.findLsByVisitIdLs(Collections.singletonList(visitId));
            if (jsonArray != null && !jsonArray.isEmpty()) {
                JSONObject svcObj = jsonArray.getJSONObject(0);
                visit.setSvcId(svcObj.getLong("svcId"));
                visit.setAcPatientId(svcObj.getLong("pid"));
            }
        }
        return visit;
    }

    @Override
    public List<OrderEntryDto> findOrderEntryLs(Long orgId, Long visitId, Integer oeTypeId) {
        List<OrderEntryDto> list = orderEntryService.findLsById(orgId, visitId, oeTypeId);
        if (!list.isEmpty()) {
            Map<Long, List<OrderEntryDto>> map = list.stream().collect(Collectors.groupingBy(OrderEntryDto::getVisitId));
            map.forEach((key, oeLs) -> setOeState(orgId, key, oeLs));
        }
        return list;
    }

    private void setOeState(Long orgId, Long visitId, List<OrderEntryDto> oeLs) {
        if (ObjectUtil.isNotEmpty(oeLs)) {
            List<Integer> oeNoLs = oeLs.stream().map(OrderEntryDto::getOeNo).distinct().collect(Collectors.toList());
            List<OeStateEntity> oeStateLs = oeStateService.list(Wrappers.lambdaQuery(OeStateEntity.class).eq(OeStateEntity::getVisitId, visitId)
                    .in(OeStateEntity::getOeNo, oeNoLs));
            List<String> deptCodeLs = oeStateLs.stream().map(OeStateEntity::getExecDeptcode).filter(Objects::nonNull).collect(Collectors.toList());
            List<OrgDeptEntity> orgDeptLs = ObjectUtil.isNotEmpty(deptCodeLs) ? orgDeptService.list(Wrappers.lambdaQuery(OrgDeptEntity.class).eq(OrgDeptEntity::getOrgId, orgId)
                    .in(OrgDeptEntity::getDeptCode, deptCodeLs)) : ListUtil.empty();
            oeLs.forEach(oe -> {
                Optional<OeStateEntity> oeState = oeStateLs.stream().filter(p -> p.getVisitId().equals(oe.getVisitId()) && p.getOeNo().equals(oe.getOeNo())).findFirst();
                if (oeState.isPresent()) {
                    oe.setDaysCount(oeState.get().getDaysCount());
                    oe.setExecTimes(oeState.get().getExecTimes());
                    oe.setLastExecDate(oeState.get().getLastExecDate());
                    oe.setLastExecSeqid(oeState.get().getLastExecSeqid());
                    // 执行科室
                    Optional<OrgDeptEntity> orgDept = orgDeptLs.stream().filter(p -> p.getDeptCode().equals(oeState.get().getExecDeptcode())).findFirst();
                    orgDept.ifPresent(orgDeptEntity -> oe.setExecDeptname(orgDeptEntity.getDeptName()));
                }
            });
            List<Long> lastExecSeqidLs = oeLs.stream().map(OrderEntryDto::getLastExecSeqid).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(lastExecSeqidLs)) {
                List<OeExecEntity> oeExecLs = oeExecService.listByIds(lastExecSeqidLs);
                List<Long> userIdLs = oeExecLs.stream().map(OeExecEntity::getUserId).distinct().collect(Collectors.toList());
                List<UserCodeEntity> userLs = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(userIdLs)) {
                    userLs = userCodeService.listByIds(userIdLs);
                }
                for (OrderEntryDto oe : oeLs) {
                    OeExecEntity oeExec = oeExecLs.stream().filter(p -> p.getExecSeqid().equals(oe.getLastExecSeqid())).findFirst().orElse(null);
                    if (oeExec != null) {
                        UserCodeEntity user = userLs.stream().filter(p -> p.getUserId().equals(oeExec.getUserId())).findFirst().orElse(null);
                        oe.setLastExecUser(user == null ? null : user.getUserName());
                        if (Convert.toInt(DateUtil.dateFormat(oeExec.getTimeStarted(), DATE_FORMAT_YMD_INT)).equals(Convert.toInt(DateUtil.dateFormat(oe.getTimeStarted(), DATE_FORMAT_YMD_INT)))
                                || Convert.toInt(oe.getIsOePlan(), 0) == 0) {
                            oe.setLastExecTime(oeExec.getTimeStarted());
                        } else {
                            oe.setLastExecTime(oe.getTimeStarted());
                        }
                    }
                }
            }
            // 皮试
            List<Long> stseqIdLs = oeStateLs.stream().map(OeStateEntity::getStSeqid).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(stseqIdLs)) {
                List<SkintestEntity> skintestLs = skintestService.listByIds(stseqIdLs);
                oeLs.forEach(oe -> {
                    Optional<OeStateEntity> oeState = oeStateLs.stream().filter(p -> p.getVisitId().equals(oe.getVisitId()) && p.getOeNo().equals(oe.getOeNo())).findFirst();
                    if (oeState.isPresent()) {
                        if (oeState.get().getStSeqid() != null) {
                            Optional<SkintestEntity> skintest = skintestLs.stream().filter(p -> p.getStSeqid().equals(oeState.get().getStSeqid())).findFirst();
                            if (skintest.isPresent()) {
                                oe.setStResult(skintest.get().getStResult());
                                oe.setStResultName(skintest.get().getStResult() != null ? StResult.getName(skintest.get().getStResult()) : "");
                            }
                        }
                    }
                });
            }
        }
    }

    @Override
    public List<DeptPostpaidItemDto> findDeptPostpaidItemLs(Long orgId, Long visitId) {
        List<DeptPostpaidItemDto> list = new ArrayList<>();
        if (visitId != null) {
            // 判断是否已生成了先诊后付划价单流水，如果未生成，则判断科室是否设置为先诊疗后付费
            VisitEntity visit = visitService.getById(visitId);
            if (visit.getPostpaidBseqid() == null) {
                OrgDeptEntity orgDept = orgDeptService.findById(orgId, visit.getDeptCode());
                if (orgDept != null && orgDept.getIsPostpaid() != null && orgDept.getIsPostpaid() == 1) {
                    list = deptPostpaidItemService.findLsByDeptCode(orgId, orgDept.getDeptCode());
                }
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VisitDto saveVisitForRegId(Long orgId, Long userId, Long regId, Long clinicianId, String deptCode, Integer clinicTypeId, Integer medTypeId) {
        // 获取挂号记录
        RegDto reg = regService.findById(regId);
        if (reg == null) {
            throw new SaveFailureException("没有挂号记录。");
        }
        // 判断是否已支付挂号费
        RegAcctEntity regAcct = regAcctService.findById(regId, RegAcctActionType.REG.getValue());
//        if (regAcct != null && regAcct.getBseqid() != null) {
//            OrgDeptEntity orgDept = orgDeptService.findById(orgId, deptCode);
//            if (orgDept != null && (orgDept.getIsPostpaid() == null || orgDept.getIsPostpaid() == 0)
//                    && billService.getPaidStatus(regAcct.getBseqid()) != PaidStatus.paid.getValue()) {
//                throw new SaveFailureException("患者还未支付挂号费。");
//            }
//        }
        VisitDto visit = visitService.saveVisitByRegId(orgId, userId, regId, clinicianId, deptCode, clinicTypeId, medTypeId);
        // 是否有挂号费用
        if (reg.getOcFlag() == null || reg.getOcFlag() == 0) {
            if (regAcct != null && regAcct.getBseqid() != null && billService.getPaidStatus(regAcct.getBseqid()) < PaidStatus.paid.getValue()) {
                LambdaUpdateWrapper<BillEntity> wrapper = Wrappers.lambdaUpdate(BillEntity.class);
                wrapper.eq(BillEntity::getBseqid, regAcct.getBseqid());
                wrapper.set(BillEntity::getVisitId, visit.getVisitId());
                wrapper.set(BillEntity::getMedTypeId, medTypeId == null ? MedicalType.outpatientRegistration.getValue() : medTypeId);
                billService.update(wrapper);
            }
//            else {
//                if (regAcct != null) {
//                    regAcctService.remove(Wrappers.lambdaQuery(RegAcctEntity.class).eq(RegAcctEntity::getRegId, regId).eq(RegAcctEntity::getActionType, RegAcctActionType.REG.getValue()));
//                }
//                hsdRegService.createBill(userId, medTypeId, regService.getById(regId));
//            }
        }
        return visit;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveVisit(Long orgId, Long userId, VisitVo entity) {
        log.info("保存诊疗记录 orgId:{} userId: {} visit:{}", orgId, userId, JSONUtil.parseObj(entity));
        if (entity.getVisitDiagLs() == null || entity.getVisitDiagLs().isEmpty()) {
            throw new SaveFailureException("诊断列表不能为空");
        }
        // 创建患者
        Long patientId = createPatient(orgId, userId, entity);
        entity.setPatientId(patientId);

        Long visitId = visitService.saveEntity(orgId, entity.getPatientId(), entity.getClinicianId(), entity.getRegId(),
                entity.getPatientName(), entity.getGenderId(), entity.getAgeOfYears(), entity.getAgeOfDays(), entity.getAgesTypeId(),
                entity.getCompanionName(), entity.getContactTel(), entity.getRelationshipId(), entity.getCompanionIdno(), entity.getCompanionAddr(),
                entity.getClinicTypeId(), Convert.toInt(DateUtil.dateFormat("yyyyMMdd")), entity.getInsuranceTypeId(),
                getPsnTypeIdByCode(entity.getPsnTypeId(), entity.getPsnTypeCode()), entity.getMedTypeId(), VisitStatus.outpatient.getValue(),
                entity.getDeptCode(), entity.getComplainedAs(), entity.getPatientStatement(), entity.getTemperature(), entity.getHeightCm(),
                entity.getWeightKg(), entity.getPulse(), entity.getRr(), entity.getDbp(), entity.getSbp(), entity.getLivingZonecode(),
                entity.getLivingAddr(), entity.getPatientTypeId(), entity.getCivilServantFlag(), entity.getEmployerName(),
                entity.getMdtrtCertTypeId(), entity.getMdtrtCertText());
        // 修改门诊病例信息
        saveOrUpdateOutpatient(visitId, entity);
        // 保存诊疗诊断
        saveVisitDiagLs(visitId, userId, entity.getClinicianId(), entity.getDeptCode(), VisitStatus.outpatient.getValue(), entity.getVisitDiagLs());
        // 修改病例状态
        LambdaUpdateWrapper<VisitEntity> wrapper = Wrappers.lambdaUpdate(VisitEntity.class);
        wrapper.eq(VisitEntity::getVisitId, visitId);
        wrapper.set(VisitEntity::getVisitStatus, VisitStatus.planOut.getValue());
        wrapper.set(VisitEntity::getTimeAdmission, new Date());
        visitService.update(wrapper);
        // 修改诊疗诊断信息
        updateVisitExtraDiag(visitId, entity.getVisitDiagLs());
        // 保存诊疗记录过敏史
        visitAllergyService.saveBatch(visitId, entity.getAllergyTypeIdLs());
        // 保存诊疗记录疾病史
        visitIllnessService.saveBatch(visitId, entity.getIllnessTypeIdLs());
        // 修改挂号信息
        updateReg(orgId, userId, entity.getDeptCode(), entity.getRegId(), visitId, entity);
        return visitId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveClinicVisit(Long orgId, Long userId, VisitVo visitVo) {
        log.info("保存诊疗记录 orgId:{} userId: {} visit:{}", orgId, userId, JSONUtil.parseObj(visitVo));
        if (visitVo.getVisitDiagLs() == null || visitVo.getVisitDiagLs().isEmpty()) {
            throw new SaveFailureException("诊断列表不能为空");
        }
        // 创建患者
        Long patientId = createPatient(orgId, userId, visitVo);
        visitVo.setPatientId(patientId);

        Long regId = visitVo.getRegId();
        if (regId == null) {
            if (visitVo.getPatientId() != null) {
                String sql = "select * from microhis_hsd.t_reg_pending where t_reg.Reg_ID = t_reg_pending.Reg_ID and t_reg.Clinic_Date = t_reg_pending.Clinic_Date";
                long count = regService.count(Wrappers.lambdaQuery(RegEntity.class).eq(RegEntity::getPatientId, visitVo.getPatientId())
                        .eq(RegEntity::getClinicDate, visitVo.getClinicDate()).eq(RegEntity::getDeptCode, visitVo.getDeptCode())
                        .eq(RegEntity::getOrgId, orgId).exists(sql));
                if (count > 0) {
                    throw new SaveFailureException("该患者在挂号科室存在未完结的挂号记录，请先完结挂号记录。");
                }
            }
            RegVo regVo = new RegVo();
            BeanUtil.copyProperties(visitVo, regVo);
            regVo.setIsReceive(1);
            regVo.setCreateRegBill(false);
            regId = hsdRegService.saveClinicReg(orgId, userId, regVo);
            if (regVo.getIsReceive() == null || regVo.getIsReceive() == 0) {
                // 修改开诊医生接诊挂号ID
                activeClinicianService.updateActiveClinicianRegId(orgId, regVo.getClinicianId(), regId);
            }
        }
        RegEntity reg = regService.getById(regId);
        visitVo.setVisitId(reg.getVisitId());

        return updateVisit(userId, visitVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateVisit(Long userId, VisitVo entity) {
        log.info("修改诊疗记录 userId: {} visit:{}", userId, JSONUtil.parseObj(entity));
        VisitEntity visit = visitService.getById(entity.getVisitId());
        if (visit == null) {
            throw new SaveFailureException("未找到诊疗记录信息。");
        }
        // 创建患者
        Long patientId = createPatient(visit.getOrgId(), userId, entity);
        // 修改诊疗信息
        updateVisit(patientId, visit.getPatientId(), visit.getClinicianId(), visit.getOrgId(), entity);
        // 修改门诊病例信息
        saveOrUpdateOutpatient(visit.getVisitId(), entity);
        // 修改诊疗记录其他信息
        updateVisitExtra(entity, true);
        // 保存诊疗诊断
        if (ObjectUtil.isNotEmpty(entity.getVisitDiagLs())) {
            saveVisitDiagLs(visit.getVisitId(), userId, entity.getClinicianId(), entity.getDeptCode(), visit.getVisitStatus(), entity.getVisitDiagLs());
            // 修改诊疗诊断信息
            updateVisitExtraDiag(entity.getVisitId(), entity.getVisitDiagLs());
        }
        // 修改病例状态
        if (visit.getVisitStatus() < VisitStatus.planOut.getValue()) {
            LambdaUpdateWrapper<VisitEntity> wrapper = Wrappers.lambdaUpdate(VisitEntity.class);
            wrapper.eq(VisitEntity::getVisitId, entity.getVisitId());
            wrapper.set(VisitEntity::getVisitStatus, VisitStatus.planOut.getValue());
            wrapper.set(VisitEntity::getTimeAdmission, new Date());
            visitService.update(wrapper);
        }
        // 保存诊疗记录过敏史
        visitAllergyService.saveBatch(entity.getVisitId(), entity.getAllergyTypeIdLs());
        // 保存诊疗记录疾病史
        visitIllnessService.saveBatch(entity.getVisitId(), entity.getIllnessTypeIdLs());
        // 修改患者统计信息
        updatePatientVisitState(visit);
        // 修改挂号信息
        updateReg(visit.getOrgId(), userId, visit.getDeptCode(), visit.getRegId(), visit.getVisitId(), entity);
        // 推送数据到EHR
//        try {
//            saveEhrVisit(visit.getVisitId());
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("EHR推送失败", e);
//        }
        return visit.getVisitId();
    }

    @Override
    public void updateVisitInfoNoReg(Long userId, VisitVo visitVo) {
        log.info("完善诊疗记录 visit:{}", JSONUtil.parseObj(visitVo));
        VisitEntity visit = visitService.getById(visitVo.getVisitId());
        if (visit == null) {
            throw new SaveFailureException("未找到诊疗记录信息。");
        }
        // 创建患者
        Long patientId = createPatient(visit.getOrgId(), userId, visitVo);
        // 修改诊疗信息
        updateVisit(patientId, visit.getPatientId(),visit.getClinicianId(), visit.getOrgId(), visitVo);
        // 修改门诊病例信息
        saveOrUpdateOutpatient(visit.getVisitId(), visitVo);
        // 修改诊疗记录其他信息
        updateVisitExtra(visitVo, false);
        // 保存诊疗诊断
        if (ObjectUtil.isNotEmpty(visitVo.getVisitDiagLs())) {
            saveVisitDiagLs(visit.getVisitId(), userId, visitVo.getClinicianId(), visitVo.getDeptCode(), visit.getVisitStatus(), visitVo.getVisitDiagLs());
            // 修改诊疗诊断信息
            updateVisitExtraDiag(visitVo.getVisitId(), visitVo.getVisitDiagLs());
        }
        // 保存诊疗记录过敏史
        visitAllergyService.saveBatch(visitVo.getVisitId(), visitVo.getAllergyTypeIdLs());
        // 保存诊疗记录疾病史
        visitIllnessService.saveBatch(visitVo.getVisitId(), visitVo.getIllnessTypeIdLs());
    }

    @Override
    @Transactional
    public void updateVisitByCda(Long visitId, Long cdaId) {
        log.debug("根据Cda修改诊疗信息visitId:{} cdaId:{}", visitId, cdaId);
        if (visitId != null && cdaId != null) {
            VisitEntity visit = visitService.getById(visitId);
            if (visit == null) {
                throw new SaveFailureException("未找到诊疗记录信息。");
            }
            List<CdaEntity> cdaLs = cdaService.list(Wrappers.lambdaQuery(CdaEntity.class).eq(CdaEntity::getVisitId, visitId).eq(CdaEntity::getValidFlag, 1));
            boolean canUpdate = false;
            if (ObjectUtil.isNotEmpty(cdaLs)) {
                // 查询cdaId是否存在，且是最后一条
                CdaEntity cda = cdaLs.stream().filter(c -> c.getCdaId().equals(cdaId)).findFirst().orElse(null);
                if (cda != null && cdaLs.get(cdaLs.size() - 1).getCdaId().equals(cdaId)) {
                    canUpdate = true;
                }
            }
            if (canUpdate) {
                // 获取电子病历结构化内容
                JSONObject jsonObject = kZhuyBlZidnrService.findKZhuyBlZidnr(visitId, cdaId);
                if (jsonObject != null) {
                    VisitVo visitVo = JSONUtil.toBean(jsonObject, VisitVo.class);
                    visitVo.setVisitId(visitId);
                    // 修改门诊病例信息
                    saveOrUpdateOutpatient(visitId, visitVo);
                    // 修改诊疗记录其他信息
                    updateVisitExtra(visitVo, false);
                }
            }
        }
    }

    @Override
    @Transactional
    public void syncVisitPatient(Long userId, Long visitId, Long patientId) {
        log.debug("诊疗记录同步修改患者信息visitId:{} patientId:{}", visitId, patientId);
        VisitEntity visit = visitService.getById(visitId);
        if (visit != null) {
            if (visit.getPatientId() != null && !visit.getPatientId().equals(patientId)) {
                throw new SaveFailureException("诊疗记录的患者与要同步的患者不是同一个患者，不能同步患者信息。");
            }
            PatientEntity patient = patientService.getById(patientId);
            // 修改诊疗患者信息
            LambdaUpdateWrapper<VisitEntity> visitWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
            visitWrapper.eq(VisitEntity::getVisitId, visitId);
            if (visit.getPatientId() == null) {
                visitWrapper.set(VisitEntity::getPatientId, patientId);
            }
            visitWrapper.set(VisitEntity::getPatientName, patient.getPatientName());
            visitWrapper.set(VisitEntity::getGenderId, patient.getGenderId());
            visitWrapper.set(VisitEntity::getZoneCode, patient.getZoneCode());
            visitWrapper.set(VisitEntity::getCareerId, patient.getCareerId());
            visitWrapper.set(VisitEntity::getMarriageStatusId, patient.getMarriageStatusId());
            if (patient.getBirthDate() != null) {
                BigDecimal[] ageAndDayCount = visitService.calculateAgeAndDayCount(patient.getBirthDate(), patient.getBirthTime());
                int ageOfYears = Convert.toInt(ageAndDayCount[0]);
                BigDecimal ageOfDays = ageAndDayCount[1];
                visitWrapper.set(VisitEntity::getAgeOfYears, ageOfYears);
                visitWrapper.set(VisitEntity::getAgeOfDays, ageOfDays);
                OrgSettingEntity orgSetting = orgSettingService.getById(visit.getOrgId());
                if (orgSetting != null) {
                    // 是否儿童病例
                    if (orgSetting.getChildrenMaxAge() != null && ageOfYears <= orgSetting.getChildrenMaxAge()) {
                        visitWrapper.set(VisitEntity::getIsChildren, 1);
                    } else {
                        visitWrapper.set(VisitEntity::getIsChildren, 0);
                    }
                    // 是否老年病例
                    if (orgSetting.getAgedMinAge() != null && ageOfYears >= orgSetting.getAgedMinAge()) {
                        visitWrapper.set(VisitEntity::getIsAged, 1);
                    } else {
                        visitWrapper.set(VisitEntity::getIsAged, 0);
                    }
                }
                if (ageOfYears <= 0) {
                    // 是否新生儿 小于28天是新生儿
                    if (Convert.toInt(ageOfDays) <= 28) {
                        visitWrapper.set(VisitEntity::getIsNewborn, 1);
                    } else {
                        visitWrapper.set(VisitEntity::getIsNewborn, 0);
                    }
                    visitWrapper.set(VisitEntity::getAgesId, visitService.getAgesId(Convert.toInt(ageOfDays), 0));
                } else {
                    visitWrapper.set(VisitEntity::getAgesId, visitService.getAgesId(0, ageOfYears));
                }
            }
            visitService.update(visitWrapper);
            // 修改挂号患者信息
            if (visit.getRegId() != null) {
                LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
                regWrapper.eq(RegEntity::getRegId, visit.getRegId());
                if (visit.getPatientId() == null) {
                    regWrapper.set(RegEntity::getPatientId, patientId);
                }
                regWrapper.set(RegEntity::getPatientName, patient.getPatientName());
                if (StrUtil.isNotBlank(patient.getTelNo()) && ValidatorUtil.isMobile(patient.getTelNo())) {
                    regWrapper.set(RegEntity::getContactTel, AESEncryptHandler.getEncryptStr(patient.getTelNo()));
                }
                regService.update(regWrapper);
            }
            // 修改诊疗其他信息
            LambdaUpdateWrapper<VisitExtraEntity> extraWrapper = Wrappers.lambdaUpdate(VisitExtraEntity.class);
            extraWrapper.eq(VisitExtraEntity::getVisitId, visitId);
            extraWrapper.set(VisitExtraEntity::getCertTypeId, patient.getCertTypeId());
            if (StrUtil.isNotBlank(patient.getIdcertNo()) && ValidatorUtil.isIdCard(patient.getIdcertNo())) {
                extraWrapper.set(VisitExtraEntity::getIdcertNo, AESEncryptHandler.getEncryptStr(patient.getIdcertNo()));
            }
            if (StrUtil.isNotBlank(patient.getFamilyZipcode())) {
                extraWrapper.set(VisitExtraEntity::getFamilyZipcode, patient.getFamilyZipcode());
            }
            if (StrUtil.isNotBlank(patient.getFamilyZonecode())) {
                extraWrapper.set(VisitExtraEntity::getFamilyZonecode, patient.getFamilyZonecode());
            }
            if (StrUtil.isNotBlank(patient.getFamilyAddr())) {
                extraWrapper.set(VisitExtraEntity::getFamilyAddr, patient.getFamilyAddr());
            }
            if (StrUtil.isNotBlank(patient.getLivingZipcode())) {
                extraWrapper.set(VisitExtraEntity::getLivingZipcode, patient.getLivingZipcode());
            }
            if (StrUtil.isNotBlank(patient.getLivingZonecode())) {
                extraWrapper.set(VisitExtraEntity::getLivingZonecode, patient.getLivingZonecode());
            }
            if (StrUtil.isNotBlank(patient.getLivingAddr())) {
                extraWrapper.set(VisitExtraEntity::getLivingAddr, patient.getLivingAddr());
            }
            if (patient.getRelationshipId() != null) {
                extraWrapper.set(VisitExtraEntity::getRelationshipId, patient.getRelationshipId());
            }
            if (StrUtil.isNotBlank(patient.getContactorName())) {
                extraWrapper.set(VisitExtraEntity::getCompanionName, patient.getContactorName());
            }
            if (StrUtil.isNotBlank(patient.getContactorTel()) && ValidatorUtil.isMobile(patient.getContactorTel())) {
                extraWrapper.set(VisitExtraEntity::getContactTel, AESEncryptHandler.getEncryptStr(patient.getContactorTel()));
            }
            visitExtraService.update(extraWrapper);
            // 未完成诊疗记录
            if (visit.getPatientId() == null) {
                VisitPendingEntity visitPending = visitPendingService.getById(visitId);
                if (visitPending == null) {
                    visitPendingService.save(VisitPendingEntity.builder().visitId(visitId).patientId(patientId).build());
                } else {
                    visitPendingService.update(Wrappers.lambdaUpdate(VisitPendingEntity.class).eq(VisitPendingEntity::getVisitId, visitId)
                            .set(VisitPendingEntity::getPatientId, patientId));
                }
            }
            // 修改申请单的患者名称
            LambdaUpdateWrapper<OrderEntity> orderWrapper = Wrappers.lambdaUpdate(OrderEntity.class);
            orderWrapper.eq(OrderEntity::getVisitId, visitId);
            orderWrapper.set(OrderEntity::getPatientName, patient.getPatientName());
            orderService.update(orderWrapper);

            LambdaUpdateWrapper<MoeOrderEntity> moeOrderWrapper = Wrappers.lambdaUpdate(MoeOrderEntity.class);
            moeOrderWrapper.eq(MoeOrderEntity::getVisitId, visitId);
            moeOrderWrapper.set(MoeOrderEntity::getPatientName, patient.getPatientName());
            if (visit.getPatientId() == null) {
                moeOrderWrapper.set(MoeOrderEntity::getPatientId, patientId);
            }
            moeOrderService.update(moeOrderWrapper);

            LambdaUpdateWrapper<TreatmentEntity> treatmentWrapper = Wrappers.lambdaUpdate(TreatmentEntity.class);
            treatmentWrapper.eq(TreatmentEntity::getVisitId, visitId);
            treatmentWrapper.set(TreatmentEntity::getPatientName, patient.getPatientName());
            if (visit.getPatientId() == null) {
                treatmentWrapper.set(TreatmentEntity::getPatientId, patientId);
            }
            treatmentService.update(treatmentWrapper);
            // 更新划价单的患者信息
            LambdaUpdateWrapper<BillEntity> billWrapper = Wrappers.lambdaUpdate(BillEntity.class);
            billWrapper.eq(BillEntity::getVisitId, visitId);
            if (visit.getPatientId() == null) {
                billWrapper.set(BillEntity::getPatientId, patientId);
            }
            billWrapper.set(BillEntity::getPatientName, patient.getPatientName());
            billService.update(billWrapper);
            // 推送转诊消息
            hsdRegService.afterReg(userId, visit.getRegId(), visit.getOrgId(), visit.getClinicianId(), visit.getDeptCode(), ImMsgType.newReg);
        }
    }

    // 患者ID为空，证件号码不为空，判断证件号码是否已有患者信息，若没有则根据证件号码新增患者信息
    private Long createPatient(Long orgId, Long userId, VisitVo visitVo) {
        Long patientId = visitVo.getPatientId();
        if (patientId == null && StrUtil.isNotBlank(visitVo.getIdcertNo()) && ValidatorUtil.isIdCard(visitVo.getIdcertNo())) {
            PatientVo patient = new PatientVo();
            patient.setOrgId(orgId);
            patient.setPatientName(visitVo.getPatientName());
            patient.setGenderId(visitVo.getGenderId());
            patient.setBirthDate(visitVo.getBirthDate());
            patient.setCertTypeId(visitVo.getCertTypeId());
            patient.setIdcertNo(visitVo.getIdcertNo());
            patient.setTelNo(visitVo.getTelNo());
            patient.setLivingZonecode(visitVo.getLivingZonecode());
            patient.setLivingAddr(visitVo.getLivingAddr());
            patient.setContactorName(visitVo.getCompanionName());
            patient.setContactorTel(visitVo.getContactTel());
            patient.setRelationshipId(visitVo.getRelationshipId());
            patientId = hsdPatientService.savePatient(orgId, userId, false, patient);
        } else if (patientId != null) {
            MpiPatientMapEntity patientMap = mpiPatientMapService.findById(patientId, orgId);
            if (patientMap == null || patientMap.getOutpatientNo() == null) {
                Integer outpatientNo = mpiPatientnoService.getNextOutpatientNo(orgId);
                mpiPatientMapService.updateOutpatientNo(orgId, patientId, outpatientNo);
            }
        }
        return patientId;
    }

    private void updateVisit(Long patientId, Long originalPatientId, Long originalClinicianId, Long orgId, VisitVo entity) {
        LambdaUpdateWrapper<VisitEntity> visitWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
        visitWrapper.eq(VisitEntity::getVisitId, entity.getVisitId());
        visitWrapper.set(VisitEntity::getInsuranceTypeId, entity.getInsuranceTypeId());
        visitWrapper.set(VisitEntity::getIsTraumatic, entity.getIsTraumatic());
        visitWrapper.set(VisitEntity::getIsTpRelative, entity.getIsTpRelative());
        if (StrUtil.isNotBlank(entity.getDeptCode())) {
            visitWrapper.set(VisitEntity::getDeptCode, entity.getDeptCode());
        }
        if (originalClinicianId == null) {
            visitWrapper.set(VisitEntity::getClinicianId, entity.getClinicianId());
        }
//        visitWrapper.set(VisitEntity::getPsnTypeId, getPsnTypeIdByCode(entity.getPsnTypeId(), entity.getPsnTypeCode()));
        visitWrapper.set(VisitEntity::getDiseaseId, entity.getDiseaseId());
        if (StrUtil.isNotBlank(entity.getPatientName())) {
            visitWrapper.set(VisitEntity::getPatientName, entity.getPatientName());
        }
        visitWrapper.set(VisitEntity::getGenderId, entity.getGenderId());
        if (originalPatientId == null) {
            visitWrapper.set(VisitEntity::getPatientId, patientId);
            if (entity.getBirthDate() != null) {
                BigDecimal[] ageAndDayCount = visitService.calculateAgeAndDayCount(entity.getBirthDate(), entity.getBirthTime());
                int ageOfYears = Convert.toInt(ageAndDayCount[0]);
                BigDecimal ageOfDays = ageAndDayCount[1];
                visitWrapper.set(VisitEntity::getAgeOfYears, ageOfYears);
                visitWrapper.set(VisitEntity::getAgeOfDays, ageOfDays);
                OrgSettingEntity orgSetting = orgSettingService.getById(orgId);
                if (orgSetting != null) {
                    // 是否儿童病例
                    if (orgSetting.getChildrenMaxAge() != null && ageOfYears <= orgSetting.getChildrenMaxAge()) {
                        visitWrapper.set(VisitEntity::getIsChildren, 1);
                    } else {
                        visitWrapper.set(VisitEntity::getIsChildren, 0);
                    }
                    // 是否老年病例
                    if (orgSetting.getAgedMinAge() != null && ageOfYears >= orgSetting.getAgedMinAge()) {
                        visitWrapper.set(VisitEntity::getIsAged, 1);
                    } else {
                        visitWrapper.set(VisitEntity::getIsAged, 0);
                    }
                }
                if (ageOfYears <= 0) {
                    // 是否新生儿 小于28天是新生儿
                    if (Convert.toInt(ageOfDays) <= 28) {
                        visitWrapper.set(VisitEntity::getIsNewborn, 1);
                    } else {
                        visitWrapper.set(VisitEntity::getIsNewborn, 0);
                    }
                    visitWrapper.set(VisitEntity::getAgesId, visitService.getAgesId(Convert.toInt(ageOfDays), 0));
                } else {
                    visitWrapper.set(VisitEntity::getAgesId, visitService.getAgesId(0, ageOfYears));
                }
            }
        }
        visitService.update(visitWrapper);
    }

    // 新增或修改门诊病例信息
    private void saveOrUpdateOutpatient(Long visitId, VisitVo entity) {
        if (outpatientService.count(Wrappers.lambdaQuery(OutpatientEntity.class).eq(OutpatientEntity::getVisitId, visitId)) == 0) {
            OutpatientEntity outpatientEntity = OutpatientEntity.builder()
                    .visitId(visitId)
                    .complainedAs(entity.getComplainedAs())
                    .hpiDesc(entity.getHpiDesc())
                    .pastHistory(entity.getPastHistory())
                    .familyHistory(entity.getFamilyHistory())
                    .allergicHistory(entity.getAllergicHistory())
                    .generalInspection(entity.getGeneralInspection())
                    .differentialDiag(entity.getDifferentialDiag())
                    .treatAbstract(entity.getTreatAbstract())
                    .patientStatement(entity.getPatientStatement())
                    .healthEducation(entity.getHealthEducation())
                    .maritalHistory(entity.getMaritalHistory())
                    .childbearingHistory(entity.getChildbearingHistory())
                    .menstrualHistory(entity.getMenstrualHistory())
                    .auxiliaryInspection(entity.getAuxiliaryInspection())
                    .progressNotes(entity.getProgressNotes())
                    .build();
            outpatientService.save(outpatientEntity);
        } else {
            LambdaUpdateWrapper<OutpatientEntity> outpatientWrapper = Wrappers.lambdaUpdate(OutpatientEntity.class);
            outpatientWrapper.eq(OutpatientEntity::getVisitId, entity.getVisitId());
            outpatientWrapper.set(OutpatientEntity::getComplainedAs, entity.getComplainedAs());
            outpatientWrapper.set(OutpatientEntity::getHpiDesc, entity.getHpiDesc());
            outpatientWrapper.set(OutpatientEntity::getPastHistory, entity.getPastHistory());
            outpatientWrapper.set(OutpatientEntity::getFamilyHistory, entity.getFamilyHistory());
            outpatientWrapper.set(OutpatientEntity::getAllergicHistory, entity.getAllergicHistory());
            outpatientWrapper.set(OutpatientEntity::getGeneralInspection, entity.getGeneralInspection());
            outpatientWrapper.set(OutpatientEntity::getDifferentialDiag, entity.getDifferentialDiag());
            outpatientWrapper.set(OutpatientEntity::getTreatAbstract, entity.getTreatAbstract());
            outpatientWrapper.set(OutpatientEntity::getPatientStatement, entity.getPatientStatement());
            outpatientWrapper.set(OutpatientEntity::getHealthEducation, entity.getHealthEducation());
            outpatientWrapper.set(OutpatientEntity::getMaritalHistory, entity.getMaritalHistory());
            outpatientWrapper.set(OutpatientEntity::getChildbearingHistory, entity.getChildbearingHistory());
            outpatientWrapper.set(OutpatientEntity::getMenstrualHistory, entity.getMenstrualHistory());
            outpatientWrapper.set(OutpatientEntity::getAuxiliaryInspection, entity.getAuxiliaryInspection());
            outpatientWrapper.set(OutpatientEntity::getProgressNotes, entity.getProgressNotes());
            outpatientService.update(outpatientWrapper);
        }
    }

    // 修改诊疗其他信息
    private void updateVisitExtra(VisitVo entity, boolean isUpdatePatient) {
        LambdaUpdateWrapper<VisitExtraEntity> extraWrapper = Wrappers.lambdaUpdate(VisitExtraEntity.class);
        extraWrapper.eq(VisitExtraEntity::getVisitId, entity.getVisitId());
        extraWrapper.set(VisitExtraEntity::getTemperature, entity.getTemperature() != null && entity.getTemperature().compareTo(BigDecimal.ZERO) != 0 ? entity.getTemperature() : null);
        extraWrapper.set(VisitExtraEntity::getHeightCm, entity.getHeightCm() != null && entity.getHeightCm() > 0 ? entity.getHeightCm() : null);
        extraWrapper.set(VisitExtraEntity::getWeightKg, entity.getWeightKg() != null && entity.getWeightKg().compareTo(BigDecimal.ZERO) != 0 ? entity.getWeightKg() : null);
        extraWrapper.set(VisitExtraEntity::getPulse, entity.getPulse() != null && entity.getPulse() > 0 ? entity.getPulse() : null);
        extraWrapper.set(VisitExtraEntity::getRr, entity.getRr() != null && entity.getRr() > 0 ? entity.getRr() : null);
        extraWrapper.set(VisitExtraEntity::getDbp, entity.getDbp() != null && entity.getDbp() > 0 ? entity.getDbp() : null);
        extraWrapper.set(VisitExtraEntity::getSbp, entity.getSbp() != null && entity.getSbp() > 0 ? entity.getSbp() : null);
        if (entity.getCertTypeId() != null) {
            extraWrapper.set(VisitExtraEntity::getCertTypeId, entity.getCertTypeId());
        }
        if (StrUtil.isNotBlank(entity.getIdcertNo()) && ValidatorUtil.isIdCard(entity.getIdcertNo())) {
            extraWrapper.set(VisitExtraEntity::getIdcertNo, AESEncryptHandler.getEncryptStr(entity.getIdcertNo()));
        }
        if (StrUtil.isNotBlank(entity.getLivingZonecode())) {
            extraWrapper.set(VisitExtraEntity::getLivingZonecode, entity.getLivingZonecode());
        }
        if (StrUtil.isNotBlank(entity.getLivingAddr())) {
            extraWrapper.set(VisitExtraEntity::getLivingAddr, entity.getLivingAddr());
        }
        if (StrUtil.isNotBlank(entity.getCompanionName())) {
            extraWrapper.set(VisitExtraEntity::getCompanionName, entity.getCompanionName());
        }
        if (entity.getRelationshipId() != null) {
            extraWrapper.set(VisitExtraEntity::getRelationshipId, entity.getRelationshipId());
        }
        if (StrUtil.isNotBlank(entity.getCompanionIdno()) && ValidatorUtil.isIdCard(entity.getCompanionIdno())) {
            extraWrapper.set(VisitExtraEntity::getCompanionIdno, AESEncryptHandler.getEncryptStr(entity.getCompanionIdno()));
        }
        if (StrUtil.isNotBlank(entity.getContactTel()) && ValidatorUtil.isMobile(entity.getContactTel())) {
            extraWrapper.set(VisitExtraEntity::getContactTel, AESEncryptHandler.getEncryptStr(entity.getContactTel()));
        }
        if (StrUtil.isNotBlank(entity.getCompanionAddr())) {
            extraWrapper.set(VisitExtraEntity::getCompanionAddr, entity.getCompanionAddr());
        }
        if (entity.getDiseaseId() != null) {
            DiseaseEntity disease = diseaseService.getById(entity.getDiseaseId());
            if (disease != null && StrUtil.isNotBlank(disease.getMiCode())) {
                extraWrapper.set(VisitExtraEntity::getMiDiseaseCode, disease.getMiCode());
                extraWrapper.set(VisitExtraEntity::getMiDiseaseName, disease.getDiseaseName());
            }
        }
        visitExtraService.update(extraWrapper);
        // 修改患者信息
        if (entity.getPatientId() != null && isUpdatePatient) {
            LambdaUpdateWrapper<PatientEntity> patientWrapper = Wrappers.lambdaUpdate(PatientEntity.class);
            patientWrapper.eq(PatientEntity::getPatientId, entity.getPatientId());
            if (StrUtil.isNotBlank(entity.getLivingZonecode())) {
                patientWrapper.set(PatientEntity::getLivingZonecode, entity.getLivingZonecode());
            }
            if (StrUtil.isNotBlank(entity.getLivingAddr())) {
                patientWrapper.set(PatientEntity::getLivingAddr, entity.getLivingAddr());
            }
            if (StrUtil.isNotBlank(entity.getCompanionName())) {
                patientWrapper.set(PatientEntity::getContactorName, entity.getCompanionName());
            }
            if (StrUtil.isNotBlank(entity.getContactTel()) && ValidatorUtil.isMobile(entity.getContactTel())) {
                patientWrapper.set(PatientEntity::getContactorTel, AESEncryptHandler.getEncryptStr(entity.getContactTel()));
            }
            if (entity.getRelationshipId() != null) {
                patientWrapper.set(PatientEntity::getRelationshipId, entity.getRelationshipId());
            }
            patientWrapper.set(PatientEntity::getLastUpdated, new Date());
            patientService.update(patientWrapper);
        }
    }

    // 保存诊疗诊断
    private void saveVisitDiagLs(Long visitId, Long userId, Long clinicianId, String deptCode, Integer visitStatus, List<VisitDiagEntity> visitDiagLs) {
        List<VisitDiagEntity> orgiVisitDiagLs = visitDiagService.list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId)
                .in(VisitDiagEntity::getDiagTypeId, DiagType.westernDiag.getValue(), DiagType.medicalDiag.getValue()));
        if (!orgiVisitDiagLs.isEmpty()) {
            List<String> originalDiagCodeLs = orgiVisitDiagLs.stream().map(VisitDiagEntity::getDiagCode).distinct().collect(Collectors.toList());
            List<String> diagCodeLs = visitDiagLs.stream().map(VisitDiagEntity::getDiagCode).distinct().collect(Collectors.toList());
            for (String diagCode : originalDiagCodeLs) {
                if (visitStatus >= VisitStatus.planOut.getValue() && !diagCodeLs.contains(diagCode)) {
                    throw new SaveFailureException("已添加诊断不支持删除，只能排除");
                } else if (!diagCodeLs.contains(diagCode)) {
                    visitDiagService.remove(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId)
                            .eq(VisitDiagEntity::getDiagCode, diagCode));
                }
            }
        }
        visitDiagService.saveBatchEntity(visitId, userId, clinicianId, deptCode, visitDiagLs);
    }

    // 修改诊疗记录其他信息的诊断
    private void updateVisitExtraDiag(Long visitId, List<VisitDiagEntity> visitDiagLs) {
        // 西医诊断
        List<VisitDiagEntity> westDiagLs = visitDiagLs.stream().filter(p -> (p.getDiagTypeId().equals(DiagType.westernDiag.getValue())
                || p.getDiagTypeId().equals(DiagType.medicalDiag.getValue())) && !p.getDiagStatus().equals(DiagStatus.removed.getValue())).collect(Collectors.toList());
        // 中医诊断
        List<VisitDiagEntity> tcmMainDiagLs = visitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmMainDiag.getValue())
                && !p.getDiagStatus().equals(DiagStatus.removed.getValue())).collect(Collectors.toList());
        List<VisitDiagEntity> tcmOtherDiagLs = visitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmOtherDiag.getValue())
                && !p.getDiagStatus().equals(DiagStatus.removed.getValue())).collect(Collectors.toList());
        // 病理学诊断
        List<VisitDiagEntity> pathologicalDiagLs = visitDiagLs.stream().filter(p -> p.getDiagCatId() != null && p.getDiagCatId().equals(DiagCat.pathology.getValue())
                && !p.getDiagStatus().equals(DiagStatus.removed.getValue())).collect(Collectors.toList());

        String opDiseaseCode = "";
        String opDiseaseName = "";
        String opTcmDiseaseCode = "";
        String opTcmDiseaseName = "";
        String opTcmSymptomCode = "";
        String opTcmSymptomName = "";
        String pathologicalDiagCode = "";
        String pathologicalDiagName = "";
        String pathologyNo = "";
        if (ObjectUtil.isNotEmpty(westDiagLs)) {
            opDiseaseCode = westDiagLs.get(0).getDiagCode();
            opDiseaseName = westDiagLs.get(0).getDiagName();
        }
        if (ObjectUtil.isNotEmpty(tcmMainDiagLs)) {
            opTcmDiseaseCode = tcmMainDiagLs.get(0).getDiagCode();
            opTcmDiseaseName = tcmMainDiagLs.get(0).getDiagName();
        }
        if (ObjectUtil.isNotEmpty(tcmOtherDiagLs)) {
            opTcmSymptomCode = tcmOtherDiagLs.get(0).getDiagCode();
            opTcmSymptomName = tcmOtherDiagLs.get(0).getDiagName();
        }
        if (ObjectUtil.isNotEmpty(pathologicalDiagLs)) {
            pathologicalDiagCode = pathologicalDiagLs.get(0).getDiagCode();
            pathologicalDiagName = pathologicalDiagLs.get(0).getDiagName();
            pathologyNo = pathologicalDiagLs.get(0).getSpecimenNo();
        }
        LambdaUpdateWrapper<VisitExtraEntity> wrapper = Wrappers.lambdaUpdate(VisitExtraEntity.class);
        wrapper.eq(VisitExtraEntity::getVisitId, visitId);
        wrapper.set(VisitExtraEntity::getOpDiseaseCode, opDiseaseCode);
        wrapper.set(VisitExtraEntity::getOpDiseaseName, opDiseaseName);
        wrapper.set(VisitExtraEntity::getOpTcmDiseaseCode, opTcmDiseaseCode);
        wrapper.set(VisitExtraEntity::getOpTcmDiseaseName, opTcmDiseaseName);
        wrapper.set(VisitExtraEntity::getOpTcmSymptomCode, opTcmSymptomCode);
        wrapper.set(VisitExtraEntity::getOpTcmSymptomName, opTcmSymptomName);
        wrapper.set(VisitExtraEntity::getPathologicalDiagCode, pathologicalDiagCode);
        wrapper.set(VisitExtraEntity::getPathologicalDiagName, pathologicalDiagName);
        wrapper.set(VisitExtraEntity::getPathologyNo, pathologyNo);
        visitExtraService.update(wrapper);
        // 修改诊疗状态诊断数量
        updateVisitState(visitId, visitDiagLs);
    }

    // 修改诊疗状态诊断数量
    private void updateVisitState(Long visitId, List<VisitDiagEntity> visitDiagLs) {
        Integer diagCode = visitDiagLs.size();
        Integer diagDenied = (int) visitDiagLs.stream().filter(p -> p.getDiagStatus().equals(DiagStatus.removed.getValue())).count();
        LambdaUpdateWrapper<VisitStateEntity> stateWrapper = Wrappers.lambdaUpdate(VisitStateEntity.class);
        stateWrapper.eq(VisitStateEntity::getVisitId, visitId);
        stateWrapper.set(VisitStateEntity::getDiagCount, diagCode);
        stateWrapper.set(VisitStateEntity::getDiagDenied, diagDenied);
        visitStateService.update(stateWrapper);
    }

    /**
     * 修改患者诊疗次数统计
     */
    private void updatePatientVisitState (VisitEntity visit) {
        if (visit.getPatientId() != null) {
            PatientStateEntity patientState = patientStateService.getById(visit.getPatientId());
            Integer medTypeId = visit.getMedTypeId();
            if (medTypeId == null) {
                medTypeId = 11;
            }
            MedicalTypeEntity medicalType = medicalTypeService.getById(medTypeId);
            if (patientState != null) {
                LambdaUpdateWrapper<PatientStateEntity> wrapper = Wrappers.lambdaUpdate(PatientStateEntity.class);
                wrapper.eq(PatientStateEntity::getPatientId, visit.getPatientId());
                wrapper.set(PatientStateEntity::getPendingVisitId, null);
                if (medicalType != null && medicalType.getForOpc() == 1) {
                    wrapper.set(PatientStateEntity::getLastOpVisitId, visit.getVisitId());
                } else if (medicalType != null && medicalType.getForIpc() == 1) {
                    wrapper.set(PatientStateEntity::getLastIpVisitId, visit.getVisitId());
                }
                patientStateService.update(wrapper);
            } else {
                patientState = PatientStateEntity.builder().patientId(visit.getPatientId()).build();
                if (medicalType != null && medicalType.getForOpc() == 1) {
                    patientState.setLastOpVisitId(visit.getVisitId());
                } else if (medicalType != null && medicalType.getForIpc() == 1) {
                    patientState.setLastIpVisitId(visit.getVisitId());
                }
                patientStateService.save(patientState);
            }
            // 患者诊疗次数统计
            if (visit.getCountedFlag() == null || visit.getCountedFlag() == 0) {
                PatientVisitStatEntity patientVisitStat = patientVisitStatService.getOne(Wrappers.lambdaQuery(PatientVisitStatEntity.class)
                        .eq(PatientVisitStatEntity::getOrgId, visit.getOrgId()).eq(PatientVisitStatEntity::getPatientId, visit.getPatientId()));
//                int visitNo; // 就诊次序
                if (patientVisitStat != null) {
                    LambdaUpdateWrapper<PatientVisitStatEntity> statWrapper = Wrappers.lambdaUpdate(PatientVisitStatEntity.class);
                    statWrapper.eq(PatientVisitStatEntity::getOrgId, visit.getOrgId());
                    statWrapper.eq(PatientVisitStatEntity::getPatientId, visit.getPatientId());
                    if (medicalType != null && medicalType.getForOpc() == 1) {
                        statWrapper.setSql("OP_Visit_Count = IFNULL(OP_Visit_Count, 0) + 1");
//                        visitNo = patientVisitStat.getOpVisitCount() == null ? 1 : patientVisitStat.getOpVisitCount() + 1;
                    } else if (medicalType != null && medicalType.getForIpc() == 1) {
                        statWrapper.setSql("IP_Visit_Count = IFNULL(IP_Visit_Count, 0) + 1");
//                        visitNo = patientVisitStat.getIpVisitCount() == null ? 1 : patientVisitStat.getIpVisitCount() + 1;
                    } else {
                        statWrapper.setSql("Other_Visit_Count = IFNULL(Other_Visit_Count, 0) + 1");
//                        visitNo = patientVisitStat.getOtherVisitCount() == null? 1 : patientVisitStat.getOtherVisitCount() + 1;
                    }
                    patientVisitStatService.update(statWrapper);
                } else {
                    patientVisitStat = PatientVisitStatEntity.builder().orgId(visit.getOrgId()).patientId(visit.getPatientId()).build();
                    if (medicalType != null && medicalType.getForOpc() == 1) {
                        patientVisitStat.setOpVisitCount(1);
                    } else if (medicalType != null && medicalType.getForIpc() == 1) {
                        patientVisitStat.setIpVisitCount(1);
                    } else {
                        patientVisitStat.setOtherVisitCount(1);
                    }
                    patientVisitStatService.save(patientVisitStat);
//                    visitNo = 1;
                }
                // 修改诊疗计数状态
                LambdaUpdateWrapper<VisitEntity> visitWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
                visitWrapper.eq(VisitEntity::getVisitId, visit.getVisitId());
                visitWrapper.set(VisitEntity::getCountedFlag, 1);
                visitWrapper.set(VisitEntity::getVisitNo, visitService.getVisitNo(visit.getClinicDate()));
                visitService.update(visitWrapper);
            }
        }
    }

    private void updateReg(Long orgId, Long userId, String deptCode, Long regId, Long visitId, VisitVo entity) {
        if (regId != null) {
            // 如果是线上还未接诊，则先接诊
            RegEntity reg = regService.findById(regId);
            if (reg.getOcFlag() != null && reg.getOcFlag().equals(1)
                    && (reg.getClinicStatus().equals(ClinicStatus.waiting.getValue()) || reg.getClinicStatus().equals(ClinicStatus.reg.getValue()))) {
                receiveReg(reg.getRegId(), reg.getClinicDate());
            } else if (reg.getClinicStatus().equals(ClinicStatus.waiting.getValue()) || reg.getClinicStatus().equals(ClinicStatus.reg.getValue())) {
                hsdRegService.receiveReg(userId, regId, entity.getClinicianId());
            }
            regService.updateReg(regId, entity.getPatientId(), entity.getRevisitFlag(), entity.getPatientName(), entity.getComplainedAs(), entity.getTemperature(),
                    entity.getHeightCm(), entity.getWeightKg(), entity.getPulse(), entity.getRr(), entity.getDbp(), entity.getSbp());
            // 如果不是线上，则修改收费信息
            if (reg.getOcFlag() == null || reg.getOcFlag().equals(0)) {
                LambdaUpdateWrapper<BillEntity> billWrapper = Wrappers.lambdaUpdate(BillEntity.class);
                billWrapper.eq(BillEntity::getRegId, regId);
                billWrapper.eq(BillEntity::getBillTypeId, BillType.regBill.getValue());
                billWrapper.isNull(BillEntity::getVisitId);
                billWrapper.notIn(BillEntity::getPaidStatus, ListUtil.of(PaidStatus.refund.getValue(), PaidStatus.cancel.getValue()));
                billWrapper.set(BillEntity::getVisitId, visitId);
                if (entity.getPatientId() != null) {
                    billWrapper.set(BillEntity::getPatientId, entity.getPatientId());
                }
                if (StrUtil.isNotBlank(entity.getPatientName())) {
                    billWrapper.set(BillEntity::getPatientName, entity.getPatientName());
                }
                billService.update(billWrapper);
            }
        }
    }

    /**
     * 接诊
     */
    private void receiveReg(Long regId, Integer clinicDate) {
        // 修改诊疗状态
        LambdaUpdateWrapper<RegEntity> wrapper = Wrappers.lambdaUpdate(RegEntity.class);
        wrapper.eq(RegEntity::getRegId, regId);
        wrapper.set(RegEntity::getClinicStatus, ClinicStatus.visit.getValue());
        wrapper.set(RegEntity::getRegStatus, RegStatus.CONFIRMED.getValue());
        wrapper.set(RegEntity::getTimeAdmission, new Date());
        regService.update(wrapper);
        // 保存未完结挂号记录
        RegPendingEntity regPending = regPendingService.getById(regId);
        if (regPending == null) {
            regPendingService.save(RegPendingEntity.builder().regId(regId).clinicDate(clinicDate).build());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveDeptPostpaidBill(Long orgId, Long userId, Long visitId, List<PostpaidArtVo> postpaidArtLs) {
        if (ObjectUtil.isNotEmpty(postpaidArtLs) && visitId != null) {
            // 把相同的项目合并
            List<PostpaidArtVo> newPostpaidArtLs = new ArrayList<>();
            for (PostpaidArtVo postpaidArt : postpaidArtLs) {
                if (ObjectUtil.isNotEmpty(newPostpaidArtLs)) {
                    PostpaidArtVo newPostpaidArt = newPostpaidArtLs.stream().filter(p -> p.getArtId().equals(postpaidArt.getArtId())).findFirst().orElse(null);
                    if (newPostpaidArt != null) {
                        newPostpaidArt.setTotal(newPostpaidArt.getTotal().add(postpaidArt.getTotal()));
                    } else {
                        newPostpaidArtLs.add(postpaidArt);
                    }
                } else {
                    newPostpaidArtLs.add(postpaidArt);
                }
            }

            VisitEntity visit = visitService.getById(visitId);
            List<Long> artIdLs = newPostpaidArtLs.stream().map(PostpaidArtVo::getArtId).distinct().collect(Collectors.toList());
            List<ArticleEntity> articleLs = articleService.listByIds(artIdLs);
            List<ArtClassEntity> artClassLs = hsdBaseService.getArtClassLsFromArtLs(articleLs);
            List<OrgItemPriceEntity> orgItemPriceLs = hsdBaseService.getOrgItemPriceLs(orgId, articleLs);
            // 如果组套获取组套计价明细金额
            List<Long> packageIdLs = articleLs.stream().filter(a -> a.getIsPackage() != null && a.getIsPackage() == 1).map(ArticleEntity::getArtId).collect(Collectors.toList());
            List<PackagePriceDto> packagePriceLs = new ArrayList<>();
            List<ArticleEntity> packageDetailArtLs = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(packageIdLs)) {
                List<PackageDetailVo> packageDetailVoLs = new ArrayList<>();
                for (Long packageId : packageIdLs) {
                    PostpaidArtVo art = newPostpaidArtLs.stream().filter(p -> p.getArtId().equals(packageId)).findFirst().orElse(null);
                    if (art != null) {
                        PackageDetailVo packageDetailVo = PackageDetailVo.builder()
                                .packageId(packageId)
                                .total(art.getTotal())
                                .clinicTypeId(visit.getClinicTypeId())
                                .build();
                        packageDetailVoLs.add(packageDetailVo);
                    }
                }
                packagePriceLs = packageDetailService.findPackagePriceLsById(orgId, visit.getPatientTypeId(), visit.getClinicDate(), packageDetailVoLs);
                if (ObjectUtil.isNotEmpty(packagePriceLs)) {
                    List<Long> detailArtIdLs = packagePriceLs.stream().map(PackagePriceDto::getArtId).distinct().collect(Collectors.toList());
                    packageDetailArtLs = articleService.listByIds(detailArtIdLs);
                }
            }
            List<BillDetailEntity> billDetailLs = new ArrayList<>();
            int lineNo = 1;
            for (PostpaidArtVo postpaidArt : newPostpaidArtLs) {
                OrgItemPriceEntity orgItemPrice = orgItemPriceLs.stream().filter(item -> item.getArtId().equals(postpaidArt.getArtId())).findFirst().orElse(null);
                ArticleEntity article = articleLs.stream().filter(a -> a.getArtId().equals(postpaidArt.getArtId())).findFirst().orElse(new ArticleEntity());
                if (article.getIsPackage() != null &&article.getIsPackage() == 1) {
                    List<PackagePriceDto> priceLs = packagePriceLs.stream().filter(p -> p.getPackageId().equals(postpaidArt.getArtId())).collect(Collectors.toList());
                    // todo Convert.toInt(detail.getStockReq(), 0) == 1 从sectionArt取价格
                    if (ObjectUtil.isNotEmpty(priceLs)) {
                        for (PackagePriceDto packagePrice : priceLs) {
                            ArticleEntity detailArt = packageDetailArtLs.stream().filter(a -> a.getArtId().equals(packagePrice.getArtId())).findFirst().orElse(new ArticleEntity());
                            BigDecimal total = Convert.toBigDecimal(packagePrice.getTotal(), BigDecimal.ONE);
                            BigDecimal price = Convert.toBigDecimal(packagePrice.getPrice(), BigDecimal.ZERO);
                            createPostpaidBillDetail(lineNo, orgId, postpaidArt.getArtId(), total, price, packagePrice.getAmount(), packagePrice.getDerated(),
                                    packagePrice.getDiscounted(), Convert.toInt(packagePrice.getUnitType(), UnitType.Pack_Unit.getValue()),
                                    packagePrice.getUnit(), detailArt, artClassLs, orgItemPriceLs, billDetailLs);
                            lineNo++;
                        }
                    }
                } else if (orgItemPrice != null && orgItemPrice.getUnitPrice() != null && orgItemPrice.getCheckedFlag() != null && orgItemPrice.getCheckedFlag() == 1) {
                    BigDecimal total = postpaidArt.getTotal() == null || postpaidArt.getTotal().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : postpaidArt.getTotal();
                    BigDecimal price = orgItemPrice.getUnitPrice() == null ? BigDecimal.ZERO : orgItemPrice.getUnitPrice();
                    createPostpaidBillDetail(lineNo, orgId, null, total, price, null, null, null, UnitType.Pack_Unit.getValue(),
                            article.getPackUnit(), article, artClassLs, orgItemPriceLs, billDetailLs);
                    lineNo++;
                }
            }
            if (ObjectUtil.isNotEmpty(billDetailLs)) {
                BigDecimal totalAmount = billDetailLs.stream().map(BillDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal derated = billDetailLs.stream().map(BillDetailEntity::getDerated).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal discounted = billDetailLs.stream().map(BillDetailEntity::getDiscounted).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal amount = totalAmount.subtract(derated).subtract(discounted);
                // 划价主表
                BillEntity bill = BillEntity.builder()
                        .visitId(visit.getVisitId())
                        .orgId(orgId)
                        .userId(userId)
                        .clinicianId(visit.getClinicianId())
                        .patientId(visit.getPatientId())
                        .patientName(visit.getPatientName())
                        .billTypeId(BillType.otherBill.getValue())
                        .billAbstract("先诊后付费用")
                        .applyDeptcode(visit.getDeptCode())
                        .execDeptcode(visit.getDeptCode())
                        .medTypeId(visit.getMedTypeId() == null ? MedicalType.normalOutpatient.getValue() : visit.getMedTypeId())
                        .clinicTypeId(Convert.toInt(visit.getClinicTypeId(), ClinicType.outpatient.getValue()))
                        .insuranceTypeId(visit.getInsuranceTypeId())
                        .paidStatus(PaidStatus.toPaid.getValue())
                        .billDate(DateUtil.getTodayInt())
                        .timeCreated(new Date())
                        .times(1)
                        .discounted(discounted)
                        .derated(derated)
                        .totalAmount(totalAmount)
                        .amount(amount)
                        .build();
                Long bseqid = billService.saveToPaidBill(bill, billDetailLs);
                LambdaUpdateWrapper<VisitEntity> visitWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
                visitWrapper.eq(VisitEntity::getVisitId, visit.getVisitId())
                        .set(VisitEntity::getPostpaidBseqid, bseqid);
                visitService.update(visitWrapper);
                return bseqid;
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long cancelDeptPostpaidBill(Long visitId) {
        if (visitId != null) {
            LambdaUpdateWrapper<VisitEntity> wrapper = Wrappers.lambdaUpdate(VisitEntity.class);
            wrapper.eq(VisitEntity::getVisitId, visitId)
                    .isNull(VisitEntity::getPostpaidBseqid)
                    .set(VisitEntity::getPostpaidBseqid, -1);
            visitService.update(wrapper);
            return -1L;
        }
        return null;
    }

    private void createPostpaidBillDetail(Integer lineNo, Long orgId, Long packageId, BigDecimal total, BigDecimal price,
                                          BigDecimal cost, BigDecimal derated, BigDecimal discounted,
                                          Integer unitType, String unit, ArticleEntity article, List<ArtClassEntity> artClassLs,
                                          List<OrgItemPriceEntity> orgItemPriceLs, List<BillDetailEntity> billDetailLs) {
        Integer feeTypeId = hsdBaseService.getFeeTypeId(article, artClassLs);
        Integer statsCatId = hsdBaseService.getStatsCatId(article.getArtClassId(), artClassLs);
        Integer udfTypeId = hsdBaseService.getUdfTypeId(orgId, article.getArtId(), feeTypeId, orgItemPriceLs);
        int packCells = article.getPackCells() == null ? 1 : article.getPackCells();
        BigDecimal detailTotal = BigDecimal.ZERO;
        BigDecimal detailPrice = BigDecimal.ZERO;
        if (unitType.equals(UnitType.Pack_Unit.getValue())) {
            detailTotal = total.multiply(BigDecimal.valueOf(packCells));
        }
        BigDecimal amount = total.multiply(price).setScale(2, RoundingMode.HALF_UP);
        if (detailTotal.compareTo(BigDecimal.ZERO) != 0) {
            detailPrice = amount.divide(detailTotal, 6, RoundingMode.HALF_UP);
        }
        BillDetailEntity billDetail = BillDetailEntity.builder()
                .lineNo(lineNo)
                .artId(article.getArtId())
                .packageId(packageId)
                .baseTotal(detailTotal)
                .total(detailTotal)
                .price(detailPrice)
                .amount(amount)
                .cost(cost)
                .derated(derated)
                .discounted(discounted)
                .times(1)
                .unit(unit)
                .derated(BigDecimal.ZERO)
                .discounted(BigDecimal.ZERO)
                .feeTypeId(feeTypeId)
                .statsCatId(statsCatId)
                .isEd(article.getIsEd() != null && article.getIsEd().equals(1))
                .nonMedicalFlag(orgNonmedicalArtService.isNonMedicalArt(orgId, article.getArtId()))
                .packCells(packCells)
                .udfTypeId(udfTypeId)
                .build();
        if (unitType.equals(UnitType.Pack_Unit.getValue())) {
            billDetail.setPackTotal(total);
            billDetail.setPackPrice(price);
        } else if (unitType.equals(UnitType.Cell_Unit.getValue())) {
            billDetail.setCellTotal(total);
            billDetail.setCellPrice(price);
        }
        billDetailLs.add(billDetail);
    }

    private void saveEhrVisit(Long visitId) {
        VisitDto visit = visitService.findById(visitId, false);
        // 诊疗信息
        EhrVisitData ehrVisit = EhrVisitData.builder()
                .pid(visit.getPatientId())
                .admissionTime(visit.getTimeAdmission())
                .ageOfYears(visit.getAgeOfYears())
                .ageOfDays(Convert.toInt(visit.getAgeOfDays()))
                .genderId(visit.getGenderId())
                .clinicianCode(visit.getClinicianNo())
                .clinicianName(visit.getClinicianName())
                .weightKg(visit.getWeightKg())
                .heightCm(visit.getHeightCm() != null ? BigDecimal.valueOf(visit.getHeightCm()) : null)
                .dischargeTime(visit.getTimeDischarged())
                .status(visit.getVisitStatus().equals(VisitStatus.outHospital.getValue()) ? 3 : 2)
                .visitTypeId(visit.getOcFlag() != null && visit.getOcFlag() == 1 ? VisitType.visit.getValue() : VisitType.apt.getValue())
                .orgId(visit.getOrgId())
                .deptCode(visit.getDeptCode())
                .orgDeptCode(visit.getDeptCode())
                .deptName(visit.getDeptName())
                .srcId(visit.getVisitId() + "")
                .build();
        // 诊疗其他信息
        EhrVisitExtraData ehrVisitExtra = EhrVisitExtraData.builder()
                .complainedAs(visit.getComplainedAs())
                .accompanyBy(visit.getCompanionName())
                .contactTel(visit.getContactTel())
                .livingAddr(visit.getLivingAddr())
                .treatAbstract(visit.getTreatAbstract())
                .orgCode(visit.getOrgCode())
                .orgName(visit.getOrgName())
                .build();
        // 诊疗诊断记录
        List<EhrVisitDiagData> ehrVisitDiagLs = new ArrayList<>();
        for (VisitDiagDto diagEntity : visit.getVisitDiagLs()) {
            EhrVisitDiagData ehrVisitDiag = EhrVisitDiagData.builder()
                    .diagNo(diagEntity.getDiagNo())
                    .diagCode(diagEntity.getDiagCode())
                    .diagName(diagEntity.getDiagName())
                    .diagTypeId(diagEntity.getDiagTypeId().equals(DiagType.tcmMainDiag.getValue()) ? 3 : diagEntity.getDiagTypeId().equals(DiagType.tcmOtherDiag.getValue()) ? 4 : 1)
                    .diagStatus(diagEntity.getDiagStatus())
                    .diagTime(diagEntity.getTimeDiagnosed())
                    .removeTime(diagEntity.getTimeDenied())
                    .build();
            ehrVisitDiagLs.add(ehrVisitDiag);
        }
        EhrVisitFullData fullModel = new EhrVisitFullData();
        fullModel.setEhrVisitModel(ehrVisit);
        fullModel.setEhrVisitExtraModel(ehrVisitExtra);
        fullModel.setDiagModelList(ehrVisitDiagLs);
        remoteEhrService.saveEhrVisit(fullModel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishedVisit(Long visitId) {
        // 删除未结诊记录表
        visitPendingService.removeById(visitId);
        // 修改患者状态
        VisitDto visit = visitService.findById(visitId, true);
        LambdaUpdateWrapper<VisitEntity> visitWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
        visitWrapper.eq(VisitEntity::getVisitId, visitId);
        visitWrapper.set(VisitEntity::getVisitStatus, VisitStatus.outHospital.getValue());
        visitWrapper.set(VisitEntity::getTimeCompleted, new Date());
        visitService.update(visitWrapper);
        // 添加门诊诊断记录
        opcDiagService.remove(Wrappers.lambdaQuery(OpcDiagEntity.class).eq(OpcDiagEntity::getVisitId, visitId));
        if (visit.getClinicTypeId() == ClinicType.outpatient.getValue()) {
            List<VisitDiagEntity> diagLs = visitDiagService.list(
                    new LambdaQueryWrapper<VisitDiagEntity>()
                            .eq(VisitDiagEntity::getVisitId, visitId)
                            .ne(VisitDiagEntity::getDiagStatus, DiagStatus.removed.getValue())
                            .orderByAsc(VisitDiagEntity::getDiagNo)
            );
            if (!diagLs.isEmpty()) {
                List<OpcDiagEntity> opcDiagLs = new ArrayList<>();
                int displayOrder = 0;
                for (VisitDiagEntity visitDiag : diagLs) {
                    OpcDiagEntity opcDiag = OpcDiagEntity.builder()
                            .visitId(visitId)
                            .diagNo(visitDiag.getDiagNo())
                            .displayOrder(displayOrder)
                            .build();
                    opcDiagLs.add(opcDiag);
                }
                opcDiagService.saveBatch(opcDiagLs);
            }
        }
        // 推送EHR完成
//        try {
//            remoteEhrService.finishEhrVisit(visit.getOrgId(), visit.getVisitId() + "", visit.getOcFlag() != null && visit.getOcFlag() == 1 ? VisitType.visit.getValue() : VisitType.apt.getValue());
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("EHR推送失败", e);
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveVisitTemplate(Long orgId, Long userId, Long visitId, String templateName, String keyWords, String templateDesc, Integer shareFlag) {
        if (visitId != null) {
            VisitDto visit = visitService.findById(visitId, false);
            // 模版数据
            CdaTemplateData templateData = CdaTemplateData.builder()
                    .orgId(visit.getOrgId())
                    .orgName(visit.getOrgName())
                    .clinicianId(visit.getClinicianId())
                    .clinicianName(visit.getClinicianName())
                    .deptCode(visit.getDeptCode())
                    .deptName(visit.getDeptName())
                    .complainedAs(visit.getComplainedAs())
                    .hpiDesc(visit.getHpiDesc())
                    .pastHistory(visit.getPastHistory())
                    .familyHistory(visit.getFamilyHistory())
                    .allergicHistory(visit.getAllergicHistory())
                    .generalInspection(visit.getGeneralInspection())
                    .differentialDiag(visit.getDifferentialDiag())
                    .treatAbstract(visit.getTreatAbstract())
                    .healthEducation(visit.getHealthEducation())
                    .build();
            // 过敏史
            List<VisitAllergyEntity> visitAllergyLs = visitAllergyService.list(Wrappers.lambdaQuery(VisitAllergyEntity.class).eq(VisitAllergyEntity::getVisitId, visitId));
            templateData.setVisitAllergyLs(visitAllergyLs);
            // 疾病史
            List<VisitIllnessEntity> visitIllnessLs = visitIllnessService.list(Wrappers.lambdaQuery(VisitIllnessEntity.class).eq(VisitIllnessEntity::getVisitId, visitId));
            templateData.setVisitIllnessLs(visitIllnessLs);
            // 诊断列表
            List<VisitDiagDto> visitDiagLs = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(visit.getVisitDiagLs())) {
                visitDiagLs = visit.getVisitDiagLs().stream().filter(p -> p.getDiagStatus() != null && !p.getDiagStatus().equals(DiagStatus.removed.getValue())).collect(Collectors.toList());
            }
            templateData.setVisitDiagLs(visitDiagLs);
            String contentUrl = "";
            try {
                String content = JSONUtil.toJsonStr(templateData);
                log.debug("模版内容: {}", content);
                contentUrl = commonService.saveRichText("visit.temp." + orgId + StrUtil.DOT + cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMdd"),
                        visitId + StrUtil.UNDERLINE + idService.getSnowflakeId(), content, true);
                if (StrUtil.isBlank(contentUrl)) {
                    throw new RuntimeException("模版内容保存失败");
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            //
            Integer subjectId = null;
            if (StrUtil.isNotBlank(visit.getDeptCode())) {
                OrgDeptEntity orgDept = orgDeptService.findById(orgId, visit.getDeptCode());
                if (orgDept != null && orgDept.getSubjectId() != null) {
                    subjectId = orgDept.getSubjectId();
                }
            }
            CdaTemplateEntity cdaTemp = CdaTemplateEntity.builder()
                    .orgId(orgId)
                    .userId(userId)
                    .clinicTypeId(visit.getClinicTypeId())
                    .subjectId(subjectId)
                    .templateName(templateName)
                    .keyWords(keyWords)
                    .templateDesc(templateDesc)
                    .shareFlag(shareFlag == null ? 0 : shareFlag)
                    .contentUrl(contentUrl)
                    .build();
            cdaTemplateService.saveEntity(cdaTemp);
        }
    }

    private Integer getPsnTypeIdByCode(Integer psnTypeId, String psnTypeCode) {
        if (StrUtil.isNotBlank(psnTypeCode)) {
            List<PersonTypeEntity> list = personTypeService.list(Wrappers.lambdaQuery(PersonTypeEntity.class).eq(PersonTypeEntity::getPsnTypeCode, psnTypeCode));
            return list.isEmpty() ? null : list.get(0).getPsnTypeId();
        } else if (psnTypeId != null) {
            return psnTypeId;
        }
        return null;
    }

    /**
     * 住院证申请
     * @param orgId     机构ID
     * @param userId    用户ID
     * @param iprVo     住院证申请信息
     * @return 住院申请流水号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveIprRecord(Long orgId, Long userId, IprVo iprVo) {
        log.debug("保存住院申请记录: {}", JSONUtil.toJsonStr(iprVo));
        Long iprId = iprVo.getIprId();
        if (iprId == null) {
            // 有诊疗信息直接调用保存的方法
            if (iprVo.getVisitId() != null) {
                VisitEntity visit = visitService.getById(iprVo.getVisitId());
                if (visit == null) {
                    throw new SaveFailureException("未找到诊疗记录信息");
                }
                IprRecordEntity iprRecord = new IprRecordEntity();
                BeanUtil.copyProperties(visit, iprRecord);
                iprRecord.setOrgId(orgId);
                iprRecord.setAdmissionWayId(2); // 默认为普通入院
                iprRecord.setInpatientTypeId(1); // 默认为普通住院
                if (StrUtil.isNotBlank(iprVo.getTimeAdmission())) {
                    iprRecord.setTimeAdmission(DateUtil.toDateTime(iprVo.getTimeAdmission(), "yyyy-MM-dd HH:mm:ss"));
                }
                iprId = iprRecordService.saveEntity(iprRecord);
            } else {
                Long patientId = iprVo.getPatientId();
                if (patientId == null) {
                    // 创建患者信息
                    patientId = createPatient(userId, orgId, iprVo.getPatientName(), iprVo.getCertTypeId(), iprVo.getIdcertNo(), iprVo.getTelNo(), iprVo.getGenderId());
                }
                // 创建住院申请记录
                IprRecordEntity iprRecord = new IprRecordEntity();
                BeanUtil.copyProperties(iprVo, iprRecord);
                iprRecord.setPatientId(patientId);
                iprRecord.setOrgId(orgId);
                iprRecord.setAdmissionWayId(2); // 默认为普通入院
                iprRecord.setInpatientTypeId(1); // 默认为普通住院
                if (StrUtil.isNotBlank(iprVo.getTimeAdmission())) {
                    iprRecord.setTimeAdmission(DateUtil.toDateTime(iprVo.getTimeAdmission(), "yyyy-MM-dd HH:mm:ss"));
                }
                // 保存住院申请记录
                iprId = iprRecordService.saveEntity(iprRecord);
            }
        } else {
            IprRecordEntity iprRecord = iprRecordService.getById(iprId);
            iprRecord.setDeptCode(iprVo.getDeptCode());
            iprRecord.setClinicianId(iprVo.getClinicianId());
            iprRecord.setClinicDate(iprVo.getClinicDate());
            iprRecord.setIsEmergency(iprVo.getIsEmergency());
            iprRecord.setNotes(iprVo.getNotes());
            iprRecord.setOpDiseaseCode(iprVo.getOpDiseaseCode());
            iprRecord.setOpDiseaseName(iprVo.getOpDiseaseName());
            iprRecord.setOpTcmDiseaseCode(iprVo.getOpTcmDiseaseCode());
            iprRecord.setOpTcmDiseaseName(iprVo.getOpTcmDiseaseName());
            iprRecord.setOpTcmSymptomCode(iprVo.getOpTcmSymptomCode());
            iprRecord.setOpTcmSymptomName(iprVo.getOpTcmSymptomName());
            iprRecord.setHospitalizedCause(iprVo.getHospitalizedCause());
            if (StrUtil.isNotBlank(iprVo.getTimeAdmission())) {
                iprRecord.setTimeAdmission(DateUtil.toDateTime(iprVo.getTimeAdmission(), "yyyy-MM-dd HH:mm:ss"));
            }
            iprRecordService.updateById(iprRecord);
        }
        return iprId;
    }

    private Long createPatient(Long userId, Long orgId, String patientName, Integer certTypeId, String idcertNo, String telNo, Integer genderId) {
        Long patientId = null;
        if (StrUtil.isNotBlank(idcertNo) && ValidatorUtil.isIdCard(idcertNo)) {
            PatientVo patient = new PatientVo();
            patient.setOrgId(orgId);
            patient.setPatientName(patientName);
            patient.setGenderId(genderId);
            patient.setBirthDate(certTypeId != null && certTypeId == CertType.jmsfz.getValue() ? Convert.toInt(IdcardUtil.getBirthByIdCard(idcertNo)) : null);
            patient.setCertTypeId(certTypeId);
            patient.setIdcertNo(idcertNo);
            patient.setTelNo(telNo);
            patient.setContactorName(patientName);
            patient.setContactorTel(telNo);
            patient.setRelationshipId(1);
            patientId = hsdPatientService.savePatient(orgId, userId, false, patient);
        }
        return patientId;
    }
}
