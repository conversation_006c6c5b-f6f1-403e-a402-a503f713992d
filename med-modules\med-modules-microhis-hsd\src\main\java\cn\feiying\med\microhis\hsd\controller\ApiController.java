package cn.feiying.med.microhis.hsd.controller;

import cn.feiying.med.aspect.annotation.ApiLog;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.GQueryWrapper;
import cn.feiying.med.common.utils.R;
import cn.feiying.med.common.validator.ValidatorUtils;
import cn.feiying.med.common.validator.group.DefaultGroup;
import cn.feiying.med.common.validator.group.EditGroup;
import cn.feiying.med.common.validator.group.SaveGroup;
import cn.feiying.med.hip.enums.DiagStatus;
import cn.feiying.med.hip.enums.DiagType;
import cn.feiying.med.hip.enums.MtaOrderType;
import cn.feiying.med.hip.mdi.entity.UserCodeEntity;
import cn.feiying.med.hip.mdi.service.UserCodeService;
import cn.feiying.med.his.moe.service.DrugReqService;
import cn.feiying.med.microhis.bcs.entity.BillDetailEntity;
import cn.feiying.med.microhis.bcs.entity.BillEntity;
import cn.feiying.med.microhis.bcs.entity.RecipeIndexEntity;
import cn.feiying.med.microhis.bcs.service.BillDetailService;
import cn.feiying.med.microhis.bcs.service.BillService;
import cn.feiying.med.microhis.bcs.service.RecipeIndexService;
import cn.feiying.med.microhis.hsd.dto.RecipeDto;
import cn.feiying.med.microhis.hsd.dto.VisitDiagDto;
import cn.feiying.med.microhis.hsd.dto.VisitDto;
import cn.feiying.med.microhis.hsd.entity.OrderEntity;
import cn.feiying.med.microhis.hsd.entity.RecipeEntity;
import cn.feiying.med.microhis.hsd.entity.VisitDiagEntity;
import cn.feiying.med.microhis.hsd.entity.VisitEntity;
import cn.feiying.med.microhis.hsd.service.*;
import cn.feiying.med.microhis.hsd.vo.*;
import cn.feiying.med.saas.api.model.CvrEventModel;
import cn.feiying.med.saas.api.model.OrderReportModel;
import cn.feiying.med.saas.api.model.ReturnRecipeArtItem;
import cn.feiying.med.saas.api.model.ReturnRecipeArtModel;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api")
public class ApiController extends AbstractController {
    @Resource
    private RecipeService recipeService;
    @Resource
    private OrderService orderService;
    @Resource
    private BillService billService;
    @Resource
    private BillDetailService billDetailService;
    @Resource
    private RecipeIndexService recipeIndexService;
    @Resource
    private HsdRegService hsdRegService;
    @Resource
    private DrugReqService drugReqService;
    @Resource
    private VisitService visitService;
    @Resource
    private HsdOrderService hsdOrderService;
    @Resource
    private VisitDiagService visitDiagService;
    @Resource
    private UserCodeService userCodeService;
    @Resource
    private HsdVisitService hsdVisitService;
    @Resource
    private HsdRecipeService hsdRecipeService;
    @Resource
    private IprRecordService iprRecordService;

    @ApiLog("处方退货")
    @PostMapping("/recipeReturnArt")
    public R<?> recipeReturnArt(@RequestBody ReturnRecipeArtModel params) {
        RecipeDto recipeEntity = recipeService.findByRxNo(params.getOrgId(), params.getRxNo());
        if (recipeEntity == null) {
            return R.error("没有找到对应的处方信息");
        }
        RecipeIndexEntity recipeIndexEntity = recipeIndexService.getById(recipeEntity.getRecipeId());
        BillEntity billEntity = billService.getById(recipeIndexEntity.getBseqid());
        List<BillDetailEntity> billDetails = billDetailService.findByMainId(billEntity.getBseqid());

        List<BillDetailEntity> returnDetails = new ArrayList<>();
        int lineNo = 1;
        for (ReturnRecipeArtItem item : params.getDetails()) {
            for (BillDetailEntity billDetailEntity : billDetails) {
                if (billDetailEntity.getArtId().equals(item.getArtId())) {
                    BillDetailEntity returnDetail = new BillDetailEntity();
                    returnDetail.setArtId(billDetailEntity.getArtId());
                    returnDetail.setLineNo(lineNo++);
                    returnDetail.setUnit(billDetailEntity.getUnit());
                    returnDetail.setTotal(item.getTotal());
                    returnDetail.setPrice(billDetailEntity.getPrice());
                    returnDetail.setAmount(billDetailEntity.getPrice().multiply(item.getTotal()).negate());
                    returnDetail.setPackTotal(item.getPackTotal());
                    returnDetail.setCellTotal(item.getCellTotal());
                    returnDetail.setPackageId(billDetailEntity.getPackageId());
                    returnDetail.setFeeTypeId(billDetailEntity.getFeeTypeId());
                    returnDetail.setPackPrice(billDetailEntity.getPackPrice());
                    returnDetail.setCellPrice(billDetailEntity.getCellPrice());
                    returnDetail.setIsEd(billDetailEntity.getIsEd());
                    returnDetail.setNonMedicalFlag(billDetailEntity.getNonMedicalFlag());
                    returnDetails.add(returnDetail);
                }
            }
        }
        billService.recipeReturnArt(billEntity, returnDetails, "处方退药");
        return R.ok();
    }

    @ApiLog("心理评测撤销")
    @PostMapping("/refundPsyOrder")
    public R<?> refundPsyOrder(@RequestBody JSONObject params) {
        log.info("refundPsyOrder params:{}", JSONUtil.toJsonStr(params));
        Long orderId = Convert.toLong(params.get("orderId"));
        if (orderId == null) {
            return R.error("请指定申请单编号");
        }
        OrderEntity order = orderService.getById(orderId);
        if (order == null) {
            return R.error("未找到对应的申请单");
        }
        if (order.getOrderTypeId() == null || !order.getOrderTypeId().equals(MtaOrderType.psychologicalCT.getValue())) {
            return R.error("申请单类型不是心理评测");
        }
        RecipeDto recipe = recipeService.findById(order.getRecipeId());
        if (recipe == null) {
            return R.error("没有找到对应的处方信息");
        }
        RecipeIndexEntity recipeIndexEntity = recipeIndexService.getById(recipe.getRecipeId());
        BillEntity oriBill = billService.getById(recipeIndexEntity.getBseqid());
        List<BillDetailEntity> oriBillDetailLs = billDetailService.findByMainId(oriBill.getBseqid());

        List<BillDetailEntity> returnDetails = new ArrayList<>();
        int lineNo = 1;
        for (BillDetailEntity oriBillDetail : oriBillDetailLs) {
            BillDetailEntity returnDetail = new BillDetailEntity();
            returnDetail.setArtId(oriBillDetail.getArtId());
            returnDetail.setLineNo(lineNo++);
            returnDetail.setUnit(oriBillDetail.getUnit());
            returnDetail.setBaseTotal(oriBillDetail.getBaseTotal());
            returnDetail.setTimes(oriBillDetail.getTimes());
            returnDetail.setUnit(oriBillDetail.getUnit());
            returnDetail.setTotal(oriBillDetail.getTotal());
            returnDetail.setPrice(oriBillDetail.getPrice());
            returnDetail.setAmount(oriBillDetail.getAmount().abs().negate());
            returnDetail.setPackageId(oriBillDetail.getPackageId());
            returnDetail.setFeeTypeId(oriBillDetail.getFeeTypeId());
            returnDetail.setIsEd(oriBillDetail.getIsEd());
            returnDetail.setNonMedicalFlag(oriBillDetail.getNonMedicalFlag());
            returnDetails.add(returnDetail);
        }
        billService.recipeReturnArt(oriBill, returnDetails, "心理评测撤销");
        return R.ok();
    }

    /**
     * 获取支付二维码参数
     */
    @ApiLog("获取支付二维码参数")
    @PostMapping("/getPayCodeParams")
    public R<?> getPayCodeParams(@RequestBody Map<String, Object> params) {
        Long visitId = Convert.toLong(params.get("visitId"));
        if (visitId == null) {
            return R.error("要支付的诊疗记录不能为空。");
        }

        Map<String, Object> map = hsdRegService.getPayCodeParams(visitId);

        return R.ok(map);
    }

    @PostMapping("/drugReqDispensing")
    public R<?> drugReqDispensing(@RequestBody Map<String, Object> params) {
        Long drugReqId = Convert.toLong(params.get("drugReqId"));
        if (drugReqId == null) {
            throw new SaveFailureException("请指定领药申请流水号");
        }
        drugReqService.drugReqDispensing(drugReqId);
        return R.ok();
    }

    @PostMapping("/drugReqDeliver")
    public R<?> drugReqDeliver(@RequestBody Map<String, Object> params) {
        Long drugReqId = Convert.toLong(params.get("drugReqId"));
        if (drugReqId == null) {
            throw new SaveFailureException("请指定领药申请流水号");
        }
        drugReqService.drugReqDeliver(drugReqId);
        return R.ok();
    }

    /**
     * 退号
     */
    @PostMapping("/rejectedReg")
    public R<?> rejectedReg(@RequestBody Map<String, Object> params) {
        Long regId = Convert.toLong(params.get("regId"));
        String rejectedNotes = Convert.toStr(params.get("rejectedNotes"));
        RegRefundData data = hsdRegService.rejectedReg(null, regId, rejectedNotes);
        return R.ok(data);
    }

    /**
     * 更新就诊信息
     */
    @PostMapping("/updateVisit")
    public R<?> updateVisit(@RequestBody VisitVo visit) {
        ValidatorUtils.validateEntity(visit, EditGroup.class, DefaultGroup.class);
        Long userId = getUserIdByClinicianId(visit.getClinicianId());
        Long visitId = hsdVisitService.updateVisit(userId, visit);

        return R.ok(visitId);
    }

    /**
     * 保存诊断
     */
    @PostMapping("/saveVisitDiag")
    public R<?> saveVisitDiag(@RequestBody VisitDiagVo visitDiag) {
        ValidatorUtils.validateEntity(visitDiag, DefaultGroup.class);
        Long userId = getUserIdByClinicianId(visitDiag.getClinicianId());
        List<VisitDiagDto> visitDiagLs = visitDiagService.saveBatchEntity(visitDiag.getVisitId(), userId, visitDiag.getClinicianId(),
                visitDiag.getDeptCode(), visitDiag.getVisitDiagLs());
        return R.ok(visitDiagLs);
    }

    /**
     * 保存诊断
     */
    @PostMapping("/delVisitDiags")
    public R<?> delVisitDiags(@RequestBody Map<String, Object> params) {
        List<VisitDiagEntity> visitDiagLs = Convert.toList(VisitDiagEntity.class, params.get("visitDiagLs"));
        visitDiagService.delBatchVisitDiag(visitDiagLs);
        return R.ok();
    }

    /**
     * 获取诊断信息
     */
    @PostMapping("/findVisitDiagLs")
    public R<?> findVisitDiagLs(@RequestBody Map<String, Object> params) {
        List<Long> visitIdLs = Convert.toList(Long.class, params.get("visitIdLs"));
        QueryWrapper<VisitDiagDto> diagWrapper = new GQueryWrapper<VisitDiagDto>().getWrapper();
        diagWrapper.in("t_visit_diag.Visit_ID", visitIdLs);
        diagWrapper.ne("t_visit_diag.Diag_Status", DiagStatus.removed.getValue());
        List<VisitDiagDto> visitList = visitDiagService.findByWrapper(diagWrapper);
        return R.ok(visitList);
    }

    /**
     * 根据处方号查询处方
     */
    @PostMapping("/findRecipeByRxNo")
    public R<?> findRecipeByRxNo(@RequestBody Map<String, Object> params) {
        List<String> rxNoLs = Convert.toList(String.class, params.get("rxNoLs"));
        return R.ok(recipeService.findRecipeByRxNo(rxNoLs));
    }

    /**
     * 保存处方
     */
    @PostMapping("/saveRecipe")
    public R<?> saveRecipe(@RequestBody Map<String, Object> params) {
        List<RecipeVo> recipeLs = Convert.toList(RecipeVo.class, params.get("recipeLs"));
        if (ObjectUtil.isEmpty(recipeLs)) {
            return R.error("请填写处方信息。");
        }
        Long clinicianId = Convert.toLong(params.get("clinicianId"));
        for (RecipeVo recipeVo : recipeLs) {
            if (recipeVo.getRecipeId() == null) {
                ValidatorUtils.validateEntity(recipeVo, SaveGroup.class, DefaultGroup.class);
            } else {
                ValidatorUtils.validateEntity(recipeVo, EditGroup.class, DefaultGroup.class);
            }
        }
        Long userId = getUserIdByClinicianId(clinicianId);
        List<RecipeDto> list = recipeService.saveBatchRecipe(userId, recipeLs);
        return R.ok(list);
    }

    /**
     * 签名处方
     */
    @PostMapping("/signRecipe")
    public R<?> signRecipe(@RequestBody Map<String, Object> params) {
        Long orgId = Convert.toLong(params.get("orgId"));
        Long clinicianId = Convert.toLong(params.get("clinicianId"));
        Long certId = Convert.toLong(params.get("certId"));
        Long userId = getUserIdByClinicianId(clinicianId);
        Integer sectionId = Convert.toInt(params.get("sectionId"));
        String storeId = Convert.toStr(params.get("storeId"));
        List<Long> recipeIdLs = Convert.toList(Long.class, params.get("recipeIdLs"));
        hsdRecipeService.clinicianSign(orgId, userId, certId, storeId, null, sectionId, null,
                true, true, recipeIdLs, null);
        return R.ok();
    }

    /**
     * 取消签名
     */
    @PostMapping("/withdrawRecipeSign")
    public R<?> withdrawRecipeSign(@RequestBody Map<String, Object> params) {
        List<Long> recipeIdLs = Convert.toList(Long.class, params.get("recipeIdLs"));

        hsdRecipeService.withdrawRecipeSign(recipeIdLs);
        return R.ok();
    }

    /**
     * 取消处方
     */
    @PostMapping("/cancelRecipe")
    public R<?> cancelRecipe(@RequestBody Map<String, Object> params) {
        Long userId = Convert.toLong(params.get("userId"));
        List<Long> recipeIdLs = Convert.toList(Long.class, params.get("recipeIdLs"));
        hsdRecipeService.batchCancelRecipe(userId, recipeIdLs);
        return R.ok();
    }

    /**
     * 批量更新处方状态
     */
    @PostMapping("/updateRecipeStatus")
    public R<?> updateRecipeStatus(@RequestBody Map<String, Object> params) {
        Integer paidStatus = Convert.toInt(params.get("paidStatus"));
        Integer execStataus = Convert.toInt(params.get("execStataus"));
        List<Long> recipeIdLs = Convert.toList(Long.class, params.get("recipeIdLs"));
        hsdRecipeService.updateRecipeStatus(paidStatus, execStataus, recipeIdLs);
        return R.ok();
    }

    /**
     * 根据订单号查询订单信息
     */
    @PostMapping("/findOrderByOrderNo")
    public R<?> findOrderByOrderNo(@RequestBody Map<String, Object> params) {
        List<String> accessionNoLs = Convert.toList(String.class, params.get("accessionNoLs"));
        return R.ok(orderService.findLsByAccessionNo(accessionNoLs));
    }

    /**
     * 保存电子申请单
     */
    @PostMapping("/saveOrder")
    public R<?> saveOrder(@RequestBody Map<String, Object> params) {
        List<OrderVo> orderLs = Convert.toList(OrderVo.class, params.get("orderLs"));
        if (ObjectUtil.isEmpty(orderLs)) {
            return R.error("请填写申请单信息。");
        }
        Long clinicianId = Convert.toLong(params.get("clinicianId"));
        Long userId = getUserIdByClinicianId(clinicianId);
        List<OrderEntity> list = orderService.saveBatchOrder(userId, orderLs);
        return R.ok(list);
    }

    /**
     * 签名申请单
     */
    @PostMapping("/signOrder")
    public R<?> signOrder(@RequestBody Map<String, Object> params) {
        Long orgId = Convert.toLong(params.get("orgId"));
        Long clinicianId = Convert.toLong(params.get("clinicianId"));
        Long certId = Convert.toLong(params.get("certId"));
        Long userId = getUserIdByClinicianId(clinicianId);
        String storeId = Convert.toStr(params.get("storeId"));
        List<Long> orderIdLs = Convert.toList(Long.class, params.get("orderIdLs"));
        hsdOrderService.signedOrder(orgId, userId, certId, storeId, null, orderIdLs);
        return R.ok();
    }

    /**
     * 取消申请单签名
     */
    @PostMapping("/withdrawSignedOrder")
    public R<?> withdrawSignedOrder(@RequestBody Map<String, Object> params) {
        List<Long> orderIdLs = Convert.toList(Long.class, params.get("orderIdLs"));

        hsdOrderService.withdrawSingedOrder(orderIdLs);
        return R.ok();
    }

    /**
     * 批量取消申请单
     */
    @PostMapping("/cancelOrder")
    public R<?> cancelOrder(@RequestBody Map<String, Object> params) {
        Long userId = Convert.toLong(params.get("userId"));
        List<Long> orderIdLs = Convert.toList(Long.class, params.get("orderIdLs"));
        hsdOrderService.batchWithdrawOrder(userId, orderIdLs);
        return R.ok();
    }

    /**
     * 删除申请单
     */
    @PostMapping("/delOrder")
    public R<?> delOrder(@RequestBody Map<String, Object> params) {
        Long userId = Convert.toLong(params.get("userId"));
        List<Long> orderIdLs = Convert.toList(Long.class, params.get("orderIdLs"));
        hsdOrderService.deleteOrder(userId, orderIdLs);
        return R.ok();
    }

    /**
     * 保存电子住院证
     */
    @PostMapping("/saveIprRecord")
    public R<?> saveIprRecord(@RequestBody IprVo iprVo) {
        Long userId = getUserIdByClinicianId(iprVo.getClinicianId());
        Long  iprId = hsdVisitService.saveIprRecord(iprVo.getOrgId(), userId, iprVo);
        return R.ok(iprId);
    }

    /**
     * 取消电子住院证
     */
    @PostMapping("/cancelIprRecord")
    public R<?> cancelIprRecord(@RequestBody Map<String, Object> params) {
        Long userId = Convert.toLong(params.get("userId"));
        List<Long> iprIdLs = Convert.toList(Long.class, params.get("iprIdLs"));
        iprRecordService.cancelEntity(userId, iprIdLs);
        return R.ok();
    }

    private Long getUserIdByClinicianId(Long clinicianId) {
        Long userId = null;
        if (clinicianId != null) {
            List<UserCodeEntity> userCodeLs = userCodeService.list(Wrappers.lambdaQuery(UserCodeEntity.class).eq(UserCodeEntity::getClinicianId, clinicianId).ne(UserCodeEntity::getUserId, 0));
            if (ObjectUtil.isNotEmpty(userCodeLs)) {
                userId = userCodeLs.get(0).getUserId();
            }
        }
        return userId;
    }

    /**
     * 更新订单状态
     */
    @PostMapping("/updateOrderStatus")
    public R<?> updateOrderStatus(@RequestBody Map<String, Object> params) {
        Long orderId = Convert.toLong(params.get("orderId"));
        Integer status = Convert.toInt(params.get("status"));
        if (orderId != null) {
            hsdOrderService.updateOrderStatus(orderId, status);
        } else {
            List<OrderReportVo> orderReporLs = Convert.toList(OrderReportVo.class, params.get("orderLs"));
            hsdOrderService.updateOrderStatus(orderReporLs);
        }
        return R.ok();
    }

    /**
     * 保存危急值
     */
    @PostMapping("/saveCvrEvent")
    public R<?> saveCvrEvent(@RequestBody CvrEventModel vo) {
        hsdOrderService.saveCvrEvent(vo);
        return R.ok();
    }

    /**
     * 保存检查报告
     */
    @PostMapping("/saveInspectionOrderReport")
    public R<?> saveInspectionOrderReport(@RequestBody OrderReportModel orderReport) {
        hsdOrderService.saveInspectionOrderReport(orderReport);
        return R.ok();
    }

    @PostMapping("/findVisitLs")
    public R<?> visitLs(@RequestParam("orgId") String orgId, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate,
                        @RequestParam("deptCode") String deptCode, @RequestParam("patientName") String patientName, @RequestParam("desensitized") String desensitized) {
        if (orgId == null) {
            return R.error("请指定查询机构");
        }
        Integer startDateInt = null;
        if (StrUtil.isNotBlank(startDate)) {
            Date start = DateUtil.parse(startDate);
            startDateInt = Convert.toInt(DatePattern.PURE_DATE_FORMAT.format(start));
        }
        Integer endDateInt = null;
        if (StrUtil.isNotBlank(endDate)) {
            Date end = DateUtil.parse(endDate);
            endDateInt = Convert.toInt(DatePattern.PURE_DATE_FORMAT.format(end));
        }
        List<VisitDto> list = visitService.findOutpatientVisitLs(Convert.toLong(orgId), startDateInt, endDateInt, deptCode, patientName, null, Convert.toBool(desensitized, Boolean.FALSE));
        if (ObjectUtil.isNotEmpty(list)) {
            list.forEach(visitDto -> {
                if (visitDto.getRevisitFlag() == null || visitDto.getRevisitFlag() == 0) {
                    visitDto.setFirstVisit("是");
                } else {
                    visitDto.setReVisit("是");
                }
                if (visitDto.getClinicDate() != null) {
                    Date clinicDate = DateUtil.parse(Convert.toStr(visitDto.getClinicDate()));
                    visitDto.setClinicYear(Convert.toInt(DateUtil.year(clinicDate)));
                    visitDto.setClinicMonth(Convert.toInt(DateUtil.month(clinicDate) + 1));
                    visitDto.setClinicDay(Convert.toInt(DateUtil.dayOfMonth(clinicDate)));
                }
            });
        }

        return R.ok(list);
    }

    @PostMapping("/visitStats")
    public R<?> visitStats(@RequestParam("orgId") Long orgId) {
//        Long orgId = Convert.toLong(params.get("orgId"));
        if (orgId == null) {
            return R.error("请指定查询机构");
        }
        return R.ok(visitService.findVisitStats(orgId));
    }
}
