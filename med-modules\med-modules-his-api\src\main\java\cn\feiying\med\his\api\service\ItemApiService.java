package cn.feiying.med.his.api.service;

import cn.feiying.med.his.api.model.req.item.ItemPriceQueryModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.ItemPriceRespModel;

import java.util.List;

/**
 * 项目相关服务接口
 */
public interface ItemApiService {
    
    /**
     * 项目价格查询
     * 
     * @param itemPriceQuery 项目价格查询请求
     * @return 项目价格查询结果
     */
    ApiResultModel<ItemPriceRespModel> queryPrice(ItemPriceQueryModel itemPriceQuery);
} 