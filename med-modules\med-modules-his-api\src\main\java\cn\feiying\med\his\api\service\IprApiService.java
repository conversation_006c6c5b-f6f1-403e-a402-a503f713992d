package cn.feiying.med.his.api.service;

import cn.feiying.med.his.api.model.req.ipr.IprCancelModel;
import cn.feiying.med.his.api.model.req.ipr.IprModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.IprRespModel;

import java.util.List;

/**
 * 电子住院证相关服务接口
 */
public interface IprApiService {
    
    /**
     * 电子住院证数据保存及修改
     * 
     * @param iprModel 电子住院证数据
     * @return 电子住院证数据保存结果
     */
    ApiResultModel<IprRespModel> saveOrUpdateIpr(IprModel iprModel);
    
    /**
     * 电子住院证作废
     * 
     * @param iprModel 电子住院证作废请求
     * @return 电子住院证作废结果
     */
    ApiResultModel<?> cancelIpr(IprCancelModel iprModel);
} 