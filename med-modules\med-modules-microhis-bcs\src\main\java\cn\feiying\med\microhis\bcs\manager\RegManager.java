package cn.feiying.med.microhis.bcs.manager;

import cn.feiying.med.apt.api.req.*;
import cn.feiying.med.apt.api.resp.*;
import cn.feiying.med.common.exception.ApiFailureException;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.MathUtil;
import cn.feiying.med.hip.enums.BillType;
import cn.feiying.med.hip.enums.CashActionType;
import cn.feiying.med.hip.enums.CashType;
import cn.feiying.med.hip.enums.PaidStatus;
import cn.feiying.med.hip.mdi.dto.ClinicianDto;
import cn.feiying.med.hip.mdi.entity.ClinicianEntity;
import cn.feiying.med.hip.mdi.entity.OrgDeptEntity;
import cn.feiying.med.hip.mdi.entity.OrgEntity;
import cn.feiying.med.hip.mdi.service.ClinicianService;
import cn.feiying.med.hip.mdi.service.OrgDeptService;
import cn.feiying.med.hip.mdi.service.OrgService;
import cn.feiying.med.hip.mpi.entity.PatientEntity;
import cn.feiying.med.hip.mpi.service.PatientService;
import cn.feiying.med.his.api.req.*;
import cn.feiying.med.his.api.resp.*;
import cn.feiying.med.his.api.resp.HisApiResp0701.Patient;
import cn.feiying.med.microhis.bcs.config.ApiConfig;
import cn.feiying.med.microhis.bcs.dto.Art;
import cn.feiying.med.microhis.bcs.dto.PayIn;
import cn.feiying.med.microhis.bcs.dto.RegDto;
import cn.feiying.med.microhis.bcs.entity.BillDetailEntity;
import cn.feiying.med.microhis.bcs.entity.BillEntity;
import cn.feiying.med.microhis.bcs.entity.BillUnpaidEntity;
import cn.feiying.med.microhis.bcs.event.BillPayRefundEvent;
import cn.feiying.med.microhis.bcs.service.*;
import cn.feiying.med.microhis.bcs.vo.PayResult;
import cn.feiying.med.microhis.bcs.vo.RegGuideBill;
import cn.feiying.med.microhis.hsd.entity.RegEntity;
import cn.feiying.med.microhis.hsd.service.RegService;
import cn.feiying.med.microhis.hsd.service.VisitService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Supplier;

import static cn.feiying.med.common.utils.DateUtil.getTodayInt;

/**
 * 挂号
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RegManager {
    private final ApiConfig cfg;
    private final RegService regService;
    private final BillService billService;
    private final CashService cashService;
    private final CashPaymentService cashPaymentService;
    private final CashTransService cashTransService;
    private final PayManager payManager;
    private final RefundManager refundManager;
    private final OrgService orgService;
    private final ClinicianService clinicianService;
    private final OrgDeptService orgDeptService;
    private final BillDetailService billDetailService;
    private final BillUnpaidService billUnpaidService;
    private final CashBillService cashBillService;
    private final PatientService patientService;
    private final VisitService visitService;

    public static String timeToStr(int time) {
        String timeStr = StringUtils.leftPad(String.valueOf(time), 6, "0");
        String hour = timeStr.substring(0, 2);
        String minute = timeStr.substring(2, 4);
        return StrUtil.format("{}:{}", hour, minute);
    }

    /**
     * 锁定号源
     *
     * @param in 挂号信息
     * @return 返回号源信息（编号、价格、医师信息等）
     */
    public RegDto lock(RegDto in) {
        Integer ageOfYear = null;
        if (in.getPatientId() != null) {
            PatientEntity patient = patientService.getById(in.getPatientId());
            if (patient != null && patient.getBirthDate() != null) {
                BigDecimal[] ageAndDayCount = visitService.calculateAgeAndDayCount(patient.getBirthDate(), patient.getBirthTime());
                ageOfYear = Convert.toInt(ageAndDayCount[0]);
            }
        }
        if (Boolean.TRUE == in.getIsExpert()) {
            if (in.getClinicianId() != null) {
                ClinicianEntity clinician = clinicianService.getById(in.getClinicianId());
                in.setClinicianCode(clinician.getClinicianNo());
            }
            // 调用HisApiReq0306获取价格费别

            HisApiResp0306 res0306 = HisApiReq0306.execute(cfg.getHisUrl(), cfg.getHisSecret(), in.getOrgCode(), in.getPatientId().toString(),
                    null, ageOfYear, in.getClinicDate(), in.getPeriodId(), in.getDeptCode(), in.getClinicianCode(), 0, null);
            Assert.isFalse(ObjectUtil.isNull(res0306) || res0306.getArt_Count() == 0, () -> new SaveFailureException("计价明细条目为空"));
            List<HisApiResp0306.Art> artList = res0306.getArt_List();
            List<Art> copyList = new ArrayList<>(artList.size());
            for (HisApiResp0306.Art art : artList) {
                copyList.add(BeanUtil.copyProperties(art, Art.class));
            }
            in.setArts(copyList);
            BigDecimal amount = BigDecimal.ZERO;
            for (HisApiResp0306.Art art : artList) {
                amount = amount.add(art.getAmount() == null ? BigDecimal.ZERO : MathUtil.fenToYuan(art.getAmount()));
            }
            in.setAmount(amount);

            // 查询号源详情
            AptResp203 res203 = AptReq203.exec(cfg.getAptUrl(), cfg.getAptSecret(), in.getClinicianCode(), in.getOrgCode(), null, in.getClinicDate(), in.getPeriodId(), in.getDeptCode(), 0, null);
            Assert.notNull(res203, () -> new SaveFailureException("查询号源请求失败"));
            Assert.isTrue(res203.getList_Count() > 0, () -> new SaveFailureException("申请失败，无可用号源"));
            Integer preferredNo = Convert.toInt(res203.getAPT_List().get(0).getAPT_No());

            // 锁定专家号源
            AptResp303 res303 = AptReq303.exec(cfg.getAptUrl(), cfg.getAptSecret(), in.getOrgCode(), null, in.getClinicianCode(), in.getClinicDate(), in.getPeriodId(), 2, null, Convert.toLong(preferredNo), 1, Convert.toStr(in.getPatientId()));
            Assert.notNull(res303, () -> new SaveFailureException("申请号源请求失败"));
            Assert.isFalse(Objects.equals(res303.getAPT_No(), 0), () -> new SaveFailureException("申请失败，无可用号源"));
            Assert.isFalse(res303.getAPT_No() < 0, () -> new SaveFailureException("申请失败，未排班或已停诊"));

            // 根据号源返回信息补充挂号信息
            in.setAptNo(Convert.toInt(res303.getAPT_No()));
            in.setSchedDuration(timeToStr(res303.getStart_Time()) + "-" + timeToStr(res303.getEnd_Time()));
        } else {
            // 调用HisApiReq0306获取价格费别
            HisApiResp0306 res0306 = HisApiReq0306.execute(cfg.getHisUrl(), cfg.getHisSecret(), in.getOrgCode(), in.getPatientId().toString(),
                    "", ageOfYear, in.getClinicDate(), in.getPeriodId(), in.getDeptCode(), null, 0, null);
            Assert.isFalse(ObjectUtil.isNull(res0306) || res0306.getArt_Count() == 0, () -> new SaveFailureException("计价明细条目为空"));
            List<HisApiResp0306.Art> artList = res0306.getArt_List();
            List<Art> copyList = new ArrayList<>(artList.size());
            for (HisApiResp0306.Art value : artList) {
                copyList.add(BeanUtil.copyProperties(value, Art.class));
            }
            in.setArts(copyList);
            BigDecimal amount = BigDecimal.ZERO;
            for (HisApiResp0306.Art art : artList) {
                amount = amount.add(art.getAmount() == null ? BigDecimal.ZERO : MathUtil.fenToYuan(art.getAmount()));
            }
            in.setAmount(amount);
            // 查询号源详情
            AptResp103 res103 = AptReq103.exec(cfg.getAptUrl(), cfg.getAptSecret(), in.getOrgCode(), null, in.getDeptCode(), in.getClinicDate(), in.getPeriodId(), 0);
            Assert.notNull(res103, () -> new SaveFailureException("查询号源请求失败"));
            Assert.isTrue(res103.getList_Count() > 0, () -> new SaveFailureException("申请失败，无可用号源"));
            Integer preferredNo = Convert.toInt(res103.getAPT_List().get(0).getAPT_No());

            // 锁定普通号源
            AptResp301 res301 = AptReq301.exec(cfg.getAptUrl(), cfg.getAptSecret(), in.getOrgCode(), null, in.getDeptCode(), in.getClinicDate(), in.getPeriodId(),
                    2, null, Convert.toLong(preferredNo), 1, Convert.toStr(in.getPatientId()));
            Assert.notNull(res301, () -> new SaveFailureException("申请号源请求失败"));
            Assert.isFalse(Objects.equals(res301.getAPT_No(), 0), () -> new SaveFailureException("申请失败，无可用号源"));
            Assert.isFalse(res301.getAPT_No() < 0, () -> new SaveFailureException("申请失败，未排班或已停诊"));

            // 根据号源返回信息补充挂号信息
            in.setAptNo(Convert.toInt(res301.getAPT_No()));
            in.setSchedDuration(timeToStr(res301.getStart_Time()) + "-" + timeToStr(res301.getEnd_Time()));
        }
        return in;
    }

    public void create0307(RegDto in) {
        HisApiResp0307 res = HisApiReq0307.execute(
                cfg.getHisUrl(),
                cfg.getHisSecret(),
                in.getOrgCode(),
                null,
                in.getDeptCode(),
                in.getClinicianCode(),
                getTodayInt(),
                in.getPeriodId(),
                in.getSchedId(),
                Convert.toLong(in.getAptNo()),
                in.getSchedDuration(),
                in.getPatientId().toString(),
                String.valueOf(in.getAptSource().getValue()),
                null
        );
        Assert.isTrue(res.getStatus() >= 0, () -> new ApiFailureException(""));
        in.setAmount(new BigDecimal(res.getAmount()));
        in.setRegId(Long.parseLong(res.getHIS_Order_ID()));
    }

    public void confirmed0308(RegDto in) {
        HisApiResp0308 res = HisApiReq0308.execute(
                cfg.getHisUrl(),
                cfg.getHisSecret(),
                in.getOrgCode(),
                null,
                in.getDeptCode(),
                in.getClinicianCode(),
                in.getClinicDate(),
                in.getPeriodId(),
                in.getSchedId(),
                Convert.toLong(in.getAptNo()),
                in.getPatientName(),
                in.getRegId().toString(),
                null
        );
        Assert.isTrue(res.getStatus() >= 0, () -> new ApiFailureException(""));
    }

    public void withdrawal0309(RegDto in) {
        HisApiResp0309 res = HisApiReq0309.execute(cfg.getHisUrl(), cfg.getHisSecret(), in.getOrgCode(), String.valueOf(in.getRegId()), null);
        Assert.isTrue(res.getStatus() >= 0, () -> new ApiFailureException(""));
    }

    @Deprecated
    public void return0304(RegDto in) {
        String notes = StrUtil.format("退号原因:{}，具体原因：{}", in.getReasonType(), in.getReason());
        HisApiResp0304 res = HisApiReq0304.execute(
                cfg.getHisUrl(),
                cfg.getHisSecret(),
                in.getOrgCode(),
                null,
                null,
                String.valueOf(in.getRegId()),
                notes,
                null
        );
        Assert.isTrue(res.getStatus() >= 0, () -> new ApiFailureException(""));
    }

    public void taskNo0303(RegDto in) {
        HisApiResp0303 res = HisApiReq0303.execute(
                cfg.getHisUrl(),
                cfg.getHisSecret(),
                in.getOrgCode(),
                String.valueOf(in.getRegId()),
                in.getDeptCode(),
                in.getClinicianCode(),
                in.getClinicDate(),
                in.getPeriodId(),
                in.getSchedId(),
                Convert.toLong(in.getAptNo()),
                in.getSchedDuration(),
                null,
                null
        );
        Assert.isTrue(res.getStatus() >= 0, () -> new ApiFailureException(""));
    }

    /**
     * 释放号源
     *
     * @param in
     * @return
     */
    public void release(RegDto in) {
        if (Boolean.TRUE == in.getIsExpert()) {
            AptResp304 res = AptReq304.exec(cfg.getAptUrl(), cfg.getAptSecret(), in.getOrgCode(), null, in.getClinicianCode(), in.getClinicDate(),
                    in.getPeriodId(), null, Convert.toLong(in.getAptNo()), Convert.toStr(in.getPatientId()));
            Assert.isFalse(res.getStatus().equals(0), () -> new SaveFailureException(""));
        } else {
            AptResp302 res = AptReq302.exec(cfg.getAptUrl(), cfg.getAptSecret(), in.getOrgCode(), null, in.getDeptCode(), in.getClinicDate(), in.getPeriodId(),
                    null,Convert.toLong(in.getAptNo()), Convert.toStr(in.getPatientId()));
            Assert.isFalse(res.getStatus().equals(0), () -> new SaveFailureException(""));
        }
    }


    public RegDto zero(RegDto in) {
        Assert.isTrue(NumberUtil.equals(in.getAmount(), BigDecimal.ZERO), () -> new SaveFailureException("金额必须等于0"));
        create0307(in);
        try {
            confirmed0308(in);
            return in;
        } catch (ApiFailureException e) {
            withdrawal0309(in);
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Supplier<PayResult> pay(RegDto regDto, PayIn payIn) {
        if (regDto.getRegId() == null) {
            create0307(regDto);
        }

        BillEntity bill = billService.get(regDto.getRegId(), BillType.regBill, "找不到挂号记录对应的划价单");
        if (bill.getCashId() != null) {
            Assert.isFalse(cashService.isFinish(bill.getCashId()), () -> new SaveFailureException("已完成，请勿重复操作！"));
            if (cashService.isEnd(bill.getCashId())) {
                regDto.setCashId(bill.getCashId());
                return PayResult::new;
            }
        }

        payIn.setBillIds(Collections.singletonList(bill.getBseqid()));
        payIn.setPayAmount(bill.getAmount());
        payIn.setCashType(CashType.reg);
        return payManager.pay(payIn);
    }

    @Transactional(rollbackFor = Exception.class)
    public void payFinish(RegDto in) {
        BillEntity bill = billService.get(in.getRegId(), BillType.regBill, "找不到挂号记录对应的划价单");
        Assert.isTrue(NumberUtil.isGreater(bill.getAmount(), BigDecimal.ZERO), () -> new SaveFailureException("金额为0"));

        Long cashId = bill.getCashId();
        payManager.payFinish(cashId);

        confirmed0308(in);
    }

    @Transactional
    public void drop(RegDto in) {
        withdrawal0309(in);
        BillEntity bill = billService.get(in.getRegId(), BillType.regBill, "找不到挂号记录对应的划价单");
        billService.updatePaidStatus(CollUtil.toList(bill.getBseqid()), PaidStatus.cancel);
        cashService.cancel(bill.getCashId(), "挂号取消");
    }

    /**
     * 退号
     * 退费操作核心思想为先把全部数据处理好。然后调用退费接口。（退费接口失败人工处理）
     *
     * @param in
     */
    @Transactional(rollbackFor = Exception.class)
    public Supplier<PayResult> returnNo(RegDto in) {
        BillEntity bill = billService.get(in.getRegId(), BillType.regBill, "找不到挂号记录对应的划价单");
        if (NumberUtil.equals(bill.getAmount(), BigDecimal.ZERO)) {
            drop(in);
            return PayResult::new;
        }

        Long originCashId = bill.getCashId();
        // 产生红单，如果没有红单，则直接复制原单并将金额改成负数
        List<BillEntity> reds = billService.findRedsByBlueId(bill.getBseqid());
        if (CollUtil.isEmpty(reds)) {
            createRedBill(bill);
        } else {
            bill = reds.get(0);
        }

        withdrawal0309(in);
        return null;
    }

    private void createRedBill(BillEntity bill) {
        Assert.isTrue(PaidStatus.paid.getValue() == bill.getPaidStatus(), () -> new SaveFailureException("已支付才能退款"));
        bill.setRelativeBseqid(bill.getBseqid());
        bill.setPaidStatus(PaidStatus.inPayment.getValue());
        bill.setTotalAmount(bill.getTotalAmount() == null ? null : bill.getTotalAmount().abs().negate());
        bill.setDerated(bill.getDerated().abs());
        bill.setDiscounted(bill.getDiscounted().abs());
        bill.setAmount(bill.getAmount().abs().negate());
        bill.setCashId(null);
        billService.saveEntity(bill);
        List<BillDetailEntity> details = billDetailService.findByMainId(bill.getBseqid());
        for (BillDetailEntity detail : details) {
            detail.setBseqid(bill.getBseqid());
            detail.setAmount(detail.getAmount().abs().negate());
        }
        billDetailService.saveBatch(details);
        billUnpaidService.save(BillUnpaidEntity.builder().bseqid(bill.getBseqid()).build());
    }

    public RegDto getInfoById(RegDto in) {
        return null;
    }

    public RegGuideBill getRegGuideBill(RegDto in) {
        RegEntity reg = regService.getById(in.getRegId());
        in.setPatientId(reg.getPatientId());
        Patient patient = getPatientById(in);

        BillEntity bill = billService.get(reg.getRegId(), BillType.regBill, "找不到划价单");
        RegGuideBill regGuideBill = RegGuideBill.builder()
                .aptNo(reg.getAptNo())
                .birthDate(patient.getBirth_Date())
                .patientName(patient.getPatient_Name())
                .genderName(patient.getPatient_Gender())
                .hcCardNo(patient.getHCCard_No())
                .outpatientNo(patient.getOutpatient_No())
                .regCategory(bill == null ? "普通门诊" : "专家门诊")
                .regAmount(bill == null ? BigDecimal.ZERO : bill.getAmount())
                .clinicDate(reg.getClinicDate())
                .schedDuration(reg.getSchedDuration())
                .build();

        OrgEntity org = orgService.getById(reg.getOrgId());
        regGuideBill.setOrgName(org.getOrgName());
        if (StrUtil.isNotBlank(reg.getDeptCode())) {
            OrgDeptEntity orgDept = orgDeptService.findById(reg.getOrgId(), reg.getDeptCode());
            regGuideBill.setDeptName(orgDept.getDeptName());
            regGuideBill.setDeptAddr(orgDept.getDeptAddr());
        }
        if (ObjectUtil.isNotNull(reg.getClinicianId())) {
            ClinicianDto clinician = clinicianService.findDtoById(reg.getClinicianId());
            regGuideBill.setClinicianName(clinician.getClinicianName());
            regGuideBill.setQualificationName(clinician.getQualificationName());
        }
        return regGuideBill;
    }

    private HisApiResp0701.Patient getPatientById(RegDto in) {
        HisApiResp0701 res = HisApiReq0701.execute(
                cfg.getHisUrl(),
                cfg.getHisSecret(),
                in.getOrgCode(),
                1,
                "",
                "",
                String.valueOf(in.getPatientId()),
                "",
                "",
                "",
                "");
        Assert.isTrue(ObjectUtil.isNotNull(res) && res.getMatch_Count() > 0, () -> new SaveFailureException("获取患者信息失败"));
        return res.getPatient_List().get(0);
    }

    @EventListener(BillPayRefundEvent.Reg.class)
    public void handleBillPayRefundEvent(BillPayRefundEvent.Reg event) {
        if (event.toPaidStatus == PaidStatus.paid && event.actionType == CashActionType.Normal) {
            for (BillEntity bill : event.bills) {
                regService.paidReg(bill.getRegId(), bill.getCashId());
            }
        } else if (event.toPaidStatus == PaidStatus.refund) {
            for (BillEntity bill : event.bills) {
                regService.refundReg(bill.getRegId(), bill.getCashId());
            }
        }
    }
}
