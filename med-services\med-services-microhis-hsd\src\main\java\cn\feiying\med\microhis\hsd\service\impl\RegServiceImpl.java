package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.*;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.entity.GenderEntity;
import cn.feiying.med.hip.mdi.service.GenderService;
import cn.feiying.med.hip.mdi.service.SysSequenceService;
import cn.feiying.med.microhis.bcs.entity.BillEntity;
import cn.feiying.med.microhis.bcs.service.BillService;
import cn.feiying.med.microhis.hsd.dao.RegDao;
import cn.feiying.med.microhis.hsd.dto.ActiveClinicianDto;
import cn.feiying.med.microhis.hsd.dto.RegDto;
import cn.feiying.med.microhis.hsd.entity.*;
import cn.feiying.med.microhis.hsd.service.*;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 挂号记录表
 *
 * <AUTHOR> 15:15:54
 */
@Slf4j
@Service("regService")
public class RegServiceImpl extends ServiceImpl<RegDao, RegEntity> implements RegService {

    @Resource
    private SysSequenceService sequenceService;
    @Resource
    private RegTriageService regTriageService;
    @Resource
    private RegAcctService regAcctService;
    @Resource
    private VisitService visitService;
    @Resource
    private RegPendingService regPendingService;
    @Resource
    private GenderService genderService;
    @Resource
    private BillService billService;
    @Resource
    private MiniqmsOrderService miniqmsOrderService;
    @Resource
    private ActiveClinicianService activeClinicianService;

    @Override
    public PageUtils<RegDto> queryPage(Map<String, Object> params) {
        QueryWrapper<RegDto> wrapper = new GQueryWrapper<RegDto>().getWrapper(params);
        IPage<RegDto> page = baseMapper.queryPage(new Query<RegDto>().getPage(params), wrapper);
        if (!page.getRecords().isEmpty()) {
            Integer isDesensitized = Convert.toInt(params.get("isDesensitized"), 0);
            if (isDesensitized == 0) {
                for (RegDto reg : page.getRecords()) {
                    reg.setContactTel(StrUtil.desensitized(reg.getContactTel(), DesensitizedUtil.DesensitizedType.MOBILE_PHONE));
                }
            }
            setVisit(page.getRecords());
            setAmount(page.getRecords());
            setSectionRoom(page.getRecords());
        }
        return new PageUtils<>(page);
    }

    @Override
    public List<RegDto> findLs(Map<String, Object> params) {
        QueryWrapper<RegDto> wrapper = new GQueryWrapper<RegDto>().getWrapper(params);
        List<RegDto> list = baseMapper.queryPage(wrapper);
        if (!list.isEmpty()) {
            for (RegDto reg : list) {
                reg.setContactTel(StrUtil.desensitized(reg.getContactTel(), DesensitizedUtil.DesensitizedType.MOBILE_PHONE));
            }
            setVisit(list);
            setAmount(list);
            setSectionRoom(list);
        }
        return list;
    }

    @Override
    public List<RegDto> findClinicDateLs(Long orgId, Long clinicianId, Integer clinicDate, String deptCode, String patientName, Integer status,
                                         Integer forClinic, Integer myPatient) {
        QueryWrapper<RegDto> wrapper = new GQueryWrapper<RegDto>().getWrapper();
        wrapper.eq("t_reg.Org_ID", orgId);
        wrapper.apply("((IFNULL(t_reg.APT_Flag, 1) = 1 and t_reg.Reg_Status IN ({0}, {1})) OR (IFNULL(t_reg.APT_Flag, 1) = 0 and t_reg.Reg_Status != {2}))", RegStatus.CONFIRMED.getValue(), RegStatus.TRANSFERRED.getValue(), RegStatus.WITHDRAWAL.getValue());
        wrapper.ne("t_reg.Clinic_Status", ClinicStatus.rejected.getValue());
//        wrapper.eq("t_reg.Clinician_ID", clinicianId);
        if (status != null) {
            if (status == 0) {
                wrapper.in("t_reg.Clinic_Status", ClinicStatus.waiting.getValue(), ClinicStatus.reg.getValue());
            } else {
                wrapper.eq("t_reg.Clinic_Status", status);
            }
        }
        if (forClinic != null && forClinic == 1) {
            wrapper.eq("t_reg.Dept_Code", deptCode);
            wrapper.apply("((t_reg.Clinic_Status = {1} AND t_reg.Clinic_Date = {0}) OR (t_reg.Clinic_Status < {1} AND t_reg.Clinic_Date <= {0}))", clinicDate, ClinicStatus.finish.getValue());
        } else {
            wrapper.and(p -> p.eq("t_reg.Clinician_ID", clinicianId).or(pp -> pp.isNull("t_reg.Clinician_ID").eq("t_reg.Dept_Code", deptCode)));
            wrapper.apply("(t_reg.Clinic_Date = {0} OR (t_reg.OC_Flag = 1 AND t_reg.Clinic_Status < {1} AND t_reg.Clinic_Date <= {0}))", clinicDate, ClinicStatus.finish.getValue());
        }
        if (myPatient != null && myPatient == 1) {
            wrapper.and(p -> p.eq("t_reg.Clinician_ID", clinicianId).or(pp -> pp.isNull("t_reg.Clinician_ID").eq("t_reg.Dept_Code", deptCode)));
        }
        if (StringUtil.isNotEmpty(patientName)) {
            wrapper.like("t_reg.Patient_Name", patientName.trim());
        }
        wrapper.orderByDesc("t_reg.Clinic_Date").orderByDesc("t_reg.APT_No");
        List<RegDto> list = baseMapper.queryPage(wrapper);
        setVisit(list);
        setAmount(list);
        setSectionRoom(list);
        return list;
    }

    @Override
    public Long countReg(Map<String, Object> params) {
        QueryWrapper<RegDto> wrapper = new GQueryWrapper<RegDto>().getWrapper(params);
        return baseMapper.countReg(wrapper);
    }

    @Override
    public Integer getLastClinicDate(Long orgId, Long patientId, Integer actionType, String deptCode) {
        RegEntity reg = baseMapper.getLastClinicDate(orgId, patientId, actionType, deptCode);
        return reg != null ? reg.getClinicDate() : null;
    }

    @Override
    public RegDto findById(Long regId) {
        RegDto reg = baseMapper.findById(regId);
        if (reg != null) {
            reg.setContactTel(StrUtil.desensitized(reg.getContactTel(), DesensitizedUtil.DesensitizedType.MOBILE_PHONE));
            setVisit(ListUtil.of(reg));
            setAmount(ListUtil.of(reg));
            setSectionRoom(ListUtil.of(reg));
        }
        return reg;
    }

    private void setVisit(List<RegDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<Long> visitIdLs = list.stream().map(RegDto::getVisitId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(visitIdLs)) {
                List<VisitEntity> visitLs = visitService.listByIds(visitIdLs);
                List<GenderEntity> genderLs = genderService.list();
                list.forEach(reg -> {
                    if (reg.getVisitId() != null) {
                        VisitEntity visit = visitLs.stream().filter(v -> v.getVisitId().equals(reg.getVisitId())).findFirst().orElse(null);
                        if (visit != null) {
                            reg.setAgeOfYears(visit.getAgeOfYears());
                            reg.setAgeOfDays(Convert.toInt(visit.getAgeOfDays()));
                            if (reg.getPatientId() == null && visit.getGenderId() != null) {
                                reg.setPatientGenderId(visit.getGenderId());
                                reg.setPatientGenderName(genderLs.stream().filter(g -> g.getGenderId().equals(visit.getGenderId())).findFirst().orElse(new GenderEntity()).getGenderName());
                            }
                        }
                    }
                });
            }
        }
    }

    private void setAmount(List<RegDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<Long> regIdLs = list.stream().map(RegDto::getRegId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
            List<BillEntity> billLs = billService.list(Wrappers.lambdaQuery(BillEntity.class).in(BillEntity::getRegId, regIdLs).eq(BillEntity::getBillTypeId, BillType.regBill.getValue())
                    .ne(BillEntity::getPaidStatus, PaidStatus.cancel.getValue()));
            if (ObjectUtil.isNotEmpty(billLs)) {
                for (RegDto reg : list) {
                    reg.setAmount(billLs.stream().filter(b -> b.getRegId().equals(reg.getRegId())).map(BillEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
        }
    }

    private void setSectionRoom(List<RegDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<Long> orgIdLs = list.stream().filter(p -> p.getOrgId() != null
                    && p.getClinicStatus() < ClinicStatus.finish.getValue()
                    && Objects.equals(p.getClinicDate(), DateUtil.getTodayInt()))
                    .map(RegDto::getOrgId).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(orgIdLs))  {
                for (Long orgId : orgIdLs) {
                    List<RegDto> regLs = list.stream().filter(item -> item.getOrgId().equals(orgId) && item.getClinicStatus() < ClinicStatus.finish.getValue()
                            && Objects.equals(item.getClinicDate(), DateUtil.getTodayInt()) && item.getClinicianId() != null).collect(Collectors.toList());
                    List<Long> clinicianIdLs = regLs.stream().map(RegDto::getClinicianId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(clinicianIdLs)) {
                        List<ActiveClinicianDto> activeClinicianLs = activeClinicianService.findLs(orgId, clinicianIdLs);
                        if (ObjectUtil.isNotEmpty(activeClinicianLs)) {
                            for (RegDto reg : list) {
                                if (Objects.equals(reg.getOrgId(), orgId) && reg.getClinicStatus() < ClinicStatus.finish.getValue()
                                        && Objects.equals(reg.getClinicDate(), DateUtil.getTodayInt()) && reg.getClinicianId() != null) {
                                    ActiveClinicianDto activeClinician = activeClinicianLs.stream().filter(item -> item.getOrgId().equals(reg.getOrgId())
                                            && item.getClinicianId().equals(reg.getClinicianId())).findFirst().orElse(null);
                                    if (activeClinician != null) {
                                        reg.setRoomId(activeClinician.getRoomId());
                                        reg.setRoomNo(activeClinician.getRoomNo());
                                        reg.setRoomName(activeClinician.getRoomName());
                                        reg.setSectionId(activeClinician.getSectionId());
                                        reg.setSectionName(activeClinician.getSectionName());
                                        reg.setSectionAddr(activeClinician.getSectionAddr());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    @Transactional
    public Long saveEntity(RegEntity entity) {
        Long regId = sequenceService.getLongNextValue(RegEntity.class.getSimpleName());
        entity.setRegId(regId);
//        entity.setClinicStatus(ClinicStatus.waiting.getValue());
//        entity.setRegStatus(0);
//        entity.setDailyNo(baseMapper.getDailyNo(entity.getClinicDate()));
        entity.setTimeCreated(new Date());
        save(entity);
        return regId;
    }

    @Override
    @Transactional
    public void updateReg(Long regId, Long patientId, Integer revisitFlag, String patientName, String complainOf, BigDecimal temperature, Integer heightCm, BigDecimal weightKg,
                          Integer pulse, Integer rr, Integer dbp, Integer sbp) {
        // 修改挂号信息
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getPatientId, patientId);
        if (StrUtil.isNotBlank(patientName)) {
            regWrapper.set(RegEntity::getPatientName, patientName);
        }
        regWrapper.set(RegEntity::getRevisitFlag, revisitFlag == null ? 0 : revisitFlag);
        update(regWrapper);
        // 修改分诊信息
        LambdaUpdateWrapper<RegTriageEntity> triageWrapper = Wrappers.lambdaUpdate(RegTriageEntity.class);
        triageWrapper.eq(RegTriageEntity::getRegId, regId);
        triageWrapper.set(RegTriageEntity::getComplainOf, complainOf);
        triageWrapper.set(RegTriageEntity::getTemperature, temperature);
        triageWrapper.set(RegTriageEntity::getHeightCm, heightCm != null ? BigDecimal.valueOf(heightCm) : null);
        triageWrapper.set(RegTriageEntity::getWeightKg, weightKg);
        triageWrapper.set(RegTriageEntity::getPulse, pulse);
        triageWrapper.set(RegTriageEntity::getRr, rr);
        triageWrapper.set(RegTriageEntity::getDbp, dbp);
        triageWrapper.set(RegTriageEntity::getSbp, sbp);
        regTriageService.update(triageWrapper);
    }

    @Override
    @Transactional
    public void delete(Long... regIdLs) {
        try {
            for (Long regId : regIdLs) {
                regTriageService.removeById(regId);
                removeById(regId);
            }
        } catch (Exception e) {
            throw new SaveFailureException("数据已被外部引用，不能删除。");
        }
    }

    @Override
    public boolean validPaidReg(Long regId) {
        log.info("校验挂号是否可以支付 regId:{}", regId);
        if (regId != null) {
            RegEntity reg = getById(regId);
            if (reg == null) {
                throw new SaveFailureException("挂号不存在");
            }
            // 如果挂号已退回不能再支付
            if (reg.getRegStatus().equals(RegStatus.WITHDRAWAL.getValue())) {
                throw new SaveFailureException("挂号已退回，不能支付。");
            }
            // 如果已支付不能再支付
            if (reg.getRegStatus().equals(RegStatus.CONFIRMED.getValue()) && reg.getPatientId() != null) {
                throw new SaveFailureException("挂号已支付，不能支付。");
            }
        }
        return true;
    }

    @Override
    @Transactional
    public void inPaymentReg(Long regId) {
        if (regId != null) {
            RegEntity reg = getById(regId);
            if (reg == null) {
                throw new SaveFailureException("挂号不存在");
            }
            if (!reg.getRegStatus().equals(RegStatus.WAITING_FOR_CONFIRMATION.getValue()) && !reg.getRegStatus().equals(RegStatus.PAYMENT_IN_PROGRESS.getValue())) {
                throw new SaveFailureException("不是待支付状态，不能支付。");
            }
            LambdaUpdateWrapper<RegEntity> wrapper = Wrappers.lambdaUpdate(RegEntity.class);
            wrapper.eq(RegEntity::getRegId, regId);
            wrapper.set(RegEntity::getRegStatus, RegStatus.PAYMENT_IN_PROGRESS.getValue());
            update(wrapper);
        }
    }

    @Override
    @Transactional
    public void paidReg(Long regId, Long cashId) {
        if (regId != null) {
            RegEntity reg = getById(regId);
            if (reg == null) {
                throw new SaveFailureException("挂号不存在");
            }
            if (reg.getRegStatus().equals(RegStatus.WITHDRAWAL.getValue())) {
                throw new SaveFailureException("已退号，不能支付。");
            }
            LambdaUpdateWrapper<RegEntity> wrapper = Wrappers.lambdaUpdate(RegEntity.class);
            wrapper.eq(RegEntity::getRegId, regId);
            wrapper.set(RegEntity::getRegStatus, RegStatus.CONFIRMED.getValue());
            if (reg.getClinicianId() != null && reg.getClinicStatus().equals(ClinicStatus.waiting.getValue())) {
                wrapper.set(RegEntity::getClinicStatus, ClinicStatus.reg.getValue());
                // 保存待呼叫
                miniqmsOrderService.saveEntity(MiniqmsOrderEntity.builder()
                        .regId(regId)
                        .continuedFlag(0)
                        .priority(QmsPriority.NORMAL.getCode())
                        .orgId(reg.getOrgId())
                        .deptCode(reg.getDeptCode())
                        .build());
            }
            wrapper.set(RegEntity::getTimePaid, new Date());
            update(wrapper);
            // 修改挂号收费记录收费信息
            if (cashId != null) {
                LambdaUpdateWrapper<RegAcctEntity> acctWrapper = Wrappers.lambdaUpdate(RegAcctEntity.class);
                acctWrapper.eq(RegAcctEntity::getRegId, regId);
                acctWrapper.eq(RegAcctEntity::getActionType, RegAcctActionType.REG.getValue());
                acctWrapper.set(RegAcctEntity::getCashId, cashId);
                acctWrapper.set(RegAcctEntity::getTimeAccounted, new Date());
                regAcctService.update(acctWrapper);
            }
        }
    }

    @Override
    @Transactional
    public void refundReg(Long regId, Long cashId) {
        if (regId != null && cashId != null) {
            if (regAcctService.count(Wrappers.lambdaQuery(RegAcctEntity.class).eq(RegAcctEntity::getRegId, regId).eq(RegAcctEntity::getActionType, RegAcctActionType.DROP.getValue())) == 0) {
                List<RegAcctEntity> regAcctLs = regAcctService.list(Wrappers.lambdaQuery(RegAcctEntity.class).eq(RegAcctEntity::getRegId, regId).eq(RegAcctEntity::getActionType, RegAcctActionType.REG.getValue()));
                List<BillEntity> billLs = billService.list(Wrappers.lambdaQuery(BillEntity.class).eq(BillEntity::getCashId, cashId));
                if (ObjectUtil.isNotEmpty(regAcctLs) && ObjectUtil.isNotEmpty(billLs)) {
                    RegAcctEntity regAcct = regAcctLs.get(0);
                    BillEntity bill = billLs.get(0);
                    // 生成冲红挂号收费记录
                    RegAcctEntity redRegAcct = RegAcctEntity.builder()
                            .regId(regId)
                            .actionType(RegAcctActionType.DROP.getValue())
                            .orgId(regAcct.getOrgId())
                            .userId(regAcct.getUserId())
                            .accDate(regAcct.getAccDate())
                            .bseqid(bill.getBseqid())
                            .amount(bill.getAmount())
                            .notes(regAcct.getAccDate() + "退号退费单。")
                            .build();
                    regAcctService.save(redRegAcct);
                }
            } else {
                LambdaUpdateWrapper<RegAcctEntity> acctWrapper = Wrappers.lambdaUpdate(RegAcctEntity.class);
                acctWrapper.eq(RegAcctEntity::getRegId, regId);
                acctWrapper.eq(RegAcctEntity::getActionType, RegAcctActionType.DROP.getValue());
                acctWrapper.set(RegAcctEntity::getCashId, cashId);
                acctWrapper.set(RegAcctEntity::getTimeAccounted, new Date());
                regAcctService.update(acctWrapper);
            }
            // 修改挂号状态
            RegEntity reg = getById(regId);
            LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
            regWrapper.eq(RegEntity::getRegId, regId);
            regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.rejected.getValue());
            regWrapper.set(RegEntity::getRegStatus, RegStatus.WITHDRAWAL.getValue());
            regWrapper.set(RegEntity::getRejectedFlag, 1);
            regWrapper.set(RegEntity::getTimeDischarged, new Date());
            update(regWrapper);
            // 如果已生成诊疗记录，则删除
            regPendingService.removeById(regId);
            if (reg.getVisitId() != null) {
                visitService.rejectedVisit(Collections.singletonList(reg.getVisitId()));
            }
        }
    }

    @Override
    public List<RegEntity> recentReg(Map<String, Object> params, Integer count) {
        Object patientId = params.get("patientId");
        IPage<RegEntity> recentRegPage = this.page(new Query<RegEntity>().getPage(params).setSize(count).setPages(1),
                new LambdaQueryWrapper<RegEntity>()
                        .eq(RegEntity::getPatientId, patientId).orderByDesc(RegEntity::getTimeCreated));
        return recentRegPage.getRecords();
    }

    @Override
    public List<RegEntity> aptReg(Map<String, Object> params, Integer count) {
        Object patientId = params.get("patientId");
        IPage<RegEntity> recentRegPage = this.page(new Query<RegEntity>().getPage(params).setSize(count).setPages(1),
                new LambdaQueryWrapper<RegEntity>().eq(RegEntity::getPatientId, patientId).eq(RegEntity::getAptFlag, 1).orderByDesc(RegEntity::getTimeCreated));
        return recentRegPage.getRecords();
    }
}
