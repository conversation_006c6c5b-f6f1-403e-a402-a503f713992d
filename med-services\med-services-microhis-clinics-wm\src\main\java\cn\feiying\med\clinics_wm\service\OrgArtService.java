package cn.feiying.med.clinics_wm.service;

import cn.feiying.med.clinics_wm.dto.OrgArtDto;
import cn.feiying.med.clinics_wm.entity.ArtStocknoEntity;
import cn.feiying.med.clinics_wm.model.ArtPriceModel;
import cn.feiying.med.clinics_wm.model.ArtStockReserveResult;
import cn.feiying.med.hip.mdi.entity.ArticleEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.clinics_wm.entity.OrgArtEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 机构商品表
 *
 * <AUTHOR> @email 
 * @date 2024-06-04 18:48:02
 * 2024-06-04 18:48:02
 */
public interface OrgArtService extends IService<OrgArtEntity> {

    PageUtils queryPage(Long orgId, Map<String, Object> params);
    List<OrgArtDto> queryList(Long orgId, Map<String, Object> params);

    void saveEntity(OrgArtEntity entity);

    void updateEntity(long orgId, OrgArtDto params);

    void batchUpdatePctAdd(long orgId, Map<String, Object> params);

    OrgArtEntity findById(Long orgId, Long artId);

    OrgArtDto findDtoById(Long orgId, Long artId);

//    OrgArtDto findPrice(long orgId, long artId, Integer stockNo);

    /**
     * 根据商品id和定价方式查询价格
     * @param orgId
     * @param artId
     * @param pricingMethod
     * @return
     */
    OrgArtDto findPrice(long orgId, long artId, Integer stockNo, Integer pricingMethod);

    /**
     * 根据商品和定价方式计算价格
     * @param pricingMethod
     * @param packCells
     * @param basePackPrice
     * @param baseCellPrice
     * @param lastBuyPrice
     * @param costPackPrice
     * @param costCellPrice
     * @param pctAdd
     * @return
     */
    ArtPriceModel findPrice(Integer pricingMethod, Integer packCells,
                            BigDecimal basePackPrice, BigDecimal baseCellPrice, BigDecimal lastBuyPrice,
                            BigDecimal costPackPrice, BigDecimal costCellPrice, BigDecimal pctAdd);

    /**
     * 采购入库确认
     * @param orgId
     * @param articleEntity
     * @param packPrice
     * @param totalPacks
     */
    void purchaseIn(Long orgId, ArticleEntity articleEntity, BigDecimal packPrice, Integer totalPacks);

    /**
     * 增加机构商品库存
     * @param orgId
     * @param artId
     * @param totalPacks
     * @param totalCells
     */
    void stockInc(Long orgId, Long artId, Integer totalPacks, BigDecimal totalCells);

    /**
     * 增加机构商品批次库存
     * @param orgId
     * @param artId
     * @param stockNo
     * @param batchNo
     * @param totalPacks
     * @param totalCells
     * @param expiry
     */
    void batchStockInc(Long orgId, Long artId, Integer stockNo, String batchNo, Integer totalPacks, BigDecimal totalCells, Integer dateManufactured, Integer expiry);

    /**
     * 减少机构商品库存
     *
     * @param orgId
     * @param artId
     * @param packCells
     * @param totalPacks
     * @param totalCells
     * @param initOrgArt
     */
    void stockDec(Long orgId, Long artId, Integer packCells, Integer totalPacks, BigDecimal totalCells, OrgArtEntity initOrgArt);

    /**
     * 减少t_org_art库存，返回更新条数
     * @param orgId
     * @param articleEntity
     * @param totalPacks
     * @param totalCells
     * @return
     */
    int stockDecCount(Long orgId, ArticleEntity articleEntity, Integer totalPacks, BigDecimal totalCells);


    /**
     * 减少机构商品批次库存
     *
     * @param orgId
     * @param articleEntity
     * @param totalPacks
     * @param totalCells
     * @return
     */
    ArtStockReserveResult batchStockDec(Long orgId, ArticleEntity articleEntity, Integer totalPacks, BigDecimal totalCells);

    List<ArtStocknoEntity> findAllStockNoByBatchNo(long orgId, Long artId, String batchNo);

    void saveOrUpdateEntity(long orgId, OrgArtEntity params);

    void saveToOrgItemPrice(Long wbSeqid, Long orgId);

    void batchSaveOrUpdate(long orgId, List<OrgArtEntity> orgArtEntities);

    /**
     * 药品库存预警 仓库
     * @param orgId
     * @param params
     * @return
     */
    PageUtils orgOrDeptArtSafeWarning(Long orgId, Map<String, Object> params);

    /**
     * 药品库存预警 机构
     * @param orgId
     * @param params
     * @return
     */
    List<OrgArtDto> orgOrDeptArtSafeWarningList(Long orgId,Map<String, Object> params);
    void remakeOrgArt(Long orgId, String deptCode, Long artId, Integer totalPacks, BigDecimal totalCells,BigDecimal reserveCells);

}


