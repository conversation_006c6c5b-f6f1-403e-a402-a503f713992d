package cn.feiying.med.microhis.hsd.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 诊疗记录表
 *
 * <AUTHOR> 2023-09-21 15:15:54
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("microhis_hsd.t_visit")
public class VisitEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 诊疗ID
	 */
	@TableId(value = "Visit_ID", type = IdType.INPUT)
	private Long visitId;
	/**
	 * 新生儿产妇诊疗ID
	 */
	private Long motherVisitid;
	/**
	 * 患者姓名
	 */
	private String patientName;
	/**
	 * 性别代号
	 */
	private Integer genderId;
	/**
	 * 年龄
	 */
	private Integer ageOfYears;
	/**
	 * 入院时间
	 */
	private Date timeAdmission;
	/**
	 * 患者类型代号
	 */
	private Integer patientTypeId;
	/**
	 * 日龄(出生时间到接诊时间不足周岁部分的天数)
	 */
	private BigDecimal ageOfDays;
	/**
	 * 险种类型代号
	 */
	private Integer insuranceTypeId;
	/**
	 * 职业代号
	 */
	private Integer careerId;
	/**
	 * 是否流动人口
	 */
	private Integer isMigrant;
	/**
	 * 患者地区代码
	 */
	private String zoneCode;
	/**
	 * 患者片区代号
	 */
	private Integer areaId;
	/**
	 * 是否儿童病例
	 */
	private Integer isChildren;
	/**
	 * 是否老年病例
	 */
	private Integer isAged;
	/**
	 * 机构ID
	 */
	private Long orgId;
	/**
	 * 诊疗类型代号
	 */
	private Integer clinicTypeId;
	/**
	 * 看诊医师ID
	 */
	private Long clinicianId;
	/**
	 * 先诊后付划价单流水
	 */
	private Long postpaidBseqid;
	/**
	 * 计划就诊日期
	 */
	private Integer clinicDate;
	/**
	 * 看诊科室代码
	 */
	private String deptCode;
	/**
	 * 医疗类别代号
	 */
	private Integer medTypeId;
	/**
	 * 患者ID
	 */
	private Long patientId;
	/**
	 * 危急值报告次数
	 */
	private Integer cvReportCount;
	/**
	 * 临床病种代号
	 */
	private Integer diseaseId;
	/**
	 * 年纪代号
	 */
	private Integer agesId;
	/**
	 * 婚姻状况代号
	 */
	private Integer marriageStatusId;
	/**
	 * 挂号ID
	 */
	private Long regId;
	/**
	 * 生理时期代号
	 */
	private Integer stageId;
	/**
	 * 是否新生儿病例
	 */
	private Integer isNewborn;
	/**
	 * 新生儿入院类型代号
	 */
	private Integer newbornAdmTypeId;
	/**
	 * 诊疗状态(0-待分诊/待入区,1-待接诊/已入区,2-已接诊/已出区,3-已诊结/已出院)
	 */
	private Integer visitStatus;
	/**
	 * 计数状态
	 */
	private Integer countedFlag;
	/**
	 * 患者诊疗次序
	 */
	private Integer visitNo;
	/**
	 * 出院科室代码
	 */
	private String dischargeDeptCode;
	/**
	 * 人员类别代号
	 */
	private Integer psnTypeId;
	/**
	 * 住院申请流水号(记录门诊开具的住院证流水)
	 */
	private Long iprId;
	/**
	 * 患者乡村代码
	 */
	private String townCode;
	/**
	 * 住院类型代号
	 */
	private Integer inpatientTypeId;
	/**
	 * 医疗小组ID
	 */
	private Integer cgId;
	/**
	 * 治疗处方ID
	 */
	private Long cureRecipeId;
	/**
	 * 公务员标志(0-否,1-是)
	 */
	private Integer civilServantFlag;
	/**
	 * 就诊凭证类型代号
	 */
	private Integer mdtrtCertTypeId;
	/**
	 * 是否外伤(0-否,1-是)
	 */
	private Integer isTraumatic;
	/**
	 * 是否涉及第三方(0-否,1-是)
	 */
	private Integer isTpRelative;
	/**
	 * 完成时间(出院办理完毕或门诊诊结时间)
	 */
	private Date timeCompleted;
	/**
	 * 就诊次数(门诊、住院分开计数)
	 */
	private Integer visitTimes;
}
