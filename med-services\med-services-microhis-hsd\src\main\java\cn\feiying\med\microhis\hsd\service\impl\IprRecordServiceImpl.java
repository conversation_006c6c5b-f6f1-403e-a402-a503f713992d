package cn.feiying.med.microhis.hsd.service.impl;

import javax.annotation.Resource;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.enums.ClinicType;
import cn.feiying.med.hip.enums.IprRecordStatus;
import cn.feiying.med.hip.enums.VisitStatus;
import cn.feiying.med.microhis.hsd.dto.IprRecordDto;
import cn.feiying.med.microhis.hsd.dto.VisitDto;
import cn.feiying.med.microhis.hsd.entity.IprPendingEntity;
import cn.feiying.med.microhis.hsd.entity.VisitEntity;
import cn.feiying.med.microhis.hsd.service.IprPendingService;
import cn.feiying.med.microhis.hsd.service.VisitService;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.feiying.med.common.utils.GQueryWrapper;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.common.utils.Query;

import cn.feiying.med.microhis.hsd.dao.IprRecordDao;
import cn.feiying.med.microhis.hsd.entity.IprRecordEntity;
import cn.feiying.med.microhis.hsd.service.IprRecordService;
import cn.feiying.med.hip.mdi.service.SysSequenceService;
import lombok.extern.slf4j.Slf4j;

/**
 * 住院申请记录表
 *
 * <AUTHOR> 2023-09-21 15:15:53
 */
@Slf4j
@Service("iprRecordService")
public class IprRecordServiceImpl extends ServiceImpl<IprRecordDao, IprRecordEntity> implements IprRecordService {

    @Resource
    private VisitService visitService;
    @Resource
    private IprPendingService iprPendingService;
    @Resource
    private SysSequenceService sequenceService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<IprRecordEntity> wrapper = new GQueryWrapper<IprRecordEntity>().getWrapper(params);
        IPage<IprRecordEntity> page = this.page(new Query<IprRecordEntity>().getPage(params), wrapper);

        return new PageUtils(page);
    }

    @Override
    public IprRecordDto findById(Long iprId) {
        return baseMapper.findById(iprId);
    }

    @Override
    public IprRecordDto findByVisitId(Long visitId) {
        QueryWrapper<IprRecordDto> wrapper = new GQueryWrapper<IprRecordDto>().getWrapper();
        String sql = "select * from microhis_hsd.t_visit where t_visit.IPR_ID = t_ipr_record.IPR_ID and t_visit.Visit_ID = " + visitId;
        wrapper.exists(sql);
        wrapper.orderByDesc("t_ipr_record.Time_Admission");
        List<IprRecordDto> list = baseMapper.queryPage(wrapper);
        if (list.size() > 0) {
            VisitDto visit = visitService.findById(visitId, false);
            list.forEach(item -> {
                item.setApplyDeptCode(visit.getDeptCode());
                item.setApplyDeptName(visit.getDeptName());
                item.setTelNo(visit.getTelNo());
                item.setIdcertNo(visit.getIdcertNo());
                item.setMarriageStatusName(visit.getMarriageStatusName());
                item.setLivingZoneName(visit.getLivingZoneName());
                item.setLivingAddr(visit.getLivingAddr());
            });
            return list.get(0);
        }
        return null;
    }

    @Override
    @Transactional
    public Long saveEntity(IprRecordEntity entity) {
        log.debug("保存住院申请信息entity:{}", JSONUtil.toJsonStr(entity));
        VisitDto visit = null;
        if (entity.getVisitId() != null) {
            visit = visitService.findById(entity.getVisitId(), true);
            if (visit == null) {
                throw new SaveFailureException("未找到诊疗记录信息。");
            }
        }
        // 查询患者是否已存在
        if (entity.getPatientId() != null) {
            Long orgId = visit != null ? visit.getOrgId() : entity.getOrgId();
            long count = visitService.count(Wrappers.lambdaQuery(VisitEntity.class).eq(VisitEntity::getOrgId, orgId)
                    .eq(VisitEntity::getPatientId, entity.getPatientId())
                    .eq(VisitEntity::getClinicTypeId, ClinicType.inpatient.getValue())
                    .ne(VisitEntity::getVisitStatus, VisitStatus.outHospital.getValue()));
            if (count > 0) {
                throw new SaveFailureException("该患者已在院，不用再申请住院。");
            }
        }
        // 判断是否已有待入院的住院申请
        if (count(Wrappers.lambdaQuery(IprRecordEntity.class).eq(IprRecordEntity::getPatientId, entity.getPatientId())
                .eq(IprRecordEntity::getStatus, IprRecordStatus.WAIT_IN_HOSPITAL.getValue())) > 0) {
            throw new SaveFailureException("该患者已有待入院申请，请勿重复申请。");
        }
        Long iprId = sequenceService.getLongNextValue(IprRecordEntity.class.getSimpleName());
        entity.setIprId(iprId);
        entity.setVisitId(null);
        if (visit != null) {
            entity.setPatientId(visit.getPatientId());
            entity.setPatientName(visit.getPatientName());
            entity.setAgeOfYears(visit.getAgeOfYears());
            entity.setAgeOfDays(Convert.toInt(visit.getAgeOfDays()));
            entity.setGenderId(visit.getGenderId());
            entity.setMdtrtCertTypeId(visit.getMdtrtCertTypeId());
            entity.setMdtrtCertText(visit.getMdtrtCertText());
        }
        if (entity.getTimeAdmission() == null) {
            entity.setTimeAdmission(new Date());
        }
        entity.setStatus(IprRecordStatus.WAIT_IN_HOSPITAL.getValue());
        save(entity);
        // 修改诊疗住院证
        if (visit != null) {
            LambdaUpdateWrapper<VisitEntity> wrapper = Wrappers.lambdaUpdate(VisitEntity.class);
            wrapper.eq(VisitEntity::getVisitId, visit.getVisitId())
                    .set(VisitEntity::getIprId, iprId);
            visitService.update(wrapper);
        }
        //
        IprPendingEntity iprPending = IprPendingEntity.builder().iprId(iprId).build();
        iprPendingService.save(iprPending);
        return iprId;
    }

    @Override
    @Transactional
    public void cancelEntity(Long userId, List<Long> iprIdLs) {
        if (ObjectUtil.isNotEmpty(iprIdLs)) {
            List<IprRecordEntity> iprRecordLs = listByIds(iprIdLs);
            iprRecordLs.forEach(iprRecord -> {
                if (iprRecord.getStatus().equals(IprRecordStatus.CANCEL.getValue())) {
                    throw new SaveFailureException("[" + iprRecord.getIprId() + "]该申请单已取消");
                }
                if (iprRecord.getStatus().equals(IprRecordStatus.IN_HOSPITAL.getValue())) {
                    throw new SaveFailureException("[" + iprRecord.getIprId() + "]该申请单已办理入院，不能取消");
                }
            });
            LambdaUpdateWrapper<IprRecordEntity> wrapper = Wrappers.lambdaUpdate(IprRecordEntity.class);
            wrapper.in(IprRecordEntity::getIprId, iprIdLs)
                    .set(IprRecordEntity::getStatus, IprRecordStatus.CANCEL.getValue())
                    .set(IprRecordEntity::getIprNo, null);
            update(wrapper);
        }
    }

    @Override
    @Transactional
    public void delete(Integer... idLs) {
        for(Integer id : idLs) {
            removeById(id);
        }
    }
}
