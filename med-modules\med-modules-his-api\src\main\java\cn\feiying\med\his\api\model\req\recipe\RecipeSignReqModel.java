package cn.feiying.med.his.api.model.req.recipe;

import lombok.Data;

import java.util.List;

/**
 * 处方签名请求模型
 */
@Data
public class RecipeSignReqModel {
    
    /**
     * 诊疗ID
     */
    private Long visitId;
    
    /**
     * 签名医师编码
     */
    private String signDoctorCode;
    
    /**
     * 签名医师名称
     */
    private String signDoctorName;
    
    /**
     * 发药药房编码
     */
    private String storeCode;
    
    /**
     * 发药药房名称
     */
    private String storeName;
    
    /**
     * 处方列表
     */
    private List<RecipeSignItem> recipeList;
    
    /**
     * 处方签名项
     */
    @Data
    public static class RecipeSignItem {
        /**
         * 处方号
         */
        private String rxNo;
    }
} 