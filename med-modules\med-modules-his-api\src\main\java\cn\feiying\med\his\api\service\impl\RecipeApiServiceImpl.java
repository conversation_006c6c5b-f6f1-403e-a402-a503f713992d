package cn.feiying.med.his.api.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.entity.ClinicianEntity;
import cn.feiying.med.hip.mdi.entity.RouteTypeEntity;
import cn.feiying.med.hip.mdi.entity.UserCodeEntity;
import cn.feiying.med.hip.mdi.service.ClinicianService;
import cn.feiying.med.hip.mdi.service.RouteTypeService;
import cn.feiying.med.hip.mdi.service.UserCodeService;
import cn.feiying.med.his.api.model.req.recipe.*;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.RecipeRespModel;
import cn.feiying.med.his.api.model.resp.RecipeSignRespModel;
import cn.feiying.med.his.api.service.RecipeApiService;
import cn.feiying.med.saas.api.service.RemoteHsdService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 处方服务接口实现类
 */
@Service
public class RecipeApiServiceImpl implements RecipeApiService {

    @Resource
    private RemoteHsdService remoteHsdService;
    @Resource
    private UserCodeService userCodeService;
    @Resource
    private ClinicianService clinicianService;
    @Resource
    private RouteTypeService routeTypeService;

    @Override
    public ApiResultModel<RecipeRespModel> saveOrUpdateRecipe(RecipeSaveReqModel recipeReq) {
        // 实现处方保存及修改逻辑
        ApiResultModel<RecipeRespModel> result = new ApiResultModel<>();

        try {
            List<RecipeRespModel.RecipeItem> recipeItemLs = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(recipeReq.getRecipeList())) {
                // 先根据rXNo查询处方信息
                List<String> rxNoLs = recipeReq.getRecipeList().stream().map(RecipeModel::getRxNo).distinct().collect(Collectors.toList());
                JSONArray recipeArr = remoteHsdService.findRecipeByRxNo(rxNoLs);
                // 先做一个判断
                for  (RecipeModel recipe : recipeReq.getRecipeList()) {
                    validRecipeIdByRxNo(recipeArr, recipe.getRxNo());
                }
                // 撤销处方签名
                withdrawRecipeSign(recipeArr);
                // 保存处方
                recipeItemLs = saveRecipe(recipeReq.getVisitId(), recipeArr, recipeReq.getRecipeList());
            }
            // 返回结果
            RecipeRespModel responseData = new RecipeRespModel();
            responseData.setVisitId(recipeReq.getVisitId());
            responseData.setRecipeList(recipeItemLs);
            result.setCode(0);
            result.setMsg("操作成功");
            result.setData(responseData);
        } catch (Exception e) {
            result.setCode(-1);
            result.setMsg("操作失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 撤销处方签名
     */
    private void withdrawRecipeSign(JSONArray recipeArr) {
        List<Long> recipeIdLs = getRecipeIdLs(recipeArr);
        if (ObjectUtil.isNotEmpty(recipeIdLs)) {
            remoteHsdService.withdrawRecipeSign(recipeIdLs);
        }
    }

    private List<Long> getRecipeIdLs(JSONArray recipeArr) {
        List<Long> recipeIdLs = new ArrayList<>();
        if (recipeArr != null && !recipeArr.isEmpty()) {
            for (int i = 0; i < recipeArr.size(); i++) {
                JSONObject jsonItem = recipeArr.getJSONObject(i);
                Long recipeId = jsonItem.getLong("recipeId");
                if (recipeId != null && !recipeIdLs.contains(recipeId)) {
                    recipeIdLs.add(recipeId);
                }
            }
        }
        return recipeIdLs;
    }

    /**
     * 保存处方
     */
    private List<RecipeRespModel.RecipeItem> saveRecipe(Long visitId, JSONArray recipeArr, List<RecipeModel> recipeLs) {
        // 获取诊断列表
        JSONArray visitDiagArr = remoteHsdService.findVisitDiagLs(Collections.singletonList(visitId));
        if (visitDiagArr == null || visitDiagArr.isEmpty()) {
            throw new RuntimeException("未找到诊疗的诊断信息，请先回写诊断信息。");
        }

        List<RecipeRespModel.RecipeItem> recipeItemLs = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(recipeLs)) {
            List<String> clinicianNoLs = recipeLs.stream().map(RecipeModel::getClinicianCode).distinct().collect(Collectors.toList());
            List<ClinicianEntity> clinicianLs = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(clinicianNoLs)) {
                clinicianLs =  clinicianService.list(Wrappers.lambdaQuery(ClinicianEntity.class).in(ClinicianEntity::getClinicianNo, clinicianNoLs));
            }
            JSONArray saveRecipeLs = new JSONArray();
            for (RecipeModel recipe : recipeLs) {
                JSONObject recipeObj = new JSONObject();
                // 处方ID
                Long recipeId = getRecipeIdByRxNo(recipeArr, recipe.getRxNo());
                recipeObj.set("recipeId", recipeId);
                recipeObj.set("rxNo", recipe.getRxNo());
                // 处方类型
                recipeObj.set("recipeTypeId", recipe.getRecipeTypeId());
                // 处方分类
                recipeObj.set("recipeCatId", RecipeCat.commonRecipe.getValue());
                // 诊疗ID
                recipeObj.set("visitId", visitId);
                // 开方医生
                if (StrUtil.isNotBlank(recipe.getClinicianCode())) {
                    clinicianLs.stream().filter(item -> StrUtil.equals(item.getClinicianNo(), recipe.getClinicianCode())).findFirst()
                            .ifPresent(clinician -> recipeObj.set("clinicianId", clinician.getClinicianId()));
                }
                // 执行科室
                recipeObj.set("exceDeptcode", recipe.getExceDeptcode());
                // 付数
                recipeObj.set("times", recipe.getTimes());
                // 处方取药类型
                recipeObj.set("deliverType", DeliverType.self.getValue());
                // 病种
                recipeObj.set("miDiseaseCode", recipe.getDiseaseCode());
                recipeObj.set("miDiseaseName", recipe.getDiseaseName());
                // 每剂分装份数
                if (recipe.getRecipeTypeId().equals(RecipeType.zy.getValue())) {
                    recipeObj.set("subpackageCount", 2);
                }
                // 处方组
                JSONArray recipeGroupArr = new JSONArray();
                // 给药途径
                List<String> routeCodeLs = recipe.getRecipeGroupList().stream().map(RecipeGroupModel::getRouteCode).distinct().collect(Collectors.toList());
                List<RouteTypeEntity> routeTypeLs = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(routeCodeLs)) {
                    routeTypeLs = routeTypeService.list(Wrappers.lambdaQuery(RouteTypeEntity.class).in(RouteTypeEntity::getRouteCode, routeCodeLs));
                }
                for (RecipeGroupModel recipeGroup : recipe.getRecipeGroupList()) {
                    JSONObject recipeGroupObj = new JSONObject();
                    // 组号
                    recipeGroupObj.set("groupNo", recipeGroup.getGroupNo());
                    // 频次
                    recipeGroupObj.set("freqCode", recipeGroup.getFreqCode());
                    // 给药途径
                    if (StrUtil.isNotBlank(recipeGroup.getRouteCode())) {
                        routeTypeLs.stream().filter(item -> StrUtil.equals(item.getRouteCode(), recipeGroup.getRouteCode())).findFirst()
                                .ifPresent(r -> recipeGroupObj.set("routeId", r.getRouteId()));
                    }
                    // 疗程周期数
                    recipeGroupObj.set("periodCycles", recipeGroup.getPeriodCycles());
                    // 嘱托
                    recipeGroupObj.set("notice", recipeGroup.getNotice());
                    // 处方组明细
                    JSONArray recipeDetailArr = getRecipeDetailArr(recipeGroup);
                    recipeGroupObj.set("recipeDetailLs", recipeDetailArr);
                    recipeGroupArr.set(recipeGroupObj);
                }
                recipeObj.set("recipeGroupLs", recipeGroupArr);
                // 诊断
                JSONArray recipeDiagArr = new JSONArray();
                for (int i = 0; i < visitDiagArr.size(); i++) {
                    JSONObject visitDiagObj = visitDiagArr.getJSONObject(i);
                    if (recipe.getRecipeTypeId().equals(RecipeType.xy.getValue()) && (visitDiagObj.getInt("diagTypeId").equals(DiagType.medicalDiag.getValue())
                            || visitDiagObj.getInt("diagTypeId").equals(DiagType.westernDiag.getValue()))) {
                        visitDiagObj.set("diagNo", visitDiagObj.getInt("diagNo"));
                    } else if (recipe.getRecipeTypeId().equals(RecipeType.zy.getValue()) && (visitDiagObj.getInt("diagTypeId").equals(DiagType.tcmMainDiag.getValue())
                            || visitDiagObj.getInt("diagTypeId").equals(DiagType.tcmOtherDiag.getValue()))) {
                        visitDiagObj.set("diagNo", visitDiagObj.getInt("diagNo"));
                    }
                    recipeDiagArr.set(visitDiagObj);
                }
                recipeObj.set("recipeDiagLs", recipeDiagArr);
                saveRecipeLs.set(recipeObj);
            }
            // 保存处方
            Map<String, Object> params = new HashMap<>();
            if (ObjectUtil.isNotEmpty(clinicianLs)) {
                params.put("clinicianId", clinicianLs.get(0).getClinicianId());
            }
            params.put("recipeLs", saveRecipeLs);
            JSONArray recipeRespArr = remoteHsdService.saveRecipe(params);
            for (JSONObject recipeRespObj : recipeRespArr.toList(JSONObject.class)) {
                RecipeRespModel.RecipeItem recipeItem = new RecipeRespModel.RecipeItem();
                recipeItem.setRecipeId(recipeRespObj.getLong("recipeId"));
                recipeItem.setRxNo(recipeRespObj.getStr("rxNo"));
                recipeItem.setOrgId(recipeRespObj.getLong("orgId"));
                List<RecipeRespModel.RecipeDetailItem> recipeDetailItemLs = new ArrayList<>();
                JSONArray recipeDetailArr = recipeRespObj.getJSONArray("recipeDetailLs");
                for (JSONObject recipeDetailObj : recipeDetailArr.toList(JSONObject.class)) {
                    RecipeRespModel.RecipeDetailItem recipeDetailItem = new RecipeRespModel.RecipeDetailItem();
                    recipeDetailItem.setLineNo(recipeDetailObj.getLong("extRecipeDetailId"));
                    recipeDetailItem.setHisLineNo(recipeDetailObj.getInt("lineNo"));
                    recipeDetailItemLs.add(recipeDetailItem);
                }
                recipeItem.setRecipeDetailList(recipeDetailItemLs);
                recipeItemLs.add(recipeItem);
                // 处方签名
//                recipeLs.stream().filter(item -> StrUtil.equals(item.getRxNo(), recipeRespObj.getStr("rxNo"))).findFirst()
//                        .ifPresent(recipeModel ->
//                                remoteHsdService.signRecipe(recipeRespObj.getLong("orgId"), recipeRespObj.getLong("clinicianId"),
//                                        null, recipeRespObj.getInt("sectionId"), recipeModel.getStoreCode(),
//                                        Collections.singletonList(recipeItem.getRecipeId()))
//                        );
            }
        }
        return recipeItemLs;
    }

    /**
     * 处方组明细
     */
    private @NotNull JSONArray getRecipeDetailArr(RecipeGroupModel recipeGroup) {
        JSONArray recipeDetailArr = new JSONArray();
        // 先根据displayOrder排序
        recipeGroup.getRecipeDetailList().sort(Comparator.comparing(RecipeDetailModel::getDisplayOrder));
        for (RecipeDetailModel recipeDetail : recipeGroup.getRecipeDetailList()) {
            JSONObject recipeDetailObj = new JSONObject();
            // 外部处方许号
            recipeDetailObj.set("extRecipeDetailId", recipeDetail.getLineNo());
            // 组号
            recipeDetailObj.set("groupNo", recipeGroup.getGroupNo());
            // 条目代号
            recipeDetailObj.set("artId", recipeDetail.getArtCode());
            // 每次制剂数量
            recipeDetailObj.set("mealCells", recipeDetail.getMealCells());
            // 制剂单位
            recipeDetailObj.set("cellUnit", recipeDetail.getCellUnit());
            // 每次使用剂量
            recipeDetailObj.set("mealDoses", recipeDetail.getMealDoses());
            // 剂量单位
            recipeDetailObj.set("doseUnit", recipeDetail.getDoseUnit());
            // 数量
            recipeDetailObj.set("total", recipeDetail.getTotal());
            // 单位
            recipeDetailObj.set("unit", recipeDetail.getUnit());
            // 单位类型
            recipeDetailObj.set("unitType", recipeDetail.getUnitType());
            // 制法说明
            recipeDetailObj.set("processMethod", recipeDetail.getProcessMethod());
            // 是否需要皮试
            recipeDetailObj.set("stRequired", recipeDetail.getStRequired());

            recipeDetailArr.set(recipeDetailObj);
        }
        return recipeDetailArr;
    }

    private void validRecipeIdByRxNo(JSONArray recipeArr, String originRxNo) {
        if (ObjectUtil.isNotEmpty(recipeArr)) {
            for (int i = 0; i < recipeArr.size(); i++) {
                JSONObject jsonItem = recipeArr.getJSONObject(i);
                String rxNo = jsonItem.getStr("rxNo");
                if (StrUtil.equals(originRxNo, rxNo)) {
                    Integer execStatus = jsonItem.getInt("execStatus");
                    Integer paidStatus = jsonItem.getInt("paidStatus");
                    if (paidStatus.equals(PaidStatus.paid.getValue())) {
                        throw new SaveFailureException("处方【" + originRxNo + "】已支付,不能在修改。");
                    }
                    if (execStatus.equals(ExecStatus.cancel.getValue())) {
                        throw new SaveFailureException("处方【" + originRxNo + "】已作废,不能在修改。");
                    }
                    if (execStatus.equals(ExecStatus.finish.getValue())) {
                        throw new SaveFailureException("处方【" + originRxNo + "】已完成,不能在修改。");
                    }
                    break;
                }
            }
        }
    }

    private Long getRecipeIdByRxNo(JSONArray recipeArr, String originRxNo) {
        Long recipeId = null;
        if (ObjectUtil.isNotEmpty(recipeArr)) {
            for (int i = 0; i < recipeArr.size(); i++) {
                JSONObject jsonItem = recipeArr.getJSONObject(i);
                String rxNo = jsonItem.getStr("rxNo");
                if (StrUtil.equals(originRxNo, rxNo)) {
                    Integer execStatus = jsonItem.getInt("execStatus");
                    if (execStatus.equals(ExecStatus.cancel.getValue())) {
                        throw new SaveFailureException("处方【" + originRxNo + "】已作废,不能在修改。");
                    }
                    recipeId = jsonItem.getLong("recipeId");
                    break;
                }
            }
        }
        return recipeId;
    }

    @Override
    public ApiResultModel<RecipeSignRespModel> signRecipe(RecipeSignReqModel recipeSignReq) {
        ApiResultModel<RecipeSignRespModel> result = new ApiResultModel<>();

        try {
            List<String> rxNoLs = recipeSignReq.getRecipeList().stream().map(RecipeSignReqModel.RecipeSignItem::getRxNo).distinct().collect(Collectors.toList());
            JSONArray jsonArray = remoteHsdService.findRecipeByRxNo(rxNoLs);
            List<Long> recipeIdLs = getRecipeIdLs(jsonArray);
            List<ClinicianEntity> clinicianLs = new ArrayList<>();
            if (StrUtil.isNotBlank(recipeSignReq.getSignDoctorCode())) {
                clinicianLs =  clinicianService.list(Wrappers.lambdaQuery(ClinicianEntity.class).eq(ClinicianEntity::getClinicianNo, recipeSignReq.getSignDoctorCode()));
            }

            // 调用签名服务
            JSONArray signResponse = remoteHsdService.signRecipe(recipeSignReq.getVisitId(), clinicianLs.get(0).getClinicianId(),
                    null, null, recipeSignReq.getStoreCode(), recipeIdLs);
            RecipeSignRespModel responseData = BeanUtil.toBean(signResponse, RecipeSignRespModel.class);
            result.setCode(0);
            result.setMsg("操作成功");
            result.setData(responseData);

        } catch (Exception e) {
            result.setCode(-1);
            result.setMsg("操作失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public ApiResultModel<?> cancelRecipe(List<RecipeCancelModel> recipeLs) {
        // 实现处方作废逻辑
        ApiResultModel<Object> result = new ApiResultModel<>();
        
        try {
            // 实际实现处方作废逻辑
            if (ObjectUtil.isNotEmpty(recipeLs)) {
                List<String> rxNoLs = recipeLs.stream().map(RecipeCancelModel::getRxNo).distinct().collect(Collectors.toList());
                List<String> userCodeLs = recipeLs.stream().map(RecipeCancelModel::getUserCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                JSONArray jsonArray = remoteHsdService.findRecipeByRxNo(rxNoLs);
                List<Long> recipeIdLs = getRecipeIdLs(jsonArray);
                // 获取用户信息
                Long userId = null;
                if (ObjectUtil.isNotEmpty(userCodeLs)) {
                    List<UserCodeEntity> userCodeList = userCodeService.list(Wrappers.<UserCodeEntity>lambdaQuery().in(UserCodeEntity::getUserCode, userCodeLs));
                    userId = ObjectUtil.isNotEmpty(userCodeList) ? userCodeList.get(0).getUserId() : null;
                }
                remoteHsdService.cancelRecipe(userId, recipeIdLs);
            } else {
                throw new SaveFailureException("待作废的处方不能为空。");
            }
            result.setCode(0);
            result.setMsg("操作成功");
        } catch (Exception e) {
            result.setCode(-1);
            result.setMsg("操作失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public ApiResultModel<?> updateRecipeStatus(List<RecipeStatusUpdateModel> recipeLs) {
        // 实现处方状态回写逻辑
        ApiResultModel<Object> result = new ApiResultModel<>();
        
        try {
            // 根据状态来分组
            if (ObjectUtil.isNotEmpty(recipeLs)) {
                // 根据处方号获取处方ID
                List<String> rxNoLs = recipeLs.stream().map(RecipeStatusUpdateModel::getRxNo).distinct().collect(Collectors.toList());
                JSONArray recipeArr = remoteHsdService.findRecipeByRxNo(rxNoLs);
                Map<Integer, List<RecipeStatusUpdateModel>> recipeStatusMap = recipeLs.stream().collect(Collectors.groupingBy(RecipeStatusUpdateModel::getStatus));
                for (Map.Entry<Integer, List<RecipeStatusUpdateModel>> entry : recipeStatusMap.entrySet()) {
                    Integer status = entry.getKey();
                    if (status == 3) {
                        throw new SaveFailureException("撤销状态请调用作废接口。");
                    }
                    List<RecipeStatusUpdateModel> list = entry.getValue();
                    List<Long> recipeIdLs = new ArrayList<>();
                    for (RecipeStatusUpdateModel recipeStatusUpdateModel : list) {
                        Long recipeId = getRecipeIdByRxNo(recipeArr, recipeStatusUpdateModel.getRxNo());
                        recipeIdLs.add(recipeId);
                    }
                    Integer execStatus = null;
                    Integer paidStatus = null;
                    if (status == 1) {
                        paidStatus = PaidStatus.paid.getValue();
                    } else if (status == 2) {
                        execStatus = ExecStatus.finish.getValue();
                    }
                    remoteHsdService.updateRecipeStatus(paidStatus, execStatus, recipeIdLs);
                }
            } else {
                throw new SaveFailureException("待修改的处方不能为空。");
            }
            result.setCode(0);
            result.setMsg("操作成功");
        } catch (Exception e) {
            result.setCode(-1);
            result.setMsg("操作失败：" + e.getMessage());
        }
        
        return result;
    }
} 