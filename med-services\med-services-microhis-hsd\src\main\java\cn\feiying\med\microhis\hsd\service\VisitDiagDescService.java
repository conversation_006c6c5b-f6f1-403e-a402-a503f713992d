package cn.feiying.med.microhis.hsd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import cn.feiying.med.microhis.hsd.entity.VisitDiagDescEntity;

/**
 * 诊疗记录诊断描述表
 *
 * <AUTHOR> @email 
 * @date 2025-05-29 10:30:00
 */
public interface VisitDiagDescService extends IService<VisitDiagDescEntity> {

    /**
     * 保存或更新诊断描述信息
     * @param visitDiagDescEntity 诊断描述实体
     */
    void saveOrUpdateDiagDesc(VisitDiagDescEntity visitDiagDescEntity);

} 