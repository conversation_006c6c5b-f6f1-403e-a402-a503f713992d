<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.feiying.med.clinics_wm.dao.WmBillDao">
    <sql id="baseDtoSql">
        select t_wm_bill.*
             , t_scm_cust.Cust_Name
             , Creator.User_Name   as Creator_UName
             , Validator.User_Name as Validator_UName
             , t_wm_bill_type.WMBill_Type_Name
             , t_org_dept.Dept_Name
             , recv_dept.Dept_Name as Recv_Dept_Name
             , t_section.Section_Name
             , t_org.Org_Name
             , t_wm_req.Notes AS Req_Notes
             , t_wm_req.Apply_Deptcode as Apply_Deptcode
             , Apply_Dept.Dept_Name as Apply_Deptname
        <include refid="baseDtoFormSql1"></include>
    </sql>
    <sql id="baseDtoFormSql">
        from microhis_clinics_wm.t_wm_bill
         left join microhis_clinics_wm.t_scm_cust on t_wm_bill.Cust_ID = t_scm_cust.Cust_ID
         left join hip_mdi.t_user_code Creator on t_wm_bill.Creator_UID = Creator.User_ID
         left join hip_mdi.t_user_code Validator on t_wm_bill.Validator_UID = Validator.User_ID
         left join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
         left join hip_mdi.t_org_dept on t_wm_bill.Org_ID = t_org_dept.Org_ID and t_wm_bill.Dept_Code = t_org_dept.Dept_Code
         left join hip_mdi.t_org_dept recv_dept on t_wm_bill.Org_ID = recv_dept.Org_ID and t_wm_bill.Recv_Dept_Code = recv_dept.Dept_Code
         left join hip_mdi.t_section on t_wm_bill.Section_ID = t_section.Section_ID
         left join hip_mdi.t_org on t_wm_bill.Org_ID = t_org.Org_ID
         left join microhis_clinics_wm.t_wm_req on t_wm_req.WB_SeqID = t_wm_bill.WB_SeqID
         left join hip_mdi.t_org_dept transfer_dept on t_wm_bill.Org_ID = transfer_dept.Org_ID
	     and t_wm_bill.Transfer_Dept_Code = transfer_dept.Dept_Code
         left join hip_mdi.t_org_dept Apply_Dept on t_wm_req.Org_ID = Apply_Dept.Org_ID and t_wm_req.Apply_Deptcode = Apply_Dept.Dept_Code
    </sql>

    <sql id="baseDtoFormSql1">
        , transfer_dept.Dept_Name as Transfer_Dept_Name
        <include refid="baseDtoFormSql"/>
    </sql>
    <sql id="basePendingDtoSql">
        select t_wm_bill.*
             , t_scm_cust.Cust_Name
             , Creator.User_Name   as Creator_UName
             , Validator.User_Name as Validator_UName
             , t_wm_bill_type.WMBill_Type_Name
             , t_org_dept.Dept_Name
             , recv_dept.Dept_Name as Recv_Dept_Name
             , t_section.Section_Name
             , t_org.Org_Name
             , t_wm_req.Notes AS Req_Notes
             , t_wm_req.Apply_Deptcode as Apply_Deptcode
             , Apply_Dept.Dept_Name as Apply_Deptname
        <include refid="basePendingDtoFormSql1"></include>
    </sql>
    <sql id="basePendingDtoFormSql">
        from microhis_clinics_wm.t_wm_bill_pending
         inner join microhis_clinics_wm.t_wm_bill on t_wm_bill_pending.WB_SeqID = t_wm_bill.WB_SeqID
         left join microhis_clinics_wm.t_scm_cust on t_wm_bill.Cust_ID = t_scm_cust.Cust_ID
         left join hip_mdi.t_user_code Creator on t_wm_bill.Creator_UID = Creator.User_ID
         left join hip_mdi.t_user_code Validator on t_wm_bill.Validator_UID = Validator.User_ID
         left join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
         left join hip_mdi.t_org_dept on t_wm_bill.Org_ID = t_org_dept.Org_ID and t_wm_bill.Dept_Code = t_org_dept.Dept_Code
         left join hip_mdi.t_org_dept recv_dept on t_wm_bill.Org_ID = recv_dept.Org_ID and t_wm_bill.Recv_Dept_Code = recv_dept.Dept_Code
         left join hip_mdi.t_section on t_wm_bill.Section_ID = t_section.Section_ID
         left join hip_mdi.t_org on t_wm_bill.Org_ID = t_org.Org_ID
         left join microhis_clinics_wm.t_wm_req on t_wm_req.WB_SeqID = t_wm_bill_pending.WB_SeqID
         left join hip_mdi.t_org_dept Apply_Dept on t_wm_req.Org_ID = Apply_Dept.Org_ID and t_wm_req.Apply_Deptcode = Apply_Dept.Dept_Code
         left join hip_mdi.t_org_dept transfer_dept on t_wm_bill.Org_ID = transfer_dept.Org_ID
	        and t_wm_bill.Transfer_Dept_Code = transfer_dept.Dept_Code
    </sql>
    <sql id="basePendingDtoFormSql1">
        , transfer_dept.Dept_Name as Transfer_Dept_Name
        <include refid="basePendingDtoFormSql"/>
    </sql>
    <update id="updateTrackcodeCollected">
        update microhis_clinics_wm.t_wm_bill
            set TrackCode_Collected = (select count(1) from microhis_clinics_wm.t_wm_bill_trackcode where t_wm_bill_trackcode.WB_SeqID = t_wm_bill.WB_SeqID)
        where WB_SeqID = #{wbSeqid}
    </update>
    <update id="updateAmount">
        update microhis_clinics_wm.t_wm_bill set Amount =
          (select sum(amount) from microhis_clinics_wm.t_wm_bill_detail where t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID),
           cost=(select sum(cost) from microhis_clinics_wm.t_wm_bill_detail where t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID)
              where t_wm_bill.WB_SeqID = #{wbSeqid}
    </update>
    <update id="updateCost">
        update microhis_clinics_wm.t_wm_bill set Cost =
            (select sum(Cost) from microhis_clinics_wm.t_wm_bill_detail where t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID)
        where t_wm_bill.WB_SeqID = #{wbSeqid}
    </update>
    <update id="resetCostAmountByDetail">
        UPDATE microhis_clinics_wm.t_wm_bill
            JOIN (SELECT WB_SeqID,
                         SUM(Cost)   AS total_cost,
                         SUM(Amount) AS total_amount
                  FROM microhis_clinics_wm.t_wm_bill_detail
                  where t_wm_bill_detail.WB_SeqID = #{wbSeqId}
                  GROUP BY WB_SeqID) d_sum ON t_wm_bill.WB_SeqID = d_sum.WB_SeqID
        SET t_wm_bill.Cost   = d_sum.total_cost,
            t_wm_bill.Amount = d_sum.total_amount
        WHERE t_wm_bill.WB_SeqID = #{wbSeqId}
    </update>

    <select id="findDtoById" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        <include refid="baseDtoSql"></include>
        where t_wm_bill.wb_seqid = #{wbSeqid}
    </select>

    <select id="queryPendingPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        select t_wm_bill.*,
               t_wm_bill_type.WMBill_Type_Name,
               t_user_code.User_Name,
               t_org_dept.Dept_Name
        from microhis_clinics_wm.t_wm_bill_pending
                 inner join microhis_clinics_wm.t_wm_bill on t_wm_bill_pending.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
                 left join hip_mdi.t_user_code on t_wm_bill.Creator_UID = t_user_code.User_ID
                 left join hip_mdi.t_org_dept
                           on t_wm_bill.Org_ID = t_org_dept.Org_ID and t_wm_bill.Dept_Code = t_org_dept.Dept_Code
        ${ew.customSqlSegment}
    </select>

    <select id="queryDtoPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        <include refid="baseDtoSql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="queryDtoSum" resultType="java.util.Map">
        select sum(t_wm_bill.Amount) as Amount,sum(t_wm_bill.Cost) as Cost
        <include refid="baseDtoFormSql"></include>
        ${ew.customSqlSegment}
    </select>

    <select id="queryPendingDtoPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        <include refid="basePendingDtoSql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="queryPendingDtoSum" resultType="java.util.Map">
        select sum(t_wm_bill.Amount) as Amount,sum(t_wm_bill.Cost) as Cost
        <include refid="basePendingDtoFormSql"></include>
        ${ew.customSqlSegment}
    </select>

<!--    <select id="queryCustBillCountPage" resultType="cn.feiying.med.clinics_wm.model.CustBillCountModel">-->
<!--        select t.Cust_ID, t.Section_ID, t_scm_cust.Cust_Name, t_section.Section_Name,-->
<!--        sum(t.in_store_count) as in_store_count, sum(t.in_store_amount) as in_store_amount,-->
<!--        sum(t.trans_out_count) as trans_out_count, sum(t.trans_out_amount) as trans_out_amount ,-->
<!--        sum(t.sale_count) as sale_count, sum(t.sale_amount) as sale_amount ,-->
<!--        sum(t.loss_count) as loss_count, sum(t.loss_amount) as loss_amount-->
<!--        from (-->
<!--            &#45;&#45; 入库笔数，入库金额-->
<!--            select t_wm_bill.Cust_ID,-->
<!--                t_wm_bill.Section_ID,-->
<!--                count(*)    as in_store_count,-->
<!--                sum(Amount) as in_store_amount,-->
<!--                0           as trans_out_count,-->
<!--                0           as trans_out_amount,-->
<!--                0           as sale_count,-->
<!--                0           as sale_amount,-->
<!--                0           as loss_count,-->
<!--                0           as loss_amount-->
<!--            from microhis_clinics_wm.t_wm_bill-->
<!--            where t_wm_bill.WMBill_Type_ID = 11 &#45;&#45; 入库-->
<!--            and t_wm_bill.Bsn_Type in (1, 4, 5)  &#45;&#45; 采购入库，库存调拔，采购直调-->
<!--            and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--            and t_wm_bill.Org_ID = #{orgId}-->
<!--            and t_wm_bill.Dept_Code = #{deptCode}-->
<!--            and t_wm_bill.Time_Validated >= #{startDate}-->
<!--            and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--            group by t_wm_bill.Cust_ID, t_wm_bill.Section_ID-->
<!--            union all-->
<!--            &#45;&#45; 入库冲红笔数，入库冲红金额-->
<!--            select t_wm_bill.Cust_ID,-->
<!--                t_wm_bill.Section_ID,-->
<!--                count(*)    as in_store_count,-->
<!--                sum(Amount * -1) as in_store_amount,-->
<!--                0           as trans_out_count,-->
<!--                0           as trans_out_amount,-->
<!--                0           as sale_count,-->
<!--                0           as sale_amount,-->
<!--                0           as loss_count,-->
<!--                0           as loss_amount-->
<!--            from microhis_clinics_wm.t_wm_bill-->
<!--            where t_wm_bill.WMBill_Type_ID = 12 &#45;&#45; 入库冲红-->
<!--            and t_wm_bill.Bsn_Type in (1, 4, 5)  &#45;&#45; 采购入库，库存调拔，采购直调-->
<!--            and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--            and t_wm_bill.Org_ID = #{orgId}-->
<!--            and t_wm_bill.Dept_Code = #{deptCode}-->
<!--            and t_wm_bill.Time_Validated >= #{startDate}-->
<!--            and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--            group by t_wm_bill.Cust_ID, t_wm_bill.Section_ID-->
<!--            union all-->
<!--            &#45;&#45; 调出笔数，调出金额-->
<!--            select t_wm_bill.Cust_ID,-->
<!--                t_wm_bill.Section_ID,-->
<!--                0           as in_store_count,-->
<!--                0           as in_store_amount,-->
<!--                count(*)    as trans_out_count,-->
<!--                sum(Amount) as trans_out_amount,-->
<!--                0           as sale_count,-->
<!--                0           as sale_amount,-->
<!--                0           as loss_count,-->
<!--                0           as loss_amount-->
<!--            from microhis_clinics_wm.t_wm_bill-->
<!--            where t_wm_bill.WMBill_Type_ID = 21 &#45;&#45; 出库-->
<!--            and t_wm_bill.Bsn_Type = 4        &#45;&#45; 库存调拔-->
<!--            and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--            and t_wm_bill.Org_ID = #{orgId}-->
<!--            and t_wm_bill.Dept_Code = #{deptCode}-->
<!--            and t_wm_bill.Time_Validated >= #{startDate}-->
<!--            and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--            group by t_wm_bill.Cust_ID, t_wm_bill.Section_ID-->
<!--            union all-->
<!--            &#45;&#45; 调出冲红笔数，调出冲红金额-->
<!--            select t_wm_bill.Cust_ID,-->
<!--                t_wm_bill.Section_ID,-->
<!--                0           as in_store_count,-->
<!--                0           as in_store_amount,-->
<!--                count(*)    as trans_out_count,-->
<!--                sum(Amount * -1) as trans_out_amount,-->
<!--                0           as sale_count,-->
<!--                0           as sale_amount,-->
<!--                0           as loss_count,-->
<!--                0           as loss_amount-->
<!--            from microhis_clinics_wm.t_wm_bill-->
<!--            where t_wm_bill.WMBill_Type_ID = 22 &#45;&#45; 出库-->
<!--            and t_wm_bill.Bsn_Type = 4        &#45;&#45; 库存调拔-->
<!--            and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--            and t_wm_bill.Org_ID = #{orgId}-->
<!--            and t_wm_bill.Dept_Code = #{deptCode}-->
<!--            and t_wm_bill.Time_Validated >= #{startDate}-->
<!--            and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--            group by t_wm_bill.Cust_ID, t_wm_bill.Section_ID-->
<!--            union all-->
<!--            &#45;&#45; 销售笔数，销售金额-->
<!--            select t_wm_bill.Cust_ID,-->
<!--                t_wm_bill.Section_ID,-->
<!--                0           as in_store_count,-->
<!--                0           as in_store_amount,-->
<!--                0           as trans_out_count,-->
<!--                0           as trans_out_amount,-->
<!--                count(*)    as sale_count,-->
<!--                sum(Amount) as sale_amount,-->
<!--                0           as loss_count,-->
<!--                0           as loss_amount-->
<!--            from microhis_clinics_wm.t_wm_bill-->
<!--            where t_wm_bill.WMBill_Type_ID = 21 &#45;&#45; 出库-->
<!--            and t_wm_bill.Bsn_Type = 2        &#45;&#45; 销售出库-->
<!--            and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--            and t_wm_bill.Org_ID = #{orgId}-->
<!--            and t_wm_bill.Dept_Code = #{deptCode}-->
<!--            and t_wm_bill.Time_Validated >= #{startDate}-->
<!--            and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--            group by t_wm_bill.Cust_ID, t_wm_bill.Section_ID-->
<!--            union all-->
<!--            &#45;&#45; 销售冲红笔数，销售冲红金额-->
<!--            select t_wm_bill.Cust_ID,-->
<!--                t_wm_bill.Section_ID,-->
<!--                0           as in_store_count,-->
<!--                0           as in_store_amount,-->
<!--                0           as trans_out_count,-->
<!--                0           as trans_out_amount,-->
<!--                count(*)    as sale_count,-->
<!--                sum(Amount * -1) as sale_amount,-->
<!--                0           as loss_count,-->
<!--                0           as loss_amount-->
<!--            from microhis_clinics_wm.t_wm_bill-->
<!--            where t_wm_bill.WMBill_Type_ID = 22 &#45;&#45; 出库-->
<!--            and t_wm_bill.Bsn_Type = 2        &#45;&#45; 销售出库-->
<!--            and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--            and t_wm_bill.Org_ID = #{orgId}-->
<!--            and t_wm_bill.Dept_Code = #{deptCode}-->
<!--            and t_wm_bill.Time_Validated >= #{startDate}-->
<!--            and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--            group by t_wm_bill.Cust_ID, t_wm_bill.Section_ID-->
<!--            union all-->
<!--            &#45;&#45; 报溢笔数，报溢金额-->
<!--            select t_wm_bill.Cust_ID,-->
<!--                t_wm_bill.Section_ID,-->
<!--                0           as in_store_count,-->
<!--                0           as in_store_amount,-->
<!--                0           as trans_out_count,-->
<!--                0           as trans_out_amount,-->
<!--                0           as sale_count,-->
<!--                0           as sale_amount,-->
<!--                count(*)    as loss_count,-->
<!--                sum(Amount) as loss_amount-->
<!--            from microhis_clinics_wm.t_wm_bill-->
<!--            where t_wm_bill.WMBill_Type_ID = 11 &#45;&#45; 入库-->
<!--            and t_wm_bill.Bsn_Type = 3        &#45;&#45; 库存损溢-->
<!--            and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--            and t_wm_bill.Org_ID = #{orgId}-->
<!--            and t_wm_bill.Dept_Code = #{deptCode}-->
<!--            and t_wm_bill.Time_Validated >= #{startDate}-->
<!--            and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--            group by t_wm_bill.Cust_ID, t_wm_bill.Section_ID-->
<!--            union all-->
<!--            &#45;&#45; 报损笔数，报损金额-->
<!--            select t_wm_bill.Cust_ID,-->
<!--                t_wm_bill.Section_ID,-->
<!--                0           as in_store_count,-->
<!--                0           as in_store_amount,-->
<!--                0           as trans_out_count,-->
<!--                0           as trans_out_amount,-->
<!--                0           as sale_count,-->
<!--                0           as sale_amount,-->
<!--                count(*)    as loss_count,-->
<!--                sum(Amount * -1) as loss_amount-->
<!--            from microhis_clinics_wm.t_wm_bill-->
<!--            where t_wm_bill.WMBill_Type_ID = 12 &#45;&#45; 入库-->
<!--            and t_wm_bill.Bsn_Type = 3        &#45;&#45; 库存损溢-->
<!--            and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--            and t_wm_bill.Org_ID = #{orgId}-->
<!--            and t_wm_bill.Dept_Code = #{deptCode}-->
<!--            and t_wm_bill.Time_Validated >= #{startDate}-->
<!--            and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--            group by t_wm_bill.Cust_ID, t_wm_bill.Section_ID-->
<!--        ) t-->
<!--        left join microhis_clinics_wm.t_scm_cust on t.Cust_ID = t_scm_cust.Cust_ID-->
<!--        left join hip_mdi.t_section on t.Section_ID = t_section.Section_ID-->
<!--        group by t.Cust_ID, t.Section_ID-->
<!--    </select>-->

    <select id="queryCustBillCountPage" resultType="cn.feiying.med.clinics_wm.model.CustBillCountModel">
        select t_wm_bill.Cust_ID,
               t_wm_bill.Section_ID,
               min(t_scm_cust.Cust_Name)   as Cust_Name,
               min(t_section.Section_Name) as Section_Name,
               sum(case
                       when (t_wm_bill.Bsn_Type_ID = 1 and t_wm_bill.WMBill_Type_ID != 12) or
                            (t_wm_bill.Bsn_Type_ID = 3 and t_wm_bill.WMBill_Type_ID = 32 and
                             t_wm_bill.Section_ID is null) or -- 病区退回放在入库不合适，放调拨更合适
                            (t_wm_bill.Bsn_Type_ID = 4 and t_wm_bill.WMBill_Type_ID = 41 and t_wm_bill.Section_ID is null)
                           then 1
                       else 0 end)         as in_store_count,
               sum(case
                       when (t_wm_bill.Bsn_Type_ID = 1 and t_wm_bill.WMBill_Type_ID != 12) or
                            (t_wm_bill.Bsn_Type_ID = 3 and t_wm_bill.WMBill_Type_ID = 32 and t_wm_bill.Section_ID is null) or
                            (t_wm_bill.Bsn_Type_ID = 4 and t_wm_bill.WMBill_Type_ID = 41 and t_wm_bill.Section_ID is null)
                           then t_wm_bill.Amount
                       when t_wm_bill.WMBill_Type_ID = 12 then t_wm_bill.Amount * -1
                       else 0 end)         as in_store_amount,
               sum(case
                       when (t_wm_bill.Bsn_Type_ID = 3 and t_wm_bill.WMBill_Type_ID = 31) or
                            (t_wm_bill.Bsn_Type_ID = 4 and t_wm_bill.WMBill_Type_ID = 42) or
                            (t_wm_bill.Bsn_Type_ID = 2 and t_wm_bill.WMBill_Type_ID = 21 and
                             t_wm_bill.Section_ID is not null and t_wm_bill.Recipe_ID is null) -- 现在药房向病区发药都是用billTypeId:21 bsnTypeId:2 销售，这部分业务算在调拨
                           then 1
                       else 0 end)         as trans_out_count,
               sum(case
                       when (t_wm_bill.Bsn_Type_ID = 3 and t_wm_bill.WMBill_Type_ID = 31) or
                            (t_wm_bill.Bsn_Type_ID = 4 and t_wm_bill.WMBill_Type_ID = 42) or
                            (t_wm_bill.Bsn_Type_ID = 2 and t_wm_bill.WMBill_Type_ID = 21 and t_wm_bill.Section_ID is not null and t_wm_bill.Recipe_ID is null)
                           then t_wm_bill.Amount
                       when t_wm_bill.Bsn_Type_ID = 3 and t_wm_bill.WMBill_Type_ID = 32 and
                            t_wm_bill.Section_ID is not null
                           then t_wm_bill.Amount * -1
                       else 0 end)         as trans_out_amount,
               sum(case
                       when t_wm_bill.Bsn_Type_ID = 2 and t_wm_bill.WMBill_Type_ID != 22 and (t_wm_bill.Recipe_ID is not null or t_wm_bill.SCMBill_ID is not null) then 1
                       else 0 end)         as sale_count,
               sum(case
                       when t_wm_bill.Bsn_Type_ID = 2 and t_wm_bill.WMBill_Type_ID != 22 and (t_wm_bill.Recipe_ID is not null or t_wm_bill.SCMBill_ID is not null)
                           then t_wm_bill.Amount
                       when t_wm_bill.WMBill_Type_ID = 22 then t_wm_bill.Amount * -1
                       else 0 end)         as sale_amount,
               sum(case
                       when t_wm_bill.WMBill_Type_ID = 51 then 1
                       else 0 end)         as overflow_in_count,
               sum(case
                       when t_wm_bill.WMBill_Type_ID = 51 then t_wm_bill.Amount
                       else 0 end)         as overflow_in_amount,
               sum(case
                       when t_wm_bill.WMBill_Type_ID = 52 then 1
                       else 0 end)         as loss_out_count,
               sum(case
                       when t_wm_bill.WMBill_Type_ID = 52 then t_wm_bill.Amount
                       else 0 end)         as loss_out_amount
        from microhis_clinics_wm.t_wm_bill
                 left join microhis_clinics_wm.t_scm_cust on t_wm_bill.Cust_ID = t_scm_cust.Cust_ID
                 left join hip_mdi.t_section on t_wm_bill.Section_ID = t_section.Section_ID
        where t_wm_bill.Status = 5
          and t_wm_bill.Org_ID = #{orgId}
          and t_wm_bill.Dept_Code = #{deptCode}
          and t_wm_bill.Time_Validated >= #{startDate}
          and t_wm_bill.Time_Validated &lt; #{endDate}
        group by t_wm_bill.Cust_ID, t_wm_bill.Section_ID
    </select>

    <select id="queryCustBillArtPage" resultType="cn.feiying.med.clinics_wm.model.CustBillArtModel">
        select t_wm_bill_detail.WB_SeqID,
        t_wm_bill_detail.Line_No,
        t_wm_bill_detail.Art_ID,
        t_article.MI_Code,
        t_article.Art_Name,
        t_article.Art_Spec,
        t_article.Producer,
        t_article.Pack_Cells,
        t_wm_bill_detail.Total_Packs,
        t_article.Pack_Unit,
        t_wm_bill_detail.Pack_Price,
        t_wm_bill_detail.Total_Cells,
        t_article.Cell_Unit,
        t_wm_bill_detail.Cell_Price,
        t_wm_bill_detail.Amount,
        t_wm_bill_detail.Batch_No,
        t_art_batch.Date_Manufactured,
        t_art_batch.Expiry,
        t_cat_type.Cat_Name
        from microhis_clinics_wm.t_wm_bill_detail
        left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
        left join microhis_clinics_wm.t_art_batch on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
        left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
        left join hip_mdi.t_cat_type on t_article.Cat_Type_ID = t_cat_type.Cat_Type_ID
            ${ew.customSqlSegment}
    </select>
    <!-- 商品业务统计查询-合计-->
    <select id="queryArtBillCountSum" resultType="java.util.Map">
        select
            sum(t.in_store_amount) as inStoreAmount,
            sum(t.trans_out_amount) as transOutAmount,
            sum(t.sale_amount) as saleAmount,
            sum(t.loss_amount) as lossAmount
        from (
-- 入库笔数，入库金额
                 select t_wm_bill_detail.Art_ID,
                        sum(t_wm_bill_detail.Total_Packs) as in_store_packs,
                        sum(t_wm_bill_detail.Total_Cells)    as in_store_cells,
                        sum(t_wm_bill_detail.Amount) as in_store_amount,
                        0 as trans_out_packs,
                        0    as trans_out_cells,
                        0 as trans_out_amount,
                        0 as sale_packs,
                        0    as sale_cells,
                        0 as sale_amount,
                        0 as loss_packs,
                        0    as loss_cells,
                        0 as loss_amount
                 from microhis_clinics_wm.t_wm_bill_detail
                          left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 where t_wm_bill.WMBill_Type_ID = 11 -- 入库
                   and t_wm_bill.Bsn_Type in (1, 4, 5)  -- 采购入库，库存调拔，采购直调
                   and t_wm_bill.Status = 5          -- 已完成
                   and t_wm_bill.Org_ID = #{orgId}
                   and t_wm_bill.Dept_Code = #{deptCode}
                   and t_wm_bill.Time_Validated >= #{startDate}
                   and t_wm_bill.Time_Validated &lt; #{endDate}
                 group by t_wm_bill_detail.Art_ID
                 union all
-- 入库冲红笔数，入库冲红金额
                 select t_wm_bill_detail.Art_ID,
                        sum(t_wm_bill_detail.Total_Packs * -1) as in_store_packs,
                        sum(t_wm_bill_detail.Total_Cells * -1)    as in_store_cells,
                        sum(t_wm_bill_detail.Amount * -1) as in_store_amount,
                        0 as trans_out_packs,
                        0    as trans_out_cells,
                        0 as trans_out_amount,
                        0 as sale_packs,
                        0    as sale_cells,
                        0 as sale_amount,
                        0 as loss_packs,
                        0    as loss_cells,
                        0 as loss_amount
                 from microhis_clinics_wm.t_wm_bill_detail
                          left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 where t_wm_bill.WMBill_Type_ID = 12 -- 入库冲红
                   and t_wm_bill.Bsn_Type in (1, 4, 5)  -- 采购入库，库存调拔，采购直调
                   and t_wm_bill.Status = 5          -- 已完成
                   and t_wm_bill.Org_ID = #{orgId}
                   and t_wm_bill.Dept_Code = #{deptCode}
                   and t_wm_bill.Time_Validated >= #{startDate}
                   and t_wm_bill.Time_Validated &lt; #{endDate}
                 group by t_wm_bill_detail.Art_ID
                 union all
-- 调出笔数，调出金额
                 select t_wm_bill_detail.Art_ID,
                        0 as in_store_packs,
                        0    as in_store_cells,
                        0 as in_store_amount,
                        sum(t_wm_bill_detail.Total_Packs) as trans_out_packs,
                        sum(t_wm_bill_detail.Total_Cells)    as trans_out_cells,
                        sum(t_wm_bill_detail.Amount) as trans_out_amount,
                        0 as sale_packs,
                        0    as sale_cells,
                        0 as sale_amount,
                        0 as loss_packs,
                        0    as loss_cells,
                        0 as loss_amount
                 from microhis_clinics_wm.t_wm_bill_detail
                          left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 where t_wm_bill.WMBill_Type_ID = 21 -- 出库
                   and t_wm_bill.Bsn_Type = 4        -- 库存调拔
                   and t_wm_bill.Status = 5          -- 已完成
                   and t_wm_bill.Org_ID = #{orgId}
                   and t_wm_bill.Dept_Code = #{deptCode}
                   and t_wm_bill.Time_Validated >= #{startDate}
                   and t_wm_bill.Time_Validated &lt; #{endDate}
                 group by t_wm_bill_detail.Art_ID
                 union all
-- 调出冲红笔数，调出冲红金额
                 select t_wm_bill_detail.Art_ID,
                        0 as in_store_packs,
                        0    as in_store_cells,
                        0 as in_store_amount,
                        sum(t_wm_bill_detail.Total_Packs * -1) as trans_out_packs,
                        sum(t_wm_bill_detail.Total_Cells * -1)    as trans_out_cells,
                        sum(t_wm_bill_detail.Amount * -1) as trans_out_amount,
                        0 as sale_packs,
                        0    as sale_cells,
                        0 as sale_amount,
                        0 as loss_packs,
                        0    as loss_cells,
                        0 as loss_amount
                 from microhis_clinics_wm.t_wm_bill_detail
                          left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 where t_wm_bill.WMBill_Type_ID = 22 -- 出库
                   and t_wm_bill.Bsn_Type = 4        -- 库存调拔
                   and t_wm_bill.Status = 5          -- 已完成
                   and t_wm_bill.Org_ID = #{orgId}
                   and t_wm_bill.Dept_Code = #{deptCode}
                   and t_wm_bill.Time_Validated >= #{startDate}
                   and t_wm_bill.Time_Validated &lt; #{endDate}
                 group by t_wm_bill_detail.Art_ID
                 union all
-- 销售笔数，销售金额
                 select t_wm_bill_detail.Art_ID,
                        0 as in_store_packs,
                        0    as in_store_cells,
                        0 as in_store_amount,
                        0 as trans_out_packs,
                        0    as trans_out_cells,
                        0 as trans_out_amount,
                        sum(t_wm_bill_detail.Total_Packs) as sale_packs,
                        sum(t_wm_bill_detail.Total_Cells)    as sale_cells,
                        sum(t_wm_bill_detail.Amount * -1) as sale_amount,
                        0 as loss_packs,
                        0    as loss_cells,
                        0 as loss_amount
                 from microhis_clinics_wm.t_wm_bill_detail
                          left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 where t_wm_bill.WMBill_Type_ID = 21 -- 出库
                   and t_wm_bill.Bsn_Type = 2        -- 销售出库
                   and t_wm_bill.Status = 5          -- 已完成
                   and t_wm_bill.Org_ID = #{orgId}
                   and t_wm_bill.Dept_Code = #{deptCode}
                   and t_wm_bill.Time_Validated >= #{startDate}
                   and t_wm_bill.Time_Validated &lt; #{endDate}
                 group by t_wm_bill_detail.Art_ID
                 union all
-- 销售冲红笔数，销售冲红金额
                 select t_wm_bill_detail.Art_ID,
                        0 as in_store_packs,
                        0    as in_store_cells,
                        0 as in_store_amount,
                        0 as trans_out_packs,
                        0    as trans_out_cells,
                        0 as trans_out_amount,
                        sum(t_wm_bill_detail.Total_Packs * -1) as sale_packs,
                        sum(t_wm_bill_detail.Total_Cells * -1)    as sale_cells,
                        sum(t_wm_bill_detail.Amount * -1) as sale_amount,
                        0 as loss_packs,
                        0    as loss_cells,
                        0 as loss_amount
                 from microhis_clinics_wm.t_wm_bill_detail
                          left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 where t_wm_bill.WMBill_Type_ID = 22 -- 出库
                   and t_wm_bill.Bsn_Type = 2        -- 销售出库
                   and t_wm_bill.Status = 5          -- 已完成
                   and t_wm_bill.Org_ID = #{orgId}
                   and t_wm_bill.Dept_Code = #{deptCode}
                   and t_wm_bill.Time_Validated >= #{startDate}
                   and t_wm_bill.Time_Validated &lt; #{endDate}
                 group by t_wm_bill_detail.Art_ID
                 union all
-- 报溢笔数，报溢金额
                 select t_wm_bill_detail.Art_ID,
                        0 as in_store_packs,
                        0    as in_store_cells,
                        0 as in_store_amount,
                        0 as trans_out_packs,
                        0    as trans_out_cells,
                        0 as trans_out_amount,
                        0 as sale_packs,
                        0    as sale_cells,
                        0 as sale_amount,
                        sum(t_wm_bill_detail.Total_Packs) as loss_packs,
                        sum(t_wm_bill_detail.Total_Cells)    as loss_cells,
                        sum(t_wm_bill_detail.Amount) as loss_amount
                 from microhis_clinics_wm.t_wm_bill_detail
                          left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 where t_wm_bill.WMBill_Type_ID = 11 -- 入库
                   and t_wm_bill.Bsn_Type = 3        -- 库存损溢
                   and t_wm_bill.Status = 5          -- 已完成
                   and t_wm_bill.Org_ID = #{orgId}
                   and t_wm_bill.Dept_Code = #{deptCode}
                   and t_wm_bill.Time_Validated >= #{startDate}
                   and t_wm_bill.Time_Validated &lt; #{endDate}
                 group by t_wm_bill_detail.Art_ID
                 union all
-- 报损笔数，报损金额
                 select t_wm_bill_detail.Art_ID,
                        0 as in_store_packs,
                        0    as in_store_cells,
                        0 as in_store_amount,
                        0 as trans_out_packs,
                        0    as trans_out_cells,
                        0 as trans_out_amount,
                        0 as sale_packs,
                        0    as sale_cells,
                        0 as sale_amount,
                        sum(t_wm_bill_detail.Total_Packs * -1) as loss_packs,
                        sum(t_wm_bill_detail.Total_Cells * -1)    as loss_cells,
                        sum(t_wm_bill_detail.Amount * -1) as loss_amount
                 from microhis_clinics_wm.t_wm_bill_detail
                          left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 where t_wm_bill.WMBill_Type_ID = 12 -- 入库
                   and t_wm_bill.Bsn_Type = 3        -- 库存损溢
                   and t_wm_bill.Status = 5          -- 已完成
                   and t_wm_bill.Org_ID = #{orgId}
                   and t_wm_bill.Dept_Code = #{deptCode}
                   and t_wm_bill.Time_Validated >= #{startDate}
                   and t_wm_bill.Time_Validated &lt; #{endDate}
                 group by t_wm_bill_detail.Art_ID
             ) t
                 left join hip_mdi.t_article on t.Art_ID = t_article.Art_ID
                 left join hip_mdi.t_cat_type on t_article.Cat_Type_ID = t_cat_type.Cat_Type_ID
            ${ew.customSqlSegment}
    </select>

    <select id="queryArtBillCountPage" resultType="cn.feiying.med.clinics_wm.model.ArtBillCountModel">
        select t.Art_ID, min(t_article.MI_Code) as MI_Code, min(t_article.Art_Name) as Art_Name, min(t_article.Art_Spec) as Art_Spec, min(t_article.Producer) as Producer,
       min(t_article.Pack_Unit) as Pack_Unit, min(t_article.Cell_Unit) as Cell_Unit, min(t_article.Pack_Cells) as Pack_Cells, min(t_article.Pack_Material) as Pack_Material,
       min(t_cat_type.Cat_Name) as Cat_Name,
       sum(t.in_store_packs) as in_store_packs, sum(t.in_store_cells) as in_store_cells, sum(t.in_store_amount) as in_store_amount,
       sum(t.trans_out_packs) as trans_out_packs, sum(t.trans_out_cells) as trans_out_cells, sum(t.trans_out_amount) as trans_out_amount,
       sum(t.sale_packs) as sale_packs, sum(t.sale_cells) as sale_cells, sum(t.sale_amount) as sale_amount,
       sum(t.loss_packs) as loss_packs, sum(t.loss_cells) as loss_cells, sum(t.loss_amount) as loss_amount
from (
-- 入库笔数，入库金额
         select t_wm_bill_detail.Art_ID,
                sum(t_wm_bill_detail.Total_Packs) as in_store_packs,
                sum(t_wm_bill_detail.Total_Cells)    as in_store_cells,
                sum(t_wm_bill_detail.Amount) as in_store_amount,
                0 as trans_out_packs,
                0    as trans_out_cells,
                0 as trans_out_amount,
                0 as sale_packs,
                0    as sale_cells,
                0 as sale_amount,
                0 as loss_packs,
                0    as loss_cells,
                0 as loss_amount
         from microhis_clinics_wm.t_wm_bill_detail
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         where t_wm_bill.WMBill_Type_ID = 11 -- 入库
           and t_wm_bill.Bsn_Type in (1, 4, 5)  -- 采购入库，库存调拔，采购直调
           and t_wm_bill.Status = 5          -- 已完成
           and t_wm_bill.Org_ID = #{orgId}
           and t_wm_bill.Dept_Code = #{deptCode}
           and t_wm_bill.Time_Validated >= #{startDate}
           and t_wm_bill.Time_Validated &lt; #{endDate}
         group by t_wm_bill_detail.Art_ID
         union all
-- 入库冲红笔数，入库冲红金额
         select t_wm_bill_detail.Art_ID,
                sum(t_wm_bill_detail.Total_Packs * -1) as in_store_packs,
                sum(t_wm_bill_detail.Total_Cells * -1)    as in_store_cells,
                sum(t_wm_bill_detail.Amount * -1) as in_store_amount,
                0 as trans_out_packs,
                0    as trans_out_cells,
                0 as trans_out_amount,
                0 as sale_packs,
                0    as sale_cells,
                0 as sale_amount,
                0 as loss_packs,
                0    as loss_cells,
                0 as loss_amount
         from microhis_clinics_wm.t_wm_bill_detail
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         where t_wm_bill.WMBill_Type_ID = 12 -- 入库冲红
           and t_wm_bill.Bsn_Type in (1, 4, 5)  -- 采购入库，库存调拔，采购直调
           and t_wm_bill.Status = 5          -- 已完成
           and t_wm_bill.Org_ID = #{orgId}
           and t_wm_bill.Dept_Code = #{deptCode}
           and t_wm_bill.Time_Validated >= #{startDate}
           and t_wm_bill.Time_Validated &lt; #{endDate}
         group by t_wm_bill_detail.Art_ID
         union all
-- 调出笔数，调出金额
         select t_wm_bill_detail.Art_ID,
                0 as in_store_packs,
                0    as in_store_cells,
                0 as in_store_amount,
                sum(t_wm_bill_detail.Total_Packs) as trans_out_packs,
                sum(t_wm_bill_detail.Total_Cells)    as trans_out_cells,
                sum(t_wm_bill_detail.Amount) as trans_out_amount,
                0 as sale_packs,
                0    as sale_cells,
                0 as sale_amount,
                0 as loss_packs,
                0    as loss_cells,
                0 as loss_amount
         from microhis_clinics_wm.t_wm_bill_detail
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         where t_wm_bill.WMBill_Type_ID = 21 -- 出库
           and t_wm_bill.Bsn_Type = 4        -- 库存调拔
           and t_wm_bill.Status = 5          -- 已完成
           and t_wm_bill.Org_ID = #{orgId}
           and t_wm_bill.Dept_Code = #{deptCode}
           and t_wm_bill.Time_Validated >= #{startDate}
           and t_wm_bill.Time_Validated &lt; #{endDate}
         group by t_wm_bill_detail.Art_ID
         union all
-- 调出冲红笔数，调出冲红金额
         select t_wm_bill_detail.Art_ID,
                0 as in_store_packs,
                0    as in_store_cells,
                0 as in_store_amount,
                sum(t_wm_bill_detail.Total_Packs * -1) as trans_out_packs,
                sum(t_wm_bill_detail.Total_Cells * -1)    as trans_out_cells,
                sum(t_wm_bill_detail.Amount * -1) as trans_out_amount,
                0 as sale_packs,
                0    as sale_cells,
                0 as sale_amount,
                0 as loss_packs,
                0    as loss_cells,
                0 as loss_amount
         from microhis_clinics_wm.t_wm_bill_detail
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         where t_wm_bill.WMBill_Type_ID = 22 -- 出库
           and t_wm_bill.Bsn_Type = 4        -- 库存调拔
           and t_wm_bill.Status = 5          -- 已完成
           and t_wm_bill.Org_ID = #{orgId}
           and t_wm_bill.Dept_Code = #{deptCode}
           and t_wm_bill.Time_Validated >= #{startDate}
           and t_wm_bill.Time_Validated &lt; #{endDate}
         group by t_wm_bill_detail.Art_ID
         union all
-- 销售笔数，销售金额
         select t_wm_bill_detail.Art_ID,
                0 as in_store_packs,
                0    as in_store_cells,
                0 as in_store_amount,
                0 as trans_out_packs,
                0    as trans_out_cells,
                0 as trans_out_amount,
                sum(t_wm_bill_detail.Total_Packs) as sale_packs,
                sum(t_wm_bill_detail.Total_Cells)    as sale_cells,
                sum(t_wm_bill_detail.Amount * -1) as sale_amount,
                0 as loss_packs,
                0    as loss_cells,
                0 as loss_amount
         from microhis_clinics_wm.t_wm_bill_detail
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         where t_wm_bill.WMBill_Type_ID = 21 -- 出库
           and t_wm_bill.Bsn_Type = 2        -- 销售出库
           and t_wm_bill.Status = 5          -- 已完成
           and t_wm_bill.Org_ID = #{orgId}
           and t_wm_bill.Dept_Code = #{deptCode}
           and t_wm_bill.Time_Validated >= #{startDate}
           and t_wm_bill.Time_Validated &lt; #{endDate}
         group by t_wm_bill_detail.Art_ID
         union all
-- 销售冲红笔数，销售冲红金额
         select t_wm_bill_detail.Art_ID,
                0 as in_store_packs,
                0    as in_store_cells,
                0 as in_store_amount,
                0 as trans_out_packs,
                0    as trans_out_cells,
                0 as trans_out_amount,
                sum(t_wm_bill_detail.Total_Packs * -1) as sale_packs,
                sum(t_wm_bill_detail.Total_Cells * -1)    as sale_cells,
                sum(t_wm_bill_detail.Amount * -1) as sale_amount,
                0 as loss_packs,
                0    as loss_cells,
                0 as loss_amount
         from microhis_clinics_wm.t_wm_bill_detail
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         where t_wm_bill.WMBill_Type_ID = 22 -- 出库
           and t_wm_bill.Bsn_Type = 2        -- 销售出库
           and t_wm_bill.Status = 5          -- 已完成
           and t_wm_bill.Org_ID = #{orgId}
           and t_wm_bill.Dept_Code = #{deptCode}
           and t_wm_bill.Time_Validated >= #{startDate}
           and t_wm_bill.Time_Validated &lt; #{endDate}
         group by t_wm_bill_detail.Art_ID
         union all
-- 报溢笔数，报溢金额
         select t_wm_bill_detail.Art_ID,
                0 as in_store_packs,
                0    as in_store_cells,
                0 as in_store_amount,
                0 as trans_out_packs,
                0    as trans_out_cells,
                0 as trans_out_amount,
                0 as sale_packs,
                0    as sale_cells,
                0 as sale_amount,
                sum(t_wm_bill_detail.Total_Packs) as loss_packs,
                sum(t_wm_bill_detail.Total_Cells)    as loss_cells,
                sum(t_wm_bill_detail.Amount) as loss_amount
         from microhis_clinics_wm.t_wm_bill_detail
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         where t_wm_bill.WMBill_Type_ID = 11 -- 入库
           and t_wm_bill.Bsn_Type = 3        -- 库存损溢
           and t_wm_bill.Status = 5          -- 已完成
           and t_wm_bill.Org_ID = #{orgId}
           and t_wm_bill.Dept_Code = #{deptCode}
           and t_wm_bill.Time_Validated >= #{startDate}
           and t_wm_bill.Time_Validated &lt; #{endDate}
         group by t_wm_bill_detail.Art_ID
         union all
-- 报损笔数，报损金额
         select t_wm_bill_detail.Art_ID,
                0 as in_store_packs,
                0    as in_store_cells,
                0 as in_store_amount,
                0 as trans_out_packs,
                0    as trans_out_cells,
                0 as trans_out_amount,
                0 as sale_packs,
                0    as sale_cells,
                0 as sale_amount,
                sum(t_wm_bill_detail.Total_Packs * -1) as loss_packs,
                sum(t_wm_bill_detail.Total_Cells * -1)    as loss_cells,
                sum(t_wm_bill_detail.Amount * -1) as loss_amount
         from microhis_clinics_wm.t_wm_bill_detail
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         where t_wm_bill.WMBill_Type_ID = 12 -- 入库
           and t_wm_bill.Bsn_Type = 3        -- 库存损溢
           and t_wm_bill.Status = 5          -- 已完成
           and t_wm_bill.Org_ID = #{orgId}
           and t_wm_bill.Dept_Code = #{deptCode}
           and t_wm_bill.Time_Validated >= #{startDate}
           and t_wm_bill.Time_Validated &lt; #{endDate}
         group by t_wm_bill_detail.Art_ID
         ) t
     left join hip_mdi.t_article on t.Art_ID = t_article.Art_ID
     left join hip_mdi.t_cat_type on t_article.Cat_Type_ID = t_cat_type.Cat_Type_ID
            ${ew.customSqlSegment}
    </select>
    <select id="queryArtBillCountByBillPage"
            resultType="cn.feiying.med.clinics_wm.model.ArtBillCountBillModel">
        select t_wm_bill_detail.WB_SeqID, t_wm_bill.Time_Validated
               , t_wm_bill_detail.Line_No
           , t_wm_bill.Bsn_Type
         , case t_wm_bill.Bsn_Type
             when 1 then '采购'
             when 2 then '销售'
             when 3 then '损溢'
             when 4 then '调拨'
             when 5 then '消耗出库'
             else '未知'
           end as Bsn_Type_Name,
           t_wm_bill.WMBill_Type_ID
         , t_wm_bill_type.WMBill_Type_Name,
            t_wm_bill.Cust_ID,
            t_scm_cust.Cust_Name,
            t_wm_bill_detail.Batch_No,
            t_wm_bill_detail.Total_Packs,
            t_wm_bill_detail.Total_Cells,
            t_wm_bill_detail.Pack_Price,
            t_wm_bill_detail.Cell_Price,
            t_wm_bill_detail.Amount,
            t_art_batch.Date_Manufactured,
            t_art_batch.Expiry,
           t_article.Pack_Unit,
           t_article.Cell_Unit
    from microhis_clinics_wm.t_wm_bill_detail
     left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
    left join microhis_clinics_wm.t_scm_cust on t_wm_bill.Cust_ID = t_scm_cust.Cust_ID
    left join microhis_clinics_wm.t_art_batch on t_wm_bill_detail.Art_ID = t_art_batch.Art_ID and t_wm_bill_detail.Batch_No = t_art_batch.Batch_No
    left join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
            left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
    ${ew.customSqlSegment}
    </select>
    <select id="queryArtBillCountByCustPage"
            resultType="cn.feiying.med.clinics_wm.model.ArtBillCountCustModel">
        select t_wm_bill.Cust_ID,
               min(t_scm_cust.Cust_Name)                                     as Cust_Name,
               sum(t_wm_bill_detail.Total_Packs * t_wm_bill_type.Total_Rate) as Total_Packs,
               sum(t_wm_bill_detail.Total_Cells * t_wm_bill_type.Total_Rate) as Total_Cells,
               sum(t_wm_bill_detail.Amount * t_wm_bill_type.Amount_Rate)     as Amount,
               min(t_article.Pack_Unit) as Pack_Unit,
               min(t_article.Cell_Unit) as Cell_Unit
        from microhis_clinics_wm.t_wm_bill_detail
             left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
             left join microhis_clinics_wm.t_scm_cust on t_wm_bill.Cust_ID = t_scm_cust.Cust_ID
             left join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
            left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
        ${ew.customSqlSegment}
    </select>
<!--    <select id="queryStoreBillCountPage" resultType="cn.feiying.med.clinics_wm.model.StoreBillCountModel">-->
<!--        select t.Dept_Code, min(t_org_dept.Dept_Name) as Dept_Name,-->
<!--        sum(t.in_store_count) as in_store_count, sum(t.in_store_amount) as in_store_amount,-->
<!--               sum(t.trans_in_count) as trans_in_count, sum(t.trans_in_amount) as trans_in_amount ,-->
<!--        sum(t.trans_out_count) as trans_out_count, sum(t.trans_out_amount) as trans_out_amount ,-->
<!--        sum(t.sale_count) as sale_count, sum(t.sale_amount) as sale_amount ,-->
<!--        sum(t.loss_count) as loss_count, sum(t.loss_amount) as loss_amount-->
<!--        from (-->
<!--        &#45;&#45; 采购入库笔数，采购入库金额-->
<!--        select t_wm_bill.org_id, t_wm_bill.Dept_Code,-->
<!--        count(*)    as in_store_count,-->
<!--        sum(Amount) as in_store_amount,-->
<!--        0           as trans_in_count,-->
<!--        0           as trans_in_amount,-->
<!--        0           as trans_out_count,-->
<!--        0           as trans_out_amount,-->
<!--        0           as sale_count,-->
<!--        0           as sale_amount,-->
<!--        0           as loss_count,-->
<!--        0           as loss_amount-->
<!--        from microhis_clinics_wm.t_wm_bill-->
<!--        where t_wm_bill.WMBill_Type_ID = 11 &#45;&#45; 入库-->
<!--        and t_wm_bill.Bsn_Type in (1, 5)  &#45;&#45; 采购入库，采购直调-->
<!--        and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--        and t_wm_bill.Org_ID = #{orgId}-->
<!--        and t_wm_bill.Time_Validated >= #{startDate}-->
<!--        and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--        group by t_wm_bill.org_id, t_wm_bill.Dept_Code-->
<!--        union all-->
<!--        &#45;&#45; 采购入库冲红笔数，采购入库冲红金额-->
<!--        select t_wm_bill.org_id, t_wm_bill.Dept_Code,-->
<!--        count(*)    as in_store_count,-->
<!--        sum(Amount * -1) as in_store_amount,-->
<!--        0           as trans_in_count,-->
<!--        0           as trans_in_amount,-->
<!--        0           as trans_out_count,-->
<!--        0           as trans_out_amount,-->
<!--        0           as sale_count,-->
<!--        0           as sale_amount,-->
<!--        0           as loss_count,-->
<!--        0           as loss_amount-->
<!--        from microhis_clinics_wm.t_wm_bill-->
<!--        where t_wm_bill.WMBill_Type_ID = 12 &#45;&#45; 入库冲红-->
<!--          and t_wm_bill.Bsn_Type in (1, 5)  &#45;&#45; 采购入库，采购直调-->
<!--        and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--        and t_wm_bill.Org_ID = #{orgId}-->
<!--        and t_wm_bill.Time_Validated >= #{startDate}-->
<!--        and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--        group by t_wm_bill.org_id, t_wm_bill.Dept_Code-->
<!--        union all-->
<!--        &#45;&#45; 调拨入库笔数，调拨入库金额-->
<!--        select t_wm_bill.org_id, t_wm_bill.Dept_Code,-->
<!--               0           as in_store_count,-->
<!--               0           as in_store_amount,-->
<!--               count(*)    as trans_in_count,-->
<!--               sum(Amount) as trans_in_amount,-->
<!--               0           as trans_out_count,-->
<!--               0           as trans_out_amount,-->
<!--               0           as sale_count,-->
<!--               0           as sale_amount,-->
<!--               0           as loss_count,-->
<!--               0           as loss_amount-->
<!--        from microhis_clinics_wm.t_wm_bill-->
<!--        where t_wm_bill.WMBill_Type_ID = 11 &#45;&#45; 入库-->
<!--          and t_wm_bill.Bsn_Type = 4  &#45;&#45; 库存调拔-->
<!--          and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--          and t_wm_bill.Org_ID = #{orgId}-->
<!--          and t_wm_bill.Time_Validated >= #{startDate}-->
<!--          and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--        group by t_wm_bill.org_id, t_wm_bill.Dept_Code-->
<!--        union all-->
<!--        &#45;&#45; 调拨入库冲红笔数，调拨入库冲红金额-->
<!--        select t_wm_bill.org_id, t_wm_bill.Dept_Code,-->
<!--               0           as in_store_count,-->
<!--               0           as in_store_amount,-->
<!--               count(*)    as trans_in_count,-->
<!--               sum(Amount * -1) as trans_in_amount,-->
<!--               0           as trans_out_count,-->
<!--               0           as trans_out_amount,-->
<!--               0           as sale_count,-->
<!--               0           as sale_amount,-->
<!--               0           as loss_count,-->
<!--               0           as loss_amount-->
<!--        from microhis_clinics_wm.t_wm_bill-->
<!--        where t_wm_bill.WMBill_Type_ID = 12 &#45;&#45; 入库冲红-->
<!--          and t_wm_bill.Bsn_Type = 4  &#45;&#45; 库存调拔-->
<!--          and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--          and t_wm_bill.Org_ID = #{orgId}-->
<!--          and t_wm_bill.Time_Validated >= #{startDate}-->
<!--          and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--        group by t_wm_bill.org_id, t_wm_bill.Dept_Code-->
<!--        union all-->
<!--        &#45;&#45; 调出笔数，调出金额-->
<!--        select t_wm_bill.org_id, t_wm_bill.Dept_Code,-->
<!--        0           as in_store_count,-->
<!--        0           as in_store_amount,-->
<!--        0           as trans_in_count,-->
<!--        0           as trans_in_amount,-->
<!--        count(*)    as trans_out_count,-->
<!--        sum(Amount) as trans_out_amount,-->
<!--        0           as sale_count,-->
<!--        0           as sale_amount,-->
<!--        0           as loss_count,-->
<!--        0           as loss_amount-->
<!--        from microhis_clinics_wm.t_wm_bill-->
<!--        where t_wm_bill.WMBill_Type_ID = 21 &#45;&#45; 出库-->
<!--        and t_wm_bill.Bsn_Type = 4        &#45;&#45; 库存调拔-->
<!--        and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--        and t_wm_bill.Org_ID = #{orgId}-->
<!--        and t_wm_bill.Time_Validated >= #{startDate}-->
<!--        and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--        group by t_wm_bill.org_id, t_wm_bill.Dept_Code-->
<!--        union all-->
<!--        &#45;&#45; 调出冲红笔数，调出冲红金额-->
<!--        select t_wm_bill.org_id, t_wm_bill.Dept_Code,-->
<!--        0           as in_store_count,-->
<!--        0           as in_store_amount,-->
<!--        0           as trans_in_count,-->
<!--        0           as trans_in_amount,-->
<!--        count(*)    as trans_out_count,-->
<!--        sum(Amount * -1) as trans_out_amount,-->
<!--        0           as sale_count,-->
<!--        0           as sale_amount,-->
<!--        0           as loss_count,-->
<!--        0           as loss_amount-->
<!--        from microhis_clinics_wm.t_wm_bill-->
<!--        where t_wm_bill.WMBill_Type_ID = 22 &#45;&#45; 出库-->
<!--        and t_wm_bill.Bsn_Type = 4        &#45;&#45; 库存调拔-->
<!--        and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--        and t_wm_bill.Org_ID = #{orgId}-->
<!--        and t_wm_bill.Time_Validated >= #{startDate}-->
<!--        and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--        group by t_wm_bill.org_id, t_wm_bill.Dept_Code-->
<!--        union all-->
<!--        &#45;&#45; 销售笔数，销售金额-->
<!--        select t_wm_bill.org_id, t_wm_bill.Dept_Code,-->
<!--        0           as in_store_count,-->
<!--        0           as in_store_amount,-->
<!--        0           as trans_in_count,-->
<!--        0           as trans_in_amount,-->
<!--        0           as trans_out_count,-->
<!--        0           as trans_out_amount,-->
<!--        count(*)    as sale_count,-->
<!--        sum(Amount) as sale_amount,-->
<!--        0           as loss_count,-->
<!--        0           as loss_amount-->
<!--        from microhis_clinics_wm.t_wm_bill-->
<!--        where t_wm_bill.WMBill_Type_ID = 21 &#45;&#45; 出库-->
<!--        and t_wm_bill.Bsn_Type = 2        &#45;&#45; 销售出库-->
<!--        and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--        and t_wm_bill.Org_ID = #{orgId}-->
<!--        and t_wm_bill.Time_Validated >= #{startDate}-->
<!--        and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--        group by t_wm_bill.org_id, t_wm_bill.Dept_Code-->
<!--        union all-->
<!--        &#45;&#45; 销售冲红笔数，销售冲红金额-->
<!--        select t_wm_bill.org_id, t_wm_bill.Dept_Code,-->
<!--        0           as in_store_count,-->
<!--        0           as in_store_amount,-->
<!--        0           as trans_in_count,-->
<!--        0           as trans_in_amount,-->
<!--        0           as trans_out_count,-->
<!--        0           as trans_out_amount,-->
<!--        count(*)    as sale_count,-->
<!--        sum(Amount * -1) as sale_amount,-->
<!--        0           as loss_count,-->
<!--        0           as loss_amount-->
<!--        from microhis_clinics_wm.t_wm_bill-->
<!--        where t_wm_bill.WMBill_Type_ID = 22 &#45;&#45; 出库-->
<!--        and t_wm_bill.Bsn_Type = 2        &#45;&#45; 销售出库-->
<!--        and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--        and t_wm_bill.Org_ID = #{orgId}-->
<!--        and t_wm_bill.Time_Validated >= #{startDate}-->
<!--        and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--        group by t_wm_bill.org_id, t_wm_bill.Dept_Code-->
<!--        union all-->
<!--        &#45;&#45; 报溢笔数，报溢金额-->
<!--        select t_wm_bill.org_id, t_wm_bill.Dept_Code,-->
<!--        0           as in_store_count,-->
<!--        0           as in_store_amount,-->
<!--        0           as trans_in_count,-->
<!--        0           as trans_in_amount,-->
<!--        0           as trans_out_count,-->
<!--        0           as trans_out_amount,-->
<!--        0           as sale_count,-->
<!--        0           as sale_amount,-->
<!--        count(*)    as loss_count,-->
<!--        sum(Amount) as loss_amount-->
<!--        from microhis_clinics_wm.t_wm_bill-->
<!--        where t_wm_bill.WMBill_Type_ID = 11 &#45;&#45; 入库-->
<!--        and t_wm_bill.Bsn_Type = 3        &#45;&#45; 库存损溢-->
<!--        and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--        and t_wm_bill.Org_ID = #{orgId}-->
<!--        and t_wm_bill.Time_Validated >= #{startDate}-->
<!--        and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--        group by t_wm_bill.org_id, t_wm_bill.Dept_Code-->
<!--        union all-->
<!--        &#45;&#45; 报损笔数，报损金额-->
<!--        select t_wm_bill.org_id, t_wm_bill.Dept_Code,-->
<!--        0           as in_store_count,-->
<!--        0           as in_store_amount,-->
<!--        0           as trans_in_count,-->
<!--        0           as trans_in_amount,-->
<!--        0           as trans_out_count,-->
<!--        0           as trans_out_amount,-->
<!--        0           as sale_count,-->
<!--        0           as sale_amount,-->
<!--        count(*)    as loss_count,-->
<!--        sum(Amount * -1) as loss_amount-->
<!--        from microhis_clinics_wm.t_wm_bill-->
<!--        where t_wm_bill.WMBill_Type_ID = 12 &#45;&#45; 入库-->
<!--        and t_wm_bill.Bsn_Type = 3        &#45;&#45; 库存损溢-->
<!--        and t_wm_bill.Status = 5          &#45;&#45; 已完成-->
<!--        and t_wm_bill.Org_ID = #{orgId}-->
<!--        and t_wm_bill.Time_Validated >= #{startDate}-->
<!--        and t_wm_bill.Time_Validated &lt; #{endDate}-->
<!--        group by t_wm_bill.org_id, t_wm_bill.Dept_Code) t-->
<!--        left join hip_mdi.t_org_dept on t.Org_ID = t_org_dept.Org_ID and t.Dept_Code = t_org_dept.Dept_Code-->
<!--        group by t.Org_ID, t.Dept_Code-->
<!--    </select>-->
    <select id="queryStoreBillCountPage" resultType="cn.feiying.med.clinics_wm.model.StoreBillCountModel">
        select t_wm_bill.Org_ID,
               t_wm_bill.Dept_Code,
               min(t_org_dept.Dept_Name) as Dept_Name,
               sum(case
                       when (t_wm_bill.Bsn_Type_ID = 1 and t_wm_bill.WMBill_Type_ID != 12)
                           then 1
                       else 0 end)       as in_store_count,
               sum(case
                       when (t_wm_bill.Bsn_Type_ID = 1 and t_wm_bill.WMBill_Type_ID != 12)
                           then t_wm_bill.Amount
                       when t_wm_bill.WMBill_Type_ID = 12 then t_wm_bill.Amount * -1
                       else 0 end)       as in_store_amount,
               sum(case
                       when (t_wm_bill.Bsn_Type_ID = 3 and t_wm_bill.WMBill_Type_ID = 32 and
                             t_wm_bill.Section_ID is null) or -- 非病区退回，病区退回的sectionId is not null 放在调出冲红
                            (t_wm_bill.Bsn_Type_ID = 4 and t_wm_bill.WMBill_Type_ID = 41 and t_wm_bill.Section_ID is null)
                           then 1
                       else 0 end)       as trans_in_count,
               sum(case
                       when (t_wm_bill.Bsn_Type_ID = 3 and t_wm_bill.WMBill_Type_ID = 32 and t_wm_bill.Section_ID is null) or
                            (t_wm_bill.Bsn_Type_ID = 4 and t_wm_bill.WMBill_Type_ID = 41 and t_wm_bill.Section_ID is null)
                           then t_wm_bill.Amount
                       else 0 end)       as trans_in_amount,
               sum(case
                       when (t_wm_bill.Bsn_Type_ID = 3 and t_wm_bill.WMBill_Type_ID = 31) or
                            (t_wm_bill.Bsn_Type_ID = 4 and t_wm_bill.WMBill_Type_ID = 42) or
                            (t_wm_bill.Bsn_Type_ID = 2 and t_wm_bill.WMBill_Type_ID = 21 and
                             t_wm_bill.Section_ID is not null and t_wm_bill.Recipe_ID is null) -- 现在药房向病区发药都是用billTypeId:21 bsnTypeId:2 销售，这部分业务算在调拨
                           then 1
                       else 0 end)       as trans_out_count,
               sum(case
                       when (t_wm_bill.Bsn_Type_ID = 3 and t_wm_bill.WMBill_Type_ID = 31) or
                            (t_wm_bill.Bsn_Type_ID = 4 and t_wm_bill.WMBill_Type_ID = 42) or
                            (t_wm_bill.Bsn_Type_ID = 2 and t_wm_bill.WMBill_Type_ID = 21 and t_wm_bill.Section_ID is not null and t_wm_bill.Recipe_ID is null)
                           then t_wm_bill.Amount
                       when t_wm_bill.Bsn_Type_ID = 3 and t_wm_bill.WMBill_Type_ID = 32 and
                            t_wm_bill.Section_ID is not null
                           then t_wm_bill.Amount * -1
                       else 0 end)       as trans_out_amount,
               sum(case
                       when t_wm_bill.Bsn_Type_ID = 2 and t_wm_bill.WMBill_Type_ID != 22 and (t_wm_bill.Recipe_ID is not null or t_wm_bill.SCMBill_ID is not null) then 1
                       else 0 end)       as sale_count,
               sum(case
                       when t_wm_bill.Bsn_Type_ID = 2 and t_wm_bill.WMBill_Type_ID != 22 and (t_wm_bill.Recipe_ID is not null or t_wm_bill.SCMBill_ID is not null)
                           then t_wm_bill.Amount
                       when t_wm_bill.WMBill_Type_ID = 22 then t_wm_bill.Amount * -1
                       else 0 end)       as sale_amount,
               sum(case
                       when t_wm_bill.WMBill_Type_ID = 51 then 1
                       else 0 end)       as overflow_in_count,
               sum(case
                       when t_wm_bill.WMBill_Type_ID = 51 then t_wm_bill.Amount
                       else 0 end)       as overflow_in_amount,
               sum(case
                       when t_wm_bill.WMBill_Type_ID = 52 then 1
                       else 0 end)       as loss_out_count,
               sum(case
                       when t_wm_bill.WMBill_Type_ID = 52 then t_wm_bill.Amount
                       else 0 end)       as loss_out_amount
        from microhis_clinics_wm.t_wm_bill
                 left join hip_mdi.t_org_dept
                           on t_wm_bill.Org_ID = t_org_dept.Org_ID and t_wm_bill.Dept_Code = t_org_dept.Dept_Code
        where t_wm_bill.Status = 5
          and t_wm_bill.Org_ID = #{orgId}
          and t_wm_bill.Time_Validated >= #{startDate}
          and t_wm_bill.Time_Validated &lt; #{endDate}
        group by t_wm_bill.Org_ID, t_wm_bill.Dept_Code
    </select>
    <select id="queryStoreBillArtPage" resultType="cn.feiying.med.clinics_wm.model.StoreBillArtModel">
        select  t.Art_ID as Art_ID,
        min(t_article.MI_Code) as MI_Code,
        min(t_article.Art_Name) as Art_Name,
        min(t_article.Art_Spec) as Art_Spec,
        min(t_article.Producer) as Producer,
        min(t_article.Pack_Material) as Pack_Material,
        min(t_article.Pack_Unit) as Pack_Unit,
        min(t_article.Cell_Unit) as Cell_Unit,
        min(t_article.Pack_Cells) as Pack_Cells,
        min(t_cat_type.Cat_Name) as Cat_Name,
        sum(t.Total_Packs) + sum(t.Total_Cells) / ifnull(t_article.Pack_Cells, 1) as Total,
        sum(t.Amount) / (sum(t.Total_Packs) + sum(t.Total_Cells) / ifnull(t_article.Pack_Cells, 1)) as price,
        sum(t.Amount) as Amount
        from (
            select t_wm_bill_detail.Art_ID,
                case
                when t_wm_bill.WMBill_Type_ID in (12, 22) then ifnull(t_wm_bill_detail.Total_Packs, 0) * -1
                else ifnull(t_wm_bill_detail.Total_Packs, 0) end as Total_Packs,
                case
                when t_wm_bill.WMBill_Type_ID in (12, 22) then ifnull(t_wm_bill_detail.Total_Cells, 0) * -1
                else ifnull(t_wm_bill_detail.Total_Cells, 0) end as Total_Cells,
                t_wm_bill_detail.Pack_Price,
                t_wm_bill_detail.Cell_Price,
                case
                when t_wm_bill.WMBill_Type_ID in (12, 22) then t_wm_bill_detail.Amount * -1
                else t_wm_bill_detail.Amount end      as Amount,
                t_wm_bill.Bsn_Type_ID,
                t_wm_bill.WMBill_Type_ID
            from microhis_clinics_wm.t_wm_bill_detail
            left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
            where t_wm_bill.Status = 5 -- 已完成
                and t_wm_bill.Org_ID = #{orgId}
                and t_wm_bill.Dept_Code = #{deptCode}
                and t_wm_bill.Time_Validated >= #{startDate}
                and t_wm_bill.Time_Validated &lt; #{endDate}
            <if test="bsnType != null">
                and t_wm_bill.Bsn_Type = #{bsnType}
            </if>
        ) t
        left join hip_mdi.t_article on t.Art_ID = t_article.Art_ID
        left join hip_mdi.t_cat_type on t_article.Cat_Type_ID = t_cat_type.Cat_Type_ID
        ${ew.customSqlSegment}
    </select>

    <select id="queryStoreBillArtSum" resultType="java.util.Map">
        select
        sum(t.Amount) as Amount
        from (
            select t_wm_bill_detail.Art_ID,
            case
            when t_wm_bill.WMBill_Type_ID in (12, 22) then t_wm_bill_detail.Amount * -1
            else t_wm_bill_detail.Amount end      as Amount,
            t_wm_bill.Bsn_Type_ID,
            t_wm_bill.WMBill_Type_ID
        from microhis_clinics_wm.t_wm_bill_detail
        left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
        where t_wm_bill.Status = 5 -- 已完成
        and t_wm_bill.Org_ID = #{orgId}
        and t_wm_bill.Dept_Code = #{deptCode}
        and t_wm_bill.Time_Validated >= #{startDate}
        and t_wm_bill.Time_Validated &lt; #{endDate}
        <if test="bsnType != null">
            and t_wm_bill.Bsn_Type = #{bsnType}
        </if>
        ) t
        left join hip_mdi.t_article on t.Art_ID = t_article.Art_ID
        left join hip_mdi.t_cat_type on t_article.Cat_Type_ID = t_cat_type.Cat_Type_ID
        ${ew.customSqlSegment}
    </select>
    <select id="querySectionBillCountPage" resultType="cn.feiying.med.clinics_wm.model.SectionBillCountModel">
        select t.Section_ID as Section_ID, min(t_section.Section_Name) as Section_Name,
        sum(in_count)   as in_count,
        sum(in_Amount) as in_Amount,
        sum(consume_count) as consume_count,
        sum(consume_amount) as consume_amount,
        sum(consume_cost) as consume_cost
        from (select t_wm_bill.Section_ID,
                     sum(t_wm_bill.Amount * t_wm_bill_type.Amount_Rate * -1) as in_Amount,
        count(*)                           as in_count,
        0                                  as consume_count,
        0                                  as consume_amount,
        0                                  as consume_cost
        from microhis_clinics_wm.t_wm_bill
                 inner join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
        where t_wm_bill.Bsn_Type in (2, 4) -- 以前只有4，现在增加了2（病区的预占计费模式）
        and t_wm_bill.Status = 5 -- 已完成
        and t_wm_bill.Org_ID = #{orgId}
        and t_wm_bill.Time_Validated >= #{startDate}
        and t_wm_bill.Time_Validated &lt; #{endDate}
        and t_wm_bill.Section_ID is not null
        group by t_wm_bill.Section_ID
        union all
          select t_wm_bill.Section_ID,
                 0 as                          in_Amount,
                 0 as                          in_count,
                 count(*)                      consume_count,
                 sum(t_wm_bill.Amount * t_wm_bill_type.Amount_Rate * -1) consume_amount,
                 sum(t_wm_bill.Cost * t_wm_bill_type.Amount_Rate * -1)   consume_cost
          from microhis_clinics_wm.t_wm_bill
                   inner join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
          where t_wm_bill.Bsn_Type in (2, 4)
            and t_wm_bill.Status = 5
            and t_wm_bill.Section_ID is not null
            and t_wm_bill.Recipe_ID is not null
            and t_wm_bill.Org_ID = #{orgId}
            and t_wm_bill.Time_Validated >= #{startDate}
            and t_wm_bill.Time_Validated &lt; #{endDate}
          group by t_wm_bill.Section_ID
        union all
        select t_section_consume.Section_ID,
        0 as                          in_Amount,
        0 as                          in_count,
        count(*)                      consume_count,
        sum(t_section_consume.Amount) consume_amount,
        sum(t_section_consume.Cost)   consume_cost
        from microhis_clinics_wm.t_section_consume
        where t_section_consume.Org_ID = #{orgId}
        and t_section_consume.Time_Processed >= #{startDate}
        and t_section_consume.Time_Processed &lt; #{endDate}
        group by t_section_consume.Section_ID) t
        left join hip_mdi.t_section on t.Section_ID = t_section.Section_ID
        group by t.Section_ID
    </select>
    <select id="querySectionBillArtPage" resultType="cn.feiying.med.clinics_wm.model.SectionBillArtModel">
        select t.Art_ID as Art_ID,
            min(t_article.MI_Code) as MI_Code,
            min(t_article.Art_Name) as Art_Name,
            min(t_article.Art_Spec) as Art_Spec,
            min(t_article.Producer) as Producer,
            min(t_article.Pack_Material) as Pack_Material,
            min(t_article.Pack_Unit) as Pack_Unit,
            min(t_article.Cell_Unit) as Cell_Unit,
            min(t_article.Pack_Cells) as Pack_Cells,
            min(t_cat_type.Cat_Name) as Cat_Name,
            sum(t.Total_Packs) + sum(t.Total_Cells) / ifnull(t_article.Pack_Cells, 1) as Total,
            sum(t.in_Amount) / (sum(t.Total_Packs) + sum(t.Total_Cells) / ifnull(t_article.Pack_Cells, 1)) as price,
            sum(t.in_Amount) as Amount,
            sum(t.in_Cost) as Cost,
            sum(t.consume_Total_packs) as consume_Total_packs,
            sum(t.consume_Total_Cells) as consume_Total_Cells,
            sum(t.consume_Total_packs) + sum(t.consume_Total_Cells) / ifnull(t_article.Pack_Cells, 1) as consume_Total,
            sum(t.consume_amount) / (sum(t.consume_Total_Cells) + sum(t.consume_Total_Cells) / ifnull(t_article.Pack_Cells, 1)) as consume_price,
            sum(t.consume_amount) as consume_amount,
            sum(t.consume_cost) as consume_cost
        from (select t_wm_bill_detail.Art_ID,
                     sum(t_wm_bill_detail.Total_Packs * t_wm_bill_type.Total_Rate * -1) as Total_Packs,
                     sum(t_wm_bill_detail.Total_Cells * t_wm_bill_type.Total_Rate * -1) as Total_Cells,
                     sum(t_wm_bill_detail.Amount * t_wm_bill_type.Amount_Rate * -1) as in_Amount,
                     sum(t_wm_bill_detail.Cost * t_wm_bill_type.Amount_Rate * -1) as in_Cost,
                0                                  as consume_Total_packs,
                0                                  as consume_Total_Cells,
                0                                  as consume_amount,
                0                                  as consume_cost
            from microhis_clinics_wm.t_wm_bill_detail
            left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
            inner join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
            where t_wm_bill.Bsn_Type in (2, 4) -- 以前只有4，现在增加了2（病区的预占计费模式）
                and t_wm_bill.Status = 5 -- 已完成
                and t_wm_bill.Org_ID = #{orgId}
                and t_wm_bill.Time_Validated >= #{startDate}
                and t_wm_bill.Time_Validated &lt; #{endDate}
                and t_wm_bill.Section_ID = #{sectionId}
                group by t_wm_bill_detail.Art_ID
              union all
              select t_wm_bill_detail.Art_ID,
                     0 as                          Total_Packs,
                     0 as                          Total_Cells,
                     0 as                          in_Amount,
                     0 as in_Cost,
                     sum(t_wm_bill_detail.Total_Packs * t_wm_bill_type.Amount_Rate * -1) consume_Total_packs,
                     sum(t_wm_bill_detail.Total_Cells * t_wm_bill_type.Amount_Rate * -1) consume_Total_Cells,
                     sum(t_wm_bill_detail.Amount * t_wm_bill_type.Amount_Rate * -1)   consume_amount,
                     sum(t_wm_bill_detail.Cost * t_wm_bill_type.Amount_Rate * -1)   consume_cost
              from microhis_clinics_wm.t_wm_bill_detail
                       left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                       inner join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
                       inner join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
              where t_wm_bill.Bsn_Type in (2, 4)
                and t_wm_bill.Status = 5
                and t_wm_bill.Recipe_ID is not null -- 中药处方、出院带药没产生t_section_consume_art
                and t_wm_bill.Org_ID = #{orgId}
                and t_wm_bill.Time_Validated >= #{startDate}
                and t_wm_bill.Time_Validated &lt; #{endDate}
                and t_wm_bill.Section_ID = #{sectionId}
              group by t_wm_bill_detail.Art_ID
            union all
            select t_section_consume_art.Art_ID,
                0 as                          Total_Packs,
                0 as                          Total_Cells,
                0 as                          in_Amount,
                0 as                          in_Cost,
                sum(t_section_consume_art.Total_Packs) consume_Total_packs,
                sum(t_section_consume_art.Total_Cells) consume_Total_Cells,
                sum(t_section_consume_art.Amount)   consume_amount,
                sum(t_section_consume_art.Cost)   consume_cost
            from microhis_clinics_wm.t_section_consume_art
            left join microhis_clinics_wm.t_section_consume on t_section_consume_art.Consume_ID = t_section_consume.Consume_ID
            where t_section_consume.Org_ID = #{orgId}
                and t_section_consume.Time_Processed >= #{startDate}
                and t_section_consume.Time_Processed &lt; #{endDate}
                and t_section_consume.Section_ID = #{sectionId}
                group by t_section_consume_art.Art_ID) t
        left join hip_mdi.t_article on t.Art_ID = t_article.Art_ID
        left join hip_mdi.t_cat_type on t_article.Cat_Type_ID = t_cat_type.Cat_Type_ID
            ${ew.customSqlSegment}
    </select>
    <select id="querySectionBillArtSum" resultType="java.util.Map">
        select
            sum(t.in_Amount) as Amount,
            sum(t.in_Cost) as Cost,
            sum(t.consume_amount) as consume_amount,
            sum(t.consume_cost) as consume_cost
        from (select t_wm_bill_detail.Art_ID,
                     sum(t_wm_bill_detail.Total_Packs * t_wm_bill_type.Total_Rate * -1) as Total_Packs,
                     sum(t_wm_bill_detail.Total_Cells * t_wm_bill_type.Total_Rate * -1) as Total_Cells,
                     sum(t_wm_bill_detail.Amount * t_wm_bill_type.Amount_Rate * -1) as in_Amount,
                     sum(t_wm_bill_detail.Cost * t_wm_bill_type.Amount_Rate * -1) as in_Cost,
                0                                  as consume_Total_packs,
                0                                  as consume_Total_Cells,
                0                                  as consume_amount,
                0                                  as consume_cost
            from microhis_clinics_wm.t_wm_bill_detail
            left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
            inner join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
            where t_wm_bill.Bsn_Type in (2, 4) -- 以前只有4，现在增加了2（病区的预占计费模式）
                and t_wm_bill.Status = 5 -- 已完成
                and t_wm_bill.Org_ID = #{orgId}
                and t_wm_bill.Time_Validated >= #{startDate}
                and t_wm_bill.Time_Validated &lt; #{endDate}
                and t_wm_bill.Section_ID = #{sectionId}
                group by t_wm_bill_detail.Art_ID
        union all
        select t_wm_bill_detail.Art_ID,
                0 as                          Total_Packs,
                0 as                          Total_Cells,
                0 as                          in_Amount,
                0 as in_Cost,
                sum(t_wm_bill_detail.Total_Packs * t_wm_bill_type.Amount_Rate * -1) consume_Total_packs,
                sum(t_wm_bill_detail.Total_Cells * t_wm_bill_type.Amount_Rate * -1) consume_Total_Cells,
                sum(t_wm_bill_detail.Amount * t_wm_bill_type.Amount_Rate * -1)   consume_amount,
                sum(t_wm_bill_detail.Cost * t_wm_bill_type.Amount_Rate * -1)   consume_cost
                from microhis_clinics_wm.t_wm_bill_detail
                left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                inner join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
                inner join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                where t_wm_bill.Bsn_Type in (2, 4)
                and t_wm_bill.Status = 5
                and t_wm_bill.Recipe_ID is not null -- 中药处方、出院带药没产生t_section_consume_art
                  and t_wm_bill.Org_ID = #{orgId}
                  and t_wm_bill.Time_Validated >= #{startDate}
                  and t_wm_bill.Time_Validated &lt; #{endDate}
                  and t_wm_bill.Section_ID = #{sectionId}
                    group by t_wm_bill_detail.Art_ID
            union all
            select t_section_consume_art.Art_ID,
                0 as                          Total_Packs,
                0 as                          Total_Cells,
                0 as                          in_Amount,
                0 as in_Cost,
                sum(t_section_consume_art.Total_Packs) consume_Total_packs,
                sum(t_section_consume_art.Total_Cells) consume_Total_Cells,
                sum(t_section_consume_art.Amount)   consume_amount,
                sum(t_section_consume_art.Cost)   consume_cost
            from microhis_clinics_wm.t_section_consume_art
            left join microhis_clinics_wm.t_section_consume on t_section_consume_art.Consume_ID = t_section_consume.Consume_ID
            where t_section_consume.Org_ID = #{orgId}
                and t_section_consume.Time_Processed >= #{startDate}
                and t_section_consume.Time_Processed &lt; #{endDate}
                and t_section_consume.Section_ID = #{sectionId}
                group by t_section_consume_art.Art_ID) t
        left join hip_mdi.t_article on t.Art_ID = t_article.Art_ID
        left join hip_mdi.t_cat_type on t_article.Cat_Type_ID = t_cat_type.Cat_Type_ID
            ${ew.customSqlSegment}
    </select>
    <select id="queryTodayRecipeDeliverPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        select t_wm_bill.WB_SeqID, t_visit.Visit_ID, t_wm_bill.WMBill_Type_ID, t_wm_bill_type.WMBill_Type_Name, t_recipe.RX_No, t_recipe.Recipe_ID,
               t_visit.Patient_Name, t_wm_bill.Amount * t_wm_bill_type.Amount_Rate * -1 as Amount
        from microhis_clinics_wm.t_wm_bill
                 inner join microhis_hsd.t_recipe on t_wm_bill.Recipe_ID = t_recipe.Recipe_ID
                 inner join microhis_hsd.t_visit on t_recipe.Visit_ID = t_visit.Visit_ID
                 inner join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
            ${ew.customSqlSegment}
    </select>
    <select id="queryTodayRecipeDeliverSum" resultType="java.util.Map">
        select sum(t_wm_bill.Amount * t_wm_bill_type.Amount_Rate * -1) as Amount
        from microhis_clinics_wm.t_wm_bill
                 inner join microhis_hsd.t_recipe on t_wm_bill.Recipe_ID = t_recipe.Recipe_ID
                 inner join microhis_hsd.t_visit on t_recipe.Visit_ID = t_visit.Visit_ID
                 inner join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
            ${ew.customSqlSegment}
    </select>
    <select id="queryArtDeliverSumPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select t_wm_bill_detail.Art_ID,
               min(t_article.Art_Name)                                       as Art_Name,
               min(t_article.Art_Spec)                                       as Art_Spec,
               min(t_article.Producer)                                       as Producer,
               min(t_article.MI_Code)                                        as MI_Code,
               min(t_article.Approval_No)                                    as Approval_No,
               min(t_article.Pack_Material)                                    as Pack_Material,
               max(t_wm_bill_detail.Pack_Price)                              as Pack_Price,
               sum(t_wm_bill_detail.Total_Packs * t_wm_bill_type.Total_Rate * -1) as Total_Packs,
               sum(t_wm_bill_detail.Total_Cells * t_wm_bill_type.Total_Rate * -1) as Total_Cells,
               sum(ifnull(t_wm_bill_detail.Cost, 0) * t_wm_bill_type.Amount_Rate * -1)       as Cost,
               sum(ifnull(t_wm_bill_detail.Amount, 0) * t_wm_bill_type.Amount_Rate * -1)     as Amount,
               min(t_article.Pack_Unit)                                      as Pack_Unit,
               min(t_article.Cell_Unit)                                      as Cell_Unit
        from t_wm_bill_detail
                 left join t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join hip_mdi.t_user_code on t_wm_bill.Validator_UID = t_user_code.User_ID
                 left join microhis_hsd.t_recipe on t_wm_bill.Recipe_ID = t_recipe.Recipe_ID
                 left join hip_mdi.t_org_dept as Apply_Dept on t_recipe.Org_ID = Apply_Dept.Org_ID and t_recipe.Apply_Deptcode = Apply_Dept.Dept_Code
                 left join microhis_hsd.t_visit on t_recipe.Visit_ID = t_visit.Visit_ID
            ${ew.customSqlSegment}
    </select>
    <select id="queryArtDeliverSumPageSum" resultType="java.util.Map">
        select
               sum(ifnull(t_wm_bill_detail.Cost, 0) * t_wm_bill_type.Amount_Rate * -1)       as Cost,
               sum(ifnull(t_wm_bill_detail.Amount, 0) * t_wm_bill_type.Amount_Rate * -1)     as Amount
        from t_wm_bill_detail
                 left join t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
                 left join hip_mdi.t_user_code on t_wm_bill.Validator_UID = t_user_code.User_ID
                 left join microhis_hsd.t_recipe on t_wm_bill.Recipe_ID = t_recipe.Recipe_ID
                 left join hip_mdi.t_org_dept as Apply_Dept on t_recipe.Org_ID = Apply_Dept.Org_ID and t_recipe.Apply_Deptcode = Apply_Dept.Dept_Code
                 left join microhis_hsd.t_visit on t_recipe.Visit_ID = t_visit.Visit_ID
            ${ew.customSqlSegment}
    </select>
    <select id="queryArtDeliverSumDetailPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDetailDto">
        select
            t_wm_bill_detail.WB_SeqID,
            t_wm_bill_detail.Line_No,
            t_wm_bill_detail.Art_ID,
            t_article.Art_Name,
            t_article.Art_Spec,
            t_article.Producer,
            t_wm_bill_detail.Pack_Price,
            t_wm_bill_detail.Total_Packs * t_wm_bill_type.Total_Rate * -1 as Total_Packs,
            t_wm_bill_detail.Total_Cells * t_wm_bill_type.Total_Rate * -1 as Total_Cells,
            t_wm_bill_detail.Cost * t_wm_bill_type.Amount_Rate * -1       as Cost,
            t_wm_bill_detail.Amount * t_wm_bill_type.Amount_Rate * -1     as Amount,
            t_wm_bill_detail.Batch_No,
            t_wm_bill_detail.Stock_No,
            t_article.Pack_Unit,
            t_article.Cell_Unit,
            t_wm_bill.Time_Validated,
            t_user_code.User_Name as Validator_Uname,
            Apply_Dept.Dept_Name as Apply_Dept_name,
            t_visit.Patient_Name,
            t_visit.Visit_ID,
            t_recipe.RX_No,
            t_recipe.Recipe_ID,
            t_clinician.Clinician_Name
        from t_wm_bill_detail
         left join t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         left join t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
         left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
         left join hip_mdi.t_user_code on t_wm_bill.Validator_UID = t_user_code.User_ID
         left join microhis_hsd.t_recipe on t_wm_bill.Recipe_ID = t_recipe.Recipe_ID
         left join hip_mdi.t_org_dept as Apply_Dept on t_recipe.Org_ID = Apply_Dept.Org_ID and t_recipe.Apply_Deptcode = Apply_Dept.Dept_Code
         left join microhis_hsd.t_visit on t_recipe.Visit_ID = t_visit.Visit_ID
         left join hip_mdi.t_clinician on t_visit.Clinician_ID = t_clinician.Clinician_ID
            ${ew.customSqlSegment}
    </select>
    <select id="queryArtDeliverSumDetailPageSum" resultType="java.util.Map">
        select
            sum(t_wm_bill_detail.Total_Packs * t_wm_bill_type.Total_Rate * -1) as Total_Packs,
            sum(t_wm_bill_detail.Total_Cells * t_wm_bill_type.Total_Rate * -1) as Total_Cells,
            sum(ifnull(t_wm_bill_detail.Cost, 0) * t_wm_bill_type.Amount_Rate * -1)       as Cost,
            sum(ifnull(t_wm_bill_detail.Amount, 0) * t_wm_bill_type.Amount_Rate * -1)     as Amount
        from t_wm_bill_detail
         left join t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
         left join t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
         left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
         left join hip_mdi.t_user_code on t_wm_bill.Validator_UID = t_user_code.User_ID
         left join microhis_hsd.t_recipe on t_wm_bill.Recipe_ID = t_recipe.Recipe_ID
         left join hip_mdi.t_org_dept as Apply_Dept on t_recipe.Org_ID = Apply_Dept.Org_ID and t_recipe.Apply_Deptcode = Apply_Dept.Dept_Code
         left join microhis_hsd.t_visit on t_recipe.Visit_ID = t_visit.Visit_ID
         left join hip_mdi.t_clinician on t_visit.Clinician_ID = t_clinician.Clinician_ID
            ${ew.customSqlSegment}
    </select>
    <select id="querySectionDeliveredPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        select t_wm_bill.*, t_section.Section_Name, t_user_code.User_Name as validatorUname
        from microhis_clinics_wm.t_wm_bill
           left join hip_mdi.t_section on t_wm_bill.Section_ID = t_section.Section_ID
           left join hip_mdi.t_user_code on t_wm_bill.Validator_UID = t_user_code.User_ID
        ${ew.customSqlSegment}
    </select>

    <!-- 病区已发药列表-按发药记录-汇总-->
    <select id="querySectionDeliveredSummaryPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        select t_wm_bill.*, t_section.Section_Name, t_user_code.User_Name as validatorUname
            ,JSON_ARRAYAGG(WB_SeqID) AS wbSeqids
        from microhis_clinics_wm.t_wm_bill
                 left join hip_mdi.t_section on t_wm_bill.Section_ID = t_section.Section_ID
                 left join hip_mdi.t_user_code on t_wm_bill.Validator_UID = t_user_code.User_ID
            ${ew.customSqlSegment}
            group by t_wm_bill.Section_ID, t_wm_bill.Validator_UID, t_wm_bill.Time_Validated
            order by t_wm_bill.Time_Validated desc
    </select>
    <select id="querySectionDeliveredPageBySection" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        select t_wm_bill.Section_ID, min(t_section.Section_Name) as Section_Name
        from microhis_clinics_wm.t_wm_bill
         left join hip_mdi.t_section on t_wm_bill.Section_ID = t_section.Section_ID
            ${ew.customSqlSegment}
    </select>
    <select id="querySectionDeliveredPageByPatient" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        select
            t_wm_bill.Section_ID,
            t_wm_req_detail.Visit_ID,
            min(t_visit.Patient_ID) as Patient_ID,
            min(t_visit.Patient_Name) as Patient_Name,
            min(t_visit.Gender_ID) as Gender_ID,
            min(t_gender.Gender_Name) as Gender_Name,
            min(t_visit.Age_of_Years) as Age_of_Years,
            min(t_visit.Age_of_Days) as Age_of_Days,
            min(t_wm_req_detail.Bed_No) as Bed_No
        from microhis_clinics_wm.t_wm_bill_detail
         left join microhis_clinics_wm.t_wm_req_detail on t_wm_bill_detail.WM_ReqID = t_wm_req_detail.WM_ReqID and t_wm_bill_detail.WM_Req_Detail_Line_No = t_wm_req_detail.Line_No
         left join microhis_hsd.t_visit on t_wm_req_detail.Visit_ID = t_visit.Visit_ID
         left join hip_mdi.t_gender on t_visit.Gender_ID = t_gender.Gender_ID
         left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
            ${ew.customSqlSegment}
    </select>
    <!--    病区-待退药-退药列表汇总-->
    <select id="queryPendingBillSumPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        select t_wm_bill.*
             ,t_scm_cust.Cust_Name
             ,Creator.User_Name   as Creator_UName
             ,Validator.User_Name as Validator_UName
             ,t_wm_bill_type.WMBill_Type_Name
             ,t_org_dept.Dept_Name
             ,recv_dept.Dept_Name as Recv_Dept_Name
             ,t_section.Section_Name
             ,t_org.Org_Name
             ,t_wm_req.Notes      AS Req_Notes
             ,JSON_ARRAYAGG(t_wm_bill.WB_SeqID) AS wbSeqids
        from microhis_clinics_wm.t_wm_bill_pending
                 inner join microhis_clinics_wm.t_wm_bill on t_wm_bill_pending.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_scm_cust on t_wm_bill.Cust_ID = t_scm_cust.Cust_ID
                 left join hip_mdi.t_user_code Creator on t_wm_bill.Creator_UID = Creator.User_ID
                 left join hip_mdi.t_user_code Validator on t_wm_bill.Validator_UID = Validator.User_ID
                 left join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
                 left join hip_mdi.t_org_dept
                           on t_wm_bill.Org_ID = t_org_dept.Org_ID and t_wm_bill.Dept_Code = t_org_dept.Dept_Code
                 left join hip_mdi.t_org_dept recv_dept
                           on t_wm_bill.Org_ID = recv_dept.Org_ID and t_wm_bill.Recv_Dept_Code = recv_dept.Dept_Code
                 left join hip_mdi.t_section on t_wm_bill.Section_ID = t_section.Section_ID
                 left join hip_mdi.t_org on t_wm_bill.Org_ID = t_org.Org_ID
                 left join microhis_clinics_wm.t_wm_req on t_wm_req.WB_SeqID = t_wm_bill_pending.WB_SeqID
            ${ew.customSqlSegment}
    </select>
    <!--    病区-待退药-退药列表明细-->
    <select id="queryPendingBillSectionPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        select t_wm_bill.*
             ,t_scm_cust.Cust_Name
             ,Creator.User_Name   as Creator_UName
             ,Validator.User_Name as Validator_UName
             ,t_wm_bill_type.WMBill_Type_Name
             ,t_org_dept.Dept_Name
             ,recv_dept.Dept_Name as Recv_Dept_Name
             ,t_section.Section_Name
             ,t_org.Org_Name
             ,t_wm_req.Notes      AS Req_Notes
        from microhis_clinics_wm.t_wm_bill_pending
                 inner join microhis_clinics_wm.t_wm_bill on t_wm_bill_pending.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_scm_cust on t_wm_bill.Cust_ID = t_scm_cust.Cust_ID
                 left join hip_mdi.t_user_code Creator on t_wm_bill.Creator_UID = Creator.User_ID
                 left join hip_mdi.t_user_code Validator on t_wm_bill.Validator_UID = Validator.User_ID
                 left join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
                 left join hip_mdi.t_org_dept
                           on t_wm_bill.Org_ID = t_org_dept.Org_ID and t_wm_bill.Dept_Code = t_org_dept.Dept_Code
                 left join hip_mdi.t_org_dept recv_dept
                           on t_wm_bill.Org_ID = recv_dept.Org_ID and t_wm_bill.Recv_Dept_Code = recv_dept.Dept_Code
                 left join hip_mdi.t_section on t_wm_bill.Section_ID = t_section.Section_ID
                 left join hip_mdi.t_org on t_wm_bill.Org_ID = t_org.Org_ID
                 left join microhis_clinics_wm.t_wm_req on t_wm_req.WB_SeqID = t_wm_bill_pending.WB_SeqID
            ${ew.customSqlSegment}
    </select>
    <!--    病区-已退药-退药列表-->
    <select id="queryReturnedBillSectionPage" resultType="cn.feiying.med.clinics_wm.dto.WmBillDto">
        select t_wm_bill.*
             , t_scm_cust.Cust_Name
             , Creator.User_Name   as Creator_UName
             , Validator.User_Name as Validator_UName
             , t_wm_bill_type.WMBill_Type_Name
             , t_org_dept.Dept_Name
             , recv_dept.Dept_Name as Recv_Dept_Name
             , t_section.Section_Name
             , t_org.Org_Name
             , t_wm_req.Notes AS Req_Notes
             ,JSON_ARRAYAGG(t_wm_bill.WB_SeqID) AS wbSeqids
        from microhis_clinics_wm.t_wm_bill
                 left join microhis_clinics_wm.t_scm_cust on t_wm_bill.Cust_ID = t_scm_cust.Cust_ID
                 left join hip_mdi.t_user_code Creator on t_wm_bill.Creator_UID = Creator.User_ID
                 left join hip_mdi.t_user_code Validator on t_wm_bill.Validator_UID = Validator.User_ID
                 left join microhis_clinics_wm.t_wm_bill_type on t_wm_bill.WMBill_Type_ID = t_wm_bill_type.WMBill_Type_ID
                 left join hip_mdi.t_org_dept on t_wm_bill.Org_ID = t_org_dept.Org_ID and t_wm_bill.Dept_Code = t_org_dept.Dept_Code
                 left join hip_mdi.t_org_dept recv_dept on t_wm_bill.Org_ID = recv_dept.Org_ID and t_wm_bill.Recv_Dept_Code = recv_dept.Dept_Code
                 left join hip_mdi.t_section on t_wm_bill.Section_ID = t_section.Section_ID
                 left join hip_mdi.t_org on t_wm_bill.Org_ID = t_org.Org_ID
                 left join microhis_clinics_wm.t_wm_req on t_wm_req.WB_SeqID = t_wm_bill.WB_SeqID
            ${ew.customSqlSegment}
    </select>

</mapper>