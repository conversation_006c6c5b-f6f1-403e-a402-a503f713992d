package cn.feiying.med.clinics_wm.dao;

import cn.feiying.med.clinics_wm.dto.DeptArtDto;
import cn.feiying.med.clinics_wm.dto.OrgArtDto;
import cn.feiying.med.clinics_wm.entity.ArtStocknoEntity;
import cn.feiying.med.clinics_wm.entity.OrgArtEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 机构商品表
 *
 * <AUTHOR> 2024-06-04 18:48:02
 */
@Mapper
public interface OrgArtDao extends BaseMapper<OrgArtEntity> {

    void updatePurchaseIn(Long orgId, Long artId, Date lastPurchased, BigDecimal lastBuyPrice, Integer totalPacks);

    void stockInc(Long orgId, Long artId, Integer totalPacks, BigDecimal totalCells);

    void stockDec(Long orgId, Long artId, Integer totalPacks, BigDecimal totalCells);

    /**
     * 减少t_org_art库存，返回更新条数
     * @param orgId
     * @param artId
     * @param totalPacks
     * @param totalCells
     * @return
     */
    int stockDecCount(Long orgId, Long artId, Integer totalPacks, BigDecimal totalCells);

    IPage<OrgArtDto> queryDtoPage(IPage<OrgArtDto> page, @Param(Constants.WRAPPER) QueryWrapper<OrgArtDto> wrapper);

    List<OrgArtDto> queryDtoPage(@Param(Constants.WRAPPER) QueryWrapper<OrgArtDto> wrapper);

    OrgArtDto findDtoById(Long orgId, Long artId);

    void batchUpdatePctAdd(@Param(Constants.WRAPPER) QueryWrapper<OrgArtDto> wrapper, BigDecimal pctAdd);

    List<ArtStocknoEntity> findAllStockNoByBatchNo(long orgId, Long artId, String batchNo);

    void updateSplittable(long orgId, Long artId, Integer splittable);

    void saveToOrgItemPrice(Long wbSeqid, Integer verifiedDate);

    void updateOrgItemPrice(Long wbSeqid);

    void updateOrgFormulary(Long wbSeqid, Long orgId);

    IPage<OrgArtDto> queryDeptArtSafeWarning(IPage<DeptArtDto> page, @Param(Constants.WRAPPER) QueryWrapper<DeptArtDto> wrapper);

    List<OrgArtDto> queryDeptArtSafeWarning(@Param(Constants.WRAPPER) QueryWrapper<DeptArtDto> wrapper);

    IPage<OrgArtDto> queryOrgArtSafeWarning(IPage<DeptArtDto> page, @Param(Constants.WRAPPER) QueryWrapper<DeptArtDto> wrapper);

    List<OrgArtDto> queryOrgArtSafeWarning(@Param(Constants.WRAPPER) QueryWrapper<DeptArtDto> wrapper);


    void remakeOrgArt(Long orgId, Long artId, Integer totalPacks, BigDecimal totalCells);

}
