package cn.feiying.med.microhis.hsd.service;

import cn.feiying.med.microhis.hsd.dto.IprRecordDto;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.microhis.hsd.entity.IprRecordEntity;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 住院申请记录表
 *
 * <AUTHOR> @email 
 * @date 2023-09-21 15:15:53
 * 2023-09-21 15:15:53
 */
public interface IprRecordService extends IService<IprRecordEntity> {

    PageUtils queryPage(Map<String, Object> params);

    IprRecordDto findById(Long iprId);

    IprRecordDto findByVisitId(Long visitId);

    Long saveEntity(IprRecordEntity entity);

    void cancelEntity(Long userId, List<Long> iprIdLs);

    void delete(Integer... idLs);
}


