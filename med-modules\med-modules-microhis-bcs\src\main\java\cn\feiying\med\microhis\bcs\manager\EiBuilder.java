package cn.feiying.med.microhis.bcs.manager;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.AgeUtil;
import cn.feiying.med.hip.enums.PaymentType;
import cn.feiying.med.hip.mdi.entity.InsuranceTypeEntity;
import cn.feiying.med.hip.mdi.entity.UserCodeEntity;
import cn.feiying.med.hip.mdi.service.InsuranceTypeService;
import cn.feiying.med.hip.mdi.service.OrgDeptService;
import cn.feiying.med.hip.mdi.service.UserCodeService;
import cn.feiying.med.hip.mpi.entity.MpiPatientMapEntity;
import cn.feiying.med.hip.mpi.entity.PatientEntity;
import cn.feiying.med.hip.mpi.service.MpiPatientMapService;
import cn.feiying.med.hip.mpi.service.PatientService;
import cn.feiying.med.mcisp.entity.TransEntity;
import cn.feiying.med.microhis.bcs.entity.BillEntity;
import cn.feiying.med.microhis.bcs.entity.CashEntity;
import cn.feiying.med.microhis.bcs.entity.InvoiceEntity;
import cn.feiying.med.microhis.bcs.service.BillService;
import cn.feiying.med.microhis.bcs.service.CashPaymentService;
import cn.feiying.med.microhis.bcs.service.CashService;
import cn.feiying.med.microhis.bcs.service.InvoiceService;
import cn.feiying.med.microhis.bcs.vo.CashPaymentVo;
import cn.feiying.med.microhis.ei.in.*;
import cn.feiying.med.microhis.hsd.entity.InpatientEntity;
import cn.feiying.med.microhis.hsd.entity.VisitEntity;
import cn.feiying.med.microhis.hsd.entity.VisitExtraEntity;
import cn.feiying.med.microhis.hsd.service.InpatientService;
import cn.feiying.med.microhis.hsd.service.VisitService;
import cn.feiying.med.microhis.hsd.service.impl.VisitExtraServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class EiBuilder {
    private final CashService cashService;
    private final UserCodeService userCodeService;
    private final PatientService patientService;
    private final VisitService visitService;
    private final InsuranceTypeService insuranceTypeService;
    private final InpatientService inpatientService;
    private final OrgDeptService orgDeptService;
    private final CashPaymentService cashPaymentService;
    private final MpiPatientMapService mpiPatientMapService;
    private final BillService billService;
    private final InvoiceService invoiceService;
    private final CashPaymentManager cashPaymentManager;
    private final VisitExtraServiceImpl visitExtraService;

    private static String toSex(Integer genderId) {
        switch (genderId) {
            case 0:
                return "未知";
            case 1:
                return "男";
            case 2:
                return "女";
            default:
                return "隐私";
        }
    }

    private void setFee(CashEntity cash, EiOutpatientIn in) {
        Long cashId = cash.getCashId();
        List<Long> prevCashIds = cashService.findPrevIds(cashId);
        Map<PaymentType, CashPaymentVo> map = cashPaymentService.getSuccessPayment(prevCashIds);
        BigDecimal ownPay = CashPaymentService.countSelfFee(map);
        in.setOwnPay(ownPay);
        in.setSelfCashPay(ownPay);

        CashPaymentVo miFund = map.get(PaymentType.miFund);
        BigDecimal miFundAmt = Optional.ofNullable(miFund).map(CashPaymentVo::getAmount).orElse(BigDecimal.ZERO);
        in.setReimbursementAmt(miFundAmt);
        if (miFund != null && NumberUtil.isGreater(miFundAmt, BigDecimal.ZERO)) {
            TransEntity trans = cashPaymentManager.getMiTrans(cashId);
            Assert.notNull(trans, () -> new SaveFailureException("未找到医保支付信息"));
            assert trans != null;
            in.setFundPay(trans.getFundPayAmt());
            in.setAccountPay(trans.getAcctPayAmt());
            in.setSelfPayAmt(trans.getPatientPartAmt());
            in.setOwnAcBalance(trans.getAcctBalance());
            in.setInsuranceNo(trans.getMdtrtId());
            in.setInsuranceTypeCode(String.valueOf(trans.getInsuranceTypeId()));
            InsuranceTypeEntity it = Assert.notNull(insuranceTypeService.getById(trans.getInsuranceTypeId()), () -> new SaveFailureException("未找到医保类型"));
            in.setInsuranceType(it.getInsuranceName());
        }

        CashPaymentVo miFamily = map.get(PaymentType.miFamily);
        BigDecimal miFamilyAmt = Optional.ofNullable(miFamily).map(CashPaymentVo::getAmount).orElse(BigDecimal.ZERO);
        in.setOtherfundPay(miFamilyAmt);
        if (miFamily != null && NumberUtil.isGreater(miFamilyAmt, BigDecimal.ZERO)) {
            List<EiOtherInsuranceIn> others = new ArrayList<>(1);
            others.add(EiOtherInsuranceIn.builder()
                    .index(1)
                    .value(String.valueOf(miFamily))
                    .name(PaymentType.miFamily.getName())
                    .build());
            in.setOtherInsurances(others);
        }

        List<EiPayChannelIn> payChannels = new ArrayList<>(map.size());
        for (Entry<PaymentType, CashPaymentVo> entry : map.entrySet()) {
            PaymentType pt = entry.getKey();
            CashPaymentVo vo = entry.getValue();
            BigDecimal amount = Optional.ofNullable(vo).map(CashPaymentVo::getAmount).orElse(BigDecimal.ZERO);
            payChannels.add(EiPayChannelIn.builder()
                    .code(String.valueOf(pt.getValue()))
                    .amount(amount)
                    .build());
        }
        in.setPayChannels(payChannels);
    }

    @Nullable
    public EiOutpatientIn buildOutpatient(InvoiceEntity invoice, @Nullable String email, @Nullable String mobile) {
        CashEntity cash = cashService.get(invoice.getCashId());
        EiOutpatientIn in = new EiOutpatientIn();
        in.setEmail(email);
        in.setMobile(mobile);
        in.setId(invoiceService.buildNo(invoice.getInvseqid()));

        BillEntity bill = billService.getFirstByCashId(invoice.getCashId());
        MpiPatientMapEntity patientMap = mpiPatientMapService.findById(bill.getPatientId(), bill.getOrgId());
        if (patientMap != null) {
            in.setMedicalRecordNo(String.valueOf(patientMap.getPatientNo()));
        }

        UserCodeEntity userCode = userCodeService.getById(cash.getUserId());
        if (userCode != null) {
            in.setPayee(userCode.getUserName());
            in.setPayeeId(userCode.getUserCode());
        }

        in.setTotalAmount(invoice.getAmount());
        in.setCreatedAt(cash.getTimeCreated());
        in.setType(cash.getCashTypeId().toString());
        in.setBizId(cash.getCashId());

        if (cash.getVisitId() == null) {
            return null;
        }
        VisitEntity visit = visitService.getById(cash.getVisitId());
        Assert.notNull(visit, () -> new SaveFailureException("未找到就诊信息"));

        PatientEntity patient = patientService.getById(visit.getPatientId());
        if (patient == null) {
            in.setCardType("10");
            in.setCardNo(String.valueOf(visit.getVisitNo()));
        } else {
            in.setCardType(patient.getCertTypeId().toString());
            in.setCardNo(patient.getIdcertNo());
            in.setIdentityNumber(patient.getIdcertNo());
            if (StrUtil.isBlank(mobile)) {
                in.setMobile(patient.getTelNo());
            }
        }

        in.setDepartment(orgDeptService.getDeptName(visit.getOrgId(), visit.getDeptCode()));
        in.setDepartmentCode(visit.getDeptCode());
        in.setVisitNo(Convert.toLong(visit.getVisitId(), null));
        in.setVisitAt(DateUtil.parse(visit.getClinicDate().toString()));
        in.setAge(AgeUtil.getAge(visit.getAgeOfYears(), Convert.toInt(visit.getAgeOfDays())));
        in.setSex(toSex(visit.getGenderId()));
        in.setName(visit.getPatientName());
        in.setPatientId(bill.getPatientId());

        setFee(cash, in);
        return in;
    }

    public EiInpatientIn buildHospitalized(InvoiceEntity invoice, @Nullable String email, @Nullable String mobile) {
        EiInpatientIn in = new EiInpatientIn();
        in.setEmail(email);
        in.setMobile(mobile);

        CashEntity cash = cashService.get(invoice.getCashId());
        if (NumberUtil.equals(cash.getRefundedAmount(), cash.getAmount())) {
            return null;
        }
        in.setId(invoiceService.buildNo(invoice.getInvseqid()));

        UserCodeEntity userCode = userCodeService.getById(cash.getUserId());
        if (userCode != null) {
            in.setPayee(userCode.getUserName());
            in.setPayeeId(userCode.getUserCode());
        }

        in.setTotalAmount(NumberUtil.sub(cash.getAmount(), cash.getRefundedAmount()));
        in.setCreatedAt(cash.getTimeCreated());
        in.setType(cash.getCashTypeId().toString());
        in.setBizId(cash.getCashId());

        Assert.notNull(cash.getVisitId(), () -> new SaveFailureException("cash visitId为空"));
        VisitEntity visit = Assert.notNull(visitService.getById(cash.getVisitId()), () -> new SaveFailureException("未找到就诊信息"));
        VisitExtraEntity ve = Assert.notNull(visitExtraService.getById(cash.getVisitId()), () -> new SaveFailureException("未找到诊疗记录其他信息表"));
        InpatientEntity inpatient = Assert.notNull(inpatientService.getById(cash.getVisitId()), () -> new SaveFailureException("未找到住院病例表"));

        in.setCardType(ve.getCertTypeId().toString());
        in.setCardNo(ve.getIdcertNo());
        in.setIdentityNumber(ve.getIdcertNo());

        in.setDay(inpatient.getHospitalizedDays());
        in.setHospitalNo(inpatient.getInpatientNo());
        in.setInAt(visit.getTimeAdmission());
        in.setOutAt(inpatient.getTimeDischarged());
        in.setMedicalRecordNo(inpatient.getCaseNo());

        in.setDepartment(orgDeptService.getDeptName(visit.getOrgId(), visit.getDeptCode()));
        in.setDepartmentCode(visit.getDeptCode());
        in.setVisitNo(Convert.toLong(visit.getVisitId(), null));
        in.setVisitAt(DateUtil.parse(visit.getClinicDate().toString()));
        in.setAge(AgeUtil.getAge(visit.getAgeOfYears(), Convert.toInt(visit.getAgeOfDays())));
        in.setSex(toSex(visit.getGenderId()));
        in.setName(visit.getPatientName());
        in.setPatientId(visit.getPatientId());
        if (StrUtil.isBlank(mobile)) {
            in.setMobile(ve.getContactTel());
        }
        setFee(cash, in);
        return in;
    }

    @Nullable
    public EiRegistrationIn buildRegistration(InvoiceEntity invoice, @Nullable String email, @Nullable String mobile) {
        EiOutpatientIn outpatient = buildOutpatient(invoice, email, mobile);
        return BeanUtil.copyProperties(outpatient, EiRegistrationIn.class);
    }
}
