<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.feiying.med.clinics_wm.dao.OrgArtDao">
    <insert id="saveToOrgItemPrice">
        insert into hip_mdi.t_org_item_price(org_id, bid_price, unit_price, verified_date, level_id, art_id, tp_flag,
                                             checked_flag, op_price, ip_price, udf_type_id)
        select min(t_wm_bill.Org_ID),
               max(t_wm_bill_detail.Pack_Price) * (1 + ifnull(t_org_art.PCT_ADD, 0) / 100),
               max(t_wm_bill_detail.Pack_Price) * (1 + ifnull(t_org_art.PCT_ADD, 0) / 100),
               #{verifiedDate},
               1,
               t_wm_bill_detail.Art_ID,
               0,
               1,
               max(t_wm_bill_detail.Pack_Price) * (1 + ifnull(t_org_art.PCT_ADD, 0) / 100),
               max(t_wm_bill_detail.Pack_Price) * (1 + ifnull(t_org_art.PCT_ADD, 0) / 100),
               min(t_article.Fee_Type_ID)
        from microhis_clinics_wm.t_wm_bill_detail
                 left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                 left join microhis_clinics_wm.t_org_art on t_wm_bill.Org_ID = t_org_art.Org_ID and t_wm_bill_detail.Art_ID = t_org_art.Art_ID
                 left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
        where t_wm_bill.WB_SeqID = #{wbSeqid}
          and not exists(select 1 from hip_mdi.t_org_item_price where t_org_item_price.org_id = t_wm_bill.Org_ID and t_org_item_price.art_id = t_wm_bill_detail.Art_ID)
        group by t_wm_bill_detail.Art_ID
    </insert>
    <update id="updateOrgItemPrice">
        update microhis_clinics_wm.t_wm_bill_detail
            left join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
            left join microhis_clinics_wm.t_org_art on t_wm_bill.Org_ID = t_org_art.Org_ID and t_wm_bill_detail.Art_ID = t_org_art.Art_ID
            left join hip_mdi.t_org_item_price on t_org_item_price.org_id = t_wm_bill.Org_ID and t_org_item_price.art_id = t_wm_bill_detail.Art_ID
            left join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.Art_ID
        set t_org_item_price.Bid_Price = t_wm_bill_detail.Pack_Price * (1 + ifnull(t_org_art.PCT_ADD, 0) / 100)
          , Unit_Price = t_wm_bill_detail.Pack_Price * (1 + ifnull(t_org_art.PCT_ADD, 0) / 100)
          , OP_Price = t_wm_bill_detail.Pack_Price * (1 + ifnull(t_org_art.PCT_ADD, 0) / 100)
          , IP_Price = t_wm_bill_detail.Pack_Price * (1 + ifnull(t_org_art.PCT_ADD, 0) / 100)
        where t_wm_bill.WB_SeqID = #{wbSeqid}
    </update>

    <update id="updateOrgFormulary">
        update hip_mdi.t_org_formulary inner join (select t_wm_bill.Org_ID,
                                                          t_wm_bill_detail.Art_ID,
                                                          t_wm_bill_detail.Pack_Price,
                                                          t_wm_bill_detail.Pack_Price * (100 + ifnull(t_org_art.PCT_ADD, 0)) / 100 Pct_Pack_Price,
                                                          t_org_art.Pack_Price as Org_Pack_Price,
                                                          t_org_art.PCT_ADD,
                                                          t_wm_bill.Time_Created,
                                                          t_wm_bill.WB_SeqID
                                                   from microhis_clinics_wm.t_wm_bill_detail
                                                            inner join microhis_clinics_wm.t_wm_bill on t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID
                                                            inner join microhis_clinics_wm.t_org_art
                                                                       on t_wm_bill.Org_ID = t_org_art.Org_ID and t_wm_bill_detail.Art_ID = t_org_art.Art_ID
                                                   where t_wm_bill.WB_SeqID = #{wbSeqid}) bill_Price on t_org_formulary.Org_ID = bill_Price.Org_ID and t_org_formulary.Art_ID = bill_Price.Art_ID
        set t_org_formulary.List_Price = bill_Price.Pct_Pack_Price
        where t_org_formulary.Org_ID = #{orgId}
    </update>

    <update id="updatePurchaseIn">
        update microhis_clinics_wm.t_org_art set
            last_purchased = #{lastPurchased},
            last_buy_price = #{lastBuyPrice},
            last_rest_packs = ifnull(last_rest_packs,0) + ifnull(#{totalPacks},0)
        where org_id = #{orgId} and art_id = #{artId}
    </update>
    <update id="stockInc">
        update microhis_clinics_wm.t_org_art set
            total_packs = ifnull(total_packs,0) + ifnull(#{totalPacks},0),
            total_cells = ifnull(total_cells,0) + ifnull(#{totalCells},0)
        where org_id = #{orgId} and art_id = #{artId}
    </update>
    <update id="stockDec">
        update microhis_clinics_wm.t_org_art set
            total_packs = ifnull(total_packs,0) - ifnull(#{totalPacks},0),
            total_cells = ifnull(total_cells,0) - ifnull(#{totalCells},0)
        where org_id = #{orgId} and art_id = #{artId}
    </update>
    <!-- 减少t_org_art库存，返回更新条数-->
    <update id="stockDecCount">
        update microhis_clinics_wm.t_org_art set
            total_packs = ifnull(total_packs,0) - ifnull(#{totalPacks},0),
            total_cells = ifnull(total_cells,0) - ifnull(#{totalCells},0)
        where org_id = #{orgId} and art_id = #{artId}
    </update>
    <update id="batchUpdatePctAdd">
        update microhis_clinics_wm.t_org_art
        left join hip_mdi.t_org_formulary on t_org_art.Org_ID = t_org_formulary.Org_ID and t_org_art.Art_ID = t_org_formulary.Art_ID
        left join hip_mdi.t_article on t_org_art.Art_ID = t_article.Art_ID
        left join hip_mdi.t_medicine on t_article.Med_ID = t_medicine.Med_ID
        set PCT_ADD = #{pctAdd}
        ${ew.customSqlSegment}
    </update>
    <update id="updateSplittable">
        update microhis_clinics_wm.t_dept_art set Splittable = #{splittable}
        where t_dept_art.Org_ID = #{orgId} and t_dept_art.Art_ID = #{artId}
    </update>
    <sql id="baseDtoSql">
        select t_org_art.*,
               t_article.Art_Code,
               t_article.Art_Name,
               t_article.Art_Spec,
               t_article.Pack_Unit,
               t_article.Producer,
               t_article.Art_Class_ID,
               t_article.is_Material,
               t_article.Cell_Unit,
               t_article.Pack_Cells,
               t_article.Dose_Unit,
               t_article.Cell_Doses,
               t_article.EAN_Code,
               t_article.Fee_Type_ID,
               t_article.Pic_Count,
               t_article.CDAN_Name,
               t_article.Shelf_Life,
               t_article.Approval_No,
               t_article.Art_Type_ID,
               t_article.Cat_Type_ID,
               t_article.Order_Type_ID,
               t_article.Dosage_Form,
               t_article.Pack_Material,
               t_article.is_OTC,
               t_article.is_ED,
               t_article.is_Homemade,
               t_article.for_OE,
               t_article.is_Antibacterial,
               t_article.Antibacterial_Level,
               t_article.for_Children,
               t_article.for_Aged,
               t_article.for_OLS,
               t_article.CFDA_Code,
               t_article.MI_Code,
               t_article.YPID_Code,
               t_article.Icon_URL,
               t_article.Specimen_Type_ID,
               t_article.is_Package,
               t_article.for_Treatment,
               t_article.for_Doctor_Order,
               t_article.No_Track_Code,
               t_article.is_Disassembled,
               t_article.Allowed_Gender,
               t_medicine.Med_Code,
               t_medicine.Chn_Name,
               t_medicine.Eng_Name,
               t_medicine.ST_Required,
               t_article.Chrgitm_LV,
               t_org_formulary.List_Price,
               t_art_extra.Max_Doses,
               t_art_extra.Meal_Doses,
               t_art_extra.Meal_Cells,
               t_art_extra.Freq_Code,
               t_art_extra.Route_ID,
               t_art_extra.Max_Kg_Doses,
               t_art_extra.Kg_Doses,
               t_art_extra.Admin_Cat_ID,
               t_art_subtype.Subtype_ID,
               t_art_subtype.Subtype_Code,
               t_art_subtype.Subtype_Name,
               t_art_subtype.Display_Order as Art_Subtype_Display_Order
        from microhis_clinics_wm.t_org_art
         left join hip_mdi.t_org_formulary on t_org_art.Org_ID = t_org_formulary.Org_ID and t_org_art.Art_ID = t_org_formulary.Art_ID
         left join hip_mdi.t_article on t_org_art.Art_ID = t_article.Art_ID
         left join hip_mdi.t_medicine on t_article.Med_ID = t_medicine.Med_ID
         left join hip_mdi.t_art_extra on t_article.Art_ID = t_art_extra.Art_ID
         left join hip_mdi.t_art_subtype on t_article.Subtype_ID = t_art_subtype.Subtype_ID
    </sql>
    <select id="queryDtoPage" resultType="cn.feiying.med.clinics_wm.dto.OrgArtDto">
        <include refid="baseDtoSql"></include>
        ${ew.customSqlSegment}
    </select>
    <select id="findDtoById" resultType="cn.feiying.med.clinics_wm.dto.OrgArtDto">
        <include refid="baseDtoSql"></include>
        where t_org_art.org_id = #{orgId} and t_org_art.art_id = #{artId}
    </select>
    <select id="findAllStockNoByBatchNo" resultType="cn.feiying.med.clinics_wm.entity.ArtStocknoEntity">
        select t_art_stockno.*
        from microhis_clinics_wm.t_art_stockno
                 left join microhis_clinics_wm.t_wm_bill on t_art_stockno.WB_SeqID = t_wm_bill.WB_SeqID
        where t_wm_bill.Org_ID = #{orgId} and t_art_stockno.Art_ID = #{artId} and t_art_stockno.Batch_No = #{batchNo}
        order by t_art_stockno.Stock_No desc
    </select>

    <select id="queryDeptArtSafeWarning" resultType="cn.feiying.med.clinics_wm.dto.OrgArtDto">
        SELECT t_article.Art_ID,
            t_article.No_Track_Code,
            t_article.is_Disassembled,
            t_article.Art_Name
             , t_article.Art_Spec
             , t_article.Producer
             , t_article.Pack_Unit
             , t_article.Cell_Unit
             , t_article.Pack_Cells
             , t_article.Art_Code
             , t_article.MI_Code
             , t_article.Dose_Unit
             , t_article.Cell_Doses
             , t_article.Pack_Material
             , t_article.Dosage_Form
             , t_article.Approval_No
             , t_dept_art.safe_cells
             , t_dept_art.Total_Packs
             , t_dept_art.Total_Cells
             , t_dept_art.Splittable
             , t_org_art.PCT_ADD
             , t_org_art.Last_Buy_Price
             , t_org_art.Pack_Price                  as Base_Pack_Price
             , t_org_art.Cell_Price                  as Base_Cell_Price
             , (COALESCE(t_dept_art.Total_Packs, 0) * COALESCE(t_article.pack_cells, 1) +
                COALESCE(t_dept_art.Total_Cells, 0)) AS Sum_Total_Cells
             , t_art_subtype.Subtype_Name
             , t_art_subtype.Display_Order           as Art_Subtype_Display_Order
        FROM microhis_clinics_wm.t_dept_art t_dept_art
                 LEFT JOIN hip_mdi.t_article t_article ON t_dept_art.Art_ID = t_article.Art_ID
                 left join hip_mdi.t_art_subtype on t_article.Subtype_ID = t_art_subtype.Subtype_ID
                 LEFT JOIN microhis_clinics_wm.t_org_art
                           on t_dept_art.Org_ID = t_org_art.Org_ID and t_dept_art.Art_ID = t_org_art.Art_ID
        ${ew.customSqlSegment}
        and COALESCE(t_dept_art.safe_cells, 0) > 0
        AND (
                COALESCE(t_dept_art.safe_cells, 0) &gt;=
                (COALESCE(t_dept_art.Total_Packs, 0) *
                    CASE
                    WHEN COALESCE(t_article.pack_cells, 0) = 0 THEN 1
                    ELSE COALESCE(t_article.pack_cells, 1)
                    END
                + COALESCE(t_dept_art.Total_Cells, 0)
                )
        )
    </select>
    <select id="queryOrgArtSafeWarning" resultType="cn.feiying.med.clinics_wm.dto.OrgArtDto">
        SELECT
               t_article.Art_ID
             , t_article.No_Track_Code
             , t_article.is_Disassembled
             , t_article.Art_Name
             , t_article.Art_Spec
             , t_article.Producer
             , t_article.Pack_Unit
             , t_article.Cell_Unit
             , t_article.Pack_Cells
             , t_article.Art_Code
             , t_article.MI_Code
             , t_article.Dose_Unit
             , t_article.Cell_Doses
             , t_article.Pack_Material
             , t_article.Dosage_Form
             , t_article.Approval_No
             , t_org_art.Safe_Packs
             , t_org_art.Total_Packs
             , t_org_art.Total_Cells
             , t_org_art.Splittable
             , t_org_art.PCT_ADD
             , t_org_art.Last_Buy_Price
             , t_org_art.Pack_Price                  as Base_Pack_Price
             , t_org_art.Cell_Price                  as Base_Cell_Price
             , (COALESCE(t_org_art.Total_Packs, 0) * COALESCE(t_article.pack_cells, 1) +
                COALESCE(t_org_art.Total_Cells, 0)) AS Sum_Total_Cells
        FROM microhis_clinics_wm.t_org_art t_org_art
                 LEFT JOIN hip_mdi.t_article t_article ON t_org_art.Art_ID = t_article.Art_ID
        ${ew.customSqlSegment}
        and COALESCE(t_org_art.Safe_Packs, 0) > 0
        AND
            COALESCE(t_org_art.Safe_Packs, 0) >= (
                COALESCE(t_org_art.Total_Packs, 0) +
                CEIL(
                    COALESCE(t_org_art.Total_Cells, 0) * 1.0 /
                        CASE
                            WHEN COALESCE(t_article.pack_cells, 0) = 0 THEN 1
                            ELSE COALESCE(t_article.pack_cells, 1)
                            END
                )
            )
    </select>
    <select id="remakeOrgArt">
        update microhis_grocery_wm.t_org_art set
        total_packs = ifnull(#{totalPacks},0),
        total_cells = ifnull(#{totalCells},0)
        where org_id = #{orgId} and art_id = #{artId}
    </select>
</mapper>