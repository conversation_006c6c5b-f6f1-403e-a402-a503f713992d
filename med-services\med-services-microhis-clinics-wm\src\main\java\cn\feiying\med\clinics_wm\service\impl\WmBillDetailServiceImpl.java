package cn.feiying.med.clinics_wm.service.impl;

import cn.feiying.med.clinics_wm.dao.WmBillDetailDao;
import cn.feiying.med.clinics_wm.dto.PackAndCellResult;
import cn.feiying.med.clinics_wm.dto.RecipeDetailDto;
import cn.feiying.med.clinics_wm.dto.RecipeDto;
import cn.feiying.med.clinics_wm.dto.WmBillDetailDto;
import cn.feiying.med.clinics_wm.entity.*;
import cn.feiying.med.clinics_wm.enums.*;
import cn.feiying.med.clinics_wm.model.ArtPriceModel;
import cn.feiying.med.clinics_wm.service.*;
import cn.feiying.med.clinics_wm.util.BeanStreamUtils;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.ArticleUtil;
import cn.feiying.med.common.utils.GQueryWrapper;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.common.utils.Query;
import cn.feiying.med.hip.enums.ExecStatus;
import cn.feiying.med.hip.enums.PaidStatus;
import cn.feiying.med.hip.mdi.entity.ArticleEntity;
import cn.feiying.med.hip.mdi.service.ArticleService;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 出入库单明细表
 *
 * <AUTHOR> 18:48:02
 */
@Slf4j
@Service("wmBillDetailService")
public class WmBillDetailServiceImpl extends ServiceImpl<WmBillDetailDao, WmBillDetailEntity> implements WmBillDetailService {

    @Resource
    private ArtStocknoService artStocknoService;
    @Resource
    private ArticleService articleService;
    @Resource
    private WmBillService wmBillService;
    @Resource
    private OrgCustMapService orgCustMapService;
    @Resource
    private OrgArtService orgArtService;
    @Resource
    private ArtTrackcodeProdService artTrackcodeProdService;

    @Override
    public PageUtils queryPage(Long wbSeqid, Map<String, Object> params, Long orgId) {
        if (wbSeqid == null) {
            throw new SaveFailureException("参数错误，出入库单流水号不能为空");
        }
        boolean sumPage = Convert.toBool(params.get("setProdNo"), true);
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.eq("t_wm_bill_detail.WB_SeqID", wbSeqid);
        IPage<WmBillDetailDto> page = this.baseMapper.queryDtoPage(new Query<WmBillDetailDto>().getPage(params), wrapper);

        Map<String, Object> extra = new HashMap<>();
        if (sumPage) {
            BigDecimal sumAmount = this.baseMapper.queryDtoPageSumAmount(wrapper);
            extra.put("sumAmount", sumAmount);
        }
        boolean setProdNo = Convert.toBool(params.get("setProdNo"), false);
        if (setProdNo) {
            setDetailArtProdNo(page.getRecords());
        }
        return new PageUtils(page, extra);
    }

    @Override
    public List<WmBillDetailDto> findLsByIds(Map<String, Object> params) {
        Long wbSeqId = Convert.toLong(params.get("wbSeqid"));
        List<Long> wbSeqIdLs = Convert.toList(Long.class,params.get("wbSeqids"));
        if (ObjectUtil.isEmpty(wbSeqId) && ObjectUtil.isEmpty(wbSeqIdLs)) {
            return new ArrayList<>();
        }
        QueryWrapper<WmBillDetailDto> wrapper = new QueryWrapper<>();
        if (wbSeqId != null) {
            wrapper.eq("t_wm_bill_detail.WB_SeqID", wbSeqId);
        }
        if (ObjectUtil.isNotEmpty(wbSeqIdLs)) {
            wrapper.in("t_wm_bill_detail.WB_SeqID", wbSeqIdLs);
        }
        List<WmBillDetailDto> list = baseMapper.findLsByWrapper(wrapper);
        boolean setProdNo = Convert.toBool(params.get("setProdNo"), false);
        if (setProdNo) {
            setDetailArtProdNo(list);
        }
        return list;
    }

    private void setDetailArtProdNo(List<WmBillDetailDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<Long> artIds = list.stream().map(WmBillDetailDto::getArtId).distinct().collect(Collectors.toList());
            List<ArtTrackcodeProdEntity> artTrackcodeProdLs = artTrackcodeProdService.list(new LambdaQueryWrapper<ArtTrackcodeProdEntity>().in(ArtTrackcodeProdEntity::getArtId, artIds));
            list.forEach(dto -> {
                List<Integer> prodNoLs = artTrackcodeProdLs.stream().filter(p->p.getArtId().equals(dto.getArtId())).map(ArtTrackcodeProdEntity::getProdNo).collect(Collectors.toList());
                dto.setProdNoLs(prodNoLs);
            });
        }
    }

    /**
     * 查询t_wm_bill_detail 按批次 还剩余库存
     *
     * @param wbSeqid
     * @param params
     * @param orgId
     * @return
     */
    @Override
    public List<WmBillDetailDto> detailAndStockNoPage(Long wbSeqid, Map<String, Object> params, Long orgId) {
        if (wbSeqid == null) {
            throw new SaveFailureException("参数错误，出入库单流水号不能为空");
        }
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.eq("t_wm_bill_detail.WB_SeqID", wbSeqid);
        List<WmBillDetailDto> page = this.baseMapper.detailAndStockNoPage(wrapper);
        //默认冲红整包数、冲红拆零数量 与批次库存数量一致
        page.forEach(dto -> {
            //默认冲红整包数、冲红拆零数量 为0，防止用户手误没有填写，但是冲红了的情况
            dto.setReturnTotalPacks(0);
            dto.setReturnTotalCells(BigDecimal.ZERO);
            //如果无剩余数量，填充为0,便于前端展示查
            dto.setDeptStockTotalPacks(dto.getDeptStockTotalPacks() == null ? 0 : dto.getDeptStockTotalPacks());
            dto.setDeptStockTotalCells(dto.getDeptStockTotalCells() == null ? BigDecimal.ZERO : dto.getDeptStockTotalCells());
        });

        // 如果库存为0的记录放到最后
        page.sort((a, b) -> {
            boolean aIsZero = (a.getDeptStockTotalPacks() == 0 && a.getDeptStockTotalCells().compareTo(BigDecimal.ZERO) == 0);
            boolean bIsZero = (b.getDeptStockTotalPacks() == 0 && b.getDeptStockTotalCells().compareTo(BigDecimal.ZERO) == 0);
            if (aIsZero && !bIsZero) return 1;
            if (!aIsZero && bIsZero) return -1;
            return 0;
        });

        return page;
    }


    @Override
    public PageUtils queryGroupByArtPage(Long wbSeqid, Map<String, Object> params) {
        if (wbSeqid == null) {
            throw new SaveFailureException("参数错误，出入库单流水号不能为空");
        }
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.eq("t_wm_bill_detail.WB_SeqID", wbSeqid);
        wrapper.groupBy("t_wm_bill_detail.Art_ID, t_wm_bill_detail.batch_no, t_wm_bill_detail.pack_price");
        IPage<WmBillDetailDto> page = this.baseMapper.queryGroupByArtPage(new Query<WmBillDetailDto>().getPage(params), wrapper);

        Map<String, Object> extra = new HashMap<>();

        QueryWrapper<WmBillDetailDto> sumWrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        sumWrapper.eq("t_wm_bill_detail.WB_SeqID", wbSeqid);
        BigDecimal sumAmount = this.baseMapper.queryDtoPageSumAmount(sumWrapper);
        extra.put("sumAmount", sumAmount);
        return new PageUtils(page, extra);
    }

    @Override
    public PageUtils queryGroupByArtPageByIds(List<Long> wbSeqids, Map<String, Object> params) {
        if (wbSeqids == null || wbSeqids.isEmpty()) {
            throw new SaveFailureException("参数错误，出入库单流水号不能为空");
        }
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.in("t_wm_bill_detail.WB_SeqID", wbSeqids);
        wrapper.groupBy("t_wm_bill_detail.Art_ID, t_wm_bill_detail.batch_no, t_wm_bill_detail.pack_price");
        IPage<WmBillDetailDto> page = this.baseMapper.queryGroupByArtPage(new Query<WmBillDetailDto>().getPage(params), wrapper);

        Map<String, Object> extra = new HashMap<>();

        QueryWrapper<WmBillDetailDto> sumWrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        sumWrapper.in("t_wm_bill_detail.WB_SeqID", wbSeqids);
        BigDecimal sumAmount = this.baseMapper.queryDtoPageSumAmount(sumWrapper);
        extra.put("sumAmount", sumAmount);
        return new PageUtils(page, extra);
    }

    @Override
    public PageUtils queryTrackCodePage(long wbSeqid, Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.eq("t_wm_bill_detail.WB_SeqID", wbSeqid);
        IPage<WmBillDetailDto> page = this.baseMapper.queryTrackCodePage(new Query<WmBillDetailDto>().getPage(params), wrapper);

        return new PageUtils(page);
    }

    @Override
    public List<WmBillDetailDto> queryList(Long wbSeqid, boolean notLinkReq) {
        WmBillEntity wmBill = wmBillService.getById(wbSeqid);
        List<WmBillDetailDto> list = new ArrayList<>();
        //req表太大，尽量不关联查询,不需要关联req表时传true
        if (notLinkReq) {
            list = baseMapper.queryDtoListNotLinkReq(wbSeqid);
        } else {
            list = baseMapper.queryDtoList(wbSeqid);
        }

        OrgCustMapEntity orgCustMap = orgCustMapService.findById(wmBill.getOrgId());
        for (WmBillDetailDto dto : list) {
            if (dto.getTotalPacks() != null && !dto.getTotalPacks().equals(0)) {
                dto.setTotal(BigDecimal.valueOf(dto.getTotalPacks()));
                dto.setUnit(dto.getPackUnit());
                if (dto.getPackPrice() != null) {
                    dto.setPrice(dto.getPackPrice());
                }
            } else if (dto.getTotalCells() != null && dto.getTotalCells().compareTo(BigDecimal.ZERO) != 0) {
                dto.setTotal(ArticleUtil.packsToCells(dto.getTotalPacks(), dto.getPackCells(), dto.getTotalCells()));
                dto.setUnit(dto.getCellUnit());
                if (dto.getCellPrice() != null) {
                    dto.setPrice(dto.getCellPrice());
                }
            }

            // 20250509 这段逻辑有问题，上面如果是有拆零数量，会给dto.setPrice会赋予拆零价，下面subtract(dto.getCostPrice());减整包成本价一定会出现负数
//            if (dto.getCost() != null) {
//                if (!wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue())) {
//                    // 采购入库单，未完成，没有关联上art_stockno
//                    if (!wmBill.getStatus().equals(WmBillStatus.COMPLETED.getValue()) && WmBillType.purchaseInLs.contains(wmBill.getWmbillTypeId())) {
//                        dto.setCostPrice(dto.getPackPrice());
//                    }
//                    //20250425之前，sql返回值中dto.getCostPrice()并没有返回
//                    BigDecimal addPrice = dto.getPrice().subtract(dto.getCostPrice());
//                    if (dto.getPrice().compareTo(BigDecimal.ZERO) != 0) {
//                        BigDecimal pct = addPrice.divide(dto.getPrice(), 4, BigDecimal.ROUND_HALF_UP);
//                        dto.setSalePctAdd(pct.multiply(BigDecimal.valueOf(100)));
//                    } else {
//                        dto.setSalePctAdd(BigDecimal.ZERO);
//                    }
//                }
//            }
        }

        // 采购入库单设置售价
        if (WmBillType.purchaseInLs.contains(wmBill.getWmbillTypeId()) || (wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue()) && wmBill.getSectionId() == null)) {
            Integer pricingMethod = orgCustMap.getPricingMethod();
            for (WmBillDetailDto dto : list) {
                BigDecimal salePackPrice = BigDecimal.ZERO;
                BigDecimal saleCellPrice = BigDecimal.ZERO;
                BigDecimal saleAmount = BigDecimal.ZERO;
                if (pricingMethod == null || pricingMethod == PricingMethod.FIXED_PRICE.getValue()) {
                    salePackPrice = dto.getOrgPackPrice();
                    saleCellPrice = dto.getOrgCellPrice();
                    saleAmount = dto.getRatedAmount();
                } else if (pricingMethod == PricingMethod.PCT_ADD.getValue()) {
                    if (wmBill.getStatus().equals(WmBillStatus.COMPLETED.getValue())) {
                        salePackPrice = dto.getPctPrice();
                        saleCellPrice = dto.getPctCellPrice();
                        saleAmount = dto.getPctAmount();
                    } else {
                        salePackPrice = dto.getPackPrice().multiply(Convert.toBigDecimal(1).add(Convert.toBigDecimal(dto.getSalePctAdd(), BigDecimal.ZERO).divide(Convert.toBigDecimal(100))));
                        saleCellPrice = dto.getCellPrice().multiply(Convert.toBigDecimal(1).add(Convert.toBigDecimal(dto.getSalePctAdd(), BigDecimal.ZERO).divide(Convert.toBigDecimal(100))));
                        saleAmount = dto.getAmount().multiply(Convert.toBigDecimal(1).add(Convert.toBigDecimal(dto.getSalePctAdd(), BigDecimal.ZERO).divide(Convert.toBigDecimal(100))));
                    }
                } else if (pricingMethod == PricingMethod.MIN_LOSS.getValue()) {
                    ArtPriceModel priceModel = orgArtService.findPrice(pricingMethod, dto.getPackCells(),
                            dto.getOrgPackPrice(), dto.getOrgCellPrice(), dto.getPackPrice(),
                            dto.getPackPrice(), dto.getCellPrice(), dto.getSalePctAdd());
                    salePackPrice = priceModel.getPackPrice();
                    saleCellPrice = priceModel.getCellPrice();
                    if (salePackPrice != null && dto.getTotalPacks() != null && !dto.getTotalPacks().equals(0)) {
                        saleAmount = saleAmount.add(BigDecimal.valueOf(dto.getTotalPacks()).multiply(salePackPrice).setScale(2, RoundingMode.HALF_UP));
                    }
                    if (saleCellPrice != null && dto.getTotalCells() != null && dto.getTotalCells().compareTo(BigDecimal.ZERO) != 0) {
                        saleAmount = saleAmount.add(dto.getTotalCells().multiply(saleCellPrice).setScale(2, RoundingMode.HALF_UP));
                    }
                }
                dto.setSalePackPrice(salePackPrice.setScale(6, RoundingMode.HALF_UP));
                dto.setSaleCellPrice(saleCellPrice.setScale(6, RoundingMode.HALF_UP));
                dto.setSaleAmount(saleAmount.setScale(2, RoundingMode.HALF_UP));
                if (dto.getTotalPacks() != null && !dto.getTotalPacks().equals(0)) {
                    dto.setSalePrice(salePackPrice);
                } else if (dto.getTotalCells() != null && dto.getTotalCells().compareTo(BigDecimal.ZERO) != 0) {
                    dto.setSalePrice(saleCellPrice);
                }
            }
        } else if (wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue()) && wmBill.getSectionId() != null) {
            list.forEach(dto -> {
                if (Convert.toInt(dto.getTotalPacks(), 0 ) > 0) {
                    dto.setSalePrice(dto.getPackPrice());
                } else if (NumberUtil.isGreater(Convert.toBigDecimal(dto.getTotalCells(), BigDecimal.ZERO), BigDecimal.ZERO)) {
                    dto.setSalePrice(dto.getCellPrice());
                } else {
                    dto.setSalePrice(dto.getPackPrice());
                }
                dto.setSaleAmount(dto.getAmount());
            });
        }
        return list;
    }

    @Override
    public PageUtils queryRecipePage(long orgId, Map<String, Object> params) {
        QueryWrapper<RecipeDto> wrapper = new GQueryWrapper<RecipeDto>().getWrapper(params);
        wrapper.eq("t_recipe.org_id", orgId);
        IPage<RecipeDto> page = this.baseMapper.queryRecipePage(new Query<RecipeDto>().getPage(params), wrapper);

        return new PageUtils(page);
    }

    @Override
    public PageUtils queryRecipeDeliverPage(long orgId, Map<String, Object> params) {
        QueryWrapper<RecipeDetailDto> wrapper = new GQueryWrapper<RecipeDetailDto>().getWrapper(params);
        wrapper.eq("t_bill.org_id", orgId);
        wrapper.isNotNull("t_recipe.Time_Delivered");
        wrapper.eq("t_recipe.Exec_Status", ExecStatus.finish.getValue());
        wrapper.eq("t_bill.Paid_Status", PaidStatus.paid.getValue());
        IPage<RecipeDetailDto> page = this.baseMapper.queryRecipeDeliverPage(new Query<RecipeDetailDto>().getPage(params), wrapper);

        return new PageUtils(page);
    }

    @Override
    public void updateStockNo(long orgId, Long wbSeqid, Integer lineNo, Integer stockNo) {
        WmBillDetailEntity wmBillDetailEntity = this.getOne(new LambdaQueryWrapper<WmBillDetailEntity>()
                .eq(WmBillDetailEntity::getWbSeqid, wbSeqid)
                .eq(WmBillDetailEntity::getLineNo, lineNo));
        ArtStocknoEntity artStocknoEntity = artStocknoService.findById(wmBillDetailEntity.getArtId(), stockNo);
        if (artStocknoEntity.getCellPrice() == null || artStocknoEntity.getCellPrice().compareTo(BigDecimal.ZERO) == 0) {
            ArticleEntity articleEntity = articleService.getById(artStocknoEntity.getArtId());
            BigDecimal cellPrice = ArticleUtil.packPriceToCellPrice(artStocknoEntity.getPackPrice(), articleEntity.getPackCells());
            artStocknoEntity.setCellPrice(cellPrice);
        }
        this.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                .eq(WmBillDetailEntity::getWbSeqid, wbSeqid)
                .eq(WmBillDetailEntity::getLineNo, lineNo)
                .set(WmBillDetailEntity::getStockNo, stockNo)
                .set(WmBillDetailEntity::getPackPrice, artStocknoEntity.getPackPrice())
                .set(WmBillDetailEntity::getCellPrice, artStocknoEntity.getCellPrice())
                .set(WmBillDetailEntity::getAmount, NumberUtil.mul(wmBillDetailEntity.getTotalPacks(), artStocknoEntity.getPackPrice()).add(NumberUtil.mul(wmBillDetailEntity.getTotalCells(), artStocknoEntity.getCellPrice())))
        );
    }


    @Override
    public PageUtils queryRecipePageByVisitId(Long visitId, Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.eq("t_recipe.Visit_ID", visitId);
        Integer finishedFlag = Convert.toInt(params.get("finishedFlag"));

        IPage<WmBillDetailDto> page;
        Map<String, Object> extra = new HashMap<>();
        if (finishedFlag != null && finishedFlag.equals(1)) {
            wrapper.eq("t_wm_req.status", WmReqStatus.OUT_OF_STOCK.getValue());
            page = this.baseMapper.queryRecipeDtoPageByVisitId(new Query<WmBillDetailDto>().getPage(params), wrapper);
            BigDecimal sumAmount = this.baseMapper.queryRecipeSumAmountByVisitId(wrapper);
            extra.put("sumAmount", sumAmount);
        } else {
            page = this.baseMapper.queryRecipePendingDtoPageByVisitId(new Query<WmBillDetailDto>().getPage(params), wrapper);
            BigDecimal sumAmount = this.baseMapper.queryRecipePendingSumAmountByVisitId(wrapper);
            extra.put("sumAmount", sumAmount);
        }

        PageUtils pageUtils = new PageUtils(page, extra);
        return pageUtils;
    }

    @Override
    public WmBillDetailEntity findById(Long wbSeqid, Integer lineNo) {
        return getOne(new LambdaQueryWrapper<WmBillDetailEntity>().eq(WmBillDetailEntity::getWbSeqid, wbSeqid).eq(WmBillDetailEntity::getLineNo, lineNo));
    }

    @Override
    public Long getArtIdFromDetailId(Long wbSeqid, Integer lineNo) {
        Long artId = null;
        WmBillDetailEntity wmBillDetail = findById(wbSeqid, lineNo);
        if (wmBillDetail != null) {
            artId = wmBillDetail.getArtId();
        }
        return artId;
    }

    @Override
    public PageUtils querySectionDeliveredDetailPage(Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);

        List<Long> wbSeqIdList = Convert.toList(Long.class, params.get("wbSeqids"));
        // 去除首位和中间的空数字
        wbSeqIdList = wbSeqIdList.stream()
                .filter(Objects::nonNull) // 过滤掉 null
                .filter(value -> value != 0) // 过滤掉 0
                .collect(Collectors.toList());
        if (wbSeqIdList.isEmpty()) {
            return new PageUtils<>();
        }
        wrapper.in("t_wm_bill_detail.WB_SeqID", wbSeqIdList);
        wrapper.orderByAsc("t_wm_bill_detail.Art_ID,t_wm_bill_detail.Batch_No");
        //由于存在一个出库单有多个批次的情况，多个批次对应一个申请单，所以先明细，再分组算和
        IPage<WmBillDetailDto> page = this.baseMapper.querySectionDeliveredDetailPage(new Query<WmBillDetailDto>().getPage(params), wrapper);

        Boolean isSummary = Convert.toBool(params.get("isSummary"), false);
        //如点击汇总行，则按品种进行汇总
        if (isSummary) {
            Map<Long, List<WmBillDetailDto>> map = Optional.ofNullable(page.getRecords())
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .filter(Objects::nonNull) // 过滤掉 null 的 DTO
                    .collect(Collectors.groupingBy(WmBillDetailDto::getArtId, LinkedHashMap::new, Collectors.toList()));

            // 2. 遍历 map，计算 shortCells、cellsDelivered、cellsReserved 的总和，并生成新的 List
            Map<String, Boolean> processedCombinations = new HashMap<>(); // 用于记录已处理的组合
            List<WmBillDetailDto> newList = new ArrayList<>(); // 用于存放计算结果

            for (Map.Entry<Long, List<WmBillDetailDto>> entry : map.entrySet()) {
                Long artId = entry.getKey();
                List<WmBillDetailDto> dtos = entry.getValue();

                BigDecimal totalShortCells = BigDecimal.ZERO;
                BigDecimal totalCellsDelivered = BigDecimal.ZERO;
                BigDecimal totalCellsReserved = BigDecimal.ZERO;
                Integer totalPacks = 0;
                BigDecimal totalCells = BigDecimal.ZERO;
                BigDecimal totalCost = BigDecimal.ZERO;
                BigDecimal totalAmount = BigDecimal.ZERO;

                for (WmBillDetailDto dto : dtos) {
                    // 检查 dto 是否为 null
                    if (dto == null) {
                        continue;
                    }

                    // 检查 WmReqid 和 WmReqDetailLineNo 是否为 null
                    String combination = Optional.ofNullable(dto.getWmReqid()).orElse(0L).toString() + "-" +
                            Optional.ofNullable(dto.getWmReqDetailLineNo()).orElse(0).toString();

                    if (!processedCombinations.containsKey(combination)) {
                        // 如果组合未处理过，累加值
                        totalShortCells = totalShortCells.add(Optional.ofNullable(dto.getShortCells()).orElse(BigDecimal.ZERO));
                        totalCellsDelivered = totalCellsDelivered.add(Optional.ofNullable(dto.getCellsDelivered()).orElse(BigDecimal.ZERO));
                        totalCellsReserved = totalCellsReserved.add(Optional.ofNullable(dto.getCellsReserved()).orElse(BigDecimal.ZERO));
                        processedCombinations.put(combination, true); // 标记为已处理
                    }

                    // 累加 totalPacks、totalCells、cost、amount
                    totalPacks += Optional.ofNullable(dto.getTotalPacks()).orElse(0);
                    totalCells = totalCells.add(Optional.ofNullable(dto.getTotalCells()).orElse(BigDecimal.ZERO));
                    totalCost = totalCost.add(Optional.ofNullable(dto.getCost()).orElse(BigDecimal.ZERO));
                    totalAmount = totalAmount.add(Optional.ofNullable(dto.getAmount()).orElse(BigDecimal.ZERO));
                }


                // 创建一个新的 DTO，设置计算结果
                WmBillDetailDto newDto = new WmBillDetailDto();
                newDto.setArtId(artId);
                newDto.setArtName(entry.getValue().get(0).getArtName());

                BigDecimal minPackPrice = BeanStreamUtils.getMinValue(dtos, WmBillDetailDto::getPackPrice, BigDecimal.ZERO);
                BigDecimal minCellPrice = BeanStreamUtils.getMinValue(dtos, WmBillDetailDto::getCellPrice, BigDecimal.ZERO);
                newDto.setPackPrice(minPackPrice);
                newDto.setCellPrice(minCellPrice);

                newDto.setArtSpec(BeanStreamUtils.getFirstNonBlankString(dtos, WmBillDetailDto::getArtSpec));
                newDto.setArtSpec(BeanStreamUtils.getFirstNonBlankString(dtos, WmBillDetailDto::getArtSpec));
                newDto.setProducer(BeanStreamUtils.getFirstNonBlankString(dtos, WmBillDetailDto::getProducer));
                newDto.setPackUnit(BeanStreamUtils.getFirstNonBlankString(dtos, WmBillDetailDto::getPackUnit));
                newDto.setCellUnit(BeanStreamUtils.getFirstNonBlankString(dtos, WmBillDetailDto::getCellUnit));


                newDto.setShortCells(totalShortCells);
                newDto.setCellsDelivered(totalCellsDelivered);
                newDto.setCellsReserved(totalCellsReserved);
                newDto.setTotalPacks(totalPacks);
                newDto.setTotalCells(totalCells);
                newDto.setCost(totalCost);
                newDto.setAmount(totalAmount);

                // 将新的 DTO 添加到新列表中
                newList.add(newDto);
            }

            // 3. 重新设置 page 的分页属性
            int newTotal = newList.size(); // 总记录数
            long newSize = page.getSize();  // 每页大小（保持不变）
            long newPages = (newTotal + newSize - 1) / newSize; // 总页数
            long newCurrent = Math.min(page.getCurrent(), newPages); // 当前页码（不能超过总页数）

            // 更新 page 的分页属性
            page.setRecords(newList); // 设置新的记录列表
            page.setTotal(newTotal);  // 设置总记录数
            page.setPages(newPages);  // 设置总页数
            page.setCurrent(newCurrent); // 设置当前页码
            page.setSize(newSize);    // 设置每页大小
        }
        return new PageUtils(page);
    }

    @Override
    public List<WmBillDetailDto> querySectionDeliveredDetailList(Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);

        List<Long> wbSeqIdList = Convert.toList(Long.class, params.get("wbSeqids"));
        // 去除首位和中间的空数字
        wbSeqIdList = wbSeqIdList.stream()
                .filter(Objects::nonNull) // 过滤掉 null
                .filter(value -> value != 0) // 过滤掉 0
                .collect(Collectors.toList());
        if (wbSeqIdList.isEmpty()) {
            return new ArrayList<>();
        }
        wrapper.in("t_wm_bill_detail.WB_SeqID", wbSeqIdList);

        List<WmBillDetailDto> list = null;
        Integer groupByArt = Convert.toInt(params.get("groupByArt"), 0);
        Integer groupByPatient = Convert.toInt(params.get("groupByPatient"), 0);
        //按品种汇总
        if (groupByArt.equals(1)) {
            wrapper.orderByAsc("t_wm_bill_detail.art_id");
            list = this.baseMapper.querySectionDeliveredDetailPageGroupByArt(wrapper);
            list = list.stream()
                    .collect(Collectors.groupingBy(
                            dto -> dto.getArtId().toString(),
                            LinkedHashMap::new, // 保持顺序
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    this::mergeArtGroupDtos
                            )
                    ))
                    .values()
                    .stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        } else if (groupByPatient.equals(1)) {
            //按患者汇总
            wrapper.groupBy("t_wm_req_detail.Visit_ID, t_wm_bill_detail.Art_ID,t_wm_bill_detail.Batch_No");
            wrapper.orderByAsc("t_wm_req_detail.Visit_ID, t_wm_bill_detail.Art_ID");
            list = this.baseMapper.querySectionDeliveredDetailPageGroupByPatient(wrapper);
            // 使用 VisitId 和 ArtId 组合作为键
            list = list.stream()
                    .collect(Collectors.groupingBy(
                            dto -> dto.getVisitId() + "-" + dto.getArtId(),
                            LinkedHashMap::new,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    this::mergePatientGroupDtos
                            )
                    ))
                    .values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        } else {
            wrapper.orderByAsc("t_wm_bill_detail.Line_No");
            list = this.baseMapper.querySectionDeliveredDetailPage(wrapper);
        }
        AtomicInteger lineNo = new AtomicInteger(1);
        list.forEach(dto -> {
            dto.setLineNo(lineNo.getAndIncrement());
            dto.setArtNameFull(String.join(" ",
                    dto.getArtName(), dto.getArtSpec(), dto.getProducer()));
            formatNumericFields(dto);
        });
        return list;
    }

    private void formatNumericFields(WmBillDetailDto dto) {
        // 申请数量格式化
        PackAndCellResult shortResult = countPackAndCell(dto.getShortCells(), dto.getPackCells());
        dto.setTotalAndUnit(formatPackAndCellUnit(
                shortResult.getPacks(),
                shortResult.getCells(),
                dto.getPackUnit(),
                dto.getCellUnit()
        ));

        // 发药数量计算
        BigDecimal reservedTotal = ArticleUtil.packsToCells(
                dto.getTotalPacks(),
                dto.getPackCells(),
                dto.getTotalCells()
        );
        PackAndCellResult reservedResult = countPackAndCell(reservedTotal, dto.getPackCells());
        dto.setReservedTotalAndUnit(formatPackAndCellUnit(
                reservedResult.getPacks(),
                reservedResult.getCells(),
                dto.getPackUnit(),
                dto.getCellUnit()
        ));

        // 短缺数量
        if (dto.getShortCells() != null && dto.getShortCells().compareTo(reservedTotal) > 0) {
            BigDecimal shortage = dto.getShortCells().subtract(reservedTotal);
            dto.setShortTotalAndUnit(NumberUtil.decimalFormat("#.##", shortage) + dto.getCellUnit());
        }
    }

    // 4. 合并品种分组数据
    private List<WmBillDetailDto> mergeArtGroupDtos(List<WmBillDetailDto> dtos) {
        if (dtos.size() <= 1) return dtos;

        Map<String, List<BigDecimal>> combinationMap = dtos.stream()
                .collect(Collectors.groupingBy(
                        dto -> dto.getWmReqid() + "-" + dto.getWmReqDetailLineNo(),
                        Collectors.mapping(WmBillDetailDto::getShortCells, Collectors.toList())
                ));

        BigDecimal finalShortCells = combinationMap.values().stream()
                .map(list -> list.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        WmBillDetailDto merged = dtos.get(0);
        merged.setShortCells(finalShortCells);
        merged.setCellsDelivered(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getCellsDelivered));
        merged.setCellsReserved(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getCellsReserved));
        merged.setTotalPacks(BeanStreamUtils.sumInteger(dtos, WmBillDetailDto::getTotalPacks));
        merged.setTotalCells(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getTotalCells));
        merged.setCost(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getCost));
        merged.setAmount(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getAmount));

        return Collections.singletonList(merged);
    }

    private List<WmBillDetailDto> mergePatientGroupDtos(List<WmBillDetailDto> dtos) {
        if (dtos.size() == 1) return dtos;

        WmBillDetailDto merged = dtos.get(0);
        // shortCells取最大值
        merged.setShortCells(dtos.stream()
                .map(WmBillDetailDto::getShortCells)
                .filter(Objects::nonNull)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO));

        // 其他字段求和
        merged.setCellsDelivered(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getCellsDelivered));
        merged.setTotalPacks(BeanStreamUtils.sumInteger(dtos, WmBillDetailDto::getTotalPacks));
        merged.setTotalCells(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getTotalCells));
        merged.setCost(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getCost));
        merged.setAmount(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getAmount));

        return Collections.singletonList(merged);
    }

    /**
     * 把总数量换算成整包数+拆零数
     *
     * @param totalCount 总的数量
     * @param packCells  包装制剂数，用于计算整包、拆零的单位数
     * @return
     */
    @Override
    public PackAndCellResult countPackAndCell(BigDecimal totalCount, Integer packCells) {
        // 处理 shortCells，如果为 null 或 <= 0，则赋值为 BigDecimal.ZERO
        BigDecimal validTotalCount = Optional.ofNullable(totalCount)
                .filter(s -> s.compareTo(BigDecimal.ZERO) > 0)
                .orElse(BigDecimal.ZERO);

        // 处理 packCells，如果为 null 或 <= 0，则赋值为 0
        int validPackCells = Optional.ofNullable(packCells)
                .filter(p -> p > 0)
                .orElse(0);

        // 如果申请数量为 0 或包装单位制剂数为 0，直接返回拆零数的形式
        if (validTotalCount.compareTo(BigDecimal.ZERO) == 0 || validPackCells == 0) {
            return new PackAndCellResult(0, validTotalCount);
        }

        // 计算整包数和拆零数
        if (validTotalCount.compareTo(BigDecimal.valueOf(validPackCells)) >= 0) {
            // 计算整包数
            int packs = validTotalCount.divide(BigDecimal.valueOf(validPackCells), 0, RoundingMode.DOWN).intValue();
            // 计算拆零数：validShortCells - (packs * validPackCells)
            BigDecimal cells = validTotalCount.subtract(BigDecimal.valueOf(packs * validPackCells));
            return new PackAndCellResult(packs, cells);
        } else {
            // 总数量小于包装单位制剂数，整包数为 0，总数量直接记为拆零数
            return new PackAndCellResult(0, validTotalCount);
        }
    }

    /**
     * 格式化整包数和拆零数
     * 整包数、拆零数 转化为带单位的形式
     *
     * @param totalPacks 整包数
     * @param totalCells 拆零数
     * @param packUnit   整包单位
     * @param cellUnit   拆零单位
     * @return 格式化后的字符串
     */
    @Override
    public String formatPackAndCellUnit(int totalPacks, BigDecimal totalCells, String packUnit, String cellUnit) {
        // 格式化拆零数
        String formattedShortReqCells = NumberUtil.decimalFormat("#.##", totalCells);

        // 根据整包数和拆零数生成显示内容
        if (totalPacks == 0 && totalCells.compareTo(BigDecimal.ZERO) == 0) {
            // 如果两者都为 0，显示 0 + cellUnit
            return "0" + cellUnit;
        } else if (totalPacks == 0) {
            // 如果整包数为 0，只显示拆零数 + cellUnit
            return formattedShortReqCells + cellUnit;
        } else if (totalCells.compareTo(BigDecimal.ZERO) == 0) {
            // 如果拆零数为 0，只显示整包数 + packUnit
            return totalPacks + packUnit;
        } else {
            // 否则，显示整包数 + packUnit + 拆零数 + cellUnit
            return totalPacks + packUnit + formattedShortReqCells + cellUnit;
        }
    }

    @Override
    public PageUtils querySectionDeliveredDetailPageBySection(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        Integer sectionId = Convert.toInt(params.get("sectionId"));
        wrapper.eq("t_wm_bill.Section_ID", sectionId);

        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        wrapper.eq("t_wm_bill.dept_code", deptCode);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        billWrapperFilterSectionBillType(wrapper);

        wrapper.groupBy("t_wm_bill_detail.Art_ID, t_art_stockno.Batch_No");
        IPage<WmBillDetailDto> page = this.baseMapper.querySectionDeliveredDetailPageBySection(new Query<WmBillDetailDto>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    @Override
    public List<WmBillDetailDto> querySectionDeliveredDetailListBySection(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        Integer sectionId = Convert.toInt(params.get("sectionId"));
        wrapper.eq("t_wm_bill.Section_ID", sectionId);

        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        wrapper.eq("t_wm_bill.dept_code", deptCode);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        billWrapperFilterSectionBillType(wrapper);

        wrapper.groupBy("t_wm_bill_detail.Art_ID, t_art_stockno.Batch_No");
        List<WmBillDetailDto> list = this.baseMapper.querySectionDeliveredDetailPageBySection(wrapper);


        QueryWrapper<WmBillDetailDto> reqWrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        reqWrapper.eq("t_wm_bill.Section_ID", sectionId);
        reqWrapper.eq("t_wm_bill.org_id", orgId);
        reqWrapper.eq("t_wm_bill.dept_code", deptCode);
        reqWrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        billWrapperFilterSectionBillType(reqWrapper);
        List<WmBillDetailDto> reqList = this.baseMapper.querySectionDeliveredReqDetailBySection(reqWrapper);
        int lineNo = 1;
        for (WmBillDetailDto dto : list) {
            dto.setLineNo(lineNo++);
            dto.setArtNameFull(dto.getArtName() + " " + dto.getArtSpec() + " " + dto.getProducer());
            Optional<WmBillDetailDto> reqDtoOptional = reqList.stream().filter(item -> item.getArtId().equals(dto.getArtId())).findFirst();
            if (reqDtoOptional.isPresent()) {
                WmBillDetailDto reqDto = reqDtoOptional.get();
                dto.setShortCells(reqDto.getShortCells());
            } else {
                dto.setShortCells(BigDecimal.ZERO);
            }
            dto.setTotalAndUnit(NumberUtil.decimalFormat("#.##", dto.getShortCells()) + dto.getCellUnit()); // 申请数量
            BigDecimal reservedTotal = ArticleUtil.packsToCells(dto.getTotalPacks(), dto.getPackCells(), dto.getTotalCells());
            dto.setReservedTotalAndUnit(NumberUtil.decimalFormat("#.##", reservedTotal) + dto.getCellUnit()); // 发药数量
            if (dto.getShortCells().compareTo(reservedTotal) > 0) {
                dto.setShortTotalAndUnit(NumberUtil.decimalFormat("#.##", dto.getShortCells().subtract(reservedTotal)) + dto.getCellUnit()); // 申请短缺
            }
        }
        return list;
    }

    @Override
    public PageUtils querySectionDeliveredDetailPageByPatient(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.org_id", orgId);
        Integer sectionId = Convert.toInt(params.get("sectionId"));
        wrapper.eq("t_wm_bill.Section_ID", sectionId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        wrapper.eq("t_wm_bill.dept_code", deptCode);

        Long visitId = Convert.toLong(params.get("visitId"));
        wrapper.eq("t_wm_req_detail.Visit_ID", visitId);

        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        billWrapperFilterSectionBillType(wrapper);

        wrapper.groupBy("t_wm_bill_detail.Art_ID, t_art_stockno.Batch_No");
        IPage<WmBillDetailDto> page = this.baseMapper.querySectionDeliveredDetailPageByPatient(new Query<WmBillDetailDto>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    @Override
    public List<WmBillDetailDto> querySectionDeliveredDetailListByPatient(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        wrapper.eq("t_wm_bill.dept_code", deptCode);
        Integer sectionId = Convert.toInt(params.get("sectionId"));
        wrapper.eq("t_wm_bill.Section_ID", sectionId);

        Long visitId = Convert.toLong(params.get("visitId"));
        wrapper.eq("t_wm_req_detail.Visit_ID", visitId);

        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        billWrapperFilterSectionBillType(wrapper);

        wrapper.groupBy("t_wm_bill_detail.Art_ID, t_art_stockno.Batch_No");
        List<WmBillDetailDto> list = this.baseMapper.querySectionDeliveredDetailPageByPatient(wrapper);


        QueryWrapper<WmBillDetailDto> reqWrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        reqWrapper.eq("t_wm_bill.org_id", orgId);
        reqWrapper.eq("t_wm_bill.dept_code", deptCode);
        reqWrapper.eq("t_wm_bill.Section_ID", sectionId);
        reqWrapper.eq("t_wm_req_detail.Visit_ID", visitId);
        reqWrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        billWrapperFilterSectionBillType(reqWrapper);
        List<WmBillDetailDto> reqList = this.baseMapper.querySectionDeliveredReqDetailBySection(reqWrapper);
        int lineNo = 1;
        for (WmBillDetailDto dto : list) {
            dto.setLineNo(lineNo++);
            dto.setArtNameFull(dto.getArtName() + " " + dto.getArtSpec() + " " + dto.getProducer());
            Optional<WmBillDetailDto> reqDtoOptional = reqList.stream().filter(item -> item.getArtId().equals(dto.getArtId())).findFirst();
            if (reqDtoOptional.isPresent()) {
                WmBillDetailDto reqDto = reqDtoOptional.get();
                dto.setShortCells(reqDto.getShortCells());
            } else {
                dto.setShortCells(BigDecimal.ZERO);
            }
            dto.setTotalAndUnit(NumberUtil.decimalFormat("#.##", dto.getShortCells()) + dto.getCellUnit()); // 申请数量
            BigDecimal reservedTotal = ArticleUtil.packsToCells(dto.getTotalPacks(), dto.getPackCells(), dto.getTotalCells());
            dto.setReservedTotalAndUnit(NumberUtil.decimalFormat("#.##", reservedTotal) + dto.getCellUnit()); // 发药数量
            if (dto.getShortCells().compareTo(reservedTotal) > 0) {
                dto.setShortTotalAndUnit(NumberUtil.decimalFormat("#.##", dto.getShortCells().subtract(reservedTotal)) + dto.getCellUnit()); // 申请短缺
            }
        }
        return list;
    }

    private static void billWrapperFilterSectionBillType(QueryWrapper<WmBillDetailDto> wrapper) {
        wrapper.eq("t_wm_bill.WMBill_Type_ID", WmBillType.SALE_OUT.getValue());
        wrapper.in("t_wm_bill.Bsn_Type", WmBillBsnType.sectionOutLs);
    }

    /**
     * 病区-待退药-药品明细
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils pendingDetailByWbSeqidsPage(Map<String, Object> params) {
        List<Long> wbSeqIdList = Convert.toList(Long.class, params.get("wbSeqids"));
        // 去除首位和中间的空数字
        wbSeqIdList = wbSeqIdList.stream()
                .filter(Objects::nonNull) // 过滤掉 null
                .filter(value -> value != 0) // 过滤掉 0
                .collect(Collectors.toList());
        if (wbSeqIdList.isEmpty()) {
            throw new SaveFailureException("参数错误，出入库单流水号不能为空");
        }
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.in("t_wm_bill_detail.WB_SeqID", wbSeqIdList);
        wrapper.orderByAsc("t_wm_bill_detail.art_id");
        IPage<WmBillDetailDto> page = this.baseMapper.queryDtoPage(new Query<WmBillDetailDto>().getPage(params), wrapper);

        Boolean isSummary = Convert.toBool(params.get("isSummary"), false);
        //如点击汇总行，则按品种进行汇总
        if (isSummary) {
            Map<Long, List<WmBillDetailDto>> map = Optional.ofNullable(page.getRecords())
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .filter(Objects::nonNull) // 过滤掉 null 的 DTO
                    .collect(Collectors.groupingBy(WmBillDetailDto::getArtId, LinkedHashMap::new, Collectors.toList()));

            // 2. 遍历 map
            Map<String, Boolean> processedCombinations = new HashMap<>(); // 用于记录已处理的组合
            List<WmBillDetailDto> newList = new ArrayList<>(); // 用于存放计算结果

            for (Map.Entry<Long, List<WmBillDetailDto>> entry : map.entrySet()) {
                Long artId = entry.getKey();
                List<WmBillDetailDto> dtos = entry.getValue();

                Integer totalPacks = 0;
                BigDecimal totalCells = BigDecimal.ZERO;
                BigDecimal totalCost = BigDecimal.ZERO;
                BigDecimal totalAmount = BigDecimal.ZERO;

                for (WmBillDetailDto dto : dtos) {
                    // 检查 dto 是否为 null
                    if (dto == null) {
                        continue;
                    }

                    String combination = Optional.ofNullable(dto.getWbSeqid()).orElse(0L).toString() + "-" +
                            Optional.ofNullable(dto.getArtId()).orElse(0L).toString()
                            + "-" + Optional.ofNullable(dto.getBatchNo());


                    if (!processedCombinations.containsKey(combination)) {
                        processedCombinations.put(combination, true); // 标记为已处理
                    }

                    // 累加 totalPacks、totalCells、cost、amount
                    totalPacks += Optional.ofNullable(dto.getTotalPacks()).orElse(0);
                    totalCells = totalCells.add(Optional.ofNullable(dto.getTotalCells()).orElse(BigDecimal.ZERO));
                    totalCost = totalCost.add(Optional.ofNullable(dto.getCost()).orElse(BigDecimal.ZERO));
                    totalAmount = totalAmount.add(Optional.ofNullable(dto.getAmount()).orElse(BigDecimal.ZERO));
                }


                // 创建一个新的 DTO，设置计算结果
                WmBillDetailDto newDto = new WmBillDetailDto();
                newDto.setArtId(artId);
                newDto.setArtName(entry.getValue().get(0).getArtName());

                BigDecimal minPackPrice = BeanStreamUtils.getMinValue(dtos, WmBillDetailDto::getPackPrice, BigDecimal.ZERO);
                BigDecimal minCellPrice = BeanStreamUtils.getMinValue(dtos, WmBillDetailDto::getCellPrice, BigDecimal.ZERO);
                newDto.setPackPrice(minPackPrice);
                newDto.setCellPrice(minCellPrice);

                newDto.setArtSpec(BeanStreamUtils.getFirstNonBlankString(dtos, WmBillDetailDto::getArtSpec));
                newDto.setArtSpec(BeanStreamUtils.getFirstNonBlankString(dtos, WmBillDetailDto::getArtSpec));
                newDto.setProducer(BeanStreamUtils.getFirstNonBlankString(dtos, WmBillDetailDto::getProducer));
                newDto.setPackUnit(BeanStreamUtils.getFirstNonBlankString(dtos, WmBillDetailDto::getPackUnit));
                newDto.setCellUnit(BeanStreamUtils.getFirstNonBlankString(dtos, WmBillDetailDto::getCellUnit));

                newDto.setTotalPacks(totalPacks);
                newDto.setTotalCells(totalCells);
                newDto.setCost(totalCost);
                newDto.setAmount(totalAmount);

                // 将新的 DTO 添加到新列表中
                newList.add(newDto);
            }

            // 3. 重新设置 page 的分页属性
            int newTotal = newList.size(); // 总记录数
            long newSize = page.getSize();  // 每页大小（保持不变）
            long newPages = (newTotal + newSize - 1) / newSize; // 总页数
            long newCurrent = Math.min(page.getCurrent(), newPages); // 当前页码（不能超过总页数）

            // 更新 page 的分页属性
            page.setRecords(newList); // 设置新的记录列表
            page.setTotal(newTotal);  // 设置总记录数
            page.setPages(newPages);  // 设置总页数
            page.setCurrent(newCurrent); // 设置当前页码
            page.setSize(newSize);    // 设置每页大小
        }

        Map<String, Object> extra = new HashMap<>();
        BigDecimal sumAmount = this.baseMapper.queryDtoPageSumAmount(wrapper);
        extra.put("sumAmount", sumAmount);
        return new PageUtils(page, extra);
    }

    /**
     * 病区-已退药-退药记录/按病区汇总-药品明细
     *
     * @param params
     * @return
     */
    @Override
    public List<WmBillDetailDto> returnDetailByWbSeqidsPage(Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);

        List<Long> wbSeqIdList = Convert.toList(Long.class, params.get("wbSeqids"));
        // 去除首位和中间的空数字
        wbSeqIdList = wbSeqIdList.stream()
                .filter(Objects::nonNull) // 过滤掉 null
                .filter(value -> value != 0) // 过滤掉 0
                .collect(Collectors.toList());
        if (wbSeqIdList.isEmpty()) {
            return new ArrayList<>();
        }
        wrapper.in("t_wm_bill_detail.WB_SeqID", wbSeqIdList);
        wrapper.orderByAsc("t_wm_bill_detail.art_id");

        List<WmBillDetailDto> list = null;
        Integer groupByArt = Convert.toInt(params.get("groupByArt"), 0);
        //按品种汇总
        if (groupByArt.equals(1)) {
            list = this.baseMapper.querySectionDeliveredDetailPageGroupByArt(wrapper);
            list = list.stream()
                    .collect(Collectors.groupingBy(
                            dto -> dto.getArtId(),
                            LinkedHashMap::new, // 保持顺序
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    this::mergeReturnArtGroupDtos
                            )
                    ))
                    .values()
                    .stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        }
        AtomicInteger lineNo = new AtomicInteger(1);
        list.forEach(dto -> {
            dto.setLineNo(lineNo.getAndIncrement());
            dto.setArtNameFull(String.join(" ",
                    dto.getArtName(), dto.getArtSpec(), dto.getProducer()));
            formatNumericFields(dto);
        });
        return list;
    }

    /**
     * 现在的逻辑门诊在签名预占时不产生pending，需要支付才产生Pending。以后调整完才能按pending过滤，会涉及发药页面status=4才允许发药（需要看发药逻辑status=4需验证）
     *
     * @return
     */
    @Override
    public List<WmBillDetailDto> findReservedLs(Long orgId, String deptCode, List<Long> artIds) {
        QueryWrapper<WmBillDetailDto> wrapper = new QueryWrapper<>();
        wrapper.eq("t_wm_bill.Org_ID", orgId);
        wrapper.eq("t_wm_bill.Dept_Code", deptCode);
        wrapper.in("t_wm_bill_detail.Art_ID", artIds);
        wrapper.and(orWrapper -> {
            orWrapper.and(andWrapper -> {
                andWrapper.in("t_wm_bill.Bsn_Type", ListUtil.of(WmBillBsnType.SALE_OUT.getValue(), WmBillBsnType.INVENTORY_TRANSFER.getValue()))
                        .in("t_wm_bill.Status", ListUtil.of(WmBillStatus.RECEIVED.getValue(), WmBillStatus.DISPATCHING.getValue(), WmBillStatus.WAIT_RECEIVE_MEDICINE.getValue()));
            }).or(andWrapper -> {
                andWrapper.eq("t_wm_bill.Bsn_Type", WmBillBsnType.INVENTORY_LOSS.getValue())
                        .eq("t_wm_bill.Status", WmBillStatus.WAIT_RECEIVE.getValue())
                        .in("t_wm_bill.WMBill_Type_ID", WmBillType.storeOutLs);
                // 历史数据的报损单用了12入库冲红，暂时补上。以后的报损单用出库52
            });
        });
        return baseMapper.findPendingLsByWrapper(wrapper);
    }

    /**
     * 此reset只根据detail表art_id、stock_no关联art_stockno的包装、拆零单价同步到detail表，然后重算成本、售价
     * 次方法只适合重置调拨、损溢业务，不产生批次加成/额定售价等销售策略
     * 次方法只适合重置调拨、损溢业务，不产生批次加成/额定售价等销售策略
     * 次方法只适合重置调拨、损溢业务，不产生批次加成/额定售价等销售策略
     *
     * @param wbSeqId
     */
    @Override
    @Transactional
    public void resetPriceCostAmount(Long wbSeqId) {
        baseMapper.resetPriceCostAmount(wbSeqId);
    }

    @Override
    public void resetBillDetailExpiry(Long wbSeqId) {
        baseMapper.resetBillDetailExpiry(wbSeqId);
    }

    private List<WmBillDetailDto> mergeReturnArtGroupDtos(List<WmBillDetailDto> dtos) {
        if (dtos.size() <= 1) return dtos;

        WmBillDetailDto merged = dtos.get(0);
        merged.setTotalPacks(BeanStreamUtils.sumInteger(dtos, WmBillDetailDto::getTotalPacks));
        merged.setTotalCells(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getTotalCells));
        merged.setCost(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getCost));
        merged.setAmount(BeanStreamUtils.sumBigDecimal(dtos, WmBillDetailDto::getAmount));

        return Collections.singletonList(merged);
    }
}
