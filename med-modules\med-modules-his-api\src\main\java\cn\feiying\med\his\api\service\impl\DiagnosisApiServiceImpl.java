package cn.feiying.med.his.api.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.enums.ClinicType;
import cn.feiying.med.hip.enums.DiagStatus;
import cn.feiying.med.hip.mdi.entity.ClinicianEntity;
import cn.feiying.med.hip.mdi.service.ClinicianService;
import cn.feiying.med.his.api.model.req.diagnosis.DiagnosisModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.DiagnosisRespModel;
import cn.feiying.med.his.api.service.DiagnosisApiService;
import cn.feiying.med.microhis.hsd.entity.VisitEntity;
import cn.feiying.med.microhis.hsd.service.VisitService;
import cn.feiying.med.saas.api.service.RemoteHsdService;
import cn.feiying.med.saas.api.service.RemoteInpatientHsdService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 诊断相关服务实现类
 */
@Slf4j
@Service
public class DiagnosisApiServiceImpl implements DiagnosisApiService {

    @Resource
    private VisitService visitService;
    @Resource
    private ClinicianService clinicianService;
    @Resource
    private RemoteHsdService remoteHsdService;
    @Resource
    private RemoteInpatientHsdService remoteInpatientHsdService;

    @Override
    public ApiResultModel<List<DiagnosisRespModel>> saveOrUpdateDiagnosis(List<DiagnosisModel> diagnosisList) {
        log.info("诊断回写开始处理, 诊断回写数量: {}", diagnosisList != null ? diagnosisList.size() : 0);
        try {
            List<DiagnosisRespModel> resultList = new ArrayList<>();
            // 实现诊断回写逻辑
            if (ObjectUtils.isNotEmpty(diagnosisList)) {
                Set<Long> visitIdLs = diagnosisList.stream().map(DiagnosisModel::getVisitId).collect(Collectors.toSet());
                List<VisitEntity> visitLs = visitService.listByIds(new ArrayList<>(visitIdLs));
                if (ObjectUtils.isNotEmpty(visitLs)) {
                    for (DiagnosisModel diagnosis : diagnosisList) {
                        VisitEntity visit = visitLs.stream().filter(v -> v.getVisitId().equals(diagnosis.getVisitId())).findFirst().orElse(null);
                        if (visit != null) {
                            List<DiagnosisModel.DiagItem> diagItemLs = diagnosis.getDiagList();
                            if (ObjectUtils.isNotEmpty(diagItemLs)) {
                                Map<String, Object> diagMap = buildDiag(visit, diagItemLs);
                                JSONArray jsonArray;
                                if (visit.getClinicTypeId().equals(ClinicType.inpatient.getValue())) { // 住院
                                    jsonArray = remoteInpatientHsdService.saveVisitDiag(diagMap);
                                } else { // 门诊
                                    jsonArray = remoteHsdService.saveVisitDiag(diagMap);
                                }
                                buildRespDiag(diagnosis.getVisitId(), jsonArray, resultList);
                            }
                        } else {
                            log.warn("未找到对应的诊疗流水号: {}", diagnosis.getVisitId());
                        }
                    }
                }
            }
            // 返回成功的诊断信息
            return ApiResultModel.success(resultList);
        } catch (Exception e) {
            log.error("诊断回写处理异常", e);
            return ApiResultModel.error("诊断回写失败：" + e.getMessage());
        }
    }

    @Override
    public ApiResultModel<?> deleteDiagnosis(List<DiagnosisModel> diagnosisList) {
        log.info("诊断删除开始处理, 删除请求数量: {}", diagnosisList != null ? diagnosisList.size() : 0);
        
        try {
            // 参数验证
            if (diagnosisList == null || ObjectUtil.isEmpty(diagnosisList)) {
                return ApiResultModel.error("诊断删除请求列表不能为空");
            }
            
            JSONArray jsonArr = new JSONArray();
            for (DiagnosisModel diagnosis : diagnosisList) {
                // 验证visitId
                if (diagnosis.getVisitId() == null) {
                    throw new SaveFailureException("visitId不能为空");
                }
                
                // 验证visitDiagLs
                if (ObjectUtil.isEmpty(diagnosis.getDiagList())) {
                    throw new SaveFailureException("visitId[" + diagnosis.getVisitId() + "]的诊断删除列表不能为空; ");
                }

                // 处理每个诊断删除项
                for (DiagnosisModel.DiagItem diagItem : diagnosis.getDiagList()) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.set("visitId", diagnosis.getVisitId());
                    jsonObject.set("diagCode", diagItem.getDiagCode());
                    jsonArr.set(jsonObject);
                }
            }
            Map<String, Object> diagMap = new HashMap<>();
            diagMap.put("visitDiagLs", jsonArr);
            remoteHsdService.delVisitDiag(diagMap);
            return ApiResultModel.success();
        } catch (Exception e) {
            log.error("诊断删除处理异常", e);
            return ApiResultModel.error("诊断删除失败：" + e.getMessage());
        }
    }

    /**
     * 构建返回的诊断信息
     */
    private void buildRespDiag(Long visitId, JSONArray jsonArray, List<DiagnosisRespModel> resultList) {
        if (jsonArray != null && !jsonArray.isEmpty()) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonItem = jsonArray.getJSONObject(i);
                DiagnosisRespModel resultMap = new DiagnosisRespModel();
                resultMap.setVisitId(visitId);
                resultMap.setDiagCode(jsonItem.getStr("diagCode"));
                resultMap.setDiagNo(jsonItem.getInt("diagNo"));
                resultList.add(resultMap);
            }
        }
    }

    /**
     * 构建诊断信息
     * @param visit      诊疗信息
     * @param diagItemLs 诊断列表
     */
    private Map<String, Object> buildDiag(VisitEntity visit, List<DiagnosisModel.DiagItem> diagItemLs) {
        Map<String, Object> diagMap = new HashMap<>();
        // 诊疗ID
        diagMap.put("visitId", visit.getVisitId());
        // 诊断医生
        diagMap.put("clinicianId", visit.getClinicianId());
        // 诊断科室编码
        diagMap.put("deptCode", visit.getDeptCode());
        // 诊断列表
        List<Map<String, Object>> diagLs = new ArrayList<>();
        List<String> clinicianNoLs = diagItemLs.stream().map(DiagnosisModel.DiagItem::getClinicianCode).filter(Objects::nonNull).collect(Collectors.toList());
        List<ClinicianEntity> clinicianLs = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(clinicianNoLs)) {
            clinicianLs = clinicianService.list(Wrappers.lambdaQuery(ClinicianEntity.class).in(ClinicianEntity::getClinicianNo, clinicianNoLs));
        }
        for (DiagnosisModel.DiagItem diagItem : diagItemLs) {
            Map<String, Object> diagItemMap = new HashMap<>();
            // 诊疗ID
            diagItemMap.put("visitId", visit.getVisitId());
            // 诊断编码
            diagItemMap.put("diagCode", diagItem.getDiagCode());
            // 诊断名称
            diagItemMap.put("diagName", diagItem.getDiagName());
            // 诊断类型
            diagItemMap.put("diagTypeId", diagItem.getDiagType());
            // 诊断医生
            if (StrUtil.isNotBlank(diagItem.getClinicianCode())) {
                ClinicianEntity clinician = clinicianLs.stream().filter(c -> c.getClinicianNo().equals(diagItem.getClinicianCode())).findFirst().orElse(null);
                if (clinician!= null) {
                    diagItemMap.put("clinicianId", clinician.getClinicianId());
                } else {
                    diagItemMap.put("clinicianId", visit.getClinicianId());
                }
            } else {
                diagItemMap.put("clinicianId", visit.getClinicianId());
            }
            // 诊断状态
            diagItemMap.put("diagStatus", DiagStatus.confirmed.getValue());
            // 诊断级别代号
            diagItemMap.put("diagLevelId", 3);
            // 诊断类别代号
            diagItemMap.put("diagCatId", diagItem.getDiagCatId());
            // 诊断科室
            diagItemMap.put("deptCode", visit.getDeptCode());
            // 诊断时间
            diagItemMap.put("timeConfirmed", DateUtil.parseDateTime(diagItem.getDiagDate()));
            diagLs.add(diagItemMap);
        }
        diagMap.put("visitDiagLs", diagLs);
        return diagMap;
    }
} 