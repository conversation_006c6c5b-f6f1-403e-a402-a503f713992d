package cn.feiying.med.his.api.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.his.api.model.req.apply.*;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.ApplyRespModel;
import cn.feiying.med.his.api.service.ApplyApiService;
import cn.feiying.med.saas.api.service.RemoteHsdService;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 申请单服务接口实现类
 */
@Slf4j
@Service
public class ApplyApiServiceImpl implements ApplyApiService {

    @Resource
    private ClinicianService clinicianService;
    @Resource
    private SpecimenTypeService specimenTypeService;
    @Resource
    private MtBodypartService mtBodypartService;
    @Resource
    private OrderTypeService orderTypeService;
    @Resource
    private UserCodeService userCodeService;
    @Resource
    private RemoteHsdService remoteHsdService;

    @Override
    public ApiResultModel<ApplyRespModel> saveOrUpdateApply(ApplySaveReqModel applyReq) {
        log.info("申请单保存及修改开始处理");

        ApiResultModel<ApplyRespModel> result = new ApiResultModel<>();
        
        try {
            // 实现申请单保存及修改逻辑
            List<ApplyRespModel.ApplyItem> applyItemList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(applyReq.getApplyList())) {
                // 根据申请单号查询已存在的申请单
                List<String> accessionNoLs = applyReq.getApplyList().stream().map(ApplyModel::getAccessionNo).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                JSONArray orderArr = findOrderLs(accessionNoLs);
                // 如果已存在的申请单先做校验
                for (ApplyModel apply : applyReq.getApplyList()) {
                    validateApply(orderArr, apply.getAccessionNo());    // 校验申请单信息
                }
                // 先调用撤销订单签名
                withdrawSignedOrder(orderArr);
                // 再调用保存申请单调用保存订单
                JSONArray saveOrderArr = toJSONObject(orderArr, applyReq.getApplyList());
                Map<String, Object> orderMap = new HashMap<>();
                orderMap.put("orderLs", saveOrderArr);
                JSONArray saveOrderLs = remoteHsdService.saveOrder(orderMap);
                for (JSONObject orderJson : saveOrderLs.toList(JSONObject.class)) {
                    ApplyRespModel.ApplyItem applyItem = new ApplyRespModel.ApplyItem();
                    applyItem.setAccessionNo(orderJson.getStr("accessionNo"));
                    applyItem.setOrderId(orderJson.getLong("orderId"));
                    applyItemList.add(applyItem);
                    // 申请单签名
//                    remoteHsdService.signOrder(orderJson.getLong("orgId"), orderJson.getLong("clinicianId"),
//                            null,null, Collections.singletonList(orderJson.getLong("orderId")));
                }
            } else {
                throw new SaveFailureException("申请单信息列表不能为空");
            }
            ApplyRespModel responseModel = new ApplyRespModel();
            responseModel.setVisitId(applyReq.getVisitId());
            responseModel.setApplyList(applyItemList);
            result.setCode(0);
            result.setMsg("操作成功");
            result.setData(responseModel);
        } catch (Exception e) {
            log.error("申请单保存及修改处理异常", e);
            result.setCode(-1);
            result.setMsg("操作失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 校验申请单信息
     */
    private void validateApply(JSONArray orderArr, String accessionNo) {
        if (ObjectUtil.isNotEmpty(orderArr)) {
            for (int i = 0; i < orderArr.size(); i++) {
                JSONObject orderJson = orderArr.getJSONObject(i);
                if (StrUtil.equals(accessionNo, orderJson.getStr("accessionNo"))) {
                   Integer paidStatus = orderJson.getInt("paidStatus");
                   Integer execStatus = orderJson.getInt("execStatus");
                   if (paidStatus.equals(PaidStatus.paid.getValue())) {
                       throw new SaveFailureException("申请单【" + accessionNo + "】已支付，不能修改");
                   }
                   if (execStatus.equals(ExecStatus.cancel.getValue())) {
                       throw new SaveFailureException("申请单【" + accessionNo + "】已作废，不能修改");
                   }
                   if (execStatus.equals(ExecStatus.finish.getValue())) {
                       throw new SaveFailureException("申请单【" + accessionNo + "】已完成，不能修改");
                   }
                }
            }
        }
    }

    /**
     * 撤销已签名的订单
     */
    private void withdrawSignedOrder(JSONArray orderArr) {
        if (ObjectUtil.isNotEmpty(orderArr)) {
            List<Long> orderIdLs = getOrderIdLs(orderArr);
            if (ObjectUtil.isNotEmpty(orderIdLs)) {
                remoteHsdService.withdrawSignedOrder(orderIdLs);
            }
        }
    }

    /**
     * 将ApplyModel列表直接转换为OrderVo参数列表
     * @param applyModels 申请单模型列表
     * @return 医嘱对象JSON数组
     */
    public JSONArray toJSONObject(JSONArray orderArr, List<ApplyModel> applyModels) {
        JSONArray orderArray = new JSONArray();

        if (ObjectUtil.isNotEmpty(applyModels)) {
            // 医师信息查询
            List<String> clinicianNoLs = applyModels.stream()
                    .map(ApplyModel::getClinicianCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            List<ClinicianEntity> clinicianLs = new ArrayList<>();
            if (!clinicianNoLs.isEmpty()) {
                clinicianLs = clinicianService.list(Wrappers.lambdaQuery(ClinicianEntity.class).in(ClinicianEntity::getClinicianNo, clinicianNoLs));
            }
            // 标本类型信息查询
            List<String> specimenCodeLs = applyModels.stream()
                   .flatMap(apply -> apply.getApplyDetailList().stream())
                   .map(ApplyDetailModel::getSpecimenTypeCode)
                   .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<SpecimenTypeEntity> specimenTypeLs = new ArrayList<>();
            if (!specimenCodeLs.isEmpty()) {
                specimenTypeLs = specimenTypeService.list(Wrappers.lambdaQuery(SpecimenTypeEntity.class).in(SpecimenTypeEntity::getSpecimenTypeCode, specimenCodeLs));
            }
            // 申请单类型
            List<String> orderTypeCodeLs = applyModels.stream()
                    .map(ApplyModel::getOrderTypeCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            List<OrderTypeEntity> orderTypeLs = new ArrayList<>();
            if (!orderTypeCodeLs.isEmpty()) {
                orderTypeLs = orderTypeService.list(Wrappers.lambdaQuery(OrderTypeEntity.class).in(OrderTypeEntity::getTypeCode, orderTypeCodeLs));
            }
            for (ApplyModel applyModel : applyModels) {
                JSONObject orderJson = convertToOrderParams(applyModel, clinicianLs, specimenTypeLs, orderTypeLs);
                Long orderId = getOrderId(orderArr, applyModel.getAccessionNo());
                if (orderId != null) {
                    orderJson.put("orderId", orderId);
                }
                Long recipeId = getRecipeId(orderArr, applyModel.getAccessionNo());
                if (recipeId != null) {
                    orderJson.put("recipeId", recipeId);
                }
                orderArray.set(orderJson);
            }
        }

        return orderArray;
    }

    /**
     * 将ApplyModel转换为OrderVo参数
     * @param applyModel 申请单模型
     * @return 医嘱对象JSON
     */
    public JSONObject convertToOrderParams(ApplyModel applyModel, List<ClinicianEntity> clinicianLs, List<SpecimenTypeEntity> specimenTypeLs, List<OrderTypeEntity> orderTypeLs) {
        JSONObject orderJson = new JSONObject();
        
        // 设置基本信息（诊疗ID在ApplyModel中有）
        orderJson.set("visitId", applyModel.getVisitId());
        
        // 设置科室及医师信息
        orderJson.set("applyDeptcode", applyModel.getApplyDeptcode());
        orderJson.set("exceDeptcode", applyModel.getExceDeptcode());
        
        // 设置医师ID
        if (ObjectUtil.isNotEmpty(clinicianLs)) {
            clinicianLs.stream()
                    .filter(c -> c.getClinicianNo().equals(applyModel.getClinicianCode()))
                    .findFirst().ifPresent(clinician -> orderJson.set("clinicianId", clinician.getClinicianId()));

        }
        // 处方分类
        orderJson.set("recipeCatId", RecipeCat.commonRecipe.getValue());
        // 设置申请类型ID
        Integer orderTypeId = null;
        if (ObjectUtil.isNotEmpty(orderTypeLs)) {
            OrderTypeEntity orderType = orderTypeLs.stream().filter(a -> a.getTypeCode().equals(applyModel.getOrderTypeCode())).findFirst().orElse(null);
            if (orderType != null) {
                orderTypeId = orderType.getOrderTypeId();
                orderJson.set("orderTypeId", orderTypeId);
                Integer mtTypeId = orderType.getMtTypeId();
                List<Integer> jyMtTypeIdLs = ListUtil.of(MtType.inspect.getValue());
                List<Integer> jcMtTypeIdLs = ListUtil.of(MtType.radiate.getValue(), MtType.ultrasonic.getValue(), MtType.endoscope.getValue(), MtType.pathology.getValue(), MtType.electrophysiology.getValue(), MtType.kemedical.getValue());
                if (jyMtTypeIdLs.contains(mtTypeId)) {
                    orderJson.set("recipeTypeId", RecipeType.sys.getValue());
                } else if (jcMtTypeIdLs.contains(mtTypeId)) {
                    orderJson.set("recipeTypeId", RecipeType.yj.getValue());
                }
            }
        }
        // 设置医疗信息
        orderJson.set("medicalHistory", applyModel.getMedicalHistory());
        orderJson.set("purposeDesc", applyModel.getPurposeDesc());
        orderJson.set("isSecret", 0);
        // 创建申请组
        JSONArray recipeGroupArray = new JSONArray();
        
        // 如果有明细列表，则根据明细创建组
        if (applyModel.getApplyDetailList() != null && !applyModel.getApplyDetailList().isEmpty()) {
            JSONObject groupJson = new JSONObject();
            groupJson.set("groupNo", 1); // 默认组号
            groupJson.set("orderTypeId", orderTypeId);
            
            // 检查部位编码
            List<String> bodypartCodeLs = new ArrayList<>();
            for (ApplyDetailModel detail : applyModel.getApplyDetailList()) {
                if (StrUtil.isNotBlank(detail.getBodypartCode())) {
                    // 可能有多个部位，用逗号分隔
                    String[] bodyCodes = detail.getBodypartCode().split(",");
                    for (String code : bodyCodes) {
                        if (StrUtil.isNotBlank(code.trim()) && !bodypartCodeLs.contains(code.trim())) {
                            bodypartCodeLs.add(code.trim());
                        }
                    }
                }
            }
            List<MtBodypartEntity> bodypartLs = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(bodypartCodeLs)) {
                bodypartLs = mtBodypartService.list(Wrappers.lambdaQuery(MtBodypartEntity.class)
                        .in(MtBodypartEntity::getBodypartCode, bodypartCodeLs));
            }
            
            // 创建明细列表
            JSONArray recipeDetailArray = new JSONArray();
            for (int i = 0; i < applyModel.getApplyDetailList().size(); i++) {
                ApplyDetailModel detailModel = applyModel.getApplyDetailList().get(i);
                JSONObject detailJson = new JSONObject();
                
                // 设置明细信息
                detailJson.set("lineNo", i + 1);  // 行号从1开始
                detailJson.set("artId", detailModel.getArtCode());
                detailJson.set("artName", detailModel.getArtName());
                detailJson.set("total", detailModel.getTotal());
                detailJson.set("unit", detailModel.getUnit());
                detailJson.set("unitType", detailModel.getUnitType());
                detailJson.set("remarks", detailModel.getRemarks());
                
                // 设置标本类型ID
                if (StrUtil.isNotBlank(detailModel.getSpecimenTypeCode())) {
                    specimenTypeLs.stream()
                            .filter(s -> s.getSpecimenTypeCode().equals(detailModel.getSpecimenTypeCode()))
                            .findFirst()
                            .ifPresent(s -> detailJson.set("specimenTypeId", s.getSpecimenTypeId()));
                    
                    // 同时保留名称
                    detailJson.set("specimenTypeName", detailModel.getSpecimenTypeName());
                }
                
                // 设置检查部位信息 (RecipeDetailPos)
                if (StrUtil.isNotBlank(detailModel.getBodypartCode())) {
                    String[] bodyCodes = detailModel.getBodypartCode().split(",");
                    String[] bodyNames = detailModel.getBodypartName() != null ? 
                            detailModel.getBodypartName().split(",") : new String[0];
                    
                    JSONArray detailPosArray = new JSONArray();
                    for (int j = 0; j < bodyCodes.length; j++) {
                        String code = bodyCodes[j].trim();
                        if (StrUtil.isNotBlank(code)) {
                            JSONObject posJson = new JSONObject();
                            // 设置身体部位ID
                            bodypartLs.stream()
                                    .filter(b -> b.getBodypartCode().equals(code))
                                    .findFirst()
                                    .ifPresent(b -> {
                                        posJson.set("bodypartId", b.getBodypartId());
                                        posJson.set("bodypartName", b.getBodypartName());
                                    });
                            
                            detailPosArray.set(posJson);
                        }
                    }
                    
                    if (!detailPosArray.isEmpty()) {
                        detailJson.set("recipeDetailPosLs", detailPosArray);
                    }
                }
                
                recipeDetailArray.set(detailJson);
            }
            
            groupJson.set("recipeDetailList", recipeDetailArray);
            recipeGroupArray.set(groupJson);
        } else {
            // 没有明细，创建一个空组
            JSONObject groupJson = new JSONObject();
            groupJson.set("groupNo", 1);
            recipeGroupArray.set(groupJson);
        }
        
        orderJson.set("recipeGroupLs", recipeGroupArray);
        
        return orderJson;
    }

    @Override
    public ApiResultModel<?> deleteApply(List<ApplyCancelModel> applyLs) {
        log.info("申请单删除开始处理, 数量: {}", applyLs != null ? applyLs.size() : 0);

        ApiResultModel<Object> result = new ApiResultModel<>();
        
        try {
            // 实现申请单删除逻辑
            if (ObjectUtil.isNotEmpty(applyLs)) {
                List<String> accessionNoLs = applyLs.stream().map(ApplyCancelModel::getAccessionNo).distinct().collect(Collectors.toList());
                JSONArray orderLs = findOrderLs(accessionNoLs);
                List<Long> orderIdLs = getOrderIdLs(orderLs);
                if (ObjectUtil.isNotEmpty(orderIdLs)) {
                    remoteHsdService.delOrder(null, orderIdLs);
                }
            } else {
                throw new SaveFailureException("申请单删除失败：未找到有效的申请单");
            }
            result.setCode(0);
            result.setMsg("操作成功");
        } catch (Exception e) {
            log.error("申请单删除处理异常", e);
            result.setCode(-1);
            result.setMsg("操作失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public ApiResultModel<?> cancelApply(List<ApplyCancelModel> applyLs) {
        log.info("申请单作废开始处理, 数量: {}", applyLs != null ? applyLs.size() : 0);

        ApiResultModel<Object> result = new ApiResultModel<>();
        
        try {
            // 实现申请单作废逻辑
            if (ObjectUtil.isNotEmpty(applyLs)) {
                List<String> accessionNoLs = applyLs.stream().map(ApplyCancelModel::getAccessionNo).distinct().collect(Collectors.toList());
                JSONArray orderLs = findOrderLs(accessionNoLs);
                List<Long> orderIdLs = getOrderIdLs(orderLs);
                if (ObjectUtil.isNotEmpty(orderIdLs)) {
                    List<String> userCodeLs = applyLs.stream().map(ApplyCancelModel::getUserCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                    Long userId = null;
                    if (ObjectUtil.isNotEmpty(userCodeLs)) {
                        List<UserCodeEntity> userCodeList = userCodeService.list(Wrappers.<UserCodeEntity>lambdaQuery().in(UserCodeEntity::getUserCode, userCodeLs));
                        userId = ObjectUtil.isNotEmpty(userCodeList) ? userCodeList.get(0).getUserId() : null;  // 取第一个用户ID
                    }
                    remoteHsdService.cancelOrder(userId, orderIdLs);
                }
            } else {
                throw new SaveFailureException("申请单作废失败：未找到有效的申请单");
            }
            result.setCode(0);
            result.setMsg("操作成功");
        } catch (Exception e) {
            log.error("申请单作废处理异常", e);
            result.setCode(-1);
            result.setMsg("操作失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public ApiResultModel<?> updateApplyStatus(List<ApplyStatusUpdateModel> applyLs) {
        log.info("申请单状态回写开始处理, 数量: {}", applyLs != null ? applyLs.size() : 0);

        ApiResultModel<Object> result = new ApiResultModel<>();
        
        try {
            // 实现申请单状态回写逻辑
            if (ObjectUtil.isNotEmpty(applyLs)) {
                List<String> accessionNoLs = applyLs.stream().map(ApplyStatusUpdateModel::getAccessionNo).distinct().collect(Collectors.toList());
                // 根据类型分组
                Map<Integer, List<ApplyStatusUpdateModel>> applyTypeMap = applyLs.stream().collect(Collectors.groupingBy(ApplyStatusUpdateModel::getClinicType));
                JSONArray orderLs = findOrderLs(accessionNoLs);
                for (Map.Entry<Integer, List<ApplyStatusUpdateModel>> entry : applyTypeMap.entrySet()) {
                    JSONArray updateOrderLs = new JSONArray();
                    for (ApplyStatusUpdateModel apply : entry.getValue()) {
                        Long orderId = getOrderId(orderLs, apply.getAccessionNo());
                        if (orderId != null) {
                            JSONObject updateOrder = new JSONObject();
                            updateOrder.set("visitId", apply.getVisitId());
                            updateOrder.set("orderId", orderId);
                            updateOrder.set("status", apply.getStatus());
                            updateOrder.set("pacsList", apply.getPacsList());
                            updateOrder.set("lisResultList", apply.getLisResultList());
                            updateOrderLs.set(updateOrder);
                        }
                    }
                    Map<String, Object> updateOrderMap = new HashMap<>();
                    updateOrderMap.put("orderLs", updateOrderLs);
                    remoteHsdService.updateOrderStatus(entry.getKey(), updateOrderMap);
                }
            } else {
                throw new SaveFailureException("申请单状态回写失败：未找到有效的申请单");
            }
            result.setCode(0);
            result.setMsg("操作成功");
        } catch (Exception e) {
            log.error("申请单状态回写处理异常", e);
            result.setCode(-1);
            result.setMsg("操作失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 根据申请单号列表查询订单列表
     */
    private JSONArray findOrderLs(List<String> accessionNoLs) {
        // 实际实现逻辑
        return remoteHsdService.findOrderByAccessionNo(accessionNoLs);
    }

    private List<Long> getOrderIdLs(JSONArray orderLs) {
        List<Long> orderIdLs = new ArrayList<>();
        if (orderLs != null && !orderLs.isEmpty()) {
            for (int i = 0; i < orderLs.size(); i++) {
                JSONObject jsonItem = orderLs.getJSONObject(i);
                Long orderId = jsonItem.getLong("orderId");
                if (orderId != null && !orderIdLs.contains(orderId)) {
                    orderIdLs.add(orderId);
                }
            }
        }
        return orderIdLs;
    }

    private Long getOrderId(JSONArray orderLs, String accessionNo) {
        Long orderId = null;
        if (orderLs != null && !orderLs.isEmpty()) {
            for (int i = 0; i < orderLs.size(); i++) {
                JSONObject jsonItem = orderLs.getJSONObject(i);
                String accessionNoItem = jsonItem.getStr("accessionNo");
                if (StrUtil.equals(accessionNo, accessionNoItem)) {
                    orderId = jsonItem.getLong("orderId");
                    break;
                }
            }
        }
        return orderId;
    }

    private Long getRecipeId(JSONArray orderLs, String accessionNo) {
        Long recipeId = null;
        if (orderLs!= null &&!orderLs.isEmpty()) {
            for (int i = 0; i < orderLs.size(); i++) {
                JSONObject jsonItem = orderLs.getJSONObject(i);
                String accessionNoItem = jsonItem.getStr("accessionNo");
                if (StrUtil.equals(accessionNo, accessionNoItem)) {
                    recipeId = jsonItem.getLong("recipeId");
                    break;
                }
            }
        }
        return recipeId;
    }
} 