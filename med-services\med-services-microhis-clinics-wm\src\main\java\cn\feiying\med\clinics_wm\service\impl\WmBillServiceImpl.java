package cn.feiying.med.clinics_wm.service.impl;

import cn.feiying.med.clinics_wm.dao.WmBillDao;
import cn.feiying.med.clinics_wm.dto.*;
import cn.feiying.med.clinics_wm.entity.*;
import cn.feiying.med.clinics_wm.enums.*;
import cn.feiying.med.clinics_wm.model.*;
import cn.feiying.med.clinics_wm.service.*;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.*;
import cn.feiying.med.hip.mdi.entity.ArticleEntity;
import cn.feiying.med.hip.mdi.entity.OrgSettingEntity;
import cn.feiying.med.hip.mdi.service.ArticleService;
import cn.feiying.med.hip.mdi.service.OrgSettingService;
import cn.feiying.med.hip.mdi.service.SysSequenceService;
import cn.feiying.med.saas.api.service.RemoteInpatientHsdService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 出入库单表
 *
 * <AUTHOR> 18:48:02
 */
@Slf4j
@Service("wmBillService")
public class WmBillServiceImpl extends ServiceImpl<WmBillDao, WmBillEntity> implements WmBillService {
    @Resource
    private SysSequenceService sysSequenceService;
    @Resource
    private WmBillDetailService wmBillDetailService;
    @Resource
    private WmBillPendingService wmBillPendingService;
    @Resource
    private WmBillTrackcodeService wmBillTrackcodeService;
    @Resource
    private OrgArtService orgArtService;
    @Resource
    private OrgStockService orgStockService;
    @Resource
    private OrgSettingService orgSettingService;
    @Resource
    private ScmCustService scmCustService;
    @Resource
    private DeptArtService deptArtService;
    @Resource
    private DeptStockService deptStockService;
    @Resource
    private DeptCustMapService deptCustMapService;
    @Resource
    private ArticleService articleService;
    @Resource
    private OrgCustMapService orgCustMapService;
    @Resource
    private ArtStocknoService artStocknoService;
    @Resource
    private SectionArtService sectionArtService;
    @Resource
    private SectionConsumeService sectionConsumeService;
    @Resource
    private RemoteInpatientHsdService remoteInpatientHsdService;
    @Resource
    private WmReqService wmReqService;
    @Resource
    private WmBillTypeServiceImpl wmBillTypeService;

    @Override
    public WmBillDto findDtoById(Long wbSeqid) {
        return baseMapper.findDtoById(wbSeqid);
    }

    @Override
    public PageUtils queryPage(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDto> wrapper = new GQueryWrapper<WmBillDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        if (StrUtil.isNotBlank(deptCode)) {
            wrapper.eq("t_wm_bill.dept_code", deptCode);
        }
        Long artId = Convert.toLong(params.get("artId"));
        if (artId != null) {
            wrapper.exists("select 1 from microhis_clinics_wm.t_wm_bill_detail where t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID and t_wm_bill_detail.art_id = {0}", artId);
        }
        Integer artCatTypeId = Convert.toInt(params.get("artCatTypeId"));
        if (artCatTypeId != null) {
            wrapper.exists("select t_wm_bill_detail.WB_SeqID\n" +
                    "             from microhis_clinics_wm.t_wm_bill_detail\n" +
                    "                      inner join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.art_id\n" +
                    "             where t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID\n" +
                    "               and t_article.Cat_Type_ID = {0}", artCatTypeId);
        }
        Integer artSubTypeId = Convert.toInt(params.get("artSubTypeId"));
        if (artSubTypeId != null) {
            wrapper.exists("select t_wm_bill_detail.WB_SeqID\n" +
                    "             from microhis_clinics_wm.t_wm_bill_detail\n" +
                    "                      inner join hip_mdi.t_article on t_wm_bill_detail.Art_ID = t_article.art_id\n" +
                    "             where t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID\n" +
                    "               and t_article.Subtype_ID = {0}", artSubTypeId);
        }

        String custName = Convert.toStr(params.get("Cust_Name"), StrUtil.EMPTY).trim();
        if (StrUtil.isNotBlank(custName)) {
            wrapper.and(i -> i.or().like("t_scm_cust.Cust_Name", custName).or()
                    .like("Upper(t_scm_cust.QS_Code1)", custName.toUpperCase()).or().like("Upper(t_scm_cust.QS_Code2)", custName.toUpperCase()));
        }
        String sectionName = Convert.toStr(params.get("Section_Name"), StrUtil.EMPTY).trim();
        if (StrUtil.isNotBlank(sectionName)) {
            wrapper.and(i -> i.like("UPPER(t_section.Section_Name)", sectionName));
        }
        String bsnAbstract = Convert.toStr(params.get("bsnAbstract"), StrUtil.EMPTY).trim();
        if (StrUtil.isNotBlank(bsnAbstract)) {
            wrapper.and(i -> i.like("t_wm_bill.Notes", bsnAbstract).or().like("t_wm_bill.Bsn_Abstract", bsnAbstract));
        }
        List<Integer> bsnTypes = Convert.toList(Integer.class, params.get("bsnTypes"));
        if (ObjectUtil.isNotEmpty(bsnTypes)) {
            wrapper.in("t_wm_bill.Bsn_Type", bsnTypes);
        }
        List<Integer> wmbillTypeIds = Convert.toList(Integer.class, params.get("wmbillTypeIds"));
        if (ObjectUtil.isNotEmpty(wmbillTypeIds)) {
            wrapper.in("t_wm_bill.WMBill_Type_ID", wmbillTypeIds);
        }

        Integer custId =  Convert.toInt(params.get("custId"));
        if (custId != null) {
            wrapper.eq("t_wm_bill.Cust_ID", custId);
        }

        Integer pendingFlag = Convert.toInt(params.get("pendingFlag"));
        IPage<WmBillDto> page;
        Map<String, Object> sumMap = null;
        if (pendingFlag != null && pendingFlag.equals(1)) {
            page = this.baseMapper.queryPendingDtoPage(new Query<WmBillDto>().getPage(params), wrapper);
            wrapper.ne("t_wm_bill.Status", -1);
            sumMap = baseMapper.queryPendingDtoSum(wrapper);
        } else {
            page = this.baseMapper.queryDtoPage(new Query<WmBillDto>().getPage(params), wrapper);
            wrapper.ne("t_wm_bill.Status", -1);
            sumMap = baseMapper.queryDtoSum(wrapper);
        }
        if (sumMap == null) {
            sumMap = new HashMap<>();
            sumMap.put("Amount", 0);
            sumMap.put("Cost", 0);
        }
//        IPage<WmBillDto> page = this.baseMapper.queryDtoPage(new Query<WmBillDto>().getPage(params), wrapper);
        for (WmBillDto dto : page.getRecords()) {
            dto.setStatusName(WmBillStatus.getName(dto.getStatus()));
            dto.setBsnTypeName(WmBillBsnType.getName(dto.getBsnType()));
            if (dto.getTrackcodeStatus() == null) {
                dto.setTrackcodeStatus(WmTrackCodeStatus.NOT_COLLECTED.getValue());
            }
            dto.setTrackcodeStatusName(WmTrackCodeStatus.getName(dto.getTrackcodeStatus()));
        }

        return new PageUtils(page, sumMap);
    }

    @Override
    public PageUtils<WmBillDto> queryReservedPage(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDto> wrapper = new GQueryWrapper<WmBillDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.Org_ID", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        if (StrUtil.isNotBlank(deptCode)) {
            wrapper.eq("t_wm_bill.Dept_Code", deptCode);
        }
        wrapper.and(orWrapper -> {
            orWrapper.and(andWrapper -> {
                andWrapper.in("t_wm_bill.Bsn_Type", ListUtil.of(WmBillBsnType.SALE_OUT.getValue(), WmBillBsnType.INVENTORY_TRANSFER.getValue()))
                        .in("t_wm_bill.Status", ListUtil.of(WmBillStatus.RECEIVED.getValue(), WmBillStatus.DISPATCHING.getValue(), WmBillStatus.WAIT_RECEIVE_MEDICINE.getValue()));
            }).or(andWrapper -> {
                andWrapper.eq("t_wm_bill.Bsn_Type", WmBillBsnType.INVENTORY_LOSS.getValue())
                        .eq("t_wm_bill.Status", WmBillStatus.WAIT_RECEIVE.getValue())
                        .in("t_wm_bill.WMBill_Type_ID", WmBillType.storeOutLs);
                // 历史数据的报损单用了12入库冲红，暂时补上。以后的报损单用出库52
            });
        });
        Long artId = Convert.toLong(params.get("artId"));
        if (artId != null) {
            wrapper.exists("select t_wm_bill_detail.WB_SeqID\n" +
                    "             from microhis_clinics_wm.t_wm_bill_pending\n" +
                    "                      inner join microhis_clinics_wm.t_wm_bill_detail\n" +
                    "                                 on t_wm_bill_pending.WB_SeqID = t_wm_bill_detail.WB_SeqID\n" +
                    "             where t_wm_bill_detail.Art_ID = {0}\n" +
                    "               and t_wm_bill.WB_SeqID = t_wm_bill_detail.WB_SeqID", artId);
        }
        IPage<WmBillDto> page = this.baseMapper.queryPendingPage(new Query<WmBillDto>().getPage(params), wrapper);
        return new PageUtils<>(page);
    }

    @Override
    public void saveEntity(WmBillEntity entity) {
        entity.setWbSeqid(sysSequenceService.getLongNextValue(WmBillEntity.class.getSimpleName()));
        if (entity.getTimeCreated() == null) {
            entity.setTimeCreated(new Date());
        }
        if (entity.getTrackcodeStatus() == null) {
            entity.setTrackcodeStatus(WmTrackCodeStatus.NOT_COLLECTED.getValue());
        }
        this.save(entity);
    }

    @Override
    @Transactional
    public Long saveBill(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details) {
        return saveBillInService(orgId, userId, wmBill, details);
    }

    private Long saveBillInService(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details) {
        if (wmBill.getWbSeqid() != null) {
            // 已经存在，走更新逻辑
            updateBill(orgId, userId, wmBill, details);
            return wmBill.getWbSeqid();
        }
        if (wmBill.getBsnType() == null) {
            throw new SaveFailureException("请选择业务类型。");
        }
        if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue())) {
            wmBill.setRecvDeptCode(null);
        }
        if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue()) && StrUtil.isBlank(wmBill.getRecvDeptCode())) {
            throw new SaveFailureException("直调仓库不能为空。");
        }
        if (wmBill.getWmbillTypeId() == null) {
            throw new SaveFailureException("请选择单据类型。");
        }
        if (getDeptStockEnabled(orgId)) {
            if (StrUtil.isBlank(wmBill.getDeptCode())) {
                throw new SaveFailureException("请选择科室");
            }
        } else {
            if (StrUtil.isNotBlank(wmBill.getDeptCode())) {
                throw new SaveFailureException("机构未开启库存管理");
            }
        }
        if (StrUtil.isBlank(wmBill.getBsnAbstract())) {
            ScmCustEntity scmCust = null;
            if (wmBill.getCustId() != null) {
                scmCust = scmCustService.getById(wmBill.getCustId());
            }
            wmBill.setBsnAbstract(StrUtil.sub(WmBillType.getName(wmBill.getWmbillTypeId()) + " " + (scmCust == null ? StrUtil.EMPTY : scmCust.getCustName()), 0, 60));
        }
        Integer bsnTypeId = wmBillTypeService.getBsnTypeByBillTypeId(wmBill.getWmbillTypeId());
        wmBill.setBsnTypeId(bsnTypeId);
        wmBill.setStatus(WmBillStatus.UNSUBMITTED.getValue());
        wmBill.setOrgId(orgId);
        wmBill.setCreatorUid(userId);
        this.saveEntity(wmBill);
        saveDetail(wmBill, details);

        wmBillPendingService.saveIfNull(wmBill.getWbSeqid());
        return wmBill.getWbSeqid();
    }


    /**
     * 采购冲红-新增主表、明细表记录
     *
     * @param orgId
     * @param userId
     * @param wmBill  冲红单主表信息
     * @param details 冲红单明细表信息
     * @return
     */
    @Override
    @Transactional
    public void inStoreSaveBill(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details) {
        if (wmBill.getWbSeqid() != null) {
            //之前是走更新逻辑，暂时此处直接报错
            throw new SaveFailureException("该冲红单已经存在");
        }
        if (wmBill.getBsnType() == null) {
            throw new SaveFailureException("请选择业务类型。");
        }
        if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue())) {
            wmBill.setRecvDeptCode(null);
        }
        //暂时屏蔽掉，因为采购直调-只做了调拨回来，再冲红
//        if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue()) && StrUtil.isBlank(wmBill.getRecvDeptCode())) {
//            throw new SaveFailureException("直调仓库不能为空。");
//        }
        if (wmBill.getWmbillTypeId() == null) {
            throw new SaveFailureException("请选择单据类型。");
        }
        if (getDeptStockEnabled(orgId)) {
            if (StrUtil.isBlank(wmBill.getDeptCode())) {
                throw new SaveFailureException("请选择科室");
            }
        } else {
            if (StrUtil.isNotBlank(wmBill.getDeptCode())) {
                throw new SaveFailureException("机构未开启库存管理");
            }
        }
        wmBill.setStatus(WmBillStatus.UNSUBMITTED.getValue());
        wmBill.setOrgId(orgId);
        wmBill.setCreatorUid(userId);
        //新增冲红单bill表
        this.saveEntity(wmBill);
        inStoreSaveDetail(wmBill, details);
    }

    private void updateBill(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details) {
        WmBillEntity bill = getById(wmBill.getWbSeqid());
        if (bill == null) {
            throw new SaveFailureException("未找到该单据。");
        }
        if (!bill.getStatus().equals(WmBillStatus.UNSUBMITTED.getValue())) {
            throw new SaveFailureException("不是未提交状态，不能进行修改。");
        }
        if (!bill.getOrgId().equals(orgId)) {
            throw new SaveFailureException("不是本机构单据，不能进行修改。");
        }
        if (!bill.getCreatorUid().equals(userId)) {
            throw new SaveFailureException("不是本人单据，不能进行修改。");
        }
        boolean result = this.update(new LambdaUpdateWrapper<WmBillEntity>()
                .eq(WmBillEntity::getWbSeqid, wmBill.getWbSeqid())
                .eq(WmBillEntity::getStatus, WmBillStatus.UNSUBMITTED.getValue())
                .set(WmBillEntity::getBsnAbstract, wmBill.getBsnAbstract())
                .set(WmBillEntity::getNotes, wmBill.getNotes())
                .set(WmBillEntity::getWmBillId, wmBill.getWmBillId())
                .set(WmBillEntity::getCustId, wmBill.getCustId())
                .set(WmBillEntity::getAcctDate, wmBill.getAcctDate())
                .set(WmBillEntity::getRecipeId, wmBill.getRecipeId())
                .set(WmBillEntity::getTimes, wmBill.getTimes())
                .set(WmBillEntity::getDeptCode, wmBill.getDeptCode())
        );
        if (!result) {
            throw new SaveFailureException("修改失败，请检查数据。");
        }
        // 20250512 历史数据中出现了还没调教的单据已扫溯源码
        List<WmBillTrackcodeEntity> wmBillTrackCodeLs = wmBillTrackcodeService.list(new LambdaQueryWrapper<WmBillTrackcodeEntity>().eq(WmBillTrackcodeEntity::getWbSeqid, wmBill.getWbSeqid()));
        if (ObjectUtil.isNotEmpty(wmBillTrackCodeLs)) {
            wmBillTrackcodeService.remove(new LambdaQueryWrapper<WmBillTrackcodeEntity>().eq(WmBillTrackcodeEntity::getWbSeqid, wmBill.getWbSeqid()));
        }
        wmBillDetailService.remove(new LambdaQueryWrapper<WmBillDetailEntity>().eq(WmBillDetailEntity::getWbSeqid, wmBill.getWbSeqid()));
        saveDetail(wmBill, details);
        if (ObjectUtil.isNotEmpty(wmBillTrackCodeLs)) {
            List<WmBillTrackcodeEntity> newWmBillTrackCodeLs = wmBillTrackCodeLs.stream().filter(t ->
                    details.stream().anyMatch(d -> d.getWbSeqid().equals(t.getWbSeqid()) && d.getLineNo().equals(t.getLineNo()) && d.getArtId().equals(t.getArtId()))).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(newWmBillTrackCodeLs)) {
                wmBillTrackcodeService.saveBatch(newWmBillTrackCodeLs);
            }
        }
    }

    @Override
    public boolean getDeptStockEnabled(long orgId) {
        OrgSettingEntity orgSettingEntity = orgSettingService.getById(orgId);
        return orgSettingEntity != null && orgSettingEntity.getDeptStockEnabled() != null && orgSettingEntity.getDeptStockEnabled().equals(1);
    }

    @Override
    public boolean getIsCostConsumeTrue(long orgId) {
        OrgSettingEntity orgSettingEntity = orgSettingService.getById(orgId);
        return orgSettingEntity == null || orgSettingEntity.getIsCostConsume() == null || orgSettingEntity.getIsCostConsume().equals(1);
    }

    private void saveDetail(WmBillEntity wmBill, List<WmBillDetailEntity> details) {
        if (details == null || details.isEmpty()) {
            throw new SaveFailureException("单据明细不能为空。");
        }
        List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        for (WmBillDetailEntity detail : details) {
            if (detail.getArtId() == null) {
                throw new SaveFailureException("有未匹配到商品编码对应的商品信息");
            }
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("未找到该药品。");
            }
            if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.SALE_OUT.getValue())) {
                if (StrUtil.isBlank(detail.getBatchNo())) {
                    throw new SaveFailureException(detail.getArtId() + "批号不能为空");
                }
                // 采购入库单，有效期不能为空
                if (WmBillType.purchaseInLs.contains(wmBill.getWmbillTypeId())) {
//                if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue())) {
                    long count = details.stream().filter(p -> p.getArtId().equals(detail.getArtId()) && p.getBatchNo().equals(detail.getBatchNo())).count();
                    if (count > 1) {
                        throw new SaveFailureException(detail.getArtId() + "同一商品编码，同一批号不能重复");
                    }
//                    if (WmBillType.purchaseInLs.contains(wmBill.getWmbillTypeId())) {
                    if (detail.getExpiry() == null) {
                        throw new SaveFailureException(detail.getArtId() + "有效期不能为空");
                    }
                    try {
                        DateUtil.parse(Convert.toStr(detail.getExpiry()), "yyyyMMdd");
                    } catch (Exception e) {
                        throw new SaveFailureException(detail.getArtId() + "有效期格式错误" + Convert.toStr(detail.getExpiry()));
                    }
                    if (detail.getDateManufactured() == null) {
                        throw new SaveFailureException(detail.getArtId() + "生产日期不能为空");
                    }
                    try {
                        DateUtil.parse(Convert.toStr(detail.getDateManufactured()), "yyyyMMdd");
                    } catch (Exception e) {
                        throw new SaveFailureException(detail.getArtId() + "生产日期格式错误" + Convert.toStr(detail.getDateManufactured()));
                    }

                    if ((detail.getTotalPacks() == null || detail.getTotalPacks().equals(0))
                            && (detail.getTotalCells() == null || detail.getTotalCells().compareTo(BigDecimal.ZERO) == 0)) {
                        throw new SaveFailureException(detail.getArtId() + "整包数量与拆零数量不能同时为空");
                    }
                    if (detail.getTotalPacks() != null && detail.getTotalPacks() < 0) {
                        throw new SaveFailureException(detail.getArtId() + "整包数量不能小于0");
                    }
                    if (detail.getTotalCells() != null && detail.getTotalCells().compareTo(BigDecimal.ZERO) < 0) {
                        throw new SaveFailureException(detail.getArtId() + "拆零数量不能小于0");
                    }
//                    }
//                    if (detail.getPackPrice() == null) {
//                        throw new SaveFailureException(detail.getArtId() +"整包单价不能为空");
//                    }
//                    detail.setCellPrice(ArticleUtil.packPriceToCellPrice(detail.getPackPrice(), articleEntity.getPackCells()));
                }
                if ((detail.getTotalPacks() == null || detail.getTotalPacks().equals(0))
                        && (detail.getTotalCells() == null || detail.getTotalCells().compareTo(BigDecimal.ZERO) == 0)) {
                    throw new SaveFailureException(detail.getArtId() + "整包数量与拆零数量不能同时为空");
                }
                if (detail.getPackPrice() == null) {
                    throw new SaveFailureException(detail.getArtId() + "整包单价不能为空");
                }
//                if (detail.getPackPrice() == null || detail.getPackPrice().compareTo(BigDecimal.ZERO) == 0) {
//                    throw new SaveFailureException(detail.getArtId() + "整包单价不能为空");
//                }

                detail.setCellPrice(ArticleUtil.packPriceToCellPrice(detail.getPackPrice(), articleEntity.getPackCells()));
                BigDecimal amount = BigDecimal.ZERO;
                if (detail.getTotalPacks() != null) {
                    amount = NumberUtil.mul(detail.getTotalPacks(), detail.getPackPrice());
                }
                if (detail.getTotalCells() != null) {
                    amount = amount.add(NumberUtil.mul(detail.getTotalCells(), detail.getCellPrice()));
                }
                if (WmBillType.purchaseInLs.contains(wmBill.getWmbillTypeId())) {
                    detail.setCost(amount);
                }
                detail.setAmount(amount);
            }
            detail.setWbSeqid(wmBill.getWbSeqid());
        }

        wmBillDetailService.saveBatch(details);
//        if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.SALE_OUT.getValue())) {
//            BigDecimal amount = details.stream().map(WmBillDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            this.update(new LambdaUpdateWrapper<WmBillEntity>()
//                    .eq(WmBillEntity::getWbSeqid, wmBill.getWbSeqid())
//                    .set(WmBillEntity::getAmount, amount)
//            );
//        }
        this.updateAmount(wmBill.getWbSeqid());
    }

    /**
     * 新增冲红明细表
     *
     * @param wmBill  冲红主表
     * @param details 冲红明细表
     */
    private void inStoreSaveDetail(WmBillEntity wmBill, List<WmBillDetailEntity> details) {
        if (details == null || details.isEmpty()) {
            throw new SaveFailureException("单据明细不能为空。");
        }
        List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        for (WmBillDetailEntity detail : details) {
            if (detail.getArtId() == null) {
                throw new SaveFailureException("有未匹配到商品编码对应的商品信息");
            }
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("未找到该药品。");
            }
            if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.SALE_OUT.getValue())) {
                //需要把手动填写的冲红数量置为detail中的整包数、拆零数，便于入库新增明细记录
                detail.setTotalPacks(detail.getReturnTotalPacks());
                detail.setTotalCells(detail.getReturnTotalCells());

                if (StrUtil.isBlank(detail.getBatchNo())) {
                    throw new SaveFailureException(detail.getArtId() + "批号不能为空");
                }
                // 采购入库单，有效期不能为空
                if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue())) {
                    long count = details.stream().filter(p -> p.getArtId().equals(detail.getArtId()) && p.getBatchNo().equals(detail.getBatchNo())).count();
                    if (count > 1) {
                        throw new SaveFailureException(detail.getArtId() + "同一商品编码，同一批号不能重复");
                    }
                }
                if ((detail.getTotalPacks() == null || detail.getTotalPacks().equals(0))
                        && (detail.getTotalCells() == null || detail.getTotalCells().compareTo(BigDecimal.ZERO) == 0)) {
                    throw new SaveFailureException(detail.getArtId() + "整包数量与拆零数量不能同时为空");
                }
                if (detail.getPackPrice() == null) {
                    throw new SaveFailureException(detail.getArtId() + "整包单价不能为空");
                }
                //为了防止2者单据不一致此处直接使用原单的拆零价格,拆零价格没有时再根据整包价格计算
                if (detail.getCellPrice() == null) {
                    detail.setCellPrice(ArticleUtil.packPriceToCellPrice(detail.getPackPrice(), articleEntity.getPackCells()));
                }
                //因为冲的条目可能是部分条目，所以此处需要算一遍amount
                BigDecimal amount = BigDecimal.ZERO;
                if (detail.getTotalPacks() != null) {
                    amount = NumberUtil.mul(detail.getTotalPacks(), detail.getPackPrice());
                }
                if (detail.getTotalCells() != null) {
                    amount = amount.add(NumberUtil.mul(detail.getTotalCells(), detail.getCellPrice()));
                }
                detail.setAmount(amount);
            }
            detail.setWbSeqid(wmBill.getWbSeqid());
        }
        ;
        //新增冲红单明细表
        wmBillDetailService.saveBatch(details);
        this.updateAmount(wmBill.getWbSeqid());
    }

    /**
     * 采购冲红
     *
     * @param orgId
     * @param userId
     * @param wmBill  冲红主表信息
     * @param details 冲红明细表信息
     * @return
     */
    @Override
    @Transactional
    public void inStoreSubmitBill(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details) {
        boolean deptStockEnabled = getDeptStockEnabled(orgId);
        if (deptStockEnabled) {
            if (StrUtil.isBlank(wmBill.getDeptCode())) {
                throw new SaveFailureException("请选择科室");
            }
        }
        //冲红单bill表、billdetail表做新增
        this.inStoreSaveBill(orgId, userId, wmBill, details);
        boolean result = this.update(new LambdaUpdateWrapper<WmBillEntity>()
                .eq(WmBillEntity::getWbSeqid, wmBill.getWbSeqid())
                .eq(WmBillEntity::getStatus, WmBillStatus.UNSUBMITTED.getValue())
                .set(WmBillEntity::getStatus, WmBillStatus.WAIT_RECEIVE.getValue())
        );
        if (!result) {
            throw new SaveFailureException("修改状态失败，请检查数据。");
        }
        wmBillPendingService.saveIfNull(wmBill.getWbSeqid());
    }

    @Override
    @Transactional
    public Long submitBill(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean reserve) {
        return submitBillInService(orgId, userId, wmBill, details, reserve);
    }

    private Long submitBillInService(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean reserve) {
        boolean deptStockEnabled = getDeptStockEnabled(orgId);
        if (deptStockEnabled) {
            if (StrUtil.isBlank(wmBill.getDeptCode())) {
                throw new SaveFailureException("请选择科室");
            }
        }
        this.saveBillInService(orgId, userId, wmBill, details);
        if (reserve && wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_LOSS.getValue()) && wmBill.getWmbillTypeId().equals(WmBillType.LOSS_OUT.getValue())) {
            // 如果是报损单，需要做预留，否则数据会有问题
            List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
            List<ArticleEntity> articleEntities = articleService.listByIds(artIds);

            List<WmBillDetailEntity> reserveDetails = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();
            for (WmBillDetailEntity detail : details) {
                ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
                if (articleEntity == null) {
                    throw new SaveFailureException("未找到该药品。");
                }
                ArtStockReserveResult result = this.artStockReserve(orgId, wmBill.getDeptCode(), articleEntity, deptStockEnabled,
                        detail.getBatchNo(), null, detail.getTotalPacks(), detail.getTotalCells(), false, true, true, "");
                if (StringUtil.isNotEmpty(result.getShortageErrorMsg())) {
                    errorMessages.add(result.getShortageErrorMsg());
                    continue;
                }
                // 通过预留库存，获取批次号，价格
                reserveDetails.addAll(result.getWmBillDetailEntities());
            }
            //库存不足统一提示
            if (!errorMessages.isEmpty()) {
                throw new SaveFailureException(String.join("\n", errorMessages));
            }

            int lineNo = 1;
            for (WmBillDetailEntity wmBillDetailEntity : reserveDetails) {
                wmBillDetailEntity.setLineNo(lineNo++);
            }
            this.updateBill(orgId, userId, wmBill, reserveDetails);
        }
        boolean result = this.update(new LambdaUpdateWrapper<WmBillEntity>()
                .eq(WmBillEntity::getWbSeqid, wmBill.getWbSeqid())
                .eq(WmBillEntity::getStatus, WmBillStatus.UNSUBMITTED.getValue())
                .set(WmBillEntity::getStatus, WmBillStatus.WAIT_RECEIVE.getValue())
        );
        if (!result) {
            throw new SaveFailureException("修改状态失败，请检查数据。");
        }
        wmBillPendingService.saveIfNull(wmBill.getWbSeqid());
        return wmBill.getWbSeqid();
    }

    @Override
    @Transactional
    public void cancelBill(long orgId, long userId, Long wbSeqid) {
        WmBillEntity wmBill = getById(wbSeqid);
        if (wmBill.getStatus().equals(WmBillStatus.UNSUBMITTED.getValue())) {
            boolean result = this.update(new LambdaUpdateWrapper<WmBillEntity>()
                    .eq(WmBillEntity::getWbSeqid, wbSeqid)
                    .eq(WmBillEntity::getStatus, WmBillStatus.UNSUBMITTED.getValue())
                    .set(WmBillEntity::getStatus, WmBillStatus.CANCEL.getValue())
            );
            if (!result) {
                throw new SaveFailureException("作废失败，请检查数据。");
            }
            wmBillPendingService.removeById(wmBill.getWbSeqid());
        } else {
            throw new SaveFailureException("不是未提交状态，不能进行作废。");
        }
    }

    private void validateBillReject(long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean deptStockEnabled) {
        wmBillPendingService.removeById(wmBill.getWbSeqid());
        WmBillStatus billStatus = WmBillStatus.CANCEL; // 审核不通过时，默认为已取消
        if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue())) {
            billStatus = WmBillStatus.UNSUBMITTED;
        }
//        if (wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue()) && wmBill.getWmbillTypeId().equals(WmBillType.IN_STORE.getValue())) {
        if (wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue()) && wmBill.getWmbillTypeId().equals(WmBillType.TRANSFER_IN.getValue())) {
            // 调拔入库单审核退回，需要产生原调拨出库单的冲红单据
            if (wmBill.getRelativeWbseqid() == null) { // 历史数据没有保存这个值
                throw new SaveFailureException("调拔入库单不允许拒绝，请通过退回的方式进行处理。");
            }
            WmBillEntity relativeBill = getById(wmBill.getRelativeWbseqid());
            WmBillEntity backBill = BeanUtil.copyProperties(relativeBill, WmBillEntity.class);
            backBill.setWbSeqid(null);
            backBill.setRelativeWbseqid(relativeBill.getWbSeqid());
//          backBill.setWmbillTypeId(WmBillType.OUT_STORE_CANCEL.getValue());
            backBill.setWmbillTypeId(WmBillType.TRANSFER_OUT_CANCEL.getValue());
            backBill.setBsnTypeId(WmBusinessType.TRANSFER_OUT.getValue());
            backBill.setCreatorUid(userId);
            backBill.setTimeCreated(new Date());
            backBill.setValidatorUid(userId);
            backBill.setTimeValidated(new Date());
            backBill.setCustId(relativeBill.getCustId());
            backBill.setTransferDeptCode(wmBill.getDeptCode());
            backBill.setBsnAbstract(StrUtil.sub(WmBillType.TRANSFER_OUT_CANCEL.getName() + relativeBill.getBsnAbstract(), 0, 60));

            List<WmBillDetailEntity> backDetails = new ArrayList<>();
            for (WmBillDetailEntity detail : details) {
                WmBillDetailEntity backDetail = BeanUtil.copyProperties(detail, WmBillDetailEntity.class);
                backDetail.setWbSeqid(null);
                backDetails.add(backDetail);
            }
            submitBillInService(wmBill.getOrgId(), userId, backBill, backDetails, false);
            //调拨冲红单直接审核通过
            validateBillCore(wmBill.getOrgId(), userId, backBill, backDetails, deptStockEnabled);
//            validateBillCore(orgId, userId, wbSeqid, wmBill, details, deptStockEnabled);
//            throw new SaveFailureException("调拔入库单不允许拒绝，请通过退回的方式进行处理。");
            // 撤销申请单
            List<WmReqEntity> wmReqLs = wmReqService.list(new LambdaQueryWrapper<WmReqEntity>().eq(WmReqEntity::getWbSeqid, wmBill.getRelativeWbseqid()));
            if (ObjectUtil.isNotEmpty(wmReqLs) && wmReqLs.size() == 1) {
                WmReqEntity wmReq = wmReqLs.get(0);
                if (wmReq.getReqType().equals(WmReqType.STORE_REPLENISHMENT.getValue())) {
                    wmReqService.update(new LambdaUpdateWrapper<WmReqEntity>()
                            .eq(WmReqEntity::getWmReqid, wmReq.getWmReqid())
                            .eq(WmReqEntity::getStatus, WmReqStatus.OUT_OF_STOCK.getValue())
                            .set(WmReqEntity::getStatus, WmReqStatus.CANCELED.getValue())
                    );
                }
            }
        }
        // 退回到未提交状态时，如果是报损单，需要撤销预留
        if (wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_LOSS.getValue()) && wmBill.getWmbillTypeId().equals(WmBillType.LOSS_OUT.getValue())) {
            List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
            List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
            // 释放被锁定库存
            for (WmBillDetailEntity detail : details) {
                if (deptStockEnabled) {
                    ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
                    if (articleEntity == null) {
                        throw new SaveFailureException("未找到该药品。");
                    }
                    ArtStockChangeResult result = deptStockService.batchStockInc(wmBill.getOrgId(), wmBill.getDeptCode(), detail.getArtId(), detail.getStockNo(), detail.getTotalPacks(), detail.getTotalCells(), null);
                    deptArtService.subReservedCells(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity.getArtId(), ArticleUtil.packsToCells(detail.getTotalPacks(), articleEntity.getPackCells(), detail.getTotalCells()));
                    wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                            .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                            .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                            .set(WmBillDetailEntity::getLastTotalPacks, result.getLastTotalPacks())
                            .set(WmBillDetailEntity::getLastTotalCells, result.getLastTotalCells())
                            .set(WmBillDetailEntity::getReturnTotalPacks, detail.getTotalPacks())
                            .set(WmBillDetailEntity::getReturnTotalCells, detail.getTotalCells())
                    );
                } else {
                    orgStockService.batchStockInc(wmBill.getOrgId(), detail.getArtId(), detail.getStockNo(), detail.getBatchNo(), detail.getTotalPacks(), detail.getTotalCells(), null, null);
                }
            }
        }
        boolean result = this.update(new LambdaUpdateWrapper<WmBillEntity>()
                .eq(WmBillEntity::getWbSeqid, wmBill.getWbSeqid())
                .eq(WmBillEntity::getStatus, WmBillStatus.WAIT_RECEIVE.getValue())
                .set(WmBillEntity::getStatus, billStatus.getValue())
                .set(WmBillEntity::getValidatorUid, userId)
                .set(WmBillEntity::getTimeValidated, new Date())
        );
        if (!result) {
            throw new SaveFailureException("审核失败，请检查数据。");
        }
        if (wmBill.getSectionId() != null && wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue()) && wmBill.getWmbillTypeId().equals(WmBillType.TRANSFER_OUT_CANCEL.getValue())) {
            // 病区领用退回，需要减少病区库存
            remoteInpatientHsdService.sectionStockReturnCancel(Convert.toLong(wmBill.getWmBillId()));
        }
    }

    private void inStoreValidateBillReject(long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean deptStockEnabled) {
        WmBillStatus billStatus = WmBillStatus.CANCEL; // 审核不通过时，默认为已取消
        if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue())) {
            billStatus = WmBillStatus.UNSUBMITTED;
        }
        // 因暂时只做采购入库的冲红，所以之后此处如果抽成公共方法，可以加上判断其他单据的冲红逻辑
//        if (wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue()) && wmBill.getWmbillTypeId().equals(WmBillType.IN_STORE.getValue())) {
//            // 调拔入库单审核退回，需要产生原调拨出库单的冲红单据
//            if (wmBill.getRelativeWbseqid() == null) { // 历史数据没有保存这个值
//                throw new SaveFailureException("调拔入库单不允许拒绝，请通过退回的方式进行处理。");
//            }
//            WmBillEntity relativeBill = getById(wmBill.getRelativeWbseqid());
//            WmBillEntity backBill = BeanUtil.copyProperties(relativeBill, WmBillEntity.class);
//            backBill.setWbSeqid(null);
//            backBill.setRelativeWbseqid(relativeBill.getWbSeqid());
//            backBill.setWmbillTypeId(WmBillType.OUT_STORE_CANCEL.getValue());
//            backBill.setCreatorUid(userId);
//            backBill.setTimeCreated(new Date());
//            backBill.setValidatorUid(userId);
//            backBill.setTimeValidated(new Date());
//            backBill.setBsnAbstract("审核被退回");
//
//            List<WmBillDetailEntity> backDetails = new ArrayList<>();
//            for (WmBillDetailEntity detail : details) {
//                WmBillDetailEntity backDetail = BeanUtil.copyProperties(detail, WmBillDetailEntity.class);
//                backDetail.setWbSeqid(null);
//                backDetails.add(backDetail);
//            }
//            Long backWbSeqid = submitBill(wmBill.getOrgId(), userId, backBill, backDetails);
//            validateBill(wmBill.getOrgId(), userId, backWbSeqid, true);
////            throw new SaveFailureException("调拔入库单不允许拒绝，请通过退回的方式进行处理。");
//        }
        // 退回到未提交状态时，如果是报损单，需要撤销预留
//        if (wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_LOSS.getValue()) && wmBill.getWmbillTypeId().equals(WmBillType.IN_STORE_CANCEL.getValue())) {
//            List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
//            List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
//            // 释放被锁定库存
//            for (WmBillDetailEntity detail : details) {
//                if (deptStockEnabled) {
//                    ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
//                    if (articleEntity == null) {
//                        throw new SaveFailureException("未找到该药品。");
//                    }
//                    ArtStockChangeResult result = deptStockService.batchStockInc(wmBill.getOrgId(), wmBill.getDeptCode(), detail.getArtId(), detail.getStockNo(), detail.getTotalPacks(), detail.getTotalCells());
//                    deptArtService.subReservedCells(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity.getArtId(), ArticleUtil.packsToCells(detail.getTotalPacks(), articleEntity.getPackCells(), detail.getTotalCells()));
//                    wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
//                            .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
//                            .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
//                            .set(WmBillDetailEntity::getLastTotalPacks, result.getLastTotalPacks())
//                            .set(WmBillDetailEntity::getLastTotalCells, result.getLastTotalCells())
//                            .set(WmBillDetailEntity::getReturnTotalPacks, detail.getTotalPacks())
//                            .set(WmBillDetailEntity::getReturnTotalCells, detail.getTotalCells())
//                    );
//                } else {
//                    orgStockService.batchStockInc(wmBill.getOrgId(), detail.getArtId(), detail.getStockNo(), detail.getBatchNo(), detail.getTotalPacks(), detail.getTotalCells(), null,null);
//                }
//            }
//        }
        boolean result = this.update(new LambdaUpdateWrapper<WmBillEntity>()
                .eq(WmBillEntity::getWbSeqid, wmBill.getWbSeqid())
                .eq(WmBillEntity::getStatus, WmBillStatus.WAIT_RECEIVE.getValue())
                .set(WmBillEntity::getStatus, billStatus.getValue())
                .set(WmBillEntity::getValidatorUid, userId)
                .set(WmBillEntity::getTimeValidated, new Date())
        );
        if (!result) {
            throw new SaveFailureException("审核失败，请检查数据。");
        }
        // 只做采购入库冲红，此处判断病区的冲红逻辑，可以注释
//        if (wmBill.getSectionId() != null && wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue()) && wmBill.getWmbillTypeId().equals(WmBillType.OUT_STORE_CANCEL.getValue())) {
//            // 病区领用退回，需要减少病区库存
//            remoteInpatientHsdService.sectionStockReturnCancel(Convert.toLong(wmBill.getWmBillId()));
//        }
    }

    @Override
    @Transactional
    public void batchValidateBill(long orgId, long userId, List<Long> wbdSeqids, boolean isPass) {
        for (Long wbdSeqid : wbdSeqids) {
            validateBill(orgId, userId, wbdSeqid, isPass);
        }
    }

    @Override
    @Transactional
    public void validateBill(long orgId, long userId, Long wbSeqid, boolean isPass) {
        WmBillEntity wmBill = getById(wbSeqid);
        if (wmBill == null) {
            throw new SaveFailureException("未找到该单据。");
        }
        if (!wmBill.getOrgId().equals(orgId)) {
            throw new SaveFailureException("单据不属于当前机构。");
        }
        if (!wmBill.getStatus().equals(WmBillStatus.WAIT_RECEIVE.getValue())) {
            throw new SaveFailureException("不是待接收状态，不能进行审核。");
        }

        boolean deptStockEnabled = getDeptStockEnabled(orgId);
        List<WmBillDetailEntity> details = wmBillDetailService.list(new LambdaQueryWrapper<WmBillDetailEntity>().eq(WmBillDetailEntity::getWbSeqid, wbSeqid));
        if (!isPass) { // 审核不通过
            validateBillReject(userId, wmBill, details, deptStockEnabled);
            return;
        }
        validateBillCore(orgId, userId, wmBill, details, deptStockEnabled);
    }

    private void validateBillCore(long orgId, long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean deptStockEnabled) {
        wmBillPendingService.removeById(wmBill.getWbSeqid());
        boolean result = this.update(new LambdaUpdateWrapper<WmBillEntity>()
                .eq(WmBillEntity::getWbSeqid, wmBill.getWbSeqid())
                .eq(WmBillEntity::getStatus, WmBillStatus.WAIT_RECEIVE.getValue())
                .set(WmBillEntity::getStatus, WmBillStatus.COMPLETED.getValue())
                .set(WmBillEntity::getValidatorUid, userId)
                .set(WmBillEntity::getTimeValidated, new Date())
        );
        if (!result) {
            throw new SaveFailureException("审核失败，请检查数据。");
        }
        if (WmBillType.saleOutLs.contains(wmBill.getWmbillTypeId())) { // 出库，减少库存
            // 销售出库单做了预留，不做处理(销售出库单走门诊/病区发药/其它发药)
            throw new SaveFailureException("销售单不允许直接审核");
        }
        //旧方式 wmBillTypeId 调拨4-入库11，新方式wmBillTypeId 调拨4-调拨入库41
        //旧方式 wmBillTypeId 调拨4-出库冲红22，新方式wmBillTypeId 调拨4-调出退回32
        if (WmBillType.storeInLs.contains(wmBill.getWmbillTypeId())) {
            inStockCheck(wmBill, details, deptStockEnabled);
            if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue())) { // 采购直调，需要补充采购仓库的调拨出库单、接收仓库的调拨入库单
                purchaseInTransferCheck(userId, wmBill, details);
            }
        } else if (WmBillType.storeOutLs.contains(wmBill.getWmbillTypeId())) { // 入库冲红、调拨出库、调入退出、报损，减少库存
            outStockCheck(wmBill, details, deptStockEnabled);
        } else if (wmBill.getWmbillTypeId().equals(WmBillType.PACK_TO_CELL.getValue())) { // 拆零组装，减少整货数量，增加拆零数量
            packToCellCheck(wmBill, details, deptStockEnabled);
        } else if (wmBill.getWmbillTypeId().equals(WmBillType.BATCH_ADJUST.getValue())) { // 批号调整(应用前端需要确保正数与负数相等)
            changeBatchNoCheck(wmBill, details, deptStockEnabled);
        }
        if ((wmBill.getBsnTypeId() != null && wmBill.getBsnTypeId().equals(WmBusinessType.LOSS_OVERFLOW.getValue()))
                || ListUtil.of(WmBillType.PACK_TO_CELL.getValue(), WmBillType.BATCH_ADJUST.getValue()).contains(wmBill.getWmbillTypeId())) {
            // 以前的代码损溢明细存在部分无cost、amount数据，这里做一次修复
            resetBillPriceCostAmount(wmBill.getWbSeqid());
        }
        wmBillDetailService.resetBillDetailExpiry(wmBill.getWbSeqid());
        if (wmBill.getSectionId() != null && wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue())
                && wmBill.getWmbillTypeId().equals(WmBillType.TRANSFER_OUT_CANCEL.getValue())
                && (wmBill.getIsBroken() != null && wmBill.getIsBroken().equals(1))) {
            // 病区领用退回，破损的情况下，没有增加库存，补一张报损单
            buildLossBill(orgId, userId, wmBill);
        }
    }

    private void resetBillPriceCostAmount(Long wbSeqId) {
        wmBillDetailService.resetPriceCostAmount(wbSeqId);
        baseMapper.resetCostAmountByDetail(wbSeqId);
    }

    /**
     * 采购冲红自动审核
     *
     * @param orgId
     * @param userId
     * @param wbSeqid
     * @param isPass  暂时默认为true
     */
    @Override
    @Transactional
    public void inStoreValidateBill(long orgId, long userId, Long wbSeqid, boolean isPass) {
        WmBillEntity wmBill = getById(wbSeqid);
        if (wmBill == null) {
            throw new SaveFailureException("未找到该单据。");
        }
        if (!wmBill.getOrgId().equals(orgId)) {
            throw new SaveFailureException("单据不属于当前机构。");
        }
        if (!wmBill.getStatus().equals(WmBillStatus.WAIT_RECEIVE.getValue())) {
            throw new SaveFailureException("不是待接收状态，不能进行审核。");
        }
        wmBillPendingService.removeById(wbSeqid);
        WmBillStatus billStatus = WmBillStatus.COMPLETED;

        boolean deptStockEnabled = getDeptStockEnabled(orgId);
        List<WmBillDetailEntity> details = wmBillDetailService.list(new LambdaQueryWrapper<WmBillDetailEntity>().eq(WmBillDetailEntity::getWbSeqid, wbSeqid));
        if (!isPass) { // 审核不通过
            inStoreValidateBillReject(userId, wmBill, details, deptStockEnabled);
            return;
        }
        boolean result = this.update(new LambdaUpdateWrapper<WmBillEntity>()
                .eq(WmBillEntity::getWbSeqid, wbSeqid)
                .eq(WmBillEntity::getStatus, WmBillStatus.WAIT_RECEIVE.getValue())
                .set(WmBillEntity::getStatus, billStatus.getValue())
                .set(WmBillEntity::getValidatorUid, userId)
                .set(WmBillEntity::getTimeValidated, new Date())
        );
        if (!result) {
            throw new SaveFailureException("审核失败，请检查数据。");
        }
        // 入库冲红，减少库存
        if (wmBill.getWmbillTypeId().equals(WmBillType.PURCHASE_IN_CANCEL.getValue())) {
            // 开启了仓库库存管理时：减少 t_dept_art 、t_org_art 库存
            // 无仓库库存管理时：   减少 t_org_stock 、t_org_art 库存
            inStoreStockCheck(wmBill, details, deptStockEnabled);
        }
        // bill_detail表中记录回收库存整包数、回收库存拆零数、余库存整包数、余库存拆零数
        if (deptStockEnabled) {
            updateBillDetailCurStock(wmBill.getOrgId(), wmBill.getDeptCode(), details);
        }
    }


    /**
     * 采购直调
     * 产生调拨出库
     */
    private void purchaseInTransferCheck(long userId, WmBillEntity wmBill, List<WmBillDetailEntity> details) {
//        WmBillEntity transOutBill = new WmBillEntity();
        DeptCustMapDto deptCustMapDto = deptCustMapService.findDtoById(wmBill.getOrgId(), wmBill.getDeptCode());
        Integer srcCustId = deptCustMapDto.getCustId();
        DeptCustMapDto applyDeptCustMapDto = deptCustMapService.findDtoById(wmBill.getOrgId(), wmBill.getRecvDeptCode());
        Integer targetCustId = applyDeptCustMapDto.getCustId();

        // 产生已完成的出库单
        WmBillEntity transOutBill = new WmBillEntity();
        transOutBill.setOrgId(wmBill.getOrgId());
        transOutBill.setTimeCreated(new Date());
        transOutBill.setStatus(WmBillStatus.COMPLETED.getValue());
        transOutBill.setWmbillTypeId(WmBillType.TRANSFER_OUT.getValue());
        transOutBill.setBsnTypeId(WmBusinessType.TRANSFER_OUT.getValue());
        transOutBill.setBsnType(WmBillBsnType.INVENTORY_TRANSFER.getValue());
        transOutBill.setCustId(targetCustId);
        transOutBill.setDeptCode(wmBill.getDeptCode());
        transOutBill.setCreatorUid(userId);
        transOutBill.setValidatorUid(userId);
        transOutBill.setTimeValidated(new Date());
        transOutBill.setCost(wmBill.getAmount());
        transOutBill.setAmount(wmBill.getAmount());
        transOutBill.setTransferDeptCode(wmBill.getRecvDeptCode());
        transOutBill.setBsnAbstract(StrUtil.sub("采购直调单" + Convert.toStr(wmBill.getWbSeqid()) + "产生[" + deptCustMapDto.getDeptName() + "]向["
                + applyDeptCustMapDto.getDeptName() + "]" + WmBillType.TRANSFER_OUT.getName(), 0, 60));
        saveEntity(transOutBill);

        List<WmBillDetailEntity> transOutDetails = new ArrayList<>();
        for (WmBillDetailEntity detail : details) {
            WmBillDetailEntity wmBillDetailEntity = BeanUtil.copyProperties(detail, WmBillDetailEntity.class);
            wmBillDetailEntity.setWbSeqid(transOutBill.getWbSeqid());
            wmBillDetailEntity.setCost(detail.getAmount());
            wmBillDetailEntity.setRestTotalPacks(Convert.toInt(detail.getRestTotalPacks(), 0) - Convert.toInt(detail.getTotalPacks(), 0));
            wmBillDetailEntity.setRestTotalCells(Convert.toBigDecimal(detail.getRestTotalCells(), BigDecimal.ZERO).subtract(Convert.toBigDecimal(detail.getTotalCells(), BigDecimal.ZERO)));
            wmBillDetailEntity.setStocknoTotalPacks(Convert.toInt(detail.getStocknoTotalPacks(), 0) - Convert.toInt(detail.getTotalPacks(), 0));
            wmBillDetailEntity.setStocknoTotalCells(Convert.toBigDecimal(detail.getStocknoTotalCells(), BigDecimal.ZERO).subtract(Convert.toBigDecimal(detail.getTotalCells(), BigDecimal.ZERO)));
            transOutDetails.add(wmBillDetailEntity);
        }
        wmBillDetailService.saveBatch(transOutDetails);

        // 产生已完成的入库单
        WmBillEntity transInBill = new WmBillEntity();
        transInBill.setOrgId(wmBill.getOrgId());
        transInBill.setTimeCreated(new Date());
        transInBill.setStatus(WmBillStatus.COMPLETED.getValue());
        transInBill.setWmbillTypeId(WmBillType.TRANSFER_IN.getValue());
        transInBill.setBsnTypeId(WmBusinessType.TRANSFER_IN.getValue());
        transInBill.setBsnType(WmBillBsnType.INVENTORY_TRANSFER.getValue());
        transInBill.setCustId(srcCustId);
        transInBill.setDeptCode(wmBill.getRecvDeptCode());
        transInBill.setCreatorUid(userId);
        transInBill.setValidatorUid(userId);
        transInBill.setTimeValidated(new Date());
        transInBill.setCost(wmBill.getAmount());
        transInBill.setAmount(wmBill.getAmount());
        transInBill.setRelativeWbseqid(transOutBill.getWbSeqid());
        transInBill.setTransferDeptCode(wmBill.getDeptCode());
        transInBill.setBsnAbstract(StrUtil.sub("采购直调单" + Convert.toStr(wmBill.getWbSeqid()) + "产生[" + deptCustMapDto.getDeptName() + "]向["
                + applyDeptCustMapDto.getDeptName() + "]" + WmBillType.TRANSFER_IN.getName(), 0, 60));
        saveEntity(transInBill);

        List<WmBillDetailEntity> transInDetails = new ArrayList<>();
        for (WmBillDetailEntity detail : details) {
            WmBillDetailEntity wmBillDetailEntity = BeanUtil.copyProperties(detail, WmBillDetailEntity.class);
            wmBillDetailEntity.setWbSeqid(transInBill.getWbSeqid());
            wmBillDetailEntity.setCost(detail.getAmount());
            transInDetails.add(wmBillDetailEntity);
        }
        billDetailSetCurStock(wmBill.getOrgId(), transInBill.getDeptCode(), transInDetails);
        wmBillDetailService.saveBatch(transInDetails);
    }

    private void billDetailSetCurStock(long orgId, String deptCode, List<WmBillDetailEntity> details) {
        if (ObjectUtil.isNotEmpty(details)) {
            List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).distinct().collect(Collectors.toList());
            List<DeptArtEntity> deptArtLs = deptArtService.list(new LambdaQueryWrapper<DeptArtEntity>().eq(DeptArtEntity::getOrgId, orgId)
                    .eq(DeptArtEntity::getDeptCode, deptCode).in(DeptArtEntity::getArtId, artIds));
            List<DeptStockEntity> deptStockLs = deptStockService.list(new LambdaQueryWrapper<DeptStockEntity>().eq(DeptStockEntity::getOrgId, orgId)
                    .eq(DeptStockEntity::getDeptCode, deptCode).in(DeptStockEntity::getArtId, artIds));
            List<WmBillDetailDto> reservedLs = wmBillDetailService.findReservedLs(orgId, deptCode, artIds);
            for (WmBillDetailEntity detail : details) {
                DeptArtEntity deptArt = deptArtLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
                DeptStockEntity deptStock = deptStockLs.stream().filter(p -> p.getArtId().equals(detail.getArtId()) && p.getStockNo().equals(detail.getStockNo())).findFirst().orElse(null);
                int reservedPacks = reservedLs.stream().filter(p -> p.getArtId().equals(detail.getArtId()) && p.getStockNo().equals(detail.getStockNo()))
                        .mapToInt(p -> Convert.toInt(p.getTotalPacks(), 0)).sum();
                BigDecimal reservedCells = reservedLs.stream().filter(p -> p.getArtId().equals(detail.getArtId()) && p.getStockNo().equals(detail.getStockNo()))
                        .map(p -> Convert.toBigDecimal(p.getTotalCells(), BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                int stockNoTotalPacks = (deptStock == null ? 0 : Convert.toInt(deptStock.getTotalPacks(), 0)) + reservedPacks;
                BigDecimal stockNoTotalCells = (deptStock == null ? BigDecimal.ZERO : Convert.toBigDecimal(deptStock.getTotalCells(), BigDecimal.ZERO)).add(reservedCells);
                detail.setRestTotalPacks(deptArt == null ? 0 : Convert.toInt(deptArt.getTotalPacks(), 0));
                detail.setRestTotalCells(deptArt == null ? BigDecimal.ZERO : Convert.toBigDecimal(deptArt.getTotalCells(), BigDecimal.ZERO));
                detail.setStocknoTotalPacks(stockNoTotalPacks);
                detail.setStocknoTotalCells(stockNoTotalCells);
            }
        }
    }

    /**
     * 入库或出库冲红， 增加库存
     *
     * @param wmBill
     * @param details
     * @param deptStockEnabled
     */
    private void inStockCheck(WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean deptStockEnabled) {
        List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).distinct().collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        List<OrgArtEntity> orgArtLs = orgArtService.list(new LambdaQueryWrapper<OrgArtEntity>().eq(OrgArtEntity::getOrgId, wmBill.getOrgId())
                .in(OrgArtEntity::getArtId, artIds));
        List<DeptArtEntity> deptArtLs = deptArtService.list(new LambdaQueryWrapper<DeptArtEntity>().eq(DeptArtEntity::getOrgId, wmBill.getOrgId())
                .eq(DeptArtEntity::getDeptCode, wmBill.getDeptCode()).in(DeptArtEntity::getArtId, artIds));
        List<DeptStockEntity> deptStockLs = deptStockService.list(new LambdaQueryWrapper<DeptStockEntity>().eq(DeptStockEntity::getOrgId, wmBill.getOrgId())
                .eq(DeptStockEntity::getDeptCode, wmBill.getDeptCode()).in(DeptStockEntity::getArtId, artIds));
        List<WmBillDetailDto> reservedLs = wmBillDetailService.findReservedLs(wmBill.getOrgId(), wmBill.getDeptCode(), artIds);
        // 采购直调
        List<DeptArtEntity> recvDeptArtLs = new ArrayList<>();
        if (wmBill.getWmbillTypeId().equals(WmBillType.PURCHASE_IN_TRANSFER.getValue())) {
            recvDeptArtLs = deptArtService.list(new LambdaQueryWrapper<DeptArtEntity>().eq(DeptArtEntity::getOrgId, wmBill.getOrgId())
                    .eq(DeptArtEntity::getDeptCode, wmBill.getRecvDeptCode()).in(DeptArtEntity::getArtId, artIds));
        }
        for (WmBillDetailEntity detail : details) {
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("未找到该药品。");
            }
            OrgArtEntity orgArt = orgArtLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            DeptArtEntity deptArt = deptArtLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            DeptStockEntity deptStock = deptStockLs.stream().filter(p -> p.getArtId().equals(detail.getArtId()) && p.getStockNo().equals(detail.getStockNo())).findFirst().orElse(null);
            int reservedPacks = reservedLs.stream().filter(p -> p.getArtId().equals(detail.getArtId()) && p.getStockNo().equals(detail.getStockNo()))
                    .mapToInt(p -> Convert.toInt(p.getTotalPacks(), 0)).sum();
            BigDecimal reservedCells = reservedLs.stream().filter(p -> p.getArtId().equals(detail.getArtId()) && p.getStockNo().equals(detail.getStockNo()))
                    .map(p -> Convert.toBigDecimal(p.getTotalCells(), BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            int restTotalPacks = (deptArt == null ? 0 : Convert.toInt(deptArt.getTotalPacks(), 0)) + Convert.toInt(detail.getTotalPacks(), 0);
            BigDecimal restTotalCells = (deptArt == null ? BigDecimal.ZERO : Convert.toBigDecimal(deptArt.getTotalCells(), BigDecimal.ZERO))
                    .add(Convert.toBigDecimal(detail.getTotalCells(), BigDecimal.ZERO));
            int stockNoTotalPacks = (deptStock == null ? 0 : Convert.toInt(deptStock.getTotalPacks(), 0)) + Convert.toInt(detail.getTotalPacks(), 0) + reservedPacks;
            BigDecimal stockNoTotalCells = (deptStock == null ? BigDecimal.ZERO : Convert.toBigDecimal(deptStock.getTotalCells(), BigDecimal.ZERO))
                    .add(Convert.toBigDecimal(detail.getTotalCells(), BigDecimal.ZERO)).add(reservedCells);
            if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue())) {
                // 采购入库需要生成商品批次序号
                Integer stockNo = artStocknoService.saveArtStockNo(detail.getArtId(), detail.getBatchNo(), detail.getWbSeqid(), detail.getLineNo(),
                        detail.getPackPrice(), detail.getCellPrice(), detail.getDateManufactured(), detail.getExpiry(), articleEntity.getPackCells(), detail.getOriginPlace());
                detail.setStockNo(stockNo);
                wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                        .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                        .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                        .set(WmBillDetailEntity::getStockNo, stockNo)
                );
                orgArtService.purchaseIn(wmBill.getOrgId(), articleEntity, detail.getPackPrice(), detail.getTotalPacks());
            }
            boolean stockIncFlag = true; // 是否需要增加库存标志
            if (wmBill.getSectionId() != null && wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue()) && wmBill.getWmbillTypeId().equals(WmBillType.TRANSFER_OUT_CANCEL.getValue())) {
                // 病区领用退回，需要减少病区库存
                sectionArtService.stockDec(wmBill.getSectionId(), articleEntity, detail.getTotalPacks(), detail.getTotalCells());
                if (wmBill.getIsBroken() != null && wmBill.getIsBroken().equals(1)) {
                    // 破损情况下不增加库存
                    stockIncFlag = false;
                }
                // 这里补一段更新，记录病区报损退回的，先加库存后续的buildLossBill减库存才能平账
                wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                        .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                        .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                        .set(WmBillDetailEntity::getRestTotalPacks, restTotalPacks)
                        .set(WmBillDetailEntity::getRestTotalCells, restTotalCells)
                        .set(WmBillDetailEntity::getStocknoTotalPacks, stockNoTotalPacks)
                        .set(WmBillDetailEntity::getStocknoTotalCells, stockNoTotalCells)
                );
            }
            if (stockIncFlag) {
                if (deptStockEnabled) {
                    String deptCode = wmBill.getDeptCode(); // 采购直调，需要将库存直接入到接收科室
                    DeptArtEntity recvDeptArt = null;
                    if (WmBillType.purchaseInLs.contains(wmBill.getWmbillTypeId())) {
                        if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN.getValue()) || wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_TRANSFER.getValue())) {
                            OrgArtEntity orgArtEntity = orgArt != null ? orgArt : orgArtService.findById(wmBill.getOrgId(), detail.getArtId());
                            deptArtService.purchaseIn(wmBill.getOrgId(), wmBill.getDeptCode(), detail.getArtId(),
                                    ArticleUtil.packsToCells(detail.getTotalPacks(), articleEntity.getPackCells(), detail.getTotalCells()), orgArtEntity.getSplittable(), deptArt);
                        } else if (wmBill.getBsnType().equals(WmBillBsnType.PURCHASE_IN_TRANSFER.getValue())) {
                            // 采购直调 加的是直调仓库科室
                            OrgArtEntity orgArtEntity = orgArt != null ? orgArt : orgArtService.findById(wmBill.getOrgId(), detail.getArtId());
                            deptCode = wmBill.getRecvDeptCode();
                            recvDeptArt = recvDeptArtLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
                            deptArtService.purchaseIn(wmBill.getOrgId(), wmBill.getRecvDeptCode(), detail.getArtId(),
                                    ArticleUtil.packsToCells(detail.getTotalPacks(), articleEntity.getPackCells(), detail.getTotalCells()), orgArtEntity.getSplittable(), recvDeptArt);
                        }
                    }
                    ArtStockChangeResult result = deptStockService.batchStockInc(wmBill.getOrgId(), deptCode, detail.getArtId(), detail.getStockNo(), detail.getTotalPacks(), detail.getTotalCells(), deptStock);
                    detail.setRestTotalPacks(restTotalPacks);
                    detail.setRestTotalCells(restTotalCells);
                    detail.setStocknoTotalPacks(stockNoTotalPacks);
                    detail.setStocknoTotalCells(stockNoTotalCells);
                    // 20250425, 现存代码在下方才增加仓库库存，晚于deptArt的查询时间，RestTotal在这里更新时先做增加
                    // deptArtService.stockInc(wmBill.getOrgId(), deptCode, detail.getArtId(), detail.getTotalPacks(), detail.getTotalCells());
                    wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                            .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                            .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                            .set(WmBillDetailEntity::getLastTotalPacks, result.getLastTotalPacks())
                            .set(WmBillDetailEntity::getLastTotalCells, result.getLastTotalCells())
                            .set(WmBillDetailEntity::getRestTotalPacks, restTotalPacks)
                            .set(WmBillDetailEntity::getRestTotalCells, restTotalCells)
                            .set(WmBillDetailEntity::getStocknoTotalPacks, stockNoTotalPacks)
                            .set(WmBillDetailEntity::getStocknoTotalCells, stockNoTotalCells)
                    );
                    deptArtService.stockInc(wmBill.getOrgId(), deptCode, detail.getArtId(), detail.getTotalPacks(), detail.getTotalCells(),
                            wmBill.getWmbillTypeId().equals(WmBillType.PURCHASE_IN_TRANSFER.getValue()) ? recvDeptArt : deptArt);
                    orgArtService.stockInc(wmBill.getOrgId(), detail.getArtId(), detail.getTotalPacks(), detail.getTotalCells());
                } else {
                    orgArtService.stockInc(wmBill.getOrgId(), detail.getArtId(), detail.getTotalPacks(), detail.getTotalCells());
                    orgArtService.batchStockInc(wmBill.getOrgId(), detail.getArtId(), detail.getStockNo(), detail.getBatchNo(), detail.getTotalPacks(), detail.getTotalCells(), detail.getDateManufactured(), detail.getExpiry());
                }
            }
        }
//        OrgEntity orgEntity = orgService.getById(wmBill.getOrgId());
        if (WmBillType.purchaseInLs.contains(wmBill.getWmbillTypeId())) {
            orgArtService.saveToOrgItemPrice(wmBill.getWbSeqid(), wmBill.getOrgId());
        }
    }

    /**
     * 入库冲红，减少库存
     *
     * @param wmBill
     * @param details
     * @param deptStockEnabled
     */
    private void outStockCheck(WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean deptStockEnabled) {
        List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        List<DeptArtEntity> deptArtLs = deptArtService.list(new LambdaQueryWrapper<DeptArtEntity>().eq(DeptArtEntity::getOrgId, wmBill.getOrgId())
                .eq(DeptArtEntity::getDeptCode, wmBill.getDeptCode()).in(DeptArtEntity::getArtId, artIds));
        List<OrgArtEntity> orgArtLs = orgArtService.list(new LambdaQueryWrapper<OrgArtEntity>().eq(OrgArtEntity::getOrgId, wmBill.getOrgId())
                .in(OrgArtEntity::getArtId, artIds));
        // todo 这里需要重构，checkSplittable是否校验拆零为false，完全可以在这里对details根据artId汇总后合计数量再进行出库，减少数据库操作次数
        for (WmBillDetailEntity detail : details) {
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("未找到该药品。");
            }
            OrgArtEntity orgArt = orgArtLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            DeptArtEntity deptArt = deptArtLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            // 一次出库wmBillDetailEntities中的artId可能重复出多次出一次以后就清orgArtLs、deptArtLs里的数据
            orgArtLs = orgArtLs.stream().filter(p -> !p.getArtId().equals(detail.getArtId())).collect(Collectors.toList());
            deptArtLs = deptArtLs.stream().filter(p -> !p.getArtId().equals(detail.getArtId())).collect(Collectors.toList());
            if (wmBill.getBsnType().equals(WmBillBsnType.INVENTORY_LOSS.getValue())) { // 报损单在生成的时候已经做了预占库，这里不需要再处理了
                if (deptStockEnabled) {
                    deptArtService.stockDec(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity.getArtId(), articleEntity.getPackCells(),
                            detail.getTotalPacks(), detail.getTotalCells(), false, deptArt);
                    // 20250423 报损单在生成的时候已经做了预占库（入库冲红单-损溢单，会做预占），需要释放预占
                    // 盘点-报损、损溢制单-入库冲红，都会走预占，都需要释放预占
                    deptArtService.subReservedCells(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity.getArtId(),
                            ArticleUtil.packsToCells(detail.getTotalPacks(), articleEntity.getPackCells(), detail.getTotalCells()));
                    orgArtService.stockDec(wmBill.getOrgId(), articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), orgArt);
                } else {
                    orgArtService.stockDec(wmBill.getOrgId(), articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), orgArt);
                }
            } else {
                if (deptStockEnabled) {
                    if (detail.getStockNo() == null) {
                        throw new SaveFailureException("入库冲红单的批次号不能为空");
                    }
//                    ArtStockReserveResult result = deptArtService.batchStockDec(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity, detail.getBatchNo(), detail.getTotalPacks(), detail.getTotalCells(), false);
//                    if (!result.getReservedFlag().equals(ReservedFlag.All_LOCKED)) {
//                        //TODO 后续扩展如果不满足时，可以替换品种
//                        throw new SaveFailureException(articleEntity.getArtName() + "库存不满足");
//                    }
                    ArtStockChangeResult artStockChangeResult = deptStockService.batchStockDec(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity.getArtId(), detail.getStockNo(), detail.getTotalPacks(), detail.getTotalCells());
                    wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                            .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                            .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                            .set(WmBillDetailEntity::getLastTotalPacks, artStockChangeResult.getLastTotalPacks())
                            .set(WmBillDetailEntity::getLastTotalCells, artStockChangeResult.getLastTotalCells())
                    );
                    deptArtService.stockDec(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity.getArtId(), articleEntity.getPackCells(),
                            detail.getTotalPacks(), detail.getTotalCells(), false, deptArt);
                    // 20250419 入库冲红单submitBill(orgId, userId, wmBill, details, false) reserve传的false，不会做预占，同理下面审核单据validateBill时也不需要扣减deptArt表的reservedCells预占数
                    if (!wmBill.getWmbillTypeId().equals(WmBillType.PURCHASE_IN_CANCEL.getValue())) {
                        deptArtService.subReservedCells(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity.getArtId(), ArticleUtil.packsToCells(detail.getTotalPacks(), articleEntity.getPackCells(), detail.getTotalCells()));
                    }
                    orgArtService.stockDec(wmBill.getOrgId(), articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), orgArt);
                } else {
                    orgArtService.stockDec(wmBill.getOrgId(), articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), orgArt);
                    orgArtService.batchStockDec(wmBill.getOrgId(), articleEntity, detail.getTotalPacks(), detail.getTotalCells());
                }
            }
        }
        if (deptStockEnabled) {
            updateBillDetailCurStock(wmBill.getOrgId(), wmBill.getDeptCode(), details);
        }
    }

    private void updateBillDetailCurStock(long orgId, String deptCode, List<WmBillDetailEntity> details) {
        billDetailSetCurStock(orgId, deptCode, details);
        for (WmBillDetailEntity detail : details) {
            wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                    .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                    .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                    .set(WmBillDetailEntity::getRestTotalPacks, detail.getRestTotalPacks())
                    .set(WmBillDetailEntity::getRestTotalCells, detail.getRestTotalCells())
                    .set(WmBillDetailEntity::getStocknoTotalPacks, detail.getStocknoTotalPacks())
                    .set(WmBillDetailEntity::getStocknoTotalCells, detail.getStocknoTotalCells()));
        }
    }

    /**
     * 采购冲红，减少库存
     * 开启了仓库库存管理时
     * 减少 t_dept_art 库存
     * 减少 t_org_art 库存
     * <p>
     * 没开启仓库库存管理时
     * 减少 t_org_art 库存
     * 减少 t_org_stock 库存
     *
     * @param wmBill
     * @param details
     * @param deptStockEnabled
     */
    private void inStoreStockCheck(WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean deptStockEnabled) {
        List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        for (WmBillDetailEntity detail : details) {
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("未找到该药品。");
            }
            //如果后续要做成公共方法，可以在此处做报损单的判断处理
            if (deptStockEnabled) {
                if (detail.getStockNo() == null) {
                    throw new SaveFailureException("入库冲红单的批次号不能为空");
                }
                ArtStockChangeResult artStockChangeResult = deptStockService.inStoreBatchStockDec(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity.getArtId(), detail.getStockNo(), detail.getTotalPacks(), detail.getTotalCells());
                wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                        .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                        .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                        .set(WmBillDetailEntity::getLastTotalPacks, artStockChangeResult.getLastTotalPacks())
                        .set(WmBillDetailEntity::getLastTotalCells, artStockChangeResult.getLastTotalCells())
                );
                int updateCount = deptArtService.stockDecCount(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity, detail.getTotalPacks(), detail.getTotalCells(), false);
                //防止dept_art表和org_art表数据不一致
                if (updateCount == 0) {
                    throw new SaveFailureException("库存不足, 扣减失败");
                }
                orgArtService.stockDec(wmBill.getOrgId(), articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), null);
            } else {
                int updateCount = orgArtService.stockDecCount(wmBill.getOrgId(), articleEntity, detail.getTotalPacks(), detail.getTotalCells());
                //防止dept_art表和org_art表数据不一致
                if (updateCount == 0) {
                    throw new SaveFailureException("库存不足, 扣减失败");
                }
                orgArtService.batchStockDec(wmBill.getOrgId(), articleEntity, detail.getTotalPacks(), detail.getTotalCells());
            }
        }
    }

    /**
     * 拆零组装，减少整货数量，增加拆零数量
     *
     * @param wmBill
     * @param details
     * @param deptStockEnabled
     */
    private void packToCellCheck(WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean deptStockEnabled) {
        List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        for (WmBillDetailEntity detail : details) {
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("未找到该药品。");
            }
            // 拆零时，整包数量为负数，拆零数量为正数
            // 组装时，整包数量为正数，拆零数量为负数
            if (deptStockEnabled) {
                ArtStockChangeResult result = deptStockService.batchStockInc(wmBill.getOrgId(), wmBill.getDeptCode(), articleEntity.getArtId(), detail.getStockNo(), detail.getTotalPacks(), detail.getTotalCells(), null);
                wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                        .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                        .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                        .set(WmBillDetailEntity::getLastTotalPacks, result.getLastTotalPacks())
                        .set(WmBillDetailEntity::getLastTotalCells, result.getLastTotalCells())
                );
                deptArtService.stockInc(wmBill.getOrgId(), wmBill.getDeptCode(), detail.getArtId(), detail.getTotalPacks(), detail.getTotalCells(), null);
                orgArtService.stockInc(wmBill.getOrgId(), detail.getArtId(), detail.getTotalPacks(), detail.getTotalCells());
            } else {
                orgArtService.stockInc(wmBill.getOrgId(), detail.getArtId(), detail.getTotalPacks(), detail.getTotalCells());
                orgStockService.batchStockInc(wmBill.getOrgId(), articleEntity.getArtId(), detail.getStockNo(), detail.getBatchNo(), detail.getTotalPacks(), detail.getTotalCells(), detail.getDateManufactured(), detail.getExpiry());
            }
        }
        updateBillDetailCurStock(wmBill.getOrgId(), wmBill.getDeptCode(), details);
    }

    /**
     * 批号调整(应用前端需要确保正数与负数相等)
     *
     * @param wmBill
     * @param details
     * @param deptStockEnabled
     */
    private void changeBatchNoCheck(WmBillEntity wmBill, List<WmBillDetailEntity> details, boolean deptStockEnabled) {
        List<Long> artIds = details.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        for (WmBillDetailEntity detail : details) {
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("未找到该药品。");
            }
            if (detail.getTotalPacks() == null) {
                detail.setTotalPacks(0);
            }
            if (detail.getTotalCells() == null) {
                detail.setTotalCells(BigDecimal.ZERO);
            }
            if (detail.getTotalPacks().longValue() > 0) {
                if (deptStockEnabled) {
                    ArtStockChangeResult result = deptStockService.batchStockInc(wmBill.getOrgId(), wmBill.getDeptCode(), detail.getArtId(), detail.getStockNo(), detail.getTotalPacks(), null, null);
                    wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                            .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                            .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                            .set(WmBillDetailEntity::getLastTotalPacks, result.getLastTotalPacks())
                            .set(WmBillDetailEntity::getLastTotalCells, result.getLastTotalCells())
                    );
                } else {
                    orgStockService.batchStockInc(wmBill.getOrgId(), detail.getArtId(), detail.getStockNo(), detail.getBatchNo(), detail.getTotalPacks(), null, null, null);
                }
            } else if (detail.getTotalPacks().longValue() < 0) {
                if (deptStockEnabled) {
                    ArtStockChangeResult result = deptStockService.batchStockDec(wmBill.getOrgId(), wmBill.getDeptCode(), detail.getArtId(), detail.getStockNo(), Math.abs(detail.getTotalPacks()), null);
                    wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                            .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                            .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                            .set(WmBillDetailEntity::getLastTotalPacks, result.getLastTotalPacks())
                            .set(WmBillDetailEntity::getLastTotalCells, result.getLastTotalCells())
                    );
                } else {
                    orgStockService.batchStockDec(wmBill.getOrgId(), detail.getArtId(), detail.getStockNo(), Math.abs(detail.getTotalPacks()), null);
                }
            }

            if (detail.getTotalCells().compareTo(BigDecimal.ZERO) > 0) {
                if (deptStockEnabled) {
                    ArtStockChangeResult result = deptStockService.batchStockInc(wmBill.getOrgId(), wmBill.getDeptCode(), detail.getArtId(), detail.getStockNo(), null, detail.getTotalCells(), null);
                    wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                            .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                            .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                            .set(WmBillDetailEntity::getLastTotalPacks, result.getLastTotalPacks())
                            .set(WmBillDetailEntity::getLastTotalCells, result.getLastTotalCells())
                    );
                } else {
                    orgStockService.batchStockInc(wmBill.getOrgId(), detail.getArtId(), detail.getStockNo(), detail.getBatchNo(), null, detail.getTotalCells(), null, null);
                }
            } else if (detail.getTotalCells().compareTo(BigDecimal.ZERO) < 0) {
                if (deptStockEnabled) {
                    ArtStockChangeResult result = deptStockService.batchStockDec(wmBill.getOrgId(), wmBill.getDeptCode(), detail.getArtId(), detail.getStockNo(), null, detail.getTotalCells().negate());
                    wmBillDetailService.update(new LambdaUpdateWrapper<WmBillDetailEntity>()
                            .eq(WmBillDetailEntity::getWbSeqid, detail.getWbSeqid())
                            .eq(WmBillDetailEntity::getLineNo, detail.getLineNo())
                            .set(WmBillDetailEntity::getLastTotalPacks, result.getLastTotalPacks())
                            .set(WmBillDetailEntity::getLastTotalCells, result.getLastTotalCells())
                    );
                } else {
                    orgStockService.batchStockDec(wmBill.getOrgId(), detail.getArtId(), detail.getStockNo(), null, detail.getTotalCells().negate());
                }
            }
        }
        updateBillDetailCurStock(wmBill.getOrgId(), wmBill.getDeptCode(), details);
    }

    /**
     * 病区领用退回，破损的情况下，没有增加库存，补一张报损单
     *
     * @param orgId
     * @param userId
     * @param wmBill
     */
    private void buildLossBill(long orgId, long userId, WmBillEntity wmBill) {
        List<WmBillDetailEntity> srcDetailLs = wmBillDetailService.list(new LambdaQueryWrapper<WmBillDetailEntity>().eq(WmBillDetailEntity::getWbSeqid, wmBill.getWbSeqid()));
        WmBillEntity lossBillEntity = new WmBillEntity();
        lossBillEntity.setOrgId(orgId);
        lossBillEntity.setTimeCreated(DateUtils.addSeconds(new Date(), 5));
        lossBillEntity.setStatus(WmBillStatus.COMPLETED.getValue());
        lossBillEntity.setWmbillTypeId(WmBillType.LOSS_OUT.getValue());
        lossBillEntity.setBsnTypeId(WmBusinessType.LOSS_OVERFLOW.getValue());
        lossBillEntity.setBsnType(WmBillBsnType.INVENTORY_LOSS.getValue());
        lossBillEntity.setValidatorUid(userId);
        lossBillEntity.setCreatorUid(userId);
        lossBillEntity.setTimeValidated(DateUtils.addSeconds(new Date(), 5));
        lossBillEntity.setBsnAbstract("病区统领库存退回，报损");
        lossBillEntity.setAmount(wmBill.getAmount());
        lossBillEntity.setDeptCode(wmBill.getDeptCode());
        lossBillEntity.setSectionId(wmBill.getSectionId());
        this.saveEntity(lossBillEntity);

        List<WmBillDetailEntity> lossDetails = new ArrayList<>();
        for (WmBillDetailEntity detail : srcDetailLs) {
            WmBillDetailEntity lossDetail = BeanUtil.copyProperties(detail, WmBillDetailEntity.class);
            lossDetail.setWbSeqid(lossBillEntity.getWbSeqid());
            lossDetails.add(lossDetail);
        }
        billDetailSetCurStock(wmBill.getOrgId(), wmBill.getDeptCode(), lossDetails);
        wmBillDetailService.saveBatch(lossDetails);
    }

    @Override
    @Transactional
    public void changeBatchNo(long orgId, long userId, String deptCode, Long artId, Integer oldStockNo,
                              Integer newStockNo, Integer totalPacks, BigDecimal totalCells) {
        if (StrUtil.isBlank(deptCode)) {
            throw new SaveFailureException("参数错误，科室编码不能为空");
        }
        if (artId == null) {
            throw new SaveFailureException("参数错误，品种不能为空");
        }
        if (oldStockNo == null) {
            throw new SaveFailureException("参数错误，原批号不能为空");
        }
        if (newStockNo == null) {
            throw new SaveFailureException("参数错误，新批号不能为空");
        }
        if (oldStockNo.equals(newStockNo)) {
            throw new SaveFailureException("参数错误，新批号不能与原批号相同");
        }
        if (totalPacks == null && totalCells == null) {
            throw new SaveFailureException("参数错误，请录入要调整的数量");
        }
        totalPacks = Math.abs(Convert.toInt(totalPacks, 0));
        totalCells = Convert.toBigDecimal(totalCells, BigDecimal.ZERO).abs();
        String bsnAbstract = StrUtil.sub(artId + ":" + oldStockNo + "调整为:" + newStockNo, 0, 60);
        DeptCustMapDto deptCustMapDto = deptCustMapService.findDtoById(orgId, deptCode);
        Integer custId = deptCustMapDto.getCustId();

        WmBillEntity wmBillEntity = new WmBillEntity();
        wmBillEntity.setWmbillTypeId(WmBillType.BATCH_ADJUST.getValue());
        wmBillEntity.setBsnType(WmBillBsnType.INVENTORY_LOSS.getValue());
        wmBillEntity.setBsnTypeId(WmBusinessType.LOSS_OVERFLOW.getValue());
        wmBillEntity.setCustId(custId);
        wmBillEntity.setBsnAbstract(bsnAbstract);
        wmBillEntity.setDeptCode(deptCode);
        List<ArtStocknoEntity> artStockNoLs = artStocknoService.list(new LambdaQueryWrapper<ArtStocknoEntity>().eq(ArtStocknoEntity::getArtId, artId)
                .in(ArtStocknoEntity::getStockNo, ListUtil.of(oldStockNo, newStockNo)));
        ArtStocknoEntity oldStock = artStockNoLs.stream().filter(p -> p.getStockNo().equals(oldStockNo)).findFirst().orElse(null);
        ArtStocknoEntity newStock = artStockNoLs.stream().filter(p -> p.getStockNo().equals(newStockNo)).findFirst().orElse(null);
        if (oldStock == null) {
            throw new SaveFailureException("原批次号不存在");
        }
        if (newStock == null) {
            throw new SaveFailureException("原批次号不存在");
        }

        List<WmBillDetailEntity> detailEntities = new ArrayList<>();
        WmBillDetailEntity fromDetail = new WmBillDetailEntity();
        fromDetail.setLineNo(1);
        fromDetail.setArtId(artId);
        fromDetail.setBatchNo(oldStock.getBatchNo());
        fromDetail.setStockNo(oldStock.getStockNo());
        if (totalPacks > 0) {
            fromDetail.setTotalPacks(totalPacks * -1);
        }
        if (totalCells.compareTo(BigDecimal.ZERO) > 0) {
            fromDetail.setTotalCells(totalCells.negate());
        }
        detailEntities.add(fromDetail);

        // 拆零合包还是放在72拆零拼装里做
//        if (Convert.toInt(newStock.getPackCells(), 0) == 1 && NumberUtil.isGreater(totalCells, BigDecimal.ZERO)) {
//            totalPacks += totalCells.intValue();
//            totalCells = BigDecimal.ZERO;
//        }

        WmBillDetailEntity toDetail = new WmBillDetailEntity();
        toDetail.setLineNo(2);
        toDetail.setArtId(artId);
        toDetail.setBatchNo(newStock.getBatchNo());
        toDetail.setStockNo(newStock.getStockNo());
        toDetail.setTotalPacks(totalPacks);
        toDetail.setTotalCells(totalCells);
        detailEntities.add(toDetail);
        this.submitBill(orgId, userId, wmBillEntity, detailEntities, false);
    }

    @Override
    @Transactional
    public List<WmBillDetailEntity> wmBillArtStockReserve(Long wbSeqid, Long orgId, String deptCode, List<WmReqArtEntity> reqArtList, boolean allReserved, boolean useCostPrice, String errorMsgPre) {
        log.debug("wmBillArtStockReserve wbSeqid:{}, orgId:{}, deptCode:{}, reqArtList:{}, allReserved:{}, errorMsgPre:{}", wbSeqid, orgId, deptCode, reqArtList, allReserved, errorMsgPre);
        List<WmBillDetailEntity> list = new ArrayList<>();
        boolean deptStockEnabled = getDeptStockEnabled(orgId);
        List<Long> artIds = reqArtList.stream().map(WmReqArtEntity::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        List<String> errorMessages = new ArrayList<>();
        for (WmReqArtEntity wmReqArtEntity : reqArtList) {
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(wmReqArtEntity.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("artId:" + wmReqArtEntity.getArtId() + "未找到该药品。");
            }
            ArtStockReserveResult result = artStockReserve(orgId, deptCode, articleEntity, deptStockEnabled, wmReqArtEntity.getReqBatchNo(), wmReqArtEntity.getReqStockNo(), wmReqArtEntity.getTotalPacks(), wmReqArtEntity.getTotalCells(), true, allReserved, useCostPrice, errorMsgPre);
            if (StringUtil.isNotEmpty(result.getShortageErrorMsg())) {
                errorMessages.add(result.getShortageErrorMsg());
                continue;
            }
            wmReqArtEntity.setPacksReserved(result.getPacksReserved());
            wmReqArtEntity.setCellsReserved(result.getCellsReserved());
            wmReqArtEntity.setReservedFlag(result.getReservedFlag().getValue());
            list.addAll(result.getWmBillDetailEntities());
        }
        //库存不足统一提示
        if (!errorMessages.isEmpty()) {
            throw new SaveFailureException(String.join("\n", errorMessages));
        }
        int lineNo = 1;
        for (WmBillDetailEntity wmBillDetailEntity : list) {
            wmBillDetailEntity.setWbSeqid(wbSeqid);
            wmBillDetailEntity.setLineNo(lineNo++);
        }
        wmBillDetailService.saveBatch(list);
        return list;
    }

    /**
     * 扣减库存
     *
     * @param wbSeqid
     * @param orgId
     * @param deptCode
     * @param wmBillDetailDtoList
     * @param allReserved
     * @param useCostPrice
     * @param errorMsgPre
     * @return
     */
    @Override
    @Transactional
    public List<WmBillDetailEntity> wmBillArtStockNoTransfer(Long wbSeqid, Long orgId, String deptCode, List<WmBillDetailDto> wmBillDetailDtoList, boolean allReserved, boolean useCostPrice, String errorMsgPre) {
        log.debug("wmBillArtStockNoTransfer wbSeqid:{}, orgId:{}, deptCode:{}, wmBillDetailDtoList:{}, allReserved:{}, errorMsgPre:{}", wbSeqid, orgId, deptCode, wmBillDetailDtoList, allReserved, errorMsgPre);
        List<WmBillDetailEntity> list = new ArrayList<>();
        boolean deptStockEnabled = getDeptStockEnabled(orgId);
        List<Long> artIds = wmBillDetailDtoList.stream().map(WmBillDetailDto::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        List<String> errorMessages = new ArrayList<>();
        for (WmBillDetailDto wmBillDetailDto : wmBillDetailDtoList) {
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(wmBillDetailDto.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("artId:" + wmBillDetailDto.getArtId() + "未找到该药品。");
            }
            //批次调拨 checkSplittable=false，不校验拆零合包，防止拆零数无法调拨问题
            ArtStockReserveResult result = artStockReserve(orgId, deptCode, articleEntity, deptStockEnabled, wmBillDetailDto.getBatchNo(), wmBillDetailDto.getStockNo(), wmBillDetailDto.getTotalPacks(), wmBillDetailDto.getTotalCells(), false, allReserved, useCostPrice, errorMsgPre);
            if (StringUtil.isNotEmpty(result.getShortageErrorMsg())) {
                errorMessages.add(result.getShortageErrorMsg());
                continue;
            }
            list.addAll(result.getWmBillDetailEntities());
        }


        //库存不足统一提示
        if (!errorMessages.isEmpty()) {
            throw new SaveFailureException(String.join("\n", errorMessages));
        }
        int lineNo = 1;
        for (WmBillDetailEntity wmBillDetailEntity : list) {
            wmBillDetailEntity.setWbSeqid(wbSeqid);
            wmBillDetailEntity.setLineNo(lineNo++);
        }
        wmBillDetailService.saveBatch(list);
        return list;
    }

    @Override
    @Transactional
    public List<WmBillDetailEntity> wmBillDetailStockReserve(Long wbSeqid, Long orgId, String deptCode, List<WmReqDetailEntity> reqArtList, boolean allReserved, boolean useCostPrice, String errorMsgPre) {
        log.debug("wmBillArtStockReserve wbSeqid:{}, orgId:{}, deptCode:{}, reqArtList:{}, allReserved:{}, errorMsgPre:{}", wbSeqid, orgId, deptCode, reqArtList, allReserved, errorMsgPre);
        List<WmBillDetailEntity> list = new ArrayList<>();
        boolean deptStockEnabled = getDeptStockEnabled(orgId);
        List<Long> artIds = reqArtList.stream().map(WmReqDetailEntity::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        List<String> errorMessages = new ArrayList<>();
        for (WmReqDetailEntity wmReqDetailEntity : reqArtList) {
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(wmReqDetailEntity.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("artId:" + wmReqDetailEntity.getArtId() + "未找到该药品。");
            }
            BigDecimal totalCells = wmReqDetailEntity.getShortCells();
            if (wmReqDetailEntity.getCellsDelivered() != null) {
                totalCells = totalCells.subtract(wmReqDetailEntity.getCellsDelivered());
            }
//            ArtStockReserveResult result = artStockReserve(orgId, deptCode, articleEntity, deptStockEnabled, null, wmReqDetailEntity.getTotalPacks(), wmReqDetailEntity.getTotalCells(), true, allReserved, useCostPrice, errorMsgPre);
            ArtStockReserveResult result = artStockReserve(orgId, deptCode, articleEntity, deptStockEnabled, null, null, null, totalCells, true, allReserved, useCostPrice, errorMsgPre);
            if (StringUtil.isNotEmpty(result.getShortageErrorMsg())) {
                errorMessages.add(result.getShortageErrorMsg());
                continue;
            }
//            wmReqDetailEntity.setPacksReserved(result.getPacksReserved());
//            wmReqDetailEntity.setCellsReserved(result.getCellsReserved());
            // 已锁定制剂数
            wmReqDetailEntity.setCellsReserved(ArticleUtil.packsToCells(result.getPacksReserved(), articleEntity.getPackCells(), result.getCellsReserved()));
            wmReqDetailEntity.setReservedFlag(result.getReservedFlag().getValue());
            if (result.getWmBillDetailEntities() != null && !result.getWmBillDetailEntities().isEmpty()) {
                for (WmBillDetailEntity wmBillDetailEntity : result.getWmBillDetailEntities()) {
                    wmBillDetailEntity.setWmReqid(wmReqDetailEntity.getWmReqid());
                    wmBillDetailEntity.setWmReqDetailLineNo(wmReqDetailEntity.getLineNo());
                }
                list.addAll(result.getWmBillDetailEntities());
            }
        }
        //库存不足统一提示
        if (!errorMessages.isEmpty()) {
            throw new SaveFailureException(String.join("\n", errorMessages));
        }
        int lineNo = 1;
        for (WmBillDetailEntity wmBillDetailEntity : list) {
            wmBillDetailEntity.setWbSeqid(wbSeqid);
            wmBillDetailEntity.setLineNo(lineNo++);
        }
        wmBillDetailService.saveBatch(list);
        return list;
    }

    // api/recipeReserve 医生签名预占库存返回成本
    @Override
    public ArtStockReserveResult artStockReserve(Long orgId, String deptCode, ArticleEntity articleEntity, boolean deptStockEnabled,
                                                 @Nullable String batchNo, @Nullable Integer stockNo, Integer totalPacks, BigDecimal totalCells, boolean checkSplittable, boolean allReserved, boolean useCostPrice, String errorMsgPre) {
        log.debug("artStockReserve orgId:{}, deptCode:{}, articleEntity:{}, deptStockEnabled:{}, batchNo:{}, totalPacks:{}, totalCells:{}, checkSplittable:{}, allReserved:{}, errorMsgPre:{}",
                orgId, deptCode, articleEntity, deptStockEnabled, batchNo, totalPacks, totalCells, checkSplittable, allReserved, errorMsgPre);
        ArtStockReserveResult result;
        if (deptStockEnabled) {
            result = deptArtService.batchStockDec(orgId, deptCode, articleEntity, batchNo, stockNo, totalPacks, totalCells, checkSplittable);
            if (allReserved && !result.getReservedFlag().equals(ReservedFlag.All_LOCKED)) {
                String shortageErrorMsg = Convert.toStr(errorMsgPre, "") + "artId:" + articleEntity.getArtId() + "," + articleEntity.getArtName() + "库存不满足";
                log.debug(shortageErrorMsg);
                //TODO 后续扩展如果不满足时，可以替换品种
                //throw new SaveFailureException(Convert.toStr(errorMsgPre, "") + "artId:" + articleEntity.getArtId() + "," + articleEntity.getArtName() + "库存不满足");
                result.setShortageErrorMsg(shortageErrorMsg);
                return result;
            }
            List<WmBillDetailEntity> list = new ArrayList<>();
            OrgCustMapEntity orgCustMapEntity = orgCustMapService.findById(orgId);
            for (DeptStockDto dto : result.getDeptStockDtos()) {
                WmBillDetailEntity wmBillDetailEntity = new WmBillDetailEntity();
                wmBillDetailEntity.setArtId(dto.getArtId());
                wmBillDetailEntity.setBatchNo(dto.getBatchNo());
                wmBillDetailEntity.setStockNo(dto.getStockNo());
                wmBillDetailEntity.setTotalPacks(dto.getTotalPacks());
                wmBillDetailEntity.setTotalCells(dto.getTotalCells());
                wmBillDetailEntity.setExpiry(dto.getExpiry());
                wmBillDetailEntity.setDateManufactured(dto.getDateManufactured());

                wmBillDetailEntity.setLastTotalPacks(dto.getLastTotalPacks());
                wmBillDetailEntity.setLastTotalCells(dto.getLastTotalCells());

                ArtStocknoEntity artStocknoEntity = artStocknoService.findById(dto.getArtId(), dto.getStockNo());
                if (useCostPrice) { // 使用成本价
                    wmBillDetailEntity.setPackPrice(artStocknoEntity.getPackPrice());
                    wmBillDetailEntity.setCellPrice(artStocknoEntity.getCellPrice());
                } else {
                    OrgArtDto orgArtDto = orgArtService.findPrice(dto.getOrgId(), dto.getArtId(), dto.getStockNo(), orgCustMapEntity.getPricingMethod());
                    wmBillDetailEntity.setPackPrice(orgArtDto.getPackPrice());
                    wmBillDetailEntity.setCellPrice(orgArtDto.getCellPrice());
                }
                BigDecimal amount = BigDecimal.ZERO;
                BigDecimal cost = BigDecimal.ZERO;
                if (wmBillDetailEntity.getTotalPacks() != null) {
                    amount = wmBillDetailEntity.getPackPrice().multiply(new BigDecimal(wmBillDetailEntity.getTotalPacks()));
                    cost = artStocknoEntity.getPackPrice().multiply(new BigDecimal(wmBillDetailEntity.getTotalPacks()));
                }
                if (wmBillDetailEntity.getTotalCells() != null) {
                    amount = amount.add(wmBillDetailEntity.getCellPrice().multiply(wmBillDetailEntity.getTotalCells()));
                    cost = cost.add(artStocknoEntity.getCellPrice().multiply(wmBillDetailEntity.getTotalCells()));
                }
                wmBillDetailEntity.setAmount(amount);
                wmBillDetailEntity.setCost(cost);
                list.add(wmBillDetailEntity);
            }
            result.setWmBillDetailEntities(list);
        } else {
            result = orgArtService.batchStockDec(orgId, articleEntity, totalPacks, totalCells);
            if (allReserved && !result.getReservedFlag().equals(ReservedFlag.All_LOCKED)) {
                String shortageErrorMsg = Convert.toStr(errorMsgPre, "") + "artId:" + articleEntity.getArtId() + "," + articleEntity.getArtName() + "库存不满足";
                //TODO 后续扩展如果不满足时，可以替换品种
//                throw new SaveFailureException(Convert.toStr(errorMsgPre, "") + "artId:" + articleEntity.getArtId() + "," + articleEntity.getArtName() + "库存不满足");
                result.setShortageErrorMsg(shortageErrorMsg);
                return result;
            }
            List<WmBillDetailEntity> list = new ArrayList<>();
            OrgCustMapEntity orgCustMapEntity = orgCustMapService.findById(orgId);
            for (OrgStockDto dto : result.getOrgStockDtos()) {
                WmBillDetailEntity wmBillDetailEntity = new WmBillDetailEntity();
                wmBillDetailEntity.setArtId(dto.getArtId());
                wmBillDetailEntity.setBatchNo(dto.getBatchNo());
                wmBillDetailEntity.setStockNo(dto.getStockNo());
                wmBillDetailEntity.setTotalPacks(dto.getTotalPacks());
                wmBillDetailEntity.setTotalCells(dto.getTotalCells());
                OrgArtDto orgArtDto = orgArtService.findPrice(dto.getOrgId(), dto.getArtId(), dto.getStockNo(), orgCustMapEntity.getPricingMethod());
                wmBillDetailEntity.setPackPrice(orgArtDto.getPackPrice());
                wmBillDetailEntity.setCellPrice(orgArtDto.getCellPrice());

                ArtStocknoEntity artStocknoEntity = artStocknoService.findById(dto.getArtId(), dto.getStockNo());
                BigDecimal amount = BigDecimal.ZERO;
                BigDecimal cost = BigDecimal.ZERO;
                if (wmBillDetailEntity.getTotalPacks() != null) {
                    amount = wmBillDetailEntity.getPackPrice().multiply(new BigDecimal(wmBillDetailEntity.getTotalPacks()));
                    cost = artStocknoEntity.getPackPrice().multiply(new BigDecimal(wmBillDetailEntity.getTotalPacks()));
                }
                if (wmBillDetailEntity.getTotalCells() != null) {
                    amount = amount.add(wmBillDetailEntity.getCellPrice().multiply(wmBillDetailEntity.getTotalCells()));
                    cost = cost.add(artStocknoEntity.getCellPrice().multiply(wmBillDetailEntity.getTotalCells()));
                }
                wmBillDetailEntity.setAmount(amount);
                wmBillDetailEntity.setCost(cost);
                list.add(wmBillDetailEntity);
            }
            result.setWmBillDetailEntities(list);
        }
        return result;
    }

    /**
     * 发药底层
     *
     * @param orgId
     * @param userId
     * @param wbSeqid
     * @param date
     * @return
     */
    @Override
    @Transactional
    public List<WmBillDetailEntity> deliverBill(long orgId, long userId, Long wbSeqid, Date date, Integer clinicTypeId) {
        boolean deptStockEnabled = getDeptStockEnabled(orgId);
        WmBillEntity wmBill = this.getById(wbSeqid);
        if (!wmBill.getStatus().equals(WmBillStatus.WAIT_RECEIVE_MEDICINE.getValue())) {
            throw new SaveFailureException("出库单不是待领取状态");
        }
        if (clinicTypeId == null) {
            //病区发药，因病区发药本身有此类型，无须再次更改
            this.update(new LambdaUpdateWrapper<WmBillEntity>()
                    .eq(WmBillEntity::getWbSeqid, wbSeqid)
                    .set(WmBillEntity::getStatus, WmBillStatus.COMPLETED.getValue())
                    .set(WmBillEntity::getValidatorUid, userId)
                    .set(WmBillEntity::getTimeValidated, date)
            );
        } else {
            //门诊发药、住院发药
            this.update(new LambdaUpdateWrapper<WmBillEntity>()
                    .eq(WmBillEntity::getWbSeqid, wbSeqid)
                    .set(WmBillEntity::getStatus, WmBillStatus.COMPLETED.getValue())
                    .set(WmBillEntity::getValidatorUid, userId)
                    .set(WmBillEntity::getTimeValidated, date)
                    .set(WmBillEntity::getClinicTypeId, clinicTypeId)
            );
        }

        wmBillPendingService.removeById(wbSeqid);
        // 扣减库存
        List<WmBillDetailEntity> wmBillDetailLs = wmBillDetailService.list(new LambdaQueryWrapper<WmBillDetailEntity>()
                .eq(WmBillDetailEntity::getWbSeqid, wbSeqid));
        if (wmBillDetailLs.isEmpty()) {
            throw new SaveFailureException("未找到锁定的明细，不能发药");
        }
        List<Long> artIds = wmBillDetailLs.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleLs = articleService.listByIds(artIds);
        List<DeptArtEntity> deptArtLs = deptArtService.list(new LambdaQueryWrapper<DeptArtEntity>().eq(DeptArtEntity::getOrgId, wmBill.getOrgId())
                .eq(DeptArtEntity::getDeptCode, wmBill.getDeptCode()).in(DeptArtEntity::getArtId, artIds));
        List<OrgArtEntity> orgArtLs = orgArtService.list(new LambdaQueryWrapper<OrgArtEntity>().eq(OrgArtEntity::getOrgId, wmBill.getOrgId())
                .in(OrgArtEntity::getArtId, artIds));

        List<Long> wmReqids = wmBillDetailLs.stream().map(WmBillDetailEntity::getWmReqid).collect(Collectors.toList());
        List<WmReqEntity> wmReqEntities = null;
        if (!wmReqids.isEmpty()) {
            wmReqEntities = wmReqService.listByIds(wmReqids);
        }

        // todo 这里需要重构，checkSplittable是否校验拆零为true，根据deptArt.getSplittable()提交区分好是否允许拆零，根据artId汇总后提前将整包数、拆零数进行合计再进行出库，减少数据库操作次数
        for (WmBillDetailEntity detail : wmBillDetailLs) {
            ArticleEntity articleEntity = articleLs.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("未找到该药品。");
            }
            OrgArtEntity orgArt = orgArtLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            DeptArtEntity deptArt = deptArtLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            // 一次出库wmBillDetailEntities中的artId可能重复出多次出一次以后就清orgArtLs、deptArtLs里的数据
            orgArtLs = orgArtLs.stream().filter(p -> !p.getArtId().equals(detail.getArtId())).collect(Collectors.toList());
            deptArtLs = deptArtLs.stream().filter(p -> !p.getArtId().equals(detail.getArtId())).collect(Collectors.toList());

            if (deptStockEnabled) {
                deptArtService.stockDec(orgId, wmBill.getDeptCode(), articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), true, deptArt);
                deptArtService.subReservedCells(orgId, wmBill.getDeptCode(), articleEntity.getArtId(), ArticleUtil.packsToCells(detail.getTotalPacks(), articleEntity.getPackCells(), detail.getTotalCells()));
                orgArtService.stockDec(orgId, articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), orgArt);
            } else {
                orgArtService.stockDec(orgId, articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), orgArt);
            }
            boolean sectionDispenseFlag = false;
            if (detail.getWmReqid() != null) {
                WmReqEntity wmReqEntity = wmReqEntities.stream().filter(r -> r.getWmReqid().equals(detail.getWmReqid())).findFirst().orElse(null);
                if (wmReqEntity != null && wmReqEntity.getReqType().equals(WmReqType.SECTION_DISPENSE.getValue())) {
                    sectionDispenseFlag = true;
                }
            }
            if (wmBill.getSectionId() != null && !sectionDispenseFlag) {
                // 将库存加到病区库存中
                sectionArtService.stockInc(wmBill.getSectionId(), articleEntity, detail.getTotalPacks(), detail.getTotalCells());
            }
        }
        if (deptStockEnabled) {
            updateBillDetailCurStock(wmBill.getOrgId(), wmBill.getDeptCode(), wmBillDetailLs);
        }
        sectionConsume(userId, wmBill, wmBillDetailLs, articleLs);
        return wmBillDetailLs;
    }

    private void sectionConsume(long userId, WmBillEntity wmBill, List<WmBillDetailEntity> wmBillDetailLs, List<ArticleEntity> articleLs) {
        if (wmBill.getSectionId() != null && wmBill.getWmbillTypeId().equals(WmBillType.SALE_OUT.getValue()) && wmBill.getBsnType().equals(WmBillBsnType.SALE_OUT.getValue())) {
            List<SectionConsumeArtEntity> sectionConsumeArtLs = new ArrayList<>();
            wmBillDetailLs.stream().collect(Collectors.groupingBy(WmBillDetailEntity::getArtId)).forEach((artId, detailLs) -> {
                SectionConsumeArtEntity sectionConsumeArt = new SectionConsumeArtEntity();
                sectionConsumeArt.setArtId(artId);
                sectionConsumeArt.setTotalPacks(detailLs.stream().map(WmBillDetailEntity::getTotalPacks).filter(Objects::nonNull).reduce(0, Integer::sum));
                sectionConsumeArt.setTotalCells(detailLs.stream().map(WmBillDetailEntity::getTotalCells).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                sectionConsumeArt.setAmount(detailLs.stream().map(WmBillDetailEntity::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                sectionConsumeArt.setCost(detailLs.stream().map(WmBillDetailEntity::getCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                sectionConsumeArtLs.add(sectionConsumeArt);
            });
            sectionConsumeService.sectionConsume(wmBill.getSectionId(), WmBillType.SALE_OUT.getName(), userId, new Date(), wmBill.getOrgId(), wmBill.getAmount(), wmBill.getCost(),
                    articleLs, sectionConsumeArtLs);
        }
    }

    @Override
    @Transactional
    public List<WmBillDetailEntity> deliverBillTransfer(long orgId, long userId, Long wbSeqid, Date date, Integer clinicTypeId) {
        boolean deptStockEnabled = getDeptStockEnabled(orgId);
        WmBillEntity wmBill = this.getById(wbSeqid);
        if (!wmBill.getStatus().equals(WmBillStatus.WAIT_RECEIVE_MEDICINE.getValue())) {
            throw new SaveFailureException("出库单不是待领取状态");
        }
        //调拨单的clinicTypeId为null
        if (clinicTypeId == null) {
            this.update(new LambdaUpdateWrapper<WmBillEntity>()
                    .eq(WmBillEntity::getWbSeqid, wbSeqid)
                    .set(WmBillEntity::getStatus, WmBillStatus.COMPLETED.getValue())
                    .set(WmBillEntity::getValidatorUid, userId)
                    .set(WmBillEntity::getTimeValidated, date)
            );
        }

        wmBillPendingService.removeById(wbSeqid);
        // 扣减库存
        List<WmBillDetailEntity> wmBillDetailEntities = wmBillDetailService.list(new LambdaQueryWrapper<WmBillDetailEntity>()
                .eq(WmBillDetailEntity::getWbSeqid, wbSeqid));
        if (wmBillDetailEntities.isEmpty()) {
            throw new SaveFailureException("未找到调拨明细");
        }
        List<Long> artIds = wmBillDetailEntities.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
        List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
        List<DeptArtEntity> deptArtLs = deptArtService.list(new LambdaQueryWrapper<DeptArtEntity>().eq(DeptArtEntity::getOrgId, wmBill.getOrgId())
                .eq(DeptArtEntity::getDeptCode, wmBill.getDeptCode()).in(DeptArtEntity::getArtId, artIds));
        List<OrgArtEntity> orgArtLs = orgArtService.list(new LambdaQueryWrapper<OrgArtEntity>().eq(OrgArtEntity::getOrgId, wmBill.getOrgId())
                .in(OrgArtEntity::getArtId, artIds));

        // todo 这里需要重构，checkSplittable是否校验拆零为false，完全可以在这里对details根据artId汇总后合计数量再进行出库，减少数据库操作次数
        for (WmBillDetailEntity detail : wmBillDetailEntities) {
            ArticleEntity articleEntity = articleEntities.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            if (articleEntity == null) {
                throw new SaveFailureException("未找到该药品。");
            }
            OrgArtEntity orgArt = orgArtLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            DeptArtEntity deptArt = deptArtLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
            // 一次出库wmBillDetailEntities中的artId可能重复出多次出一次以后就清orgArtLs、deptArtLs里的数据
            orgArtLs = orgArtLs.stream().filter(p -> !p.getArtId().equals(detail.getArtId())).collect(Collectors.toList());
            deptArtLs = deptArtLs.stream().filter(p -> !p.getArtId().equals(detail.getArtId())).collect(Collectors.toList());

            if (deptStockEnabled) {
                //批次调拨 checkSplittable=false，不校验拆零合包，防止拆零数无法调拨问题
                deptArtService.stockDec(orgId, wmBill.getDeptCode(), articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), false, deptArt);
                deptArtService.subReservedCells(orgId, wmBill.getDeptCode(), articleEntity.getArtId(), ArticleUtil.packsToCells(detail.getTotalPacks(), articleEntity.getPackCells(), detail.getTotalCells()));
                orgArtService.stockDec(orgId, articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), orgArt);
            } else {
                orgArtService.stockDec(orgId, articleEntity.getArtId(), articleEntity.getPackCells(), detail.getTotalPacks(), detail.getTotalCells(), orgArt);
            }
        }
        if (deptStockEnabled) {
            updateBillDetailCurStock(orgId, wmBill.getDeptCode(), wmBillDetailEntities);
        }
        return wmBillDetailEntities;
    }

    @Override
    public void updateTrackcodeCollected(Long wbSeqid) {
        this.baseMapper.updateTrackcodeCollected(wbSeqid);
    }

    @Override
    public PageUtils queryCustBillCountPage(long orgId, Map<String, Object> params) {
        QueryWrapper<CustBillCountModel> wrapper = new GQueryWrapper<CustBillCountModel>().getWrapper(params);
        String deptCode = Convert.toStr(params.get("deptCode"));
        String startDate = Convert.toStr(params.get("startDate"));
        // 这里方法前端endDate已加1天，后端不需要处理
        String endDate = Convert.toStr(params.get("endDate"));
        IPage<CustBillCountModel> page = baseMapper.queryCustBillCountPage(orgId, deptCode, startDate, endDate, new Query<CustBillCountModel>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryCustBillArtPage(long orgId, Map<String, Object> params) {
        QueryWrapper<CustBillArtModel> wrapper = new GQueryWrapper<CustBillArtModel>().getWrapper(params);
        wrapper.eq("t_wm_bill.Org_ID", orgId);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }
        IPage<CustBillArtModel> page = baseMapper.queryCustBillArtPage(new Query<CustBillArtModel>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    @Override
    public List<CustBillArtModel> queryCustBillArtList(long orgId, Map<String, Object> params) {
        QueryWrapper<CustBillArtModel> wrapper = new GQueryWrapper<CustBillArtModel>().getWrapper(params);
        wrapper.eq("t_wm_bill.Org_ID", orgId);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }
        List<CustBillArtModel> list = baseMapper.queryCustBillArtPage(wrapper);
        return list;
    }

    @Override
    public PageUtils queryArtBillCountPage(long orgId, Map<String, Object> params) {
        QueryWrapper<ArtBillCountModel> wrapper = new GQueryWrapper<ArtBillCountModel>().getWrapper(params);
        String deptCode = Convert.toStr(params.get("deptCode"));
        String startDate = Convert.toStr(params.get("startDate"));
        String endDate = Convert.toStr(params.get("endDate"));
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }

        Map<String, Object> sumMap = baseMapper.queryArtBillCountSum(orgId, deptCode, startDate, endDate, wrapper);
        if (sumMap == null) {
            sumMap = new HashMap<>();
            sumMap.put("inStoreAmount", 0);//购入金额
            sumMap.put("transOutAmount", 0);//调出金额
            sumMap.put("saleAmount", 0);//消耗金额
            sumMap.put("lossAmount", 0);//损溢金额
        }
        wrapper.groupBy("t.Art_ID");
        IPage<ArtBillCountModel> page = baseMapper.queryArtBillCountPage(orgId, deptCode, startDate, endDate, new Query<ArtBillCountModel>().getPage(params), wrapper);
        return new PageUtils(page, sumMap);

    }

    /**
     * 商品业务统计查询-列表，不分页
     *
     * @param orgId
     * @param params
     * @return
     */
    @Override
    public List<ArtBillCountModel> queryArtBillCountList(long orgId, Map<String, Object> params) {
        QueryWrapper<ArtBillCountModel> wrapper = new GQueryWrapper<ArtBillCountModel>().getWrapper(params);
        String deptCode = Convert.toStr(params.get("deptCode"));
        String startDate = Convert.toStr(params.get("startDate"));
        String endDate = Convert.toStr(params.get("endDate"));
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }

        wrapper.groupBy("t.Art_ID");
        List<ArtBillCountModel> list = baseMapper.queryArtBillCountPage(orgId, deptCode, startDate, endDate, wrapper);
        return list;
    }

    @Override
    public PageUtils queryArtBillCountByBillPage(long orgId, Map<String, Object> params) {
        QueryWrapper<ArtBillCountBillModel> wrapper = new GQueryWrapper<ArtBillCountBillModel>().getWrapper(params);
        wrapper.eq("t_wm_bill.Org_ID", orgId);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        wrapper.eq("t_wm_bill.Dept_Code", Convert.toStr(params.get("deptCode")));
        IPage<ArtBillCountBillModel> page = baseMapper.queryArtBillCountByBillPage(new Query<ArtBillCountBillModel>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryArtBillCountByCustPage(long orgId, Map<String, Object> params) {
        QueryWrapper<ArtBillCountCustModel> wrapper = new GQueryWrapper<ArtBillCountCustModel>().getWrapper(params);
        wrapper.eq("t_wm_bill.Org_ID", orgId);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        wrapper.groupBy("t_wm_bill.Cust_ID");
        IPage<ArtBillCountCustModel> page = baseMapper.queryArtBillCountByCustPage(new Query<ArtBillCountCustModel>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryStoreBillCountPage(long orgId, Map<String, Object> params) {
        QueryWrapper<StoreBillCountModel> wrapper = new GQueryWrapper<StoreBillCountModel>().getWrapper(params);
        String startDate = Convert.toStr(params.get("startDate"));
        String endDate = Convert.toStr(params.get("endDate"));
        IPage<StoreBillCountModel> page = baseMapper.queryStoreBillCountPage(orgId, startDate, endDate, new Query<StoreBillCountModel>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryStoreBillArtPage(long orgId, Map<String, Object> params) {
        QueryWrapper<StoreBillArtModel> wrapper = new GQueryWrapper<StoreBillArtModel>().getWrapper(params);
        String deptCode = Convert.toStr(params.get("deptCode"));
        String startDate = Convert.toStr(params.get("startDate"));
        String endDate = Convert.toStr(params.get("endDate"));
        Integer bsnType = Convert.toInt(params.get("bsnType"));
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }
        Map<String, Object> sumMap = baseMapper.queryStoreBillArtSum(orgId, deptCode, startDate, endDate, bsnType, wrapper);
        if (sumMap == null) {
            sumMap = new HashMap<>();
            sumMap.put("Amount", 0);
        }


        wrapper.groupBy("t.Art_ID");
        IPage<StoreBillArtModel> page = baseMapper.queryStoreBillArtPage(orgId, deptCode, startDate, endDate, bsnType, new Query<StoreBillArtModel>().getPage(params), wrapper);
        return new PageUtils(page, sumMap);
    }

    @Override
    public List<StoreBillArtModel> queryStoreBillArtList(long orgId, Map<String, Object> params) {
        QueryWrapper<StoreBillArtModel> wrapper = new GQueryWrapper<StoreBillArtModel>().getWrapper(params);
        String deptCode = Convert.toStr(params.get("deptCode"));
        String startDate = Convert.toStr(params.get("startDate"));
        String endDate = Convert.toStr(params.get("endDate"));
        Integer bsnType = Convert.toInt(params.get("bsnType"));
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }
        wrapper.groupBy("t.Art_ID");
        List<StoreBillArtModel> list = baseMapper.queryStoreBillArtPage(orgId, deptCode, startDate, endDate, bsnType, wrapper);
        return list;
    }

    @Override
    public PageUtils querySectionBillCountPage(long orgId, Map<String, Object> params) {
        QueryWrapper<SectionBillCountModel> wrapper = new GQueryWrapper<SectionBillCountModel>().getWrapper(params);
        String startDate = Convert.toStr(params.get("startDate"));
        String endDate = Convert.toStr(params.get("endDate"));
        IPage<SectionBillCountModel> page = baseMapper.querySectionBillCountPage(orgId, startDate, endDate, new Query<SectionBillCountModel>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    @Override
    public PageUtils querySectionBillArtPage(long orgId, Map<String, Object> params) {
        QueryWrapper<SectionBillArtModel> wrapper = new GQueryWrapper<SectionBillArtModel>().getWrapper(params);
        String startDate = Convert.toStr(params.get("startDate"));
        String endDate = Convert.toStr(params.get("endDate"));
        Integer sectionId = Convert.toInt(params.get("sectionId"));
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }
        Map<String, Object> sumMap = baseMapper.querySectionBillArtSum(orgId, sectionId, startDate, endDate, wrapper);
        if (sumMap == null) {
            sumMap = new HashMap<>();
            sumMap.put("Cost", 0);
            sumMap.put("Amount", 0);
            sumMap.put("consume_cost", 0);
            sumMap.put("consume_amount", 0);
        }
        wrapper.groupBy("t.Art_ID");
        IPage<SectionBillArtModel> page = baseMapper.querySectionBillArtPage(orgId, sectionId, startDate, endDate, new Query<SectionBillArtModel>().getPage(params), wrapper);
        return new PageUtils(page, sumMap);
    }

    @Override
    public List<SectionBillArtModel> querySectionBillArtList(long orgId, Map<String, Object> params) {
        QueryWrapper<SectionBillArtModel> wrapper = new GQueryWrapper<SectionBillArtModel>().getWrapper(params);
        String startDate = Convert.toStr(params.get("startDate"));
        String endDate = Convert.toStr(params.get("endDate"));
        Integer sectionId = Convert.toInt(params.get("sectionId"));
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }
        wrapper.groupBy("t.Art_ID");
        List<SectionBillArtModel> list = baseMapper.querySectionBillArtPage(orgId, sectionId, startDate, endDate, wrapper);
        return list;
    }

    @Override
    public PageUtils queryTodayRecipeDeliverPage(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDto> wrapper = new GQueryWrapper<WmBillDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.Org_ID", orgId);
        wrapper.eq("t_wm_bill.dept_code", Convert.toStr(params.get("deptCode")));
        wrapper.eq("t_wm_bill.Bsn_Type", WmBillBsnType.SALE_OUT.getValue());
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
//        wrapper.ge("t_wm_bill.Time_Validated", DateUtil.today());
//        wrapper.lt("t_wm_bill.Time_Validated", DateUtil.formatDate(DateUtil.tomorrow()));

        Map<String, Object> sumMap = baseMapper.queryTodayRecipeDeliverSum(wrapper);
        if (sumMap == null) {
            sumMap = new HashMap<>();
            sumMap.put("Amount", 0);
        }

//        wrapper.groupBy("t_visit.Visit_ID, t_wm_bill.WMBill_Type_ID");
        IPage<WmBillDto> page = baseMapper.queryTodayRecipeDeliverPage(new Query<WmBillDto>().getPage(params), wrapper);
        return new PageUtils(page, sumMap);
    }

    @Override
    public void updateAmount(Long wbSeqid) {
        baseMapper.updateAmount(wbSeqid);
    }

    @Override
    public void updateCost(Long wbSeqid) {
        baseMapper.updateCost(wbSeqid);
    }

    @Override
    public PageUtils queryArtDeliverSumPage(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.Org_ID", orgId);
        wrapper.eq("t_wm_bill.dept_code", Convert.toStr(params.get("deptCode")));
        wrapper.eq("t_wm_bill.Bsn_Type", WmBillBsnType.SALE_OUT.getValue());
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }

        Map<String, Object> sumMap = baseMapper.queryArtDeliverSumPageSum(wrapper);
        if (sumMap == null) {
            sumMap = new HashMap<>();
            sumMap.put("Amount", 0);
            sumMap.put("Cost", 0);
        }

        wrapper.groupBy("t_wm_bill_detail.Art_ID");

        IPage<WmBillDetailDto> page = baseMapper.queryArtDeliverSumPage(new Query<WmBillDetailDto>().getPage(params), wrapper);
        PageUtils pageUtils = new PageUtils(page, sumMap);
        return pageUtils;
    }

    /**
     * 药品消耗情况列表-不分页
     *
     * @param orgId
     * @param params
     * @return
     */
    @Override
    public List<WmBillDetailDto> queryArtDeliverSumList(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.Org_ID", orgId);
        wrapper.eq("t_wm_bill.dept_code", Convert.toStr(params.get("deptCode")));
        wrapper.eq("t_wm_bill.Bsn_Type", WmBillBsnType.SALE_OUT.getValue());
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }

        wrapper.groupBy("t_wm_bill_detail.Art_ID");
        List<WmBillDetailDto> list = baseMapper.queryArtDeliverSumPage(wrapper);
        return list;
    }

    @Override
    public PageUtils queryArtDeliverSumDetailPage(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDetailDto> wrapper = new GQueryWrapper<WmBillDetailDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.Org_ID", orgId);
        wrapper.eq("t_wm_bill.dept_code", Convert.toStr(params.get("deptCode")));
        wrapper.eq("t_wm_bill.Bsn_Type", WmBillBsnType.SALE_OUT.getValue());
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword).or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword).or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }

        Map<String, Object> sumMap = baseMapper.queryArtDeliverSumDetailPageSum(wrapper);
        if (sumMap == null) {
            sumMap = new HashMap<>();
            sumMap.put("Amount", 0);
            sumMap.put("Cost", 0);
        }

        IPage<WmBillDetailDto> page = baseMapper.queryArtDeliverSumDetailPage(new Query<WmBillDetailDto>().getPage(params), wrapper);
        PageUtils pageUtils = new PageUtils(page, sumMap);
        return pageUtils;
    }


    @Override
    public PageUtils querySectionDeliveredPage(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDto> wrapper = new GQueryWrapper<WmBillDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        wrapper.eq("t_wm_bill.dept_code", deptCode);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        billWrapperFilterSectionBillType(wrapper);
        wrapper.isNotNull("t_wm_bill.Section_ID");
        List<Long> wbSeqIdList = Convert.toList(Long.class, params.get("wbSeqids"));
        // 去除首位和中间的空数字
        wbSeqIdList = wbSeqIdList.stream()
                .filter(Objects::nonNull)
                .filter(value -> value != 0)
                .collect(Collectors.toList());
        if (wbSeqIdList.isEmpty()) {
            return new PageUtils<>();
        }
        wrapper.in("t_wm_bill.WB_SeqID", wbSeqIdList);

//        wrapper.orderByDesc("t_wm_bill.Time_Validated");
        IPage<WmBillDto> page = this.baseMapper.querySectionDeliveredPage(new Query<WmBillDto>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    /**
     * 病区已发药列表-按发药记录-汇总
     *
     * @param orgId
     * @param params
     * @return
     */
    @Override
    public PageUtils querySectionDeliveredSummaryPage(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDto> wrapper = new GQueryWrapper<WmBillDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        wrapper.eq("t_wm_bill.dept_code", deptCode);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        billWrapperFilterSectionBillType(wrapper);
        wrapper.isNotNull("t_wm_bill.Section_ID");
        IPage<WmBillDto> page = this.baseMapper.querySectionDeliveredSummaryPage(new Query<WmBillDto>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    @Override
    public PageUtils querySectionDeliveredPageBySection(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDto> wrapper = new GQueryWrapper<WmBillDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        wrapper.eq("t_wm_bill.dept_code", deptCode);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        billWrapperFilterSectionBillType(wrapper);
        wrapper.isNotNull("t_wm_bill.Section_ID");
        wrapper.groupBy("t_wm_bill.Section_ID");
        IPage<WmBillDto> page = this.baseMapper.querySectionDeliveredPageBySection(new Query<WmBillDto>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    @Override
    public PageUtils querySectionDeliveredPageByPatient(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDto> wrapper = new GQueryWrapper<WmBillDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        wrapper.eq("t_wm_bill.dept_code", deptCode);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        billWrapperFilterSectionBillType(wrapper);
        wrapper.isNotNull("t_wm_bill.Section_ID");
        wrapper.isNotNull("t_wm_req_detail.Visit_ID");
        wrapper.groupBy("t_wm_bill.Section_ID, t_wm_req_detail.Visit_ID");
        IPage<WmBillDto> page = this.baseMapper.querySectionDeliveredPageByPatient(new Query<WmBillDto>().getPage(params), wrapper);
        return new PageUtils(page);
    }

    private static void billWrapperFilterSectionBillType(QueryWrapper<WmBillDto> wrapper) {
        wrapper.eq("t_wm_bill.WMBill_Type_ID", WmBillType.SALE_OUT.getValue());
        wrapper.in("t_wm_bill.Bsn_Type", WmBillBsnType.sectionOutLs);
    }

    /**
     * 病区-待退药-退药列表汇总
     *
     * @param orgId
     * @param params
     * @return
     */
    @Override
    public PageUtils returingMedicationSumPage(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDto> wrapper = new GQueryWrapper<WmBillDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        if (StrUtil.isNotBlank(deptCode)) {
            wrapper.eq("t_wm_bill.dept_code", deptCode);
        }
        Long artId = Convert.toLong(params.get("artId"));
        if (artId != null) {
            wrapper.exists("select 1 from microhis_clinics_wm.t_wm_bill_detail where t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID and t_wm_bill_detail.art_id = {0}", artId);
        }
        String custName = Convert.toStr(params.get("Cust_Name"), StrUtil.EMPTY).trim();
        if (StrUtil.isNotBlank(custName)) {
            wrapper.and(i -> i.or().like("t_scm_cust.Cust_Name", custName).or()
                    .like("Upper(t_scm_cust.QS_Code1)", custName.toUpperCase()).or().like("Upper(t_scm_cust.QS_Code2)", custName.toUpperCase()));
        }
        String sectionName = Convert.toStr(params.get("Section_Name"));
        if (StrUtil.isNotBlank(sectionName)) {
            wrapper.and(i -> i.like("UPPER(t_section.Section_Name)", sectionName));
        }
        wrapperFilterSectionTransferOut(wrapper);
        wrapper.isNotNull("t_wm_bill.Section_ID");
        wrapper.groupBy("t_wm_bill.Section_ID");

        IPage<WmBillDto> page = this.baseMapper.queryPendingBillSumPage(new Query<WmBillDto>().getPage(params), wrapper);
        for (WmBillDto dto : page.getRecords()) {
            dto.setStatusName(WmBillStatus.getName(dto.getStatus()));
            dto.setBsnTypeName(WmBillBsnType.getName(dto.getBsnType()));
            if (dto.getTrackcodeStatus() == null) {
                dto.setTrackcodeStatus(WmTrackCodeStatus.NOT_COLLECTED.getValue());
            }
            dto.setTrackcodeStatusName(WmTrackCodeStatus.getName(dto.getTrackcodeStatus()));
        }
        return new PageUtils(page);
    }

    /**
     * 病区-待退药/已退药-退药列表
     *
     * @param orgId
     * @param params
     * @return
     */
    @Override
    public PageUtils returnBillPage(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDto> wrapper = new GQueryWrapper<WmBillDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        if (StrUtil.isNotBlank(deptCode)) {
            wrapper.eq("t_wm_bill.dept_code", deptCode);
        }
        Long artId = Convert.toLong(params.get("artId"));
        if (artId != null) {
            wrapper.exists("select 1 from microhis_clinics_wm.t_wm_bill_detail where t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID and t_wm_bill_detail.art_id = {0}", artId);
        }

        wrapperFilterSectionTransferOut(wrapper);
        wrapper.isNotNull("t_wm_bill.Section_ID");

        List<Long> wbSeqIdList = Convert.toList(Long.class, params.get("wbSeqids"));
        // 去除首位和中间的空数字
        wbSeqIdList = wbSeqIdList.stream()
                .filter(Objects::nonNull)
                .filter(value -> value != 0)
                .collect(Collectors.toList());

        IPage<WmBillDto> page;
        Integer pendingFlag = Convert.toInt(params.get("pendingFlag"));
        if (pendingFlag != null && pendingFlag.equals(1)) {
            //查待退药
            if (wbSeqIdList.isEmpty()) {
                return new PageUtils<>();
            }
            wrapper.eq("t_wm_bill.Status", WmBillStatus.WAIT_RECEIVE.getValue());
            wrapper.in("t_wm_bill.WB_SeqID", wbSeqIdList);
            page = this.baseMapper.queryPendingBillSectionPage(new Query<WmBillDto>().getPage(params), wrapper);

        } else {
            //查已退药
            wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
            wrapper.groupBy("t_wm_bill.Time_Validated");
            page = this.baseMapper.queryReturnedBillSectionPage(new Query<WmBillDto>().getPage(params), wrapper);
        }

        for (WmBillDto dto : page.getRecords()) {
            dto.setStatusName(WmBillStatus.getName(dto.getStatus()));
            dto.setBsnTypeName(WmBillBsnType.getName(dto.getBsnType()));
        }

        return new PageUtils(page);
    }

    /**
     * 病区-已退药-退药记录-按病区汇总
     *
     * @param orgId
     * @param params
     * @return
     */
    @Override
    public PageUtils returnedBillBySectionPage(long orgId, Map<String, Object> params) {
        QueryWrapper<WmBillDto> wrapper = new GQueryWrapper<WmBillDto>().getWrapper(params);
        wrapper.eq("t_wm_bill.org_id", orgId);
        String deptCode = Convert.toStr(params.get("deptCode"));
        if (StrUtil.isNotBlank(deptCode)) {
            wrapper.eq("t_wm_bill.dept_code", deptCode);
        }
        Long artId = Convert.toLong(params.get("artId"));
        if (artId != null) {
            wrapper.exists("select 1 from microhis_clinics_wm.t_wm_bill_detail where t_wm_bill_detail.WB_SeqID = t_wm_bill.WB_SeqID and t_wm_bill_detail.art_id = {0}", artId);
        }

        wrapperFilterSectionTransferOut(wrapper);
        wrapper.eq("t_wm_bill.Status", WmBillStatus.COMPLETED.getValue());
        wrapper.isNotNull("t_wm_bill.Section_ID");
        wrapper.groupBy("t_wm_bill.Section_ID");


        IPage<WmBillDto> page = this.baseMapper.queryReturnedBillSectionPage(new Query<WmBillDto>().getPage(params), wrapper);
        for (WmBillDto dto : page.getRecords()) {
            dto.setStatusName(WmBillStatus.getName(dto.getStatus()));
            dto.setBsnTypeName(WmBillBsnType.getName(dto.getBsnType()));
        }
        return new PageUtils(page);
    }

    private static void wrapperFilterSectionTransferOut(QueryWrapper<WmBillDto> wrapper) {
        wrapper.eq("t_wm_bill.WMBill_Type_ID", WmBillType.TRANSFER_OUT_CANCEL.getValue());
        wrapper.eq("t_wm_bill.Bsn_Type", WmBillBsnType.INVENTORY_TRANSFER.getValue());
    }
    public void performSplitPack(long orgId, Map<String, Object> params) {
        String record = Convert.toStr(params.get("record"));
        JSONObject objectMapper = new JSONObject();
        Object json = objectMapper.getJSONArray(record);
        Map<String, Object> recordMap = new HashMap<>();
    }

}
