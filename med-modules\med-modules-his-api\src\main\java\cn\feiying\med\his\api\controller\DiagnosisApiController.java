package cn.feiying.med.his.api.controller;

import cn.feiying.med.his.api.model.req.ApiReqModel;
import cn.feiying.med.his.api.model.req.diagnosis.DiagnosisModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.DiagnosisRespModel;
import cn.feiying.med.his.api.service.DiagnosisApiService;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 诊断相关API控制器
 */
@Slf4j
@RestController
@RequestMapping("/his/api/diagnosis")
public class DiagnosisApiController extends BaseApiController {
    
    @Resource
    private DiagnosisApiService diagnosisApiService;
    
    /**
     * 诊断回写
     */
    @PostMapping("/saveOrUpdate")
    public ApiResultModel<List<DiagnosisRespModel>> save(HttpServletRequest request, @RequestBody ApiReqModel apiReq) {
        try {
            // 获取请求参数
            log.debug("接收到诊断回写请求");
            List<DiagnosisModel> diagnostisList = decryptRequest(request, apiReq, new TypeReference<List<DiagnosisModel>>() {});
            log.info("诊断回写请求: {}", diagnostisList);

            // 调用服务处理业务逻辑
            return diagnosisApiService.saveOrUpdateDiagnosis(diagnostisList);
        } catch (Exception e) {
            log.error("诊断回写失败", e);
            return ApiResultModel.error("诊断回写失败: " + e.getMessage());
        }
    }
    
    /**
     * 诊断删除
     */
    @PostMapping("/delete")
    public ApiResultModel<?> deleteDiagnosis(HttpServletRequest request, @RequestBody ApiReqModel apiReq) {
        try {
            // 解密请求数据
            List<DiagnosisModel> diagnosisList = decryptRequest(request, apiReq, new TypeReference<List<DiagnosisModel>>() {});
            log.info("诊断删除请求: {}", diagnosisList);
            
            // 调用服务处理业务逻辑
            return diagnosisApiService.deleteDiagnosis(diagnosisList);
        } catch (Exception e) {
            log.error("诊断删除失败", e);
            return ApiResultModel.error("诊断删除失败: " + e.getMessage());
        }
    }
} 