package cn.feiying.med.clinics_wm.service.impl;

import cn.feiying.med.clinics_wm.dao.OrgArtDao;
import cn.feiying.med.clinics_wm.dto.*;
import cn.feiying.med.clinics_wm.entity.*;
import cn.feiying.med.clinics_wm.enums.PricingMethod;
import cn.feiying.med.clinics_wm.enums.ReservedFlag;
import cn.feiying.med.clinics_wm.model.ArtPriceModel;
import cn.feiying.med.clinics_wm.model.ArtStockReserveResult;
import cn.feiying.med.clinics_wm.service.*;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.*;
import cn.feiying.med.hip.enums.ArtCatType;
import cn.feiying.med.hip.enums.ArtType;
import cn.feiying.med.hip.mdi.entity.ArticleEntity;
import cn.feiying.med.hip.mdi.service.ArticleService;
import cn.feiying.med.hip.mdi.service.OrgArtTypeService;
import cn.feiying.med.hip.model.hsd.SectionArtVo;
import cn.feiying.med.saas.api.service.RemoteInpatientHsdService;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 机构商品表
 *
 * <AUTHOR> 18:48:02
 */
@Slf4j
@Service("orgArtService")
public class OrgArtServiceImpl extends ServiceImpl<OrgArtDao, OrgArtEntity> implements OrgArtService {
    @Resource
    private OrgStockService orgStockService;
    @Resource
    private OrgCustMapService orgCustMapService;
    @Resource
    private ArtStocknoService artStocknoService;
    @Resource
    private RemoteInpatientHsdService remoteInpatientHsdService;
    @Resource
    private ArticleService articleService;
    @Resource
    private OrgArtTypeService orgArtTypeService;
    @Resource
    private WmBillDetailService wmBillDetailService;
    @Resource
    private ArtTrackcodeProdService artTrackcodeProdService;

    private QueryWrapper<OrgArtDto> buildQueryDtoWrapper(Long orgId, Map<String, Object> params) {
        QueryWrapper<OrgArtDto> wrapper = new GQueryWrapper<OrgArtDto>().getWrapper(params);
        wrapper.eq("t_org_art.Org_ID", orgId);
        wrapper.eq("t_article.Stock_Req", 1); // 药房药库只显示需要管理库存的品种
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword).or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword.toUpperCase())
                    .or().like("UPPER(t_article.QS_Code2)", keyword.toUpperCase()));
        }
        String artName = Convert.toStr(params.get("artName"), StrUtil.EMPTY).trim().toUpperCase();
        if (StringUtil.isNotEmpty(artName)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", artName).or().like("UPPER(t_article.Art_Name)", artName)
                    .or().like("UPPER(t_article.QS_Code1)", artName).or().like("UPPER(t_article.QS_Code2)", artName)
                    .or().like("UPPER(t_article.CDAN_Name)", artName).or().like("UPPER(t_article.MI_Code)", artName)
                    .or().like("UPPER(t_article.YPID_Code)", artName).or().like("UPPER(t_medicine.Chn_Name)", artName)
                    .or().like("UPPER(t_medicine.Eng_Name)", artName).or().like("UPPER(t_medicine.Chm_Name)", artName)
                    .or().like("UPPER(t_medicine.Med_Code)", artName).or().like("UPPER(t_medicine.QS_Code1)", artName)
                    .or().like("UPPER(t_medicine.QS_Code2)", artName));
        }
        if (params.containsKey("artIds")) {
            wrapper.in("t_article.Art_ID", Convert.toLongArray(params.get("artIds")));
        }
        // 禁用状态
        Integer disabled = Convert.toInt(params.get("disabled"));
        if (disabled != null) {
            wrapper.apply("IFNULL(t_article.Disabled, 0) = {0}", disabled);
            wrapper.apply("IFNULL(t_org_formulary.Disabled, 0) = {0}", disabled);
        } else {
            wrapper.eq("ifnull(t_article.Disabled,0)", 0);
        }
        // 查询是否在机构处方集里面
        Integer isFormulary = Convert.toInt(params.get("isFormulary"), 0);
        if (isFormulary == 1) {
            wrapper.eq("t_org_formulary.Org_ID", orgId);
        }
        Integer forOutpatient = Convert.toInt(params.get("forOutpatient"));
        if (forOutpatient != null) {
            wrapper.and(p -> p.isNull("t_org_formulary.For_Outpatient").or().eq("t_org_formulary.For_Outpatient", forOutpatient));
        }
        Integer forInpatient = Convert.toInt(params.get("forInpatient"));
        if (forInpatient != null) {
            wrapper.and(p -> p.isNull("t_org_formulary.For_Inpatient").or().eq("t_org_formulary.For_Inpatient", forInpatient));
        }
        Integer genderId = Convert.toInt(params.get("genderId"));
        if (genderId != null) {
            String sql = "(t_org_formulary.Allow_Gender is null OR t_org_formulary.Allow_Gender = 'A'";
            if (genderId == 1) {
                sql += " OR t_org_formulary.Allow_Gender = 'M'";
            } else if (genderId == 2) {
                sql += " OR t_org_formulary.Allow_Gender = 'F'";
            }
            sql += ")";
            wrapper.apply(sql);
        }
        String deptCode = Convert.toStr(params.get("deptCode"), StrUtil.EMPTY).trim();
        if (StringUtil.isNotEmpty(deptCode)) {
            wrapper.apply("(t_org_formulary.Include_Depts is null OR CONCAT(',', t_org_formulary.Include_Depts, ',') REGEXP CONCAT(',', '" + deptCode + "', ','))");
            wrapper.apply("(t_org_formulary.Exclude_Depts is null OR CONCAT(',', t_org_formulary.Exclude_Depts, ',') NOT REGEXP CONCAT(',', '" + deptCode + "', ','))");
        }
        Integer forOe = Convert.toInt(params.get("forOe"));
        if (forOe != null) {
            wrapper.apply("IFNULL(t_article.For_Oe, 0) = {0}", forOe);
        }
        Integer forDrug = Convert.toInt(params.get("forDrug"));
        if (forDrug != null && forDrug == 1) {
            wrapper.and(p -> p.in("t_article.Art_Type_ID", ArtType.MoeDrug.getValue(), ArtType.MoeChineseDrug.getValue())
                    .or().eq("t_article.Cat_Type_ID", ArtCatType.CONTRACT_PRESCRIPTION.getValue()));
        }
        //默认设置条目亚类，查条目亚类不为空的数据
        int noArtSubtype = Convert.toInt(params.get("noArtsubtype"), 0);
        if (noArtSubtype == 1) {
            wrapper.isNull("t_article.Subtype_ID");
        }
        int noTrackCode = Convert.toInt(params.get("noTrackCode"), 0);
        if (noTrackCode == 1) {
            wrapper.eq("t_article.No_Track_Code", 1);
        }
        int isDisassembled = Convert.toInt(params.get("isDisassembled"), 0);
        if (isDisassembled == 1) {
            wrapper.eq("t_article.is_Disassembled", 1);
        }

        return wrapper;
    }

    private void buildDtoList(List<OrgArtDto> list) {
        list.forEach(item -> {
            buildDto(item);
        });
    }

    private void buildDto(OrgArtDto item) {
        if (item.getPackCells() == null) {
            item.setPackCells(1);
        }
        if (item.getTotalPacks() == null) {
            item.setTotalPacks(0);
        }
        if (item.getTotalCells() == null) {
            item.setTotalCells(BigDecimal.ZERO);
        }
        if (item.getLastRestPacks() != null && !item.getLastRestPacks().equals(0)) {
            // 已用包装数
            BigDecimal usedPacks = BigDecimal.valueOf(item.getLastRestPacks()).subtract(BigDecimal.valueOf(item.getTotalPacks()));
            if (usedPacks.compareTo(BigDecimal.ZERO) > 0) {
                // 动销率=(最近采购后存货包装数-存货整包数量)/最近采购后存货包装数 * 100%
                item.setDxl(usedPacks.divide(BigDecimal.valueOf(item.getLastRestPacks()), 2, BigDecimal.ROUND_DOWN).multiply(BigDecimal.valueOf(100)));
                // 日均消耗=(最近采购后存货包装数-存货整包数量)/(当前时间-最近采购时间的间隔天数)
                long days = DateUtil.betweenDay(new Date(), item.getLastPurchased(), true);
                if (days <= 0) {
                    days = 1;
                }
                item.setUsedByDay(usedPacks.divide(BigDecimal.valueOf(days), 2, BigDecimal.ROUND_DOWN));
                // 预计可用天数=存货整包数量/日均消耗
                if (Convert.toBigDecimal(item.getUsedByDay(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) != 0) {
                    item.setCanUseDays(BigDecimal.valueOf(item.getTotalPacks()).divide(item.getUsedByDay(), 0, BigDecimal.ROUND_DOWN).intValue());
                }
            }
        }
    }

    @Override
    public PageUtils queryPage(Long orgId, Map<String, Object> params) {
        QueryWrapper<OrgArtDto> wrapper = buildQueryDtoWrapper(orgId, params);
        IPage<OrgArtDto> page = this.baseMapper.queryDtoPage(new Query<OrgArtDto>().getPage(params), wrapper);
        buildDtoList(page.getRecords());
        Integer sectionId = Convert.toInt(params.get("sectionId"));
        if (sectionId != null && !page.getRecords().isEmpty()) {
            List<Long> artIds = page.getRecords().stream().map(p -> p.getArtId()).collect(Collectors.toList());
            List<SectionArtVo> sectionArtEntities = remoteInpatientHsdService.sectionArt(sectionId, artIds);
//            List<SectionArtEntity> sectionArtEntities = sectionArtService.list(new LambdaQueryWrapper<SectionArtEntity>()
//                    .eq(SectionArtEntity::getSectionId, sectionId)
//                    .in(SectionArtEntity::getArtId, artIds)
//            );
            page.getRecords().forEach(item -> {
                SectionArtVo sectionArtEntity = sectionArtEntities.stream().filter(p -> p.getArtId().equals(item.getArtId())).findFirst().orElse(null);
                if (sectionArtEntity != null) {
                    item.setSectionTotalPacks(sectionArtEntity.getTotalPacks());
                    item.setSectionTotalCells(sectionArtEntity.getTotalCells());
                }
            });
        }
        setDetailArtProdNo(page.getRecords());
        return new PageUtils(page);
    }

    private void setDetailArtProdNo(List<OrgArtDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<Long> artIds = list.stream().map(OrgArtDto::getArtId).distinct().collect(Collectors.toList());
            List<ArtTrackcodeProdEntity> artTrackcodeProdLs = artTrackcodeProdService.list(new LambdaQueryWrapper<ArtTrackcodeProdEntity>().in(ArtTrackcodeProdEntity::getArtId, artIds));
            list.forEach(dto -> {
                List<Integer> prodNoLs = artTrackcodeProdLs.stream().filter(p->p.getArtId().equals(dto.getArtId())).map(ArtTrackcodeProdEntity::getProdNo).collect(Collectors.toList());
                dto.setProdNoLs(prodNoLs);
            });
        }
    }

    @Override
    public List<OrgArtDto> queryList(Long orgId, Map<String, Object> params) {
        QueryWrapper<OrgArtDto> wrapper = buildQueryDtoWrapper(orgId, params);
        List<OrgArtDto> list = baseMapper.queryDtoPage(wrapper);
        buildDtoList(list);
        return list;
    }

    @Override
    public void saveEntity(OrgArtEntity entity) {
        ArticleEntity articleEntity = articleService.getById(entity.getArtId());
        if (articleEntity.getPackCells() == null || articleEntity.getPackCells().equals(1)) {
            entity.setSplittable(0);
        }
        save(entity);
    }

    @Override
    @Transactional
    public void updateEntity(long orgId, OrgArtDto params) {
        ArticleEntity articleEntity = articleService.getById(params.getArtId());
        if (articleEntity.getPackCells() == null || articleEntity.getPackCells().equals(1)) {
            params.setSplittable(0);
        }
        this.update(new LambdaUpdateWrapper<OrgArtEntity>()
                .eq(OrgArtEntity::getOrgId, orgId)
                .eq(OrgArtEntity::getArtId, params.getArtId())
                .set(OrgArtEntity::getRackNo, params.getRackNo())
                .set(OrgArtEntity::getPackPrice, params.getPackPrice())
                .set(OrgArtEntity::getCellPrice, params.getCellPrice())
                .set(OrgArtEntity::getSafePacks, params.getSafePacks())
                .set(OrgArtEntity::getPctAdd, params.getPctAdd())
                .set(OrgArtEntity::getSplittable, params.getSplittable())
                .set(OrgArtEntity::getPurchaseDisabled, params.getPurchaseDisabled())
                .set(OrgArtEntity::getSaleDisabled, params.getSaleDisabled())
        );
        // 默认将所有仓库的拆零属性一起设置
//        this.deptArtService.update(new LambdaUpdateWrapper<DeptArtEntity>()
//                .eq(DeptArtEntity::getOrgId, orgId)
//                .eq(DeptArtEntity::getArtId, params.getArtId())
//                .set(DeptArtEntity::getSplittable, params.getSplittable())
//        );
        this.baseMapper.updateSplittable(orgId, params.getArtId(), params.getSplittable());

        //如果条目亚类有更改，需要更新t_article
        if (!Objects.equals(articleEntity.getSubtypeId(), params.getSubtypeId())) {
            articleService.update(new LambdaUpdateWrapper<ArticleEntity>()
            .eq(ArticleEntity::getArtId,params.getArtId())
            .set(ArticleEntity::getSubtypeId,params.getSubtypeId()));
        }
        updateArtInfo(params.getArtId(), params.getNoTrackCode(), params.getIsDisassembled());
    }

    private void updateArtInfo(Long artId, Integer noTrackCode, Integer isDisassembled) {
        articleService.update(new LambdaUpdateWrapper<ArticleEntity>()
                .eq(ArticleEntity::getArtId, artId)
                .set(ArticleEntity::getNoTrackCode, Convert.toInt(noTrackCode, 0) == 1 ? 1 : null)
                .set(ArticleEntity::getIsDisassembled, Convert.toInt(isDisassembled, 0) == 1 ? 1 : null));
    }

    @Override
    public void batchUpdatePctAdd(long orgId, Map<String, Object> params) {
        QueryWrapper<OrgArtDto> wrapper = buildQueryDtoWrapper(orgId, params);
        BigDecimal pctAdd = Convert.toBigDecimal(params.get("pctAdd"));
        baseMapper.batchUpdatePctAdd(wrapper, pctAdd);
    }

    @Override
    public OrgArtEntity findById(Long orgId, Long artId) {
        return getOne(new LambdaQueryWrapper<OrgArtEntity>()
                .eq(OrgArtEntity::getOrgId, orgId)
                .eq(OrgArtEntity::getArtId, artId)
        );
    }

    @Override
    public OrgArtDto findDtoById(Long orgId, Long artId) {
        return baseMapper.findDtoById(orgId, artId);
    }


    //    @Override
//    public OrgArtDto findPrice(long orgId, long artId, Integer stockNo) {
//        OrgCustMapEntity orgCustMapEntity = orgCustMapService.findById(orgId);
//        return findPrice(orgId, artId, stockNo, orgCustMapEntity.getPricingMethod());
//    }
    @Override
    public OrgArtDto findPrice(long orgId, long artId, Integer stockNo, Integer pricingMethod) {
        OrgArtDto orgArtDto = findDtoById(orgId, artId);
        if (orgArtDto == null) {
            return null;
        }
        BigDecimal costPackPrice = null;
        BigDecimal costCellPrice = null;
        BigDecimal pctAdd = null;
        if (pricingMethod == PricingMethod.PCT_ADD.getValue()) {
            ArtStocknoEntity artStocknoEntity = artStocknoService.findById(orgArtDto.getArtId(), stockNo);
            if (artStocknoEntity == null) {
                throw new SaveFailureException("未找到商品批次数据");
            }
            costPackPrice = artStocknoEntity.getPackPrice();
            costCellPrice = artStocknoEntity.getCellPrice();
            pctAdd = orgArtDto.getPctAdd();
        }
        ArtPriceModel artPriceModel = findPrice(pricingMethod, orgArtDto.getPackCells(),
                orgArtDto.getPackPrice(), orgArtDto.getCellPrice(), orgArtDto.getLastBuyPrice(),
                costPackPrice, costCellPrice, pctAdd);
        orgArtDto.setPackPrice(artPriceModel.getPackPrice());
        orgArtDto.setCellPrice(artPriceModel.getCellPrice());
        return orgArtDto;
    }

    @Override
    public ArtPriceModel findPrice(Integer pricingMethod, Integer packCells,
                                   BigDecimal basePackPrice, BigDecimal baseCellPrice, BigDecimal lastBuyPrice,
                                   BigDecimal costPackPrice, BigDecimal costCellPrice, BigDecimal pctAdd) {
        ArtPriceModel artPriceModel = new ArtPriceModel();

        // 获取商品拆零系数
        if (packCells == null) {
            packCells = 1;
        }
//        出库计价方式：
//        1-核定售价：按机构商品表销售单价计价。
//        2-批次加成：按批次成本加成计价。
//        3-最小损失：按机构商品表销售单价和最近采购单价中的较高者计价。
        if (pricingMethod == null || pricingMethod == PricingMethod.FIXED_PRICE.getValue()) {
            if (basePackPrice == null) {
                throw new SaveFailureException("核定售价下机构商品中未设置销售单价");
            }
            if (baseCellPrice == null) {
                baseCellPrice = basePackPrice.divide(BigDecimal.valueOf(packCells), 6, BigDecimal.ROUND_HALF_UP);
            }
            artPriceModel.setPackPrice(basePackPrice);
            artPriceModel.setCellPrice(baseCellPrice);
        } else if (pricingMethod == PricingMethod.PCT_ADD.getValue()) {
            if (costCellPrice == null) {
                costCellPrice = costPackPrice.divide(BigDecimal.valueOf(packCells), 6, BigDecimal.ROUND_HALF_UP);
            }
            if (pctAdd == null) {
                artPriceModel.setPackPrice(costPackPrice);
                artPriceModel.setCellPrice(costCellPrice);
            } else {
                artPriceModel.setPackPrice(costPackPrice.multiply(pctAdd.divide(BigDecimal.valueOf(100)).add(BigDecimal.ONE)));
                artPriceModel.setCellPrice(costCellPrice.multiply(pctAdd.divide(BigDecimal.valueOf(100)).add(BigDecimal.ONE)));
            }
        } else if (pricingMethod == PricingMethod.MIN_LOSS.getValue()) {
            if (lastBuyPrice == null) {
                lastBuyPrice = BigDecimal.ZERO;
            }
            if (basePackPrice.compareTo(lastBuyPrice) < 0) {
                basePackPrice = lastBuyPrice;
            }
            BigDecimal cellPrice = basePackPrice.divide(BigDecimal.valueOf(packCells), 6, RoundingMode.HALF_UP);
            artPriceModel.setPackPrice(basePackPrice);
            artPriceModel.setCellPrice(cellPrice);
        } else {
            throw new SaveFailureException("药品不支持该定价策略");
        }
        return artPriceModel;
    }

    @Override
    public void purchaseIn(Long orgId, ArticleEntity articleEntity, BigDecimal packPrice, Integer totalPacks) {
        OrgArtEntity orgArtEntity = findById(orgId, articleEntity.getArtId());
        if (orgArtEntity == null) {
            orgArtEntity = new OrgArtEntity();
            orgArtEntity.setOrgId(orgId);
            orgArtEntity.setArtId(articleEntity.getArtId());
            orgArtEntity.setPackPrice(packPrice);
            orgArtEntity.setCellPrice(ArticleUtil.packPriceToCellPrice(packPrice, articleEntity.getPackCells()));
            this.saveEntity(orgArtEntity);
        }
        if (orgArtEntity.getPurchaseDisabled() != null && orgArtEntity.getPurchaseDisabled().equals(1)) {
            throw new SaveFailureException("该商品禁止采购");
        }
        if (orgArtEntity.getPackPrice() == null) {
            this.update(new LambdaUpdateWrapper<OrgArtEntity>()
                    .eq(OrgArtEntity::getOrgId, orgId)
                    .eq(OrgArtEntity::getArtId, articleEntity.getArtId())
                    .set(OrgArtEntity::getPackPrice, packPrice)
                    .set(OrgArtEntity::getCellPrice, ArticleUtil.packPriceToCellPrice(packPrice, articleEntity.getPackCells()))
            );
        }
        this.baseMapper.updatePurchaseIn(orgId, articleEntity.getArtId(), new Date(), packPrice, totalPacks);
    }

    @Override
    public void stockInc(Long orgId, Long artId, Integer totalPacks, BigDecimal totalCells) {
        OrgArtEntity orgArtEntity = findById(orgId, artId);
        if (orgArtEntity == null) {
            orgArtEntity = new OrgArtEntity();
            orgArtEntity.setOrgId(orgId);
            orgArtEntity.setArtId(artId);
            orgArtEntity.setTotalPacks(totalPacks);
            orgArtEntity.setTotalCells(totalCells);
            this.saveEntity(orgArtEntity);
        } else {
            this.baseMapper.stockInc(orgId, artId, totalPacks, totalCells);
        }
    }

    @Override
    public void batchStockInc(Long orgId, Long artId, Integer stockNo, String batchNo, Integer totalPacks, BigDecimal totalCells, Integer dateManufactured, Integer expiry) {
        // 增加批号库存
        orgStockService.batchStockInc(orgId, artId, stockNo, batchNo, totalPacks, totalCells, dateManufactured, expiry);
    }

    @Override
    public void stockDec(Long orgId, Long artId, Integer packCells, Integer totalPacks, BigDecimal totalCells, OrgArtEntity initOrgArt) {
        log.debug("orgArt stockDec orgId:{}, artId:{}, packCells:{}, totalPacks:{}, totalCells:{}", orgId, artId, packCells, totalPacks, totalCells);
        totalPacks = Math.abs(Convert.toInt(totalPacks, 0));
        totalCells = Convert.toBigDecimal(totalCells, BigDecimal.ZERO).abs();
        packCells = Convert.toInt(packCells, 1);

        // 先将拆零数取出整包数量
        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && totalCells.compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            int tempPacks = totalCells.divide(BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue();
            totalPacks = totalPacks + tempPacks;
            totalCells = totalCells.subtract(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempPacks)));
        }

        OrgArtEntity orgArtEntity = initOrgArt != null ? initOrgArt : findById(orgId, artId);
        if (orgArtEntity == null) {
            orgArtEntity = new OrgArtEntity();
            orgArtEntity.setOrgId(orgId);
            orgArtEntity.setArtId(artId);
            this.saveEntity(orgArtEntity);
        }
//        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && Convert.toInt(orgArtEntity.getSplittable(), 0) == 0) { // 不允许拆零
//            totalCells = BigDecimal.ZERO;
//            totalPacks = totalPacks + 1;
//        }

        if (orgArtEntity.getTotalCells() == null) {
            orgArtEntity.setTotalCells(BigDecimal.ZERO);
        }
        if (orgArtEntity.getTotalPacks() == null) {
            orgArtEntity.setTotalPacks(0);
        }

        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && totalCells.compareTo(orgArtEntity.getTotalCells()) > 0) { // 需求要拆零，拆零数量缺少
            totalPacks = totalPacks + 1; // 整包数量+1
            BigDecimal tempCells = BigDecimal.valueOf(packCells).subtract(totalCells); // 剩余数量
            totalCells = tempCells.negate();
            orgArtEntity.setTotalCells(orgArtEntity.getTotalCells().subtract(totalCells));
        }

        int needPacks = totalPacks - orgArtEntity.getTotalPacks();
        // 整包数不够，从拆零数量中锁定
        if (totalPacks > 0 && needPacks > 0 && orgArtEntity.getTotalCells().compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            int tempPacks = orgArtEntity.getTotalCells().divide(BigDecimal.valueOf(packCells), 0, RoundingMode.DOWN).intValue();
            if (needPacks <= tempPacks) {
                totalPacks = totalPacks - needPacks;
                totalCells = totalCells.add(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(needPacks)));
            } else {
                totalPacks = totalPacks - tempPacks;
                totalCells = totalCells.add(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempPacks)));
            }
        }

        this.baseMapper.stockDec(orgId, artId, totalPacks, totalCells);
    }

    /**
     * 减少t_org_art库存，返回更新条数
     * @param orgId
     * @param articleEntity
     * @param totalPacks
     * @param totalCells
     * @return
     */
    @Override
    public int stockDecCount(Long orgId, ArticleEntity articleEntity, Integer totalPacks, BigDecimal totalCells) {
        if (totalPacks != null) {
            totalPacks = Math.abs(totalPacks);
        } else {
            totalPacks = 0;
        }
        if (totalCells != null) {
            totalCells = totalCells.abs();
        } else {
            totalCells = BigDecimal.ZERO;
        }
        // 获取商品拆零系数
        Integer packCells = 1;
        if (articleEntity.getPackCells() != null) {
            packCells = articleEntity.getPackCells();
        }

        // 先将拆零数取出整包数量
        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && totalCells.compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            int tempPacks = totalCells.divide(BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue();
            totalPacks = totalPacks + tempPacks;
            totalCells = totalCells.subtract(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempPacks)));
        }

        OrgArtEntity orgArtEntity = findById(orgId, articleEntity.getArtId());
        if (orgArtEntity == null) {
            orgArtEntity = new OrgArtEntity();
            orgArtEntity.setOrgId(orgId);
            orgArtEntity.setArtId(articleEntity.getArtId());
            this.saveEntity(orgArtEntity);
        }

        if (orgArtEntity.getTotalCells() == null) {
            orgArtEntity.setTotalCells(BigDecimal.ZERO);
        }
        if (orgArtEntity.getTotalPacks() == null) {
            orgArtEntity.setTotalPacks(0);
        }

        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && totalCells.compareTo(orgArtEntity.getTotalCells()) > 0) { // 需求要拆零，拆零数量缺少
            totalPacks = totalPacks + 1; // 整包数量+1
            BigDecimal tempCells = BigDecimal.valueOf(packCells).subtract(totalCells); // 剩余数量
            totalCells = tempCells.negate();
            orgArtEntity.setTotalCells(orgArtEntity.getTotalCells().subtract(totalCells));
        }

        int needPacks = totalPacks - orgArtEntity.getTotalPacks();
        // 整包数不够，从拆零数量中锁定
        if (totalPacks > 0 && needPacks > 0 && orgArtEntity.getTotalCells().compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            int tempPacks = orgArtEntity.getTotalCells().divide(BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue();
            if (needPacks <= tempPacks) {
                totalPacks = totalPacks - needPacks;
                totalCells = totalCells.add(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(needPacks)));
            } else {
                totalPacks = totalPacks - tempPacks;
                totalCells = totalCells.add(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempPacks)));
            }
        }
        int count = this.baseMapper.stockDecCount(orgId, articleEntity.getArtId(), totalPacks, totalCells);
        return count;
    }

    @Override
    public ArtStockReserveResult batchStockDec(Long orgId, ArticleEntity articleEntity, Integer totalPacks, BigDecimal totalCells) {
        ArtStockReserveResult result = new ArtStockReserveResult();
        result.setArtId(articleEntity.getArtId());
        result.setTotalPacks(totalPacks);
        result.setTotalCells(totalCells);
        result.setReservedFlag(ReservedFlag.NOT_LOCKED);

        List<OrgStockDto> orgStockDtos = new ArrayList<>();
        // 初始化默认值
        Integer packCells = 1;
        if (articleEntity.getPackCells() != null) {
            packCells = articleEntity.getPackCells();
        }
        if (totalPacks == null) {
            totalPacks = 0;
        }
        if (totalCells == null) {
            totalCells = BigDecimal.ZERO;
        }
        // 先将拆零数取出整包数量
        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && totalCells.compareTo(BigDecimal.valueOf(packCells)) >= 0) {
            int tempPacks = totalCells.divide(BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue();
            totalPacks = totalPacks + tempPacks;
            totalCells = totalCells.subtract(BigDecimal.valueOf(packCells).multiply(BigDecimal.valueOf(tempPacks)));
        }
//        OrgArtEntity orgArtEntity = findById(orgId, articleEntity.getArtId());
//        if (totalCells.compareTo(BigDecimal.ZERO) > 0 && Convert.toInt(orgArtEntity.getSplittable(), 0) == 0) { // 不允许拆零
//            totalCells = BigDecimal.ZERO;
//            totalPacks = totalPacks + 1;
//        }
        // 获取有库存的列表
        List<OrgStockDto> orgStockEntities = orgStockService.findDtoList(orgId, articleEntity.getArtId());
        // 过滤有库存的数据
        orgStockEntities = orgStockEntities.stream().filter(p -> (p.getTotalPacks() != null && p.getTotalPacks() > 0)
                || (p.getTotalCells() != null && p.getTotalCells().compareTo(BigDecimal.ZERO) > 0)).collect(Collectors.toList());

        Integer packsReserved = 0; // 已锁定整包数量
        BigDecimal cellsReserved = BigDecimal.ZERO; // 已锁定拆零数量

        for (OrgStockDto orgStockDto : orgStockEntities) {
            if (orgStockDto.getTotalPacks() == null) {
                orgStockDto.setTotalPacks(0);
            }
            if (orgStockDto.getTotalCells() == null) {
                orgStockDto.setTotalCells(BigDecimal.ZERO);
            }
            boolean lockFlag = false;
            Integer lockTotalPacks = 0; // 锁定的整包数量
            BigDecimal lockTotalCells = BigDecimal.ZERO; // 锁定的拆零数量

            Integer billTotalPacks = 0; // 满足的整包数量
            BigDecimal billTotalCells = BigDecimal.ZERO; // 满足的拆零数量
            if (totalPacks > 0 && orgStockDto.getTotalPacks() > 0) { // 需求要整包，并且库存有整包
                if (totalPacks <= orgStockDto.getTotalPacks()) { // 库存全部满足
                    orgStockDto.setTotalPacks(orgStockDto.getTotalPacks() - totalPacks);
                    lockTotalPacks = totalPacks;
                    billTotalPacks = totalPacks;
                    totalPacks = 0;
                } else {
                    // 整包数量缺少，稍后继续查找下一个批号
                    lockTotalPacks = orgStockDto.getTotalPacks();
                    billTotalPacks = orgStockDto.getTotalPacks();
                    totalPacks = totalPacks - orgStockDto.getTotalPacks();
                    orgStockDto.setTotalPacks(0);
                }
                lockFlag = true;
            }
            if (totalCells.compareTo(BigDecimal.ZERO) > 0) { // 需求要拆零数量
                if (totalCells.compareTo(orgStockDto.getTotalCells()) <= 0) { // 库存全部满足
                    orgStockDto.setTotalCells(orgStockDto.getTotalCells().subtract(totalCells));
                    lockTotalCells = totalCells;
                    billTotalCells = totalCells;
                    totalCells = BigDecimal.ZERO;
                } else { // 拆零数量缺少
                    if (orgStockDto.getTotalPacks() > 0) { // 还有整包, 新拆一包来满足
                        orgStockDto.setTotalPacks(orgStockDto.getTotalPacks() - 1); // 整包库存-1
                        lockTotalPacks = lockTotalPacks + 1; // 锁定整包数量+1

                        BigDecimal tempCells = BigDecimal.valueOf(packCells).subtract(totalCells); // 剩余数量
                        lockTotalCells = tempCells.negate();
                        billTotalCells = totalCells;
                        totalCells = BigDecimal.ZERO;
                        orgStockDto.setTotalCells(orgStockDto.getTotalCells().add(tempCells)); // 将拆零后未锁定的数量加到拆零库存中
                    } else { // 没有整包了，只能满足部分拆零数量
                        totalCells = totalCells.subtract(orgStockDto.getTotalCells());
                        lockTotalCells = orgStockDto.getTotalCells();
                        billTotalCells = orgStockDto.getTotalCells();
                        orgStockDto.setTotalCells(BigDecimal.ZERO);
                    }
                }
                lockFlag = true;
            }
            if (lockFlag) {
                // 减少批号库存
                orgStockService.batchStockDec(orgId, articleEntity.getArtId(), orgStockDto.getStockNo(), lockTotalPacks, lockTotalCells);

                OrgStockDto dto = new OrgStockDto();
                dto.setArtId(orgStockDto.getArtId());
                dto.setOrgId(orgStockDto.getOrgId());
                dto.setStockNo(orgStockDto.getStockNo());
                dto.setBatchNo(orgStockDto.getBatchNo());
                dto.setTotalPacks(billTotalPacks);
                dto.setTotalCells(billTotalCells);
                orgStockDtos.add(dto);

                packsReserved = packsReserved + lockTotalPacks;
                cellsReserved = cellsReserved.add(lockTotalCells);
            }
            if (totalPacks == 0 && totalCells.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
        }
//        if (totalPacks > 0 || totalCells.compareTo(BigDecimal.ZERO) > 0) {
//            throw new SaveFailureException(articleEntity.getArtName() + "库存不满足");
//        }
//        return result;
        if (packsReserved.equals(0) && cellsReserved.compareTo(BigDecimal.ZERO) == 0) {
            result.setReservedFlag(ReservedFlag.NOT_STOCK);
        } else if (totalPacks > 0 || totalCells.compareTo(BigDecimal.ZERO) > 0) {
            result.setReservedFlag(ReservedFlag.PARTIAL_LOCKED);
        } else {
            result.setReservedFlag(ReservedFlag.All_LOCKED);
        }
        result.setPacksReserved(packsReserved);
        result.setCellsReserved(cellsReserved);
        result.setOrgStockDtos(orgStockDtos);
        return result;
    }

    @Override
    public List<ArtStocknoEntity> findAllStockNoByBatchNo(long orgId, Long artId, String batchNo) {
        return baseMapper.findAllStockNoByBatchNo(orgId, artId, batchNo);
    }

    @Override
    public void saveOrUpdateEntity(long orgId, OrgArtEntity params) {
        OrgCustMapEntity orgCustMapEntity = orgCustMapService.findById(orgId);
        if (orgCustMapEntity.getPricingMethod().equals(PricingMethod.FIXED_PRICE.getValue()) || orgCustMapEntity.getPricingMethod().equals(PricingMethod.MIN_LOSS.getValue())) {
            if (params.getPackPrice() == null || params.getPackPrice().compareTo(BigDecimal.ZERO) == 0) {
                throw new SaveFailureException("整包销售单价不能为空");
            }
            if (params.getCellPrice() == null || params.getCellPrice().compareTo(BigDecimal.ZERO) == 0) {
                throw new SaveFailureException("拆零销售单价不能为空");
            }
        }
        if (orgCustMapEntity.getPricingMethod().equals(PricingMethod.PCT_ADD.getValue()) && params.getPctAdd() == null) {
            // throw new SaveFailureException("加成比例不能为空");
            params.setPctAdd(BigDecimal.ZERO);
        }
        OrgArtEntity orgArtEntity = findById(orgId, params.getArtId());
        if (orgArtEntity == null) {
            params.setOrgId(orgId);
            saveEntity(params);
        } else {
            this.update(new LambdaUpdateWrapper<OrgArtEntity>()
                    .eq(OrgArtEntity::getOrgId, orgId)
                    .eq(OrgArtEntity::getArtId, params.getArtId())
                    .set(OrgArtEntity::getRackNo, params.getRackNo())
                    .set(OrgArtEntity::getPackPrice, params.getPackPrice())
                    .set(OrgArtEntity::getCellPrice, params.getCellPrice())
                    .set(OrgArtEntity::getSafePacks, params.getSafePacks())
                    .set(OrgArtEntity::getPctAdd, params.getPctAdd())
            );
        }
    }

    @Override
    public void saveToOrgItemPrice(Long wbSeqid, Long orgId) {
        orgArtTypeService.saveArtTypeNotExistsFeeType();
        this.baseMapper.saveToOrgItemPrice(wbSeqid, cn.feiying.med.common.utils.DateUtil.getTodayInt());
        this.baseMapper.updateOrgItemPrice(wbSeqid);
        if (orgId != null) {
            baseMapper.updateOrgFormulary(wbSeqid, orgId);
        }
    }

    @Override
    @Transactional
    public void batchSaveOrUpdate(long orgId, List<OrgArtEntity> orgArtEntities) {
        // 批量保存或更新, 如果存在则更新, 不存在则保存
        List<Long> artIds = orgArtEntities.stream().map(OrgArtEntity::getArtId).collect(Collectors.toList());
        List<OrgArtEntity> dbList = this.list(new LambdaQueryWrapper<OrgArtEntity>().eq(OrgArtEntity::getOrgId, orgId).in(OrgArtEntity::getArtId, artIds));
        List<Long> dbArtIds = dbList.stream().map(OrgArtEntity::getArtId).collect(Collectors.toList());
        List<OrgArtEntity> saveList = orgArtEntities.stream().filter(p -> !dbArtIds.contains(p.getArtId())).collect(Collectors.toList());
        List<OrgArtEntity> updateList = dbList.stream().filter(p -> dbArtIds.contains(p.getArtId())).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(saveList)) {
            this.saveBatch(saveList);
        }
//        if (ObjectUtil.isNotEmpty(updateList)) {
//            for(OrgArtEntity item : updateList) {
//                this.update(new LambdaUpdateWrapper<OrgArtEntity>()
//                        .eq(OrgArtEntity::getOrgId, item.getOrgId())
//                        .eq(OrgArtEntity::getArtId, item.getArtId())
//                        .set(OrgArtEntity::getPctAdd, item.getPctAdd())
//                        .set(OrgArtEntity::getSplittable, item.getSplittable())
//                );
//            }
//        }
    }

    /**
     * 分页 品库存预警
     * @param orgId
     * @param params
     * @return
     */
    @Override
    public PageUtils orgOrDeptArtSafeWarning(Long orgId, Map<String, Object> params) {
        IPage<OrgArtDto> orgArtDtoIPage = (IPage<OrgArtDto>) querySafeWarning(orgId, true, params);
        return new PageUtils(orgArtDtoIPage);
    }

    /**
     * 不分页 品库存预警
     * @param params
     * @return
     */
    @Override
    public List<OrgArtDto> orgOrDeptArtSafeWarningList(Long orgId,Map<String, Object> params) {
        List<OrgArtDto> resultList = (List<OrgArtDto>) querySafeWarning(orgId, false, params);
        return resultList;
    }

    private Object querySafeWarning(Long orgId, boolean paging, Map<String, Object> params) {
        QueryWrapper<DeptArtDto> wrapper = new GQueryWrapper<DeptArtDto>().getWrapper(params);
        String keyword = Convert.toStr(params.get("keyword"), StrUtil.EMPTY).trim().toUpperCase();
        String deptCode = Convert.toStr(params.get("deptCode"));
        boolean isDept = StringUtil.isNotEmpty(deptCode);
        // 关键词查询条件
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("UPPER(t_article.Art_Code)", keyword)
                    .or().like("UPPER(t_article.Art_Name)", keyword)
                    .or().like("UPPER(t_article.QS_Code1)", keyword)
                    .or().like("UPPER(t_article.QS_Code2)", keyword)
                    .or().like("UPPER(t_article.CDAN_Name)", keyword)
                    .or().like("UPPER(t_article.MI_Code)", keyword)
                    .or().like("UPPER(t_article.YPID_Code)", keyword));
        }

        List<OrgArtDto> resultList;
        IPage<OrgArtDto> orgArtDtoIPage;
        if (paging){
            if (isDept) {
                // 科室安全库存查询
                wrapper.eq("t_dept_art.Org_ID", orgId)
                        .eq("t_dept_art.Dept_Code", deptCode);
                orgArtDtoIPage =  this.baseMapper.queryDeptArtSafeWarning(new Query<DeptArtDto>().getPage(params), wrapper);

                // 安全库存转换逻辑
                for (OrgArtDto dto : orgArtDtoIPage.getRecords()) {
                    PackAndCellResult reservedResult = wmBillDetailService.countPackAndCell(
                            new BigDecimal(dto.getSafeCells()!= null ? dto.getSafeCells() : 0),
                            ObjectUtil.defaultIfNull(dto.getPackCells(), 1)
                    );
                    if (reservedResult != null) {
                        dto.setSafePacks(ObjectUtil.defaultIfNull(reservedResult.getPacks(), 0));
                        dto.setSafeCells(ObjectUtil.defaultIfNull(reservedResult.getCells(), BigDecimal.ZERO).intValue());
                    } else {
                        dto.setSafePacks(0);
                        dto.setSafeCells(0);
                    }
                }
            } else {
                // 机构安全库存查询
                wrapper.eq("t_org_art.Org_ID", orgId);
                orgArtDtoIPage = this.baseMapper.queryOrgArtSafeWarning(new Query<DeptArtDto>().getPage(params), wrapper);
            }
            // 计算缺少数
            calculateLackQuantities(orgArtDtoIPage.getRecords());
            return orgArtDtoIPage;
        }else{
            if (isDept) {
                // 科室安全库存查询
                wrapper.eq("t_dept_art.Org_ID", orgId)
                        .eq("t_dept_art.Dept_Code", deptCode);
                resultList = this.baseMapper.queryDeptArtSafeWarning(wrapper); // 需要补充mapper方法

                // 安全库存转换逻辑
                for (OrgArtDto dto : resultList) {
                    PackAndCellResult reservedResult = wmBillDetailService.countPackAndCell(
                            new BigDecimal(dto.getSafeCells()!= null ? dto.getSafeCells() : 0),
                            ObjectUtil.defaultIfNull(dto.getPackCells(), 1)
                    );
                    if (reservedResult != null) {
                        dto.setSafePacks(ObjectUtil.defaultIfNull(reservedResult.getPacks(), 0));
                        dto.setSafeCells(ObjectUtil.defaultIfNull(reservedResult.getCells(), BigDecimal.ZERO).intValue());
                    } else {
                        dto.setSafePacks(0);
                        dto.setSafeCells(0);
                    }
                }
            } else {
                // 机构安全库存查询
                wrapper.eq("t_org_art.Org_ID", orgId);
                resultList = this.baseMapper.queryOrgArtSafeWarning(wrapper); // 需要补充mapper方法
            }
            // 计算缺少数
            calculateLackQuantities(resultList);
            return resultList;
        }


    }

    private void calculateLackQuantities(List<OrgArtDto> dto){
        Iterator<OrgArtDto> iterator = dto.iterator();
        while (iterator.hasNext()) {
            OrgArtDto item = iterator.next();

            // 总数量
            BigDecimal sumTotalCells = item.getSumTotalCells() != null ? item.getSumTotalCells() : BigDecimal.ZERO;
            BigDecimal packCellsValue = new BigDecimal(item.getPackCells() != null ? item.getPackCells() : 1); // 默认1
            BigDecimal safePacksValue = new BigDecimal(item.getSafePacks() != null ? item.getSafePacks() : 0); // 默认0
            BigDecimal safeCellsValue = new BigDecimal(item.getSafeCells() != null ? item.getSafeCells() : 0); // 默认0

            // 计算安全库存总量 = 安全整包数 * 每包数量 + 安全零散数
            BigDecimal safeTotal = safePacksValue.multiply(packCellsValue).add(safeCellsValue);

            // 判断当前总库存是否超过安全库存
            if (sumTotalCells.compareTo(safeTotal) > 0) {
                iterator.remove(); // 移除满足条件的item
            } else {
                // 计算缺少的总数量 = 安全库存总量 - 当前总库存
                BigDecimal lackTotal = safeTotal.subtract(sumTotalCells);

                // 调用方法计算缺少的整包和零散数
                PackAndCellResult reservedResult = wmBillDetailService.countPackAndCell(lackTotal, item.getPackCells());

                // 设置缺少的数量，处理可能的null值
                item.setLackPacks(ObjectUtil.isNotEmpty(reservedResult.getPacks()) ? reservedResult.getPacks() : 0);
                item.setLackCells(ObjectUtil.isNotEmpty(reservedResult.getCells()) ? reservedResult.getCells() : BigDecimal.ZERO);
            }
        }
    }
    public void remakeOrgArt(Long orgId, String deptCode, Long artId, Integer totalPacks, BigDecimal totalCells,BigDecimal reserveCells)
    {
        this.baseMapper.remakeOrgArt(orgId, deptCode, artId, totalPacks, totalCells,reserveCells);
    }
}
