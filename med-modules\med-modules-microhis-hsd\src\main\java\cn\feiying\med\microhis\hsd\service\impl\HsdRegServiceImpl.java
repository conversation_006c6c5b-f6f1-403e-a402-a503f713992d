package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.apt.api.resp.*;
import cn.feiying.med.common.mysql.AESEncryptHandler;
import cn.feiying.med.common.utils.ValidatorUtil;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.DateUtil;
import cn.feiying.med.common.utils.StringUtil;
import cn.feiying.med.hip.mdi.dto.*;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.hip.mpi.dto.*;
import cn.feiying.med.hip.mpi.entity.*;
import cn.feiying.med.hip.mpi.service.*;
import cn.feiying.med.his.api.req.HisApiReq0307;
import cn.feiying.med.his.api.resp.HisApiResp0307;
import cn.feiying.med.microhis.bcs.entity.*;
import cn.feiying.med.microhis.bcs.manager.PayManager;
import cn.feiying.med.microhis.bcs.service.*;
import cn.feiying.med.microhis.hsd.dto.*;
import cn.feiying.med.microhis.hsd.entity.*;
import cn.feiying.med.microhis.hsd.service.*;
import cn.feiying.med.microhis.hsd.vo.*;
import cn.feiying.med.saas.api.service.*;
import cn.feiying.med.saas.api.vo.*;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HsdRegServiceImpl implements HsdRegService {

    @Resource
    private RegService regService;
    @Resource
    private RegTriageService regTriageService;
    @Resource
    private RegPendingService regPendingService;
    @Resource
    private RegAcctService regAcctService;
    @Resource
    private HsdVisitService hsdVisitService;
    @Resource
    private VisitService visitService;
    @Resource
    private VisitPendingService visitPendingService;
    @Resource
    private VisitExtraService visitExtraService;
    @Resource
    private OrderService orderService;
    @Resource
    private RecipeService recipeService;
    @Resource
    private RecipeExtraService recipeExtraService;
    @Resource
    private OrgDailyOpcStatService orgDailyOpcStatService;
    @Resource
    private OrgDailyRegStatService orgDailyRegStatService;
    @Resource
    private OrgPeriodTimeService orgPeriodTimeService;
    @Resource
    private PatientService patientService;
    @Resource
    private EmSectionService emSectionService;
    @Resource
    private PatientStateService patientStateService;
    @Resource
    private BillService billService;
    @Resource
    private BillDetailService billDetailService;
    @Resource
    private BillUnpaidService billUnpaidService;
    @Resource
    private UserCodeService userCodeService;
    @Resource
    private OrgSettingService orgSettingService;
    @Resource
    private RemoteAcService remoteAcService;
    @Resource
    private RemoteRegService remoteRegService;
    @Resource
    private SchedService schedService;
    @Resource
    private RemoteImService remoteImService;
    @Resource
    private AgesService agesService;
    @Resource
    private ArticleService articleService;
    @Resource
    private OrgNonmedicalArtService orgNonmedicalArtService;
    @Resource
    private ActiveClinicianService activeClinicianService;
    @Resource
    private MiniqmsCalledService miniqmsCalledService;
    @Resource
    private HsdPatientService hsdPatientService;
    @Resource
    private MpiPatientMapService mpiPatientMapService;
    @Resource
    private MpiPatientnoService mpiPatientnoService;
    @Resource
    private ArtDiscountedSettingService artDiscountedSettingService;
    @Resource
    private InsuranceTypeService insuranceTypeService;
    @Resource
    private PayManager payManager;
    @Resource
    private OpcDiagService opcDiagService;
    @Resource
    private OrgDeptService orgDeptService;
    @Resource
    private PatientVisitStatService patientVisitStatService;
    @Resource
    private HsdBaseService hsdBaseService;
    @Resource
    private OutpatientService outpatientService;
    @Resource
    private PersonTypeService personTypeService;
    @Resource
    private MiniqmsOrderService miniqmsOrderService;

    @Override
    public List<RegDto> findRegClinicDateLs(Long orgId, Long clinicianId, Integer clinicDate, String deptCode, String patientName,
                                            Integer status, Integer forClinic, Integer myPatient) {
        List<RegDto> list = regService.findClinicDateLs(orgId, clinicianId, clinicDate, deptCode, patientName, status, forClinic, myPatient);
        if (!list.isEmpty()) {
            List<Long> visitIdLs = list.stream().map(RegDto::getVisitId).distinct().collect(Collectors.toList());
            if (!visitIdLs.isEmpty()) {
                // 获取处方
                List<RecipeEntity> recipeLs = recipeService.list(Wrappers.lambdaQuery(RecipeEntity.class).in(RecipeEntity::getVisitId, visitIdLs)
                        .ne(RecipeEntity::getExecStatus, ExecStatus.cancel.getValue()));
                if (ObjectUtil.isNotEmpty(recipeLs)) {
                    List<Long> recipeIdLs = recipeLs.stream().map(RecipeEntity::getRecipeId).distinct().collect(Collectors.toList());
                    List<OrderEntity> orderLs = orderService.list(Wrappers.lambdaQuery(OrderEntity.class)
                            .in(OrderEntity::getRecipeId, recipeIdLs).gt(OrderEntity::getUnreadReportCount, 0));
                    for (RegDto reg : list) {
                        if (reg.getVisitId() != null) {
                            List<Long> idLs = recipeLs.stream().filter(recipe -> recipe.getVisitId().equals(reg.getVisitId())).map(RecipeEntity::getRecipeId).distinct().collect(Collectors.toList());
                            // 获取驳回的处方
                            List<RecipeEntity> rejectedRecipeLs = recipeLs.stream().filter(recipe -> recipe.getVisitId().equals(reg.getVisitId()) && recipe.getExecStatus().equals(ExecStatus.rejected.getValue())).collect(Collectors.toList());
                            int westCount = Convert.toInt(rejectedRecipeLs.stream().filter(q -> q.getVisitId().equals(reg.getVisitId())
                                    && q.getRecipeTypeId().equals(RecipeType.xy.getValue())).count());
                            int tcmCount = Convert.toInt(rejectedRecipeLs.stream().filter(q -> q.getVisitId().equals(reg.getVisitId())
                                    && q.getRecipeTypeId().equals(RecipeType.zl.getValue())).count());
                            reg.setWestRejectedCount(westCount);
                            reg.setTcmRejectedCount(tcmCount);
                            // 获取待读的报告
                            List<OrderEntity> unreadOrderLs = orderLs.stream().filter(q -> idLs.contains(q.getRecipeId())).collect(Collectors.toList());
                            for (OrderEntity order : unreadOrderLs) {
                                RecipeEntity recipe = recipeLs.stream().filter(r -> r.getRecipeId().equals(order.getRecipeId())).findFirst().orElse(null);
                                reg.setUnreadCount((reg.getUnreadCount() == null ? 0 : reg.getUnreadCount()) + (order.getUnreadReportCount() == null ? 0 : order.getUnreadReportCount()));
                                if (recipe != null && recipe.getRecipeTypeId().equals(RecipeType.sys.getValue())) {
                                    reg.setJyUnreadCount((reg.getJyUnreadCount() == null ? 0 : reg.getJyUnreadCount()) + (order.getUnreadReportCount() == null ? 0 : order.getUnreadReportCount()));
                                } else if (recipe != null && recipe.getRecipeTypeId().equals(RecipeType.yj.getValue())) {
                                    reg.setJcUnreadCount((reg.getJcUnreadCount() == null ? 0 : reg.getJcUnreadCount()) + (order.getUnreadReportCount() == null ? 0 : order.getUnreadReportCount()));
                                }
                            }
                        }
                    }
                }
            }
            // 获取线上SVCID
            visitIdLs = list.stream().filter(p -> p.getOcFlag() != null && p.getOcFlag() == 1).map(RegDto::getVisitId).distinct().collect(Collectors.toList());
            if (!visitIdLs.isEmpty()) {
                JSONArray jsonArray = remoteAcService.findLsByVisitIdLs(visitIdLs);
                if (jsonArray != null) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject svcObj = jsonArray.getJSONObject(i);
                        Optional<RegDto> optional = list.stream().filter(p -> p.getVisitId().equals(svcObj.getLong("visitId"))).findFirst();
                        optional.ifPresent(reg -> reg.setSvcId(svcObj.getLong("svcId")));
                        optional.ifPresent(reg -> reg.setSvcTypeId(svcObj.getInt("svcTypeId")));
                        optional.ifPresent(reg -> reg.setSvcOrgId(svcObj.getLong("orgId")));
                    }
                }
            }
        }
        return list;
    }

    @Override
    public SimplePatientDto findSimplePatientById(Long regId) {
        SimplePatientDto simpleReg = new SimplePatientDto();
        RegDto reg = regService.findById(regId);
        if (reg != null) {
            simpleReg.setPatientName(reg.getPatientName());
            if (reg.getVisitId() != null) {
                VisitEntity visit = visitService.getById(reg.getVisitId());
                if (visit!= null) {
                    simpleReg.setGenderId(visit.getGenderId());
                }
                VisitExtraEntity visitExtra = visitExtraService.getById(reg.getVisitId());
                if (visitExtra!= null) {
                    simpleReg.setRelationshipId(visitExtra.getRelationshipId());
                    simpleReg.setContactorName(visitExtra.getCompanionName());
                    simpleReg.setContactorTel(visitExtra.getContactTel());
                }
            }
        }
        return simpleReg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VisitDto findPatientPendingReg(Long orgId, Long userId, Long patientId, Long clinicianId, Integer clinicDate, String deptCode) {
        String sql = "select * from microhis_hsd.t_reg_pending where t_reg.Reg_ID = t_reg_pending.Reg_ID and t_reg.Clinic_Date = t_reg_pending.Clinic_Date";
        List<RegEntity> regLs = regService.list(Wrappers.lambdaQuery(RegEntity.class).eq(RegEntity::getOrgId, orgId)
                .eq(RegEntity::getPatientId, patientId).le(RegEntity::getClinicDate, clinicDate).eq(RegEntity::getDeptCode, deptCode)
                .and(p -> p.isNull(RegEntity::getClinicianId).or().eq(RegEntity::getClinicianId, clinicianId))
                .in(RegEntity::getClinicStatus, ClinicStatus.reg.getValue(), ClinicStatus.waiting.getValue(), ClinicStatus.visit.getValue())
                .exists(sql).orderByAsc(RegEntity::getAptNo));
        // 获取挂号
        if (!regLs.isEmpty()) {
            RegEntity reg = regLs.get(0);
            if (reg.getClinicStatus() != null && reg.getClinicStatus().equals(ClinicStatus.visit.getValue())) {
                return visitService.findById(reg.getVisitId(), true);
            } else {
                VisitDto visit = visitService.saveVisitByRegId(orgId, userId, reg.getRegId(), clinicianId, deptCode, ClinicType.outpatient.getValue(), MedicalType.normalOutpatient.getValue());
                receiveReg(userId, reg.getRegId(), clinicianId);
                return visit;
            }
        } else {
            // 获取患者最近的诊疗记录
            return visitService.findLastVisitByPatientId(patientId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReg(Long orgId, Long userId, RegVo reg) {
        if (reg.getPeriodId() == null) {
            throw new SaveFailureException("请选择午别。");
        }
        if (StringUtil.isEmpty(reg.getDeptCode())) {
            throw new SaveFailureException("挂号科室不能为空。");
        }
        // 查询当前午别是否已挂号
        long count = regService.count(Wrappers.lambdaQuery(RegEntity.class).eq(RegEntity::getOrgId, orgId)
                .eq(RegEntity::getPatientId, reg.getPatientId()).eq(RegEntity::getPeriodId, reg.getPeriodId()).eq(RegEntity::getClinicDate, reg.getClinicDate())
                .in(RegEntity::getClinicStatus, ClinicStatus.waiting.getValue(), ClinicStatus.reg.getValue(), ClinicStatus.visit.getValue()));
        if (count > 0) {
            throw new SaveFailureException("当前午别已有其他的挂号信息，不能在挂号。");
        }
        // 申请号源
        JSONObject aptObj = getAptNo(orgId, reg.getClinicianId(), reg.getDeptCode(), reg.getClinicDate(), reg.getPeriodId(), reg.getAptNo());
        try {
            HisApiResp0307 resp0307 = HisApiReq0307.execute(
                    schedService.getHisApiUrl(orgId, reg.getDeptCode()),
                    "",
                    Convert.toStr(schedService.getHospitalNo(orgId)),
                    null,
                    reg.getDeptCode(),
                    schedService.getClinicianCode(reg.getClinicianId()),
                    reg.getClinicDate(),
                    reg.getPeriodId(),
                    null,
                    Convert.toLong(aptObj.get("aptNo")),
                    Convert.toStr(aptObj.get("startTime") + "-" + aptObj.get("endTime")),
                    Convert.toStr(reg.getPatientId()),
                    AptSource.clinicRoom.getValue() + "",
                    new HashMap<>());
            if (resp0307 == null || resp0307.getStatus() != 1) {
                log.error("预约失败，失败原因：{}", JSONUtil.toJsonStr(resp0307));
                releaseAptNo(orgId, reg.getClinicianId(), reg.getDeptCode(), reg.getClinicDate(), reg.getPeriodId(), Convert.toLong(aptObj.get("aptNo")));
                throw new SaveFailureException("预约失败");
            } else {
                LambdaUpdateWrapper<RegEntity> updateWrapper = Wrappers.lambdaUpdate(RegEntity.class);
                updateWrapper.eq(RegEntity::getRegId, Convert.toLong(resp0307.getHIS_Order_ID()));
                updateWrapper.set(RegEntity::getUserId, userId);
                regService.update(updateWrapper);
            }
        } catch (Exception e) {
            log.error("预约失败，失败原因：{}", e.getMessage());
            releaseAptNo(orgId, reg.getClinicianId(), reg.getDeptCode(), reg.getClinicDate(), reg.getPeriodId(), Convert.toLong(aptObj.get("aptNo")));
            throw new SaveFailureException("预约失败，失败原因：" + e.getMessage());
        }
    }

    private JSONObject getAptNo(Long orgId, Long clinicianId, String deptCode, Integer clinicDate, Integer periodId, Integer preferredNo) {
        JSONObject result = new JSONObject();
        String startTime;
        String endTime;
        Integer aptNo;
        if (clinicianId != null) {
            AptResp303 resp303 = schedService.getClinicianAptNo(orgId, clinicianId, clinicDate, periodId, 2, preferredNo, 1, null);
            if (resp303.getAPT_No() == null || resp303.getAPT_No() <= 0) {
                throw new SaveFailureException("号源申请失败。");
            }
            String now = DateUtil.dateFormat("yyyyMMdd");
            startTime = DateUtil.dateFormat(DateUtil.toDateTime(now + StringUtils.leftPad(String.valueOf(resp303.getStart_Time()), 6, "0"), "yyyyMMddHHmmss"), "HH:mm");
            endTime = DateUtil.dateFormat(DateUtil.toDateTime(now + StringUtils.leftPad(String.valueOf(resp303.getEnd_Time()), 6, "0"), "yyyyMMddHHmmss"), "HH:mm");
            aptNo = Convert.toInt(resp303.getAPT_No());
        } else {
            AptResp301 resp301 = schedService.getDeptAptNo(orgId, deptCode, clinicDate, periodId, 2, preferredNo, 1, null);
            if (resp301.getAPT_No() == null || resp301.getAPT_No() <= 0) {
                throw new SaveFailureException("号源申请失败。");
            }
            String now = DateUtil.dateFormat("yyyyMMdd");
            startTime = DateUtil.dateFormat(DateUtil.toDateTime(now + StringUtils.leftPad(String.valueOf(resp301.getStart_Time()), 6, "0"), "yyyyMMddHHmmss"), "HH:mm");
            endTime = DateUtil.dateFormat(DateUtil.toDateTime(now + StringUtils.leftPad(String.valueOf(resp301.getEnd_Time()), 6, "0"), "yyyyMMddHHmmss"), "HH:mm");
            aptNo = Convert.toInt(resp301.getAPT_No());
        }
        result.set("aptNo", aptNo);
        result.set("startTime", startTime);
        result.set("endTime", endTime);
        return result;
    }

    private void releaseAptNo(Long orgId, Long clinicianId, String deptCode, Integer clinicDate, Integer periodId, Long aptNo) {
        if (clinicianId != null) {
            schedService.releaseClinicianAptNo(orgId, clinicianId, clinicDate, periodId, aptNo, null);
        } else {
            schedService.releaseDeptAptNo(orgId, deptCode, clinicDate, periodId, aptNo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VisitDto saveOcReg(Long orgId, Long userId, RegVo regVo) {
        if (regVo.getAcRegId() == null) {
            throw new SaveFailureException("线上挂号记录ID不能为空。");
        }
        // 判断是否已生成了诊疗记录
        JSONObject object = remoteAcService.findOcRegById(regVo.getAcRegId());
        if (object == null) {
            throw new SaveFailureException("线上挂号记录信息不存在。");
        }
        Long visitId = null;
        if (object.containsKey("visitId")) {
            visitId = object.getLong("visitId");
        }
        if (visitId == null) {
            // 保存挂号记录信息
            RegEntity entity = new RegEntity();
            BeanUtils.copyProperties(regVo, entity);
            entity.setOrgId(orgId);
            entity.setOcFlag(1);
            entity.setRegTypeId(1);
            entity.setSourceId(AptSource.pc.getValue());
            entity.setSrcRegId(Convert.toStr(regVo.getAcRegId()));
            entity.setClinicStatus(ClinicStatus.waiting.getValue());
            entity.setRegStatus(RegStatus.CONFIRMED.getValue());
            String timeStr = DateUtil.dateFormat("HH:mm");
            entity.setSchedDuration(timeStr);
            entity.setUserId(userId);
//            Integer time = Convert.toInt(timeStr.replace(":", ""));
            // 获取午别
//            entity.setPeriodId(getPeriodId(orgId, time));
            Integer ageOfYears = null;
            BigDecimal ageOfDays = null;
            if (regVo.getPatientId() != null) {
                PatientDto patient = getPatient(orgId, regVo.getPatientId());
                entity.setPatientId(patient.getPatientId());
                regVo.setPatientId(patient.getPatientId());
                entity.setPatientName(patient.getPatientName());
                entity.setContactTel(patient.getTelNo());
                ageOfYears = patient.getAgeOfYears();
                ageOfDays = patient.getAgeOfDays();
            }
            Long regId = regService.saveEntity(entity);
            // 保存分诊记录表
            if (StringUtil.isEmpty(regVo.getComplainOf()) && StringUtil.isNotEmpty(regVo.getPatientStatement()) && regVo.getPatientStatement().length() < 60) {
                regVo.setComplainOf(regVo.getPatientStatement());
            }
            regTriageService.saveEntity(RegTriageEntity.builder().regId(regId).complainOf(regVo.getComplainOf()).userId(userId)
                    .patientStatement(regVo.getPatientStatement()).build());
            // 保存未结诊挂号记录
            regPendingService.save(RegPendingEntity.builder().regId(regId).clinicDate(entity.getClinicDate()).build());
            // 保存诊疗记录
            visitId = visitService.saveEntity(orgId, regVo.getPatientId(), regVo.getClinicianId(), regId, regVo.getPatientName(),
                    regVo.getGenderId(), ageOfYears, ageOfDays, regVo.getAgesTypeId(), regVo.getContactorName(), regVo.getContactorTel(),
                    regVo.getRelationshipId(), regVo.getCompanionIdno(), regVo.getCompanionAddr(), regVo.getClinicTypeId(), regVo.getClinicDate(),
                    regVo.getInsuranceTypeId(), getPsnTypeIdByCode(regVo.getPsnTypeCode()), regVo.getMedTypeId(), VisitStatus.outpatient.getValue(), regVo.getDeptCode(), regVo.getComplainOf(),
                    regVo.getPatientStatement(), regVo.getTemperature(), regVo.getHeightCm(), regVo.getWeightKg(), regVo.getPulse(),
                    regVo.getRr(), regVo.getDbp(), regVo.getSbp(), regVo.getLivingZonecode(), regVo.getLivingAddr(), regVo.getPatientTypeId(),
                    regVo.getCivilServantFlag(), regVo.getEmployerName(), regVo.getMdtrtCertTypeId(), regVo.getMdtrtCertText());
            // 修改诊疗状态
            LambdaUpdateWrapper<RegEntity> wrapper = Wrappers.lambdaUpdate(RegEntity.class);
            wrapper.eq(RegEntity::getRegId, regId);
            wrapper.set(RegEntity::getVisitId, visitId);
            wrapper.set(RegEntity::getClinicStatus, ClinicStatus.reg.getValue());
            wrapper.set(RegEntity::getTimeArrival, new Date());
            regService.update(wrapper);
            // 修改线上
            remoteAcService.reciveReg(regVo.getAcRegId(), userId, object.getLong("clinicianId"), visitId);
            // 是否接诊
            if (regVo.getIsReceive() != null && regVo.getIsReceive() == 1) {
                regVo.setCreateRegBill(false);
                receiveReg(userId, regId, regVo.getClinicianId());
            }
            // 判断是否为后付费，如果是，则修改先诊后付划价单流水为-1
            OrgDeptEntity orgDept = orgDeptService.findById(orgId, regVo.getDeptCode());
            if (orgDept != null && orgDept.getIsPostpaid() != null && orgDept.getIsPostpaid() == 1) {
                LambdaUpdateWrapper<VisitEntity> updateWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
                updateWrapper.eq(VisitEntity::getVisitId, visitId);
                updateWrapper.set(VisitEntity::getPostpaidBseqid, -1);
                visitService.update(updateWrapper);
            }
        }
        return visitService.findById(visitId, false);
    }

    /**
     * 获取午别
     */
    private Integer getPeriodId(Long orgId, Integer time) {
        Integer periodId = null;
        List<OrgPeriodTimeEntity> orgPeriodTimeLs = orgPeriodTimeService.list(Wrappers.lambdaQuery(OrgPeriodTimeEntity.class).eq(OrgPeriodTimeEntity::getOrgId, orgId).orderByAsc(OrgPeriodTimeEntity::getPeriodId));
        if (!orgPeriodTimeLs.isEmpty()) {
            Optional<OrgPeriodTimeEntity> optional = orgPeriodTimeLs.stream().filter(p -> (p.getStartTime() == null || (p.getStartTime() != null && p.getStartTime() <= time))
                    && (p.getEndTime() == null || (p.getEndTime() != null && p.getEndTime() >= time))).findFirst();
            if (optional.isPresent()) {
                periodId = optional.get().getPeriodId();
            } else {
                for (OrgPeriodTimeEntity entity : orgPeriodTimeLs) {
                    if (entity.getStartTime() >= time) {
                        periodId = entity.getPeriodId();
                        break;
                    }
                }
            }
        }
        return periodId;
    }

    @Override
    @Transactional
    public void bindPatientId(Long orgId, long userId, Long regId, Long patientId) {
        PatientEntity patient = patientService.getById(patientId);
        if (patient == null) {
            throw new SaveFailureException("未找到患者信息，患者或未建卡。");
        }
        RegEntity reg = regService.getById(regId);
        if (reg == null) {
            throw new SaveFailureException("未找到预约挂号信息。");
        } else {
            LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
            regWrapper.eq(RegEntity::getRegId, regId);
            regWrapper.set(RegEntity::getPatientId, patientId);
            if (StrUtil.isBlank(reg.getContactTel())) {
                regWrapper.set(RegEntity::getContactTel, AESEncryptHandler.getEncryptStr(patient.getTelNo()));
            }
            regService.update(regWrapper);
            // 修改诊疗记录
            if (reg.getVisitId() != null) {
                visitService.updateVisitPatientById(reg.getVisitId(), patientId);
            }
            // 修改划价单
            if (reg.getOcFlag() == null || reg.getOcFlag() == 0) {
                RegAcctEntity regAcc = regAcctService.findById(regId, RegAcctActionType.REG.getValue());
                if (regAcc != null && regAcc.getBseqid() != null) {
                    LambdaUpdateWrapper<BillEntity> wrapper = Wrappers.lambdaUpdate(BillEntity.class);
                    wrapper.eq(BillEntity::getBseqid, regAcc.getBseqid());
                    wrapper.set(BillEntity::getPatientId, patientId);
                    billService.update(wrapper);
                } else if (StrUtil.isNotBlank(reg.getDeptCode())) {
                    if (regAcc != null) {
                        regAcctService.remove(Wrappers.lambdaQuery(RegAcctEntity.class).eq(RegAcctEntity::getRegId, regId).eq(RegAcctEntity::getActionType, RegAcctActionType.REG.getValue()));
                    }
                    createBill(orgId, userId, null, null, regService.getById(regId));
                }
            }
        }
    }

    @Override
    @Transactional
    public void receiveReg(Long userId, Long regId, Long clinicianId) {
        RegEntity entity = regService.getById(regId);
        if (entity == null) {
            throw new SaveFailureException("未找到预约挂号信息。");
        }
        // 修改诊疗状态
        LambdaUpdateWrapper<RegEntity> wrapper = Wrappers.lambdaUpdate(RegEntity.class);
        wrapper.eq(RegEntity::getRegId, regId);
        wrapper.set(RegEntity::getClinicStatus, ClinicStatus.visit.getValue());
        wrapper.set(RegEntity::getRegStatus, RegStatus.CONFIRMED.getValue());
        wrapper.set(RegEntity::getClinicianId, clinicianId);
        wrapper.set(RegEntity::getTimeAdmission, new Date());
        wrapper.set(entity.getTimeArrival() == null, RegEntity::getTimeArrival, new Date());
        regService.update(wrapper);
        // 保存未完结挂号记录
        RegPendingEntity regPending = regPendingService.getById(regId);
        if (regPending == null) {
            regPendingService.save(RegPendingEntity.builder().regId(regId).clinicDate(entity.getClinicDate()).build());
        }
        // 修改诊疗状态
        if (entity.getVisitId() != null) {
            visitService.updateVisitStatus(entity.getVisitId(), VisitStatus.inHospital);
        }
        // 修改开诊医生接诊挂号ID
        if (clinicianId != null) {
            activeClinicianService.updateActiveClinicianRegId(entity.getOrgId(), clinicianId, regId);
        }
        // 修改呼叫状态
        miniqmsOrderService.calledReg(regId, clinicianId);
        // 删除待播报记录
        miniqmsCalledService.deleteMiniqmsCallingByRegId(regId);
//        // 判断是否生成挂号费
//        if (createRegBill && (entity.getOcFlag() == null || entity.getOcFlag() == 0)) {
//            RegAcctEntity regAcc = regAcctService.findById(regId, RegAcctActionType.REG.getValue());
//            if (regAcc == null || regAcc.getBseqid() == null) {
//                if (regAcc != null) {
//                    regAcctService.remove(Wrappers.lambdaQuery(RegAcctEntity.class).eq(RegAcctEntity::getRegId, regId).eq(RegAcctEntity::getActionType, RegAcctActionType.REG.getValue()));
//                }
//                createBill(userId, null, regService.getById(regId));
//            }
//        }
    }

    @Override
    @Transactional
    public Map<String, Object> saveAppointmentReg(Long orgId, Long userId, RegVo regVo) {
        log.info("Save Appointment Reg orgId:{} userId:{} params:{}", orgId, userId, JSONUtil.toJsonStr(regVo));
        /*
          1. 患者ID不为空，判断是否有未完结的挂号记录
          2. 患者ID为空，证件号码不为空，判断证件号码是否已有患者信息，若没有则根据证件号码新增患者信息
         */
        if (regVo.getPatientId() != null) {
            String sql = "select * from microhis_hsd.t_reg_pending where t_reg.Reg_ID = t_reg_pending.Reg_ID and t_reg.Clinic_Date = t_reg_pending.Clinic_Date";
            long count = regService.count(Wrappers.lambdaQuery(RegEntity.class).eq(RegEntity::getPatientId, regVo.getPatientId())
                    .eq(RegEntity::getClinicDate, regVo.getClinicDate()).eq(RegEntity::getDeptCode, regVo.getDeptCode())
                    .eq(RegEntity::getOrgId, orgId).exists(sql));
            if (count > 0) {
                throw new SaveFailureException("该患者在挂号科室存在未完结的挂号记录，请先完结挂号记录。");
            }
        } else if (StrUtil.isNotBlank(regVo.getIdcertNo()) && ValidatorUtil.isIdCard(regVo.getIdcertNo())) {
            PatientVo patient = new PatientVo();
            patient.setOrgId(orgId);
            patient.setPatientName(regVo.getPatientName());
            patient.setGenderId(regVo.getGenderId());
            patient.setBirthDate(regVo.getBirthDate());
            patient.setCertTypeId(regVo.getCertTypeId());
            patient.setIdcertNo(regVo.getIdcertNo());
            patient.setTelNo(regVo.getContactTel());
            patient.setContactorName(regVo.getContactorName());
            patient.setContactorTel(regVo.getContactorTel());
            patient.setRelationshipId(regVo.getRelationshipId());
            patient.setLivingZonecode(regVo.getLivingZonecode());
            patient.setLivingAddr(regVo.getLivingAddr());
            Long patientId = hsdPatientService.savePatient(orgId, userId, false, patient);
            regVo.setPatientId(patientId);
        }
        if (regVo.getPatientId() != null) {
            MpiPatientMapEntity patientMap = mpiPatientMapService.findById(regVo.getPatientId(), orgId);
            if (patientMap == null || patientMap.getOutpatientNo() == null) {
                Integer outpatientNo = mpiPatientnoService.getNextOutpatientNo(orgId);
                mpiPatientMapService.updateOutpatientNo(orgId, regVo.getPatientId(), outpatientNo);
            }
            // 修改患者信息
            PatientEntity oriPatient = patientService.getById(regVo.getPatientId());
            LambdaUpdateWrapper<PatientEntity> patientWrapper = Wrappers.lambdaUpdate(PatientEntity.class);
            patientWrapper.eq(PatientEntity::getPatientId, regVo.getPatientId());
            boolean isUpdate = false;
            if (StrUtil.isNotBlank(regVo.getContactTel()) && ValidatorUtil.isMobile(regVo.getContactTel()) &&
                    (StrUtil.isBlank(oriPatient.getTelNo()) || !oriPatient.getTelNo().equals(regVo.getContactTel()))) {
                patientWrapper.set(PatientEntity::getTelNo, AESEncryptHandler.getEncryptStr(regVo.getContactTel()));
                isUpdate = true;
            }
            if (StrUtil.isNotBlank(regVo.getLivingAddr()) && (StrUtil.isBlank(oriPatient.getLivingAddr())
                    || !oriPatient.getLivingAddr().equals(regVo.getLivingAddr()))) {
                patientWrapper.set(PatientEntity::getLivingAddr, regVo.getLivingAddr());
                isUpdate = true;
            }
            if (isUpdate) {
                patientService.update(patientWrapper);
            }
        }
        // 保存挂号记录信息
        Integer aptNo = orgDailyRegStatService.getOrgDailyRegCount(orgId, regVo.getClinicDate());
//        if ((aptNo + "").length() < 3) {
//            aptNo = Convert.toInt("9" + String.format("%03d", Long.valueOf(aptNo)));
//        } else {
//            aptNo = Convert.toInt("9" + (aptNo + ""));
//        }
        String timeStr = DateUtil.dateFormat("HH:mm");
        String schedDuration = timeStr + "-" + timeStr;
//        Integer time = Convert.toInt(timeStr.replace(":", ""));
        // 获取午别
        Integer periodId = null; // getPeriodId(orgId, time);
        return saveRegNotPatientId(orgId, userId, aptNo, periodId, schedDuration, regVo);
//        if (regVo.getPatientId() != null && StrUtil.isNotBlank(regVo.getDeptCode())) {
//            HisApiResp0307 resp0307 = HisApiReq0307.execute(
//                    schedService.getHisApiUrl(orgId, regVo.getDeptCode()),
//                    "",
//                    Convert.toStr(schedService.getHospitalNo(orgId)),
//                    regVo.getDeptCode(),
//                    regVo.getClinicianId() != null ? schedService.getClinicianCode(regVo.getClinicianId()) : "",
//                    regVo.getClinicDate(),
//                    periodId,
//                    Convert.toInt(aptNo),
//                    schedDuration,
//                    Convert.toStr(regVo.getPatientId()),
//                    Convert.toStr(regVo.getSourceId()),
//                    new HashMap<>());
//            if (resp0307 != null && resp0307.getStatus() == 1) {
//                Long regId = Convert.toLong(resp0307.getHIS_Order_ID());
//                Long bseqid = Convert.toLong(resp0307.getHIS_Biz_ID());
//                // 保存分诊记录
//                regTriageService.saveEntity(RegTriageEntity.builder().regId(regId).complainOf(regVo.getComplainOf()).userId(userId)
//                        .patientStatement(regVo.getPatientStatement()).build());
//                // 保存诊疗信息
//                PatientDto patient = getPatient(null, regVo.getPatientId());
//                Long visitId = visitService.saveEntity(orgId, regVo.getPatientId(), regVo.getClinicianId(), regId, regVo.getPatientName(),
//                        regVo.getGenderId(), patient.getAgeOfYears(), patient.getAgeOfDays(), regVo.getAgesTypeId(), regVo.getContactorName(),
//                        regVo.getContactorTel(), regVo.getRelationshipId(), regVo.getClinicTypeId() == null ? ClinicType.outpatient.getValue() : regVo.getClinicTypeId(),
//                        regVo.getClinicDate(), regVo.getInsuranceTypeId(), regVo.getMedTypeId() == null ? MedicalType.outpatientRegistration.getValue() : regVo.getMedTypeId(),
//                        VisitStatus.outpatient.getValue(), regVo.getDeptCode(), regVo.getComplainOf(), regVo.getPatientStatement(),
//                        regVo.getTemperature(), regVo.getHeightCm(), regVo.getWeightKg(), regVo.getPulse(), regVo.getRr(),
//                        regVo.getDbp(), regVo.getSbp(), regVo.getLivingZonecode(), regVo.getLivingAddr());
//                // 修改挂号记录
//                LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
//                regWrapper.eq(RegEntity::getRegId, regId);
//                regWrapper.set(RegEntity::getVisitId, visitId);
//                if (StrUtil.isNotBlank(regVo.getNotes())) {
//                    regWrapper.set(RegEntity::getNotes, regVo.getNotes());
//                }
//                regService.update(regWrapper);
//                // 保存未完结挂号记录
//                RegPendingEntity regPending = regPendingService.getById(regId);
//                if (regPending == null) {
//                    regPendingService.save(RegPendingEntity.builder().regId(regId).clinicDate(regVo.getClinicDate()).build());
//                }
//                // 修改划价单诊疗流水号
//                if (bseqid != null) {
//                    LambdaUpdateWrapper<BillEntity> wrapper = Wrappers.lambdaUpdate(BillEntity.class);
//                    wrapper.eq(BillEntity::getBseqid, bseqid);
//                    wrapper.set(BillEntity::getVisitId, visitId);
//                    wrapper.set(BillEntity::getMedTypeId, regVo.getMedTypeId() == null ? MedicalType.outpatientRegistration.getValue() : regVo.getMedTypeId());
//                    billService.update(wrapper);
//                }
//                return regId;
//            } else {
//                throw new SaveFailureException("保存挂号失败，失败原因：" + (resp0307 != null ? resp0307.getResultMsg() : ""));
//            }
//        } else {
//            return saveRegNotPatientId(orgId, userId, aptNo, periodId, schedDuration, regVo);
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VisitDto saveRegVisit(Long orgId, Long userId, RegVo regVo) {
        log.info("Save Reg Visit orgId:{} userId:{} params:{}", orgId, userId, JSONUtil.toJsonStr(regVo));
        // 保存挂号记录信息
        Integer aptNo = orgDailyRegStatService.getOrgDailyRegCount(orgId, regVo.getClinicDate());
        String timeStr = DateUtil.dateFormat("HH:mm");
        String schedDuration = timeStr + "-" + timeStr;
        // 获取午别
        Long regId;
        Integer periodId = null; // getPeriodId(orgId, time);
        if (regVo.getPatientId() != null && StrUtil.isNotBlank(regVo.getDeptCode())) {
            HisApiResp0307 resp0307 = HisApiReq0307.execute(
                    schedService.getHisApiUrl(orgId, regVo.getDeptCode()),
                    "",
                    Convert.toStr(schedService.getHospitalNo(orgId)),
                    null,
                    regVo.getDeptCode(),
                    regVo.getClinicianId() != null ? schedService.getClinicianCode(regVo.getClinicianId()) : "",
                    regVo.getClinicDate(),
                    periodId,
                    null,
                    Convert.toLong(aptNo),
                    schedDuration,
                    Convert.toStr(regVo.getPatientId()),
                    Convert.toStr(regVo.getSourceId()),
                    new HashMap<>());
            if (resp0307 != null && resp0307.getStatus() == 1) {
                regId = Convert.toLong(resp0307.getHIS_Order_ID());
                Long bseqid = Convert.toLong(resp0307.getHIS_Biz_ID());
                // 保存分诊记录
                regTriageService.saveEntity(RegTriageEntity.builder().regId(regId).complainOf(regVo.getComplainOf()).userId(userId)
                        .patientStatement(regVo.getPatientStatement()).build());
                // 保存诊疗信息
                PatientDto patient = getPatient(null, regVo.getPatientId());
                Long visitId = visitService.saveEntity(orgId, regVo.getPatientId(), regVo.getClinicianId(), regId, regVo.getPatientName(),
                        regVo.getGenderId(), patient.getAgeOfYears(), patient.getAgeOfDays(), regVo.getAgesTypeId(), regVo.getContactorName(),
                        regVo.getContactorTel(), regVo.getRelationshipId(), regVo.getCompanionIdno(), regVo.getCompanionAddr(),
                        regVo.getClinicTypeId() == null ? ClinicType.outpatient.getValue() : regVo.getClinicTypeId(), regVo.getClinicDate(), regVo.getInsuranceTypeId(), getPsnTypeIdByCode(regVo.getPsnTypeCode()),
                        regVo.getMedTypeId() == null ? MedicalType.outpatientRegistration.getValue() : regVo.getMedTypeId(), VisitStatus.outpatient.getValue(),
                        regVo.getDeptCode(), regVo.getComplainOf(), regVo.getPatientStatement(), regVo.getTemperature(), regVo.getHeightCm(), regVo.getWeightKg(),
                        regVo.getPulse(), regVo.getRr(), regVo.getDbp(), regVo.getSbp(), regVo.getLivingZonecode(), regVo.getLivingAddr(), regVo.getPatientTypeId(),
                        regVo.getCivilServantFlag(), regVo.getEmployerName(), regVo.getMdtrtCertTypeId(), regVo.getMdtrtCertText());
                // 修改挂号记录
                LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
                regWrapper.eq(RegEntity::getRegId, regId);
                regWrapper.set(RegEntity::getUserId, userId);
                regWrapper.set(RegEntity::getVisitId, visitId);
                if (StrUtil.isNotBlank(regVo.getNotes())) {
                    regWrapper.set(RegEntity::getNotes, regVo.getNotes());
                }
                regService.update(regWrapper);
                // 保存未完结挂号记录
                RegPendingEntity regPending = regPendingService.getById(regId);
                if (regPending == null) {
                    regPendingService.save(RegPendingEntity.builder().regId(regId).clinicDate(regVo.getClinicDate()).build());
                }
                // 修改划价单诊疗流水号
                if (bseqid != null) {
                    LambdaUpdateWrapper<BillEntity> wrapper = Wrappers.lambdaUpdate(BillEntity.class);
                    wrapper.eq(BillEntity::getBseqid, bseqid);
                    wrapper.set(BillEntity::getVisitId, visitId);
                    wrapper.set(BillEntity::getMedTypeId, regVo.getMedTypeId() == null ? MedicalType.outpatientRegistration.getValue() : regVo.getMedTypeId());
                    billService.update(wrapper);
                }
            } else {
                throw new SaveFailureException("保存挂号失败");
            }
        } else {
            Map<String, Object> result = saveRegNotPatientId(orgId, userId, aptNo, periodId, schedDuration, regVo);
            regId = Convert.toLong(result.get("regId"));
        }
        // 保存诊疗信息
        return visitService.saveVisitByRegId(orgId, userId, regId, regVo.getClinicianId(), regVo.getDeptCode(),
                regVo.getClinicTypeId() == null ? ClinicType.outpatient.getValue() : regVo.getClinicTypeId(), MedicalType.normalOutpatient.getValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveClinicReg(Long orgId, Long userId, RegVo regVo) {
        log.info("保存挂号诊疗信息 orgId:{} userId:{} params:{}", orgId, userId, JSONUtil.toJsonStr(regVo));
        // 保存挂号记录信息
        Integer aptNo = orgDailyRegStatService.getOrgDailyRegCount(orgId, regVo.getClinicDate());
        String timeStr = DateUtil.dateFormat("HH:mm");
        String schedDuration = timeStr + "-" + timeStr;
        Map<String, Object> result = saveRegNotPatientId(orgId, userId, aptNo, null, schedDuration, regVo);
        Long regId = Convert.toLong(result.get("regId"));
        // 是否接诊
        if (regVo.getIsReceive() != null && regVo.getIsReceive() == 1) {
            receiveReg(userId, regId, regVo.getClinicianId());
        }
        return regId;
    }

    // 保存没有患者ID的挂号记录
    private Map<String, Object> saveRegNotPatientId(Long orgId, Long userId, Integer aptNo, Integer periodId, String schedDuration, RegVo regVo) {
        Map<String, Object> result = new HashMap<>();
        RegEntity entity = new RegEntity();
        BeanUtils.copyProperties(regVo, entity);
        entity.setOrgId(orgId);
        Integer now = DateUtil.getTodayInt();
        if (!regVo.getClinicDate().equals(now)) {
            entity.setAptFlag(1);
        } else {
            entity.setAptFlag(0);
        }
        entity.setAptNo(aptNo);
        entity.setClinicStatus(ClinicStatus.waiting.getValue());
        entity.setRegStatus(RegStatus.WAITING_FOR_CONFIRMATION.getValue());
        entity.setSchedDuration(schedDuration);
        entity.setPeriodId(periodId);
        Integer ageOfYears = regVo.getAgeOfYears();
        BigDecimal ageOfDays = regVo.getAgeOfDays();
        if (regVo.getPatientId() != null) {
            PatientDto patient = getPatient(null, regVo.getPatientId());
            entity.setContactTel(patient.getTelNo());
            ageOfYears = patient.getAgeOfYears();
            ageOfDays = patient.getAgeOfDays();
        }
        // 判断是否已挂号
        // 险种类别
        if (StrUtil.isNotBlank(regVo.getInsuredCode()) && StrUtil.isNotBlank(regVo.getBusinessCode())) {
            List<InsuranceTypeEntity> insuranceTypeLs = insuranceTypeService.list(Wrappers.lambdaQuery(InsuranceTypeEntity.class)
                    .eq(InsuranceTypeEntity::getBusinessCode, regVo.getBusinessCode())
                    .eq(InsuranceTypeEntity::getInsuredCode, regVo.getInsuredCode()));
            if (ObjectUtil.isNotEmpty(insuranceTypeLs)) {
                List<Integer> insuranceTypeIdLs = insuranceTypeLs.stream().map(InsuranceTypeEntity::getInsuranceTypeId).collect(Collectors.toList());
                if (regVo.getInsuranceTypeId() == null || insuranceTypeIdLs.contains(regVo.getInsuranceTypeId())) {
                    entity.setInsuranceTypeId(insuranceTypeLs.get(0).getInsuranceTypeId());
                }
            }
        }
        if (entity.getRegTypeId() == null) {
            entity.setRegTypeId(1);
        }
        entity.setUserId(userId);
        Long regId = regService.saveEntity(entity);
        // 保存分诊记录
        regTriageService.saveEntity(RegTriageEntity.builder().regId(regId).complainOf(regVo.getComplainOf()).userId(userId)
                .patientStatement(regVo.getPatientStatement()).build());
        // 保存未结诊挂号记录
        regPendingService.save(RegPendingEntity.builder().regId(regId).clinicDate(entity.getClinicDate()).build());
        // 保存诊疗信息
        Long visitId = visitService.saveEntity(orgId, regVo.getPatientId(), regVo.getClinicianId(), regId, regVo.getPatientName(),
                regVo.getGenderId(), ageOfYears, ageOfDays, regVo.getAgesTypeId(), regVo.getContactorName(), regVo.getContactorTel(),
                regVo.getRelationshipId(), regVo.getCompanionIdno(), regVo.getCompanionAddr(),
                regVo.getClinicTypeId() == null ? ClinicType.outpatient.getValue() : regVo.getClinicTypeId(), regVo.getClinicDate(),
                entity.getInsuranceTypeId(), getPsnTypeIdByCode(regVo.getPsnTypeCode()), regVo.getMedTypeId() == null ? MedicalType.outpatientRegistration.getValue() : regVo.getMedTypeId(),
                VisitStatus.outpatient.getValue(), regVo.getDeptCode(), regVo.getComplainOf(), regVo.getPatientStatement(), regVo.getTemperature(),
                regVo.getHeightCm(), regVo.getWeightKg(), regVo.getPulse(), regVo.getRr(), regVo.getDbp(), regVo.getSbp(), regVo.getLivingZonecode(),
                regVo.getLivingAddr(), regVo.getPatientTypeId(), regVo.getCivilServantFlag(), regVo.getEmployerName(), regVo.getMdtrtCertTypeId(), regVo.getMdtrtCertText());
        // 修改挂号记录
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getVisitId, visitId);
        regService.update(regWrapper);
        // 挂号收费记录
        if (StrUtil.isNotBlank(regVo.getDeptCode()) && regVo.isCreateRegBill()) {
            entity.setVisitId(visitId);
            Long bseqid = createBill(orgId, userId, ageOfYears, null, entity);
            if (bseqid != null) {
                result.put("bseqid", bseqid);
            }
        }
        result.put("regId", regId);
        result.put("visitId", visitId);
        // 推送转诊消息
        afterReg(userId, regId, entity.getOrgId(), entity.getClinicianId(), entity.getDeptCode(), ImMsgType.newReg);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createBill(Long orgId, Long userId, Integer ageOfYears, Integer medTypeId, RegEntity entity) {
//        if (entity.getPatientId() == null) {
//            return null;
//        }
        // 判断是否还在免挂号费区间
        OrgSettingEntity orgSetting = orgSettingService.getById(entity.getOrgId());
        if (entity.getPatientId() != null && entity.getClinicDate() != null && orgSetting != null
                && orgSetting.getFreeRevisitDays() != null && orgSetting.getFreeRevisitDays() > 0) {
            Integer freeDays = orgSetting.getFreeRevisitDays();
//            PatientVisitStatEntity patientVisitStat = patientVisitStatService.getOne(Wrappers.lambdaQuery(PatientVisitStatEntity.class)
//                    .eq(PatientVisitStatEntity::getOrgId, entity.getOrgId()).eq(PatientVisitStatEntity::getPatientId, entity.getPatientId()));
            Integer regClinicDate = regService.getLastClinicDate(entity.getOrgId(), entity.getPatientId(), RegAcctActionType.REG.getValue(), entity.getDeptCode());
//            if (patientVisitStat != null && patientVisitStat.getLastClinicDate() != null) {
            if (regClinicDate != null) {
                Date lastClinicDate = DateUtil.toDateTime(Convert.toStr(regClinicDate), "yyyyMMdd");
                Date clinicDate = DateUtil.toDateTime(Convert.toStr(entity.getClinicDate()), "yyyyMMdd");
                // 挂号日期与上次挂号日期相隔天数要加上当天
                int diff = DateUtil.calculateDayInterval(lastClinicDate, clinicDate) + 1;
                if (diff <= freeDays) {
                    return null;
                }
            }
        }
        // 挂号科室是否后付费
        OrgDeptEntity orgDept = orgDeptService.findById(entity.getOrgId(), entity.getDeptCode());
        if (orgDept != null && orgDept.getIsPostpaid() != null && orgDept.getIsPostpaid() == 1) {
            return null;
        }
        if (entity.getPatientId() != null) {
            PatientDto patient = getPatient(null, entity.getPatientId());
            ageOfYears = Convert.toInt(patient.getAgeOfYears(), 0);
        } else {
            ageOfYears = Convert.toInt(ageOfYears, 0);
        }
        boolean isChild = false; // 是否儿童
        boolean isAged = false; // 是否老年
        boolean isEmergency = entity.getIsEmergency() != null && entity.getIsEmergency() == 1; // 是否急诊
        // 是否儿童病例
        if (orgSetting != null && Convert.toInt(orgSetting.getChildrenMaxAge(), 0) > 0) {
            isChild = ageOfYears <= Convert.toInt(orgSetting.getChildrenMaxAge(), 0);
        }
        // 是否老年病例 大于60岁是老年
        if (orgSetting != null && Convert.toInt(orgSetting.getAgedMinAge(), 0) > 0) {
            isAged = ageOfYears >= Convert.toInt(orgSetting.getAgedMinAge(), 0);
        }
        List<RegFeeEntity> regFeeLs = regAcctService.saveRegAcc(entity.getRegId(), entity.getRegTypeId(), entity.getOrgId(), userId, entity.getClinicianId(),
                ClinicType.outpatient.getValue(), entity.getDeptCode(), entity.getClinicDate(), isChild, isAged, isEmergency, RegAcctActionType.REG);

        if (!regFeeLs.isEmpty()) {
            // 划价主表
            BillEntity bill = BillEntity.builder()
                    .orgId(entity.getOrgId())
                    .userId(userId)
                    .clinicianId(entity.getClinicianId())
                    .patientId(entity.getPatientId())
                    .patientName(entity.getPatientName())
                    .billTypeId(BillType.regBill.getValue())
                    .applyDeptcode(entity.getDeptCode())
                    .execDeptcode(entity.getDeptCode())
                    .medTypeId(medTypeId == null ? MedicalType.outpatientRegistration.getValue() : medTypeId)
                    .paidStatus(PaidStatus.toPaid.getValue())
                    .billDate(Convert.toInt(DateUtil.dateFormat("yyyyMMdd")))
                    .timeCreated(new Date())
                    .billAbstract("预约挂号申请")
                    .regId(entity.getRegId())
                    .visitId(entity.getVisitId())
                    .insuranceTypeId(entity.getInsuranceTypeId())
                    .clinicTypeId(ClinicType.outpatient.getValue())
                    .times(1)
                    .build();
            BigDecimal amount = BigDecimal.ZERO;
            BigDecimal derated = BigDecimal.ZERO;
            BigDecimal discounted = BigDecimal.ZERO;
            BigDecimal nonMedicalAmt = BigDecimal.ZERO;
            // 获取费用明细
            List<BillDetailEntity> billDetailLs = new ArrayList<>();
            int lineNo = 1;
            List<Long> artIdLs = regFeeLs.stream().map(RegFeeEntity::getArtId).distinct().collect(Collectors.toList());
            List<ArticleEntity> articleLs = articleService.listByIds(artIdLs);
            List<ArtDiscountedSettingEntity> discountedSettingLs = new ArrayList<>();
            if (entity.getPatientTypeId() != null && ObjectUtil.isNotEmpty(artIdLs)) {
                discountedSettingLs = artDiscountedSettingService.list(Wrappers.lambdaQuery(ArtDiscountedSettingEntity.class)
                        .eq(ArtDiscountedSettingEntity::getOrgId, entity.getOrgId()).eq(ArtDiscountedSettingEntity::getPatientTypeId, entity.getPatientTypeId())
                        .in(ArtDiscountedSettingEntity::getArtId, artIdLs)
                        .and(q -> q.isNull(ArtDiscountedSettingEntity::getStartDate).or().le(ArtDiscountedSettingEntity::getStartDate, Convert.toInt(DateUtil.dateFormat("yyyyMMdd"))))
                        .and(q -> q.isNull(ArtDiscountedSettingEntity::getEndDate).or().ge(ArtDiscountedSettingEntity::getEndDate, Convert.toInt(DateUtil.dateFormat("yyyyMMdd")))));
            }
            List<ArtClassEntity> artClassLs = hsdBaseService.getArtClassLsFromArtLs(articleLs);
            List<OrgItemPriceEntity> orgItemPriceLs = hsdBaseService.getOrgItemPriceLs(orgId, articleLs);
            for (RegFeeEntity regFee : regFeeLs) {
                ArticleEntity article = articleLs.stream().filter(a -> a.getArtId().equals(regFee.getArtId())).findFirst().orElse(new ArticleEntity());
                ArtDiscountedSettingEntity discountedSetting = discountedSettingLs.stream().filter(d -> d.getOrgId().equals(entity.getOrgId())
                        && entity.getPatientTypeId() != null && d.getPatientTypeId().equals(entity.getPatientTypeId()) && d.getArtId().equals(regFee.getArtId())).findFirst().orElse(null);
                Integer feeTypeId = hsdBaseService.getFeeTypeId(article, artClassLs);
                Integer statsCatId = hsdBaseService.getStatsCatId(article.getArtClassId(), artClassLs);
                Integer udfTypeId = hsdBaseService.getUdfTypeId(orgId, article.getArtId(), feeTypeId, orgItemPriceLs);
                BillDetailEntity billDetail = BillDetailEntity.builder()
                        .lineNo(lineNo)
                        .artId(regFee.getArtId())
                        .total(regFee.getTotal())
                        .price(regFee.getPrice())
                        .times(1)
                        .amount(regFee.getAmount() == null ? BigDecimal.ZERO : regFee.getAmount().setScale(2, RoundingMode.HALF_UP))
                        .derated(BigDecimal.ZERO)
                        .discounted(BigDecimal.ZERO)
                        .unit(article.getCellUnit())
                        .packTotal(regFee.getTotal())
                        .packPrice(regFee.getPrice())
                        .feeTypeId(feeTypeId)
                        .statsCatId(statsCatId)
                        .isEd(article.getIsEd() != null && article.getIsEd().equals(1))
                        .nonMedicalFlag(orgNonmedicalArtService.isNonMedicalArt(entity.getOrgId(), regFee.getArtId()))
                        .packCells(1)
                        .udfTypeId(udfTypeId)
                        .build();
                if (discountedSetting != null) {
                    billDetail.setDerated(discountedSetting.getDeratedAmount() == null ? BigDecimal.ZERO : discountedSetting.getDeratedAmount().setScale(2, RoundingMode.HALF_UP));
                    BigDecimal discountPct = (discountedSetting.getDiscountPct() == null ? BigDecimal.ZERO : discountedSetting.getDiscountPct()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                    BigDecimal discountAmount = (billDetail.getAmount().subtract(billDetail.getDerated())).multiply(discountPct).setScale(2, RoundingMode.HALF_UP);
                    billDetail.setDiscounted(discountAmount);
                }
                billDetailLs.add(billDetail);
                amount = amount.add(billDetail.getAmount());
                derated = derated.add(billDetail.getDerated() == null ? BigDecimal.ZERO : billDetail.getDerated());
                discounted = discounted.add(billDetail.getDiscounted() == null ? BigDecimal.ZERO : billDetail.getDiscounted());
                nonMedicalAmt = nonMedicalAmt.add(billDetail.getNonMedicalFlag() ? billDetail.getAmount() : BigDecimal.ZERO);
                lineNo++;
            }
            /*
              实际支付金额=(需要支付金额-优惠金额)*(1-折让比例)=(需要支付金额-优惠金额)-(需要支付金额-优惠金额)*折让比例
             */
            bill.setTotalAmount(amount);
            bill.setDerated(derated);
            bill.setDiscounted(discounted);
            bill.setAmount(amount.subtract(derated.add(discounted)));
            bill.setNonMedicalAmt(nonMedicalAmt);
            Long bseqid = billService.saveToPaidBill(bill, billDetailLs);
            // 修改挂号收费记录
            LambdaUpdateWrapper<RegAcctEntity> regAcctWrapper = Wrappers.lambdaUpdate(RegAcctEntity.class);
            regAcctWrapper.eq(RegAcctEntity::getRegId, entity.getRegId());
            regAcctWrapper.eq(RegAcctEntity::getActionType, RegAcctActionType.REG.getValue());
            regAcctWrapper.set(RegAcctEntity::getBseqid, bseqid);
            regAcctService.update(regAcctWrapper);
            // 如果金额为0，完成收款
            if (bill.getAmount() != null && bill.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                payManager.payOfZero(userId, Collections.singletonList(bseqid), CashType.reg);
                return null;
            }
            return bseqid;
        }
        return null;
    }

    private Long createRedBill(RegAcctEntity regAcct) {
        Long bseqid = regAcct.getBseqid();
        if (bseqid != null) {
            BillEntity bill = billService.getById(bseqid);
            if (bill != null && bill.getPaidStatus().equals(PaidStatus.paid.getValue())) {
                // 判断是已生成了红单
                List<BillEntity> redBillLs = billService.list(Wrappers.lambdaQuery(BillEntity.class).eq(BillEntity::getRelativeBseqid, bill.getBseqid())
                        .ne(BillEntity::getPaidStatus, PaidStatus.cancel.getValue()));
                boolean canAdd = true;
                if (ObjectUtil.isNotEmpty(redBillLs)) {
                    BigDecimal redAmt = redBillLs.stream().map(BillEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).abs();
                    if (redAmt.compareTo(bill.getAmount()) >= 0) {
                        canAdd = false;
                    }
                }
                if (canAdd) {
                    List<BillDetailEntity> billDetailLs = billDetailService.list(Wrappers.lambdaQuery(BillDetailEntity.class).eq(BillDetailEntity::getBseqid, bill.getBseqid()));
                    BillEntity redBill = new BillEntity();
                    BeanUtils.copyProperties(bill, redBill);
                    redBill.setBseqid(null);
                    redBill.setCashId(null);
                    redBill.setRelativeBseqid(bill.getBseqid());
                    redBill.setPaidStatus(PaidStatus.toPaid.getValue());
                    redBill.setTimeCreated(new Date());
                    redBill.setNonMedicalAmt(bill.getNonMedicalAmt() != null ? bill.getNonMedicalAmt().multiply(BigDecimal.valueOf(-1)) : BigDecimal.ZERO);
                    redBill.setAmount(bill.getAmount() != null ? bill.getAmount().multiply(BigDecimal.valueOf(-1)) : BigDecimal.ZERO);
                    redBill.setTotalAmount(bill.getTotalAmount() != null ? bill.getTotalAmount().multiply(BigDecimal.valueOf(-1)) : BigDecimal.ZERO);
                    redBill.setDerated(bill.getDerated() != null ? bill.getDerated().multiply(BigDecimal.valueOf(-1)) : BigDecimal.ZERO);
                    redBill.setDiscounted(bill.getDiscounted() != null ? bill.getDiscounted().multiply(BigDecimal.valueOf(-1)) : BigDecimal.ZERO);
                    redBill.setBillAbstract(redBill.getBillAbstract() + "红冲单。");
                    // 明细
                    if (ObjectUtil.isNotEmpty(billDetailLs)) {
                        for (BillDetailEntity billDetail : billDetailLs) {
                            billDetail.setBseqid(null);
                            billDetail.setTotal(billDetail.getTotal() != null ? billDetail.getTotal().multiply(BigDecimal.valueOf(-1)) : null);
                            billDetail.setCellTotal(billDetail.getCellTotal() != null ? billDetail.getCellTotal().multiply(BigDecimal.valueOf(-1)) : null);
                            billDetail.setPackTotal(billDetail.getPackTotal() != null ? billDetail.getPackTotal().multiply(BigDecimal.valueOf(-1)) : null);
//                        billDetail.setCellPrice(billDetail.getCellPrice() != null ? billDetail.getCellPrice().multiply(BigDecimal.valueOf(-1)) : null);
//                        billDetail.setPackPrice(billDetail.getPackPrice() != null ? billDetail.getPackPrice().multiply(BigDecimal.valueOf(-1)) : null);
//                        billDetail.setPrice(billDetail.getPrice() != null ? billDetail.getPrice().multiply(BigDecimal.valueOf(-1)) : null);
                            billDetail.setAmount(billDetail.getAmount() != null ? billDetail.getAmount().multiply(BigDecimal.valueOf(-1)) : null);
                            billDetail.setDiscounted(billDetail.getDiscounted() != null ? billDetail.getDiscounted().multiply(BigDecimal.valueOf(-1)) : null);
                            billDetail.setDerated(billDetail.getDerated() != null ? billDetail.getDerated().multiply(BigDecimal.valueOf(-1)) : null);
                            billDetail.setCost(billDetail.getCost() != null ? billDetail.getCost().multiply(BigDecimal.valueOf(-1)) : null);
                        }
                    }
                    billService.saveToPaidBill(redBill, billDetailLs);
                    // 生成冲红挂号收费记录
                    RegAcctEntity redRegAcct = RegAcctEntity.builder()
                            .regId(regAcct.getRegId())
                            .actionType(RegAcctActionType.DROP.getValue())
                            .orgId(regAcct.getOrgId())
                            .userId(regAcct.getUserId())
                            .accDate(regAcct.getAccDate())
                            .bseqid(redBill.getBseqid())
                            .amount(redBill.getAmount())
                            .notes(regAcct.getAccDate() + "退号退费单。")
                            .build();
                    regAcctService.save(redRegAcct);
                    // 如果金额为空0，自动处理
                    if (redBill.getAmount() != null && redBill.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                        payManager.refundOfZero(redBill.getUserId(), Collections.singletonList(redBill.getBseqid()));
                    } else {
                        return redBill.getBseqid();
                    }
                }
            } else if (bill != null && bill.getPaidStatus() < PaidStatus.paid.getValue()) {
                LambdaUpdateWrapper<BillEntity> updateWrapper = Wrappers.lambdaUpdate(BillEntity.class);
                updateWrapper.eq(BillEntity::getBseqid, bill.getBseqid());
                updateWrapper.set(BillEntity::getPaidStatus, PaidStatus.cancel.getValue());
                billService.update(updateWrapper);
                billUnpaidService.removeById(bill.getBseqid());
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAdditionReg(Long orgId, Long userId, Long clinicianId, List<RegVo> regLs) {
        log.info("Save Addition Reg orgId:{} userId:{} params:{}", orgId, userId, JSONUtil.toJsonStr(regLs));
        if (regLs == null || regLs.isEmpty()) {
            throw new SaveFailureException("请选择审批的加号申请。");
        }
        // 推送到REG审核
        List<Long> arIdLs = regLs.stream().map(RegVo::getAcRegId).distinct().collect(Collectors.toList());
        remoteRegService.additionAgree(schedService.getHospitalNo(orgId), userId, schedService.getClinicianCode(clinicianId), arIdLs);
        // 保存挂号记录信息
        for (RegVo regVo : regLs) {
            RegEntity entity = new RegEntity();
            BeanUtils.copyProperties(regVo, entity);
            entity.setOrgId(orgId);
            entity.setAdditionFlag(1);
            entity.setClinicStatus(ClinicStatus.waiting.getValue());
            entity.setRegStatus(RegStatus.WAITING_FOR_CONFIRMATION.getValue());
            String timeStr = DateUtil.dateFormat("HH:mm");
            entity.setSchedDuration(timeStr);
            if (regVo.getPatientId() != null) {
                PatientEntity patient = getPatient(orgId, regVo.getPatientId());
                entity.setPatientId(patient.getPatientId());
                regVo.setPatientId(patient.getPatientId());
                entity.setPatientName(patient.getPatientName());
                entity.setContactTel(patient.getTelNo());
            }
            entity.setSrcRegId(Convert.toStr(regVo.getAcRegId()));
            if (entity.getRegTypeId() == null) {
                entity.setRegTypeId(1);
            }
            entity.setUserId(userId);
            Long regId = regService.saveEntity(entity);
            // 保存未结诊挂号记录
            regPendingService.save(RegPendingEntity.builder().regId(regId).clinicDate(entity.getClinicDate()).build());
        }
    }

    // 获取同步患者信息
    private PatientDto getPatient(Long orgId, Long patientId) {
        PatientDto patientDto;
        if (orgId != null) {
            MdiPatientVo mdiPatient = remoteAcService.findPatientById(schedService.getHospitalNo(orgId), patientId, "");
            if (mdiPatient != null && StrUtil.isNotBlank(mdiPatient.getPatientNo())) {
//                PatientEntity patient = patientService.getById(mdiPatient.getPatientNo());
                MpiPatientMapEntity mpiPatientMap = mpiPatientMapService.findByPatientNo(orgId, mdiPatient.getPatientNo());
//                if (patient == null) {
//                    throw new SaveFailureException("病例号【" + mdiPatient.getPatientNo() + "】未找到绑定的患者。");
//                }
                if (mpiPatientMap == null) {
                    PatientVo patient = PatientVo.builder()
                            .patientName(mdiPatient.getPatientName())
                            .usedName(mdiPatient.getUsedName())
    //                            .genderId(mdiPatient.getGenderId())
    //                            .careerId(mdiPatient.getCareerId())
                            .telNo(mdiPatient.getTelNo())
    //                            .familyAddr(mdiPatient.getFamilyAddr())
    //                            .livingAddr(mdiPatient.getLivingAddr())
    //                            .zoneCode(mdiPatient.getZoneCode())
    //                            .countryCode(mdiPatient.getCountryCode())
    //                            .nationalityCode(mdiPatient.getNationalityCode())
                            .birthDate(mdiPatient.getBirthDate())
    //                            .birthTime(mdiPatient.getBirthTime())
    //                            .birthPlace(mdiPatient.getBirthPlace())
                            .certTypeId(mdiPatient.getCertTypeId())
                            .idcertNo(mdiPatient.getCertIdNo())
    //                            .marriageStatusId(mdiPatient.getMarriageStatusId())
    //                            .degreeId(mdiPatient.getDegreeId())
    //                            .eduBackgroundId(mdiPatient.getEduBackgroundId())
                            .build();
                    patientId = hsdPatientService.savePatient(orgId, null, false, patient);
                }
                PatientEntity patient = patientService.getById(patientId);
                patientDto = new PatientDto();
                BeanUtils.copyProperties(patient, patientDto);
                if (patient.getBirthDate() != null) {
                    BigDecimal[] ageAndDays = visitService.calculateAgeAndDayCount(patient.getBirthDate(), patient.getBirthTime());
                    int ageOfYears = Convert.toInt(ageAndDays[0]);
                    patientDto.setAgeOfYears(ageOfYears);
                    BigDecimal ageOfDays = ageAndDays[1];
                    patientDto.setAgeOfDays(ageOfDays);
                    if (ageOfYears <= 0) {
                        patientDto.setAgesId(getAgesId(Convert.toInt(ageOfDays), 0));
                    } else {
                        patientDto.setAgesId(getAgesId(0, ageOfYears));
                    }
                }
            } else {
                throw new SaveFailureException("未找到患者信息或患者未与院内绑定关系。");
            }
        } else {
            PatientEntity patient = patientService.getById(patientId);
            if (patient == null) {
                return new PatientDto();
            }
            patientDto = new PatientDto();
            BeanUtils.copyProperties(patient, patientDto);
            if (patient.getBirthDate() != null) {
                BigDecimal[] ageAndDays = visitService.calculateAgeAndDayCount(patient.getBirthDate(), patient.getBirthTime());
                int ageOfYears = Convert.toInt(ageAndDays[0]);
                patientDto.setAgeOfYears(ageOfYears);
                BigDecimal ageOfDays = ageAndDays[1];
                patientDto.setAgeOfDays(ageOfDays);
                if (ageOfYears <= 0) {
                    patientDto.setAgesId(getAgesId(Convert.toInt(ageOfDays), 0));
                } else {
                    patientDto.setAgesId(getAgesId(0, ageOfYears));
                }
            }
        }
        return patientDto;
    }

    private Integer getAgesId(Integer ageOfDays, Integer ageOfYears) {
        List<AgesEntity> agesLs = agesService.list(Wrappers.lambdaQuery(AgesEntity.class).orderByAsc(AgesEntity::getDisplayOrder));
        if (ageOfYears < 0 && ageOfDays < 0) {
            return 0;
        }
        if (ageOfYears >= 90) {
            List<AgesEntity> list = agesLs.stream().filter(p -> p.getMinAge() != null && p.getMinAge() <= ageOfYears).collect(Collectors.toList());
            if (!list.isEmpty()) {
                return list.get(0).getAgesId();
            } else {
                return 0;
            }
        }
        List<AgesEntity> list;
        if (ageOfYears <= 0) {
            list = agesLs.stream().filter(p -> p.getMinDays() != null && p.getMaxDays() != null
                    && p.getMinDays() <= ageOfDays && p.getMaxDays() >= ageOfDays).collect(Collectors.toList());
        } else {
            list = agesLs.stream().filter(p -> p.getMinAge() != null && p.getMaxAge() != null
                    && p.getMinAge() <= ageOfYears && p.getMaxAge() >= ageOfYears).collect(Collectors.toList());
        }
        if (!list.isEmpty()) {
            return list.get(0).getAgesId();
        } else {
            return 0;
        }
    }

    @Override
    @Transactional
    public void saveEmergency(Long orgId, Long userId, EmergencyRegVo regVo) {
        log.info("Save Emergency Reg orgId:{} userId:{} reg:{}", orgId, userId, JSONUtil.toJsonStr(regVo));
        Integer aptNo = emSectionService.getEmSectionAptNo(regVo.getSectionId());
//        if ((aptNo + "").length() < 3) {
//            aptNo = Convert.toInt("9" + String.format("%03d", Long.valueOf(aptNo)));
//        } else {
//            aptNo = Convert.toInt("9" + (aptNo + ""));
//        }
        EmSectionDto emSection = emSectionService.findById(regVo.getSectionId());
        // 保存挂号记录信息
        RegEntity entity = new RegEntity();
        BeanUtils.copyProperties(regVo, entity);
        entity.setOrgId(orgId);
        entity.setAptNo(aptNo);
        entity.setClinicDate(emSection.getCurrentedDate());
        entity.setDeptCode(emSection.getDeptCode());
        entity.setClinicStatus(ClinicStatus.waiting.getValue());
        entity.setRegStatus(RegStatus.WAITING_FOR_CONFIRMATION.getValue());
        String timeStr = DateUtil.dateFormat("HH:mm");
        entity.setSchedDuration(timeStr);
//        Integer time = Convert.toInt(timeStr.replace(":", ""));
        // 获取午别
        Integer periodId = null; // getPeriodId(orgId, time);
        entity.setPeriodId(periodId);
        if (regVo.getPatientId() != null) {
            PatientEntity patient = patientService.getById(regVo.getPatientId());
            entity.setPatientName(patient.getPatientName());
            entity.setContactTel(patient.getTelNo());
        }
        entity.setIsEmergency(1);
        if (entity.getRegTypeId() == null) {
            entity.setRegTypeId(1);
        }
        entity.setUserId(userId);
        Long regId = regService.saveEntity(entity);
        // 如果没有患者ID，生成诊疗记录
        Integer ageOfYears = null;
        BigDecimal ageOfDays = null;
        if (regVo.getPatientId() != null) {
            PatientDto patient = getPatient(null, regVo.getPatientId());
            ageOfYears = patient.getAgeOfYears();
            ageOfDays = patient.getAgeOfDays();
        }
        Long visitId = visitService.saveEntity(orgId, regVo.getPatientId(), null, regId, regVo.getPatientName(), regVo.getGenderId(), ageOfYears, ageOfDays,
                regVo.getAgesTypeId(), regVo.getContactorName(), regVo.getContactorTel(), regVo.getRelationshipId(), regVo.getCompanionIdno(),
                regVo.getCompanionAddr(), ClinicType.emergency.getValue(), entity.getClinicDate(), regVo.getInsuranceTypeId(), getPsnTypeIdByCode(regVo.getPsnTypeCode()),
                MedicalType.emergency.getValue(), VisitStatus.outpatient.getValue(), entity.getDeptCode(), regVo.getComplainOf(), regVo.getPatientStatement(),
                null, null, null, null, null, null, null, "", "",
                regVo.getPatientTypeId(), regVo.getCivilServantFlag(), regVo.getEmployerName(), regVo.getMdtrtCertTypeId(), regVo.getMdtrtCertText());
        // 修改挂号诊疗ID
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getVisitId, visitId);
        regService.update(regWrapper);
        // 保存分诊记录
        regTriageService.saveEntity(RegTriageEntity.builder().regId(regId).complainOf(regVo.getComplainOf()).userId(userId)
                .patientStatement(regVo.getPatientStatement()).build());
        // 保存未结诊挂号记录
        regPendingService.save(RegPendingEntity.builder().regId(regId).clinicDate(entity.getClinicDate()).build());
        // 推送转诊消息
        afterReg(userId, regId, entity.getOrgId(), entity.getClinicianId(), entity.getDeptCode(), ImMsgType.newReg);
    }

    @Override
    @Transactional
    public Map<String, Object> revisitReg(Long orgId, Long userId, Long regId, Long visitId, Long clinicianId, String deptCode, Integer clinicDate) {
        log.info("复诊挂号记录 orgId:{} userId:{} regId:{} visitId:{} clinicianId:{} deptCode:{} clinicDate:{}", orgId, userId, regId, visitId, clinicianId, deptCode, clinicDate);
        RegEntity entity = regService.getById(regId);
        if (entity == null) {
            throw new SaveFailureException("未找到挂号记录。");
        }
        if (entity.getPatientId() == null) {
            throw new SaveFailureException("挂号记录就诊人未建档，不能复诊。");
        }
        if (entity.getRegStatus() < RegStatus.CONFIRMED.getValue()) {
            throw new SaveFailureException("挂号记录就诊人还未付款确认，不能复诊。");
        }
        if (entity.getClinicStatus() == ClinicStatus.rejected.getValue()) {
            throw new SaveFailureException("挂号记录已拒诊，不能复诊。");
        }
        if (!entity.getClinicStatus().equals(ClinicStatus.finish.getValue())) {
            throw new SaveFailureException("诊疗记录还未结诊，不能复诊。");
        }
        if (entity.getRegStatus() == RegStatus.WITHDRAWAL.getValue()) {
            throw new SaveFailureException("挂号记录已被退号，不能复诊。");
        }
        if (Convert.toInt(entity.getRevisitFlag(), 0) == 1) {
            throw new SaveFailureException("改挂号记录是复诊记录，不能重复复诊。");
        }
        if (clinicDate == null) {
            clinicDate = DateUtil.getTodayInt();
        }
        VisitEntity visit = visitService.getById(visitId);
        VisitExtraEntity visitExtra = visitExtraService.getById(visitId);
        OutpatientEntity outpatient = outpatientService.getById(visitId);
        // 生成复诊挂号记录
        Integer aptNo = orgDailyRegStatService.getOrgDailyRegCount(orgId, clinicDate);
        String timeStr = DateUtil.dateFormat("HH:mm");
        String schedDuration = timeStr + "-" + timeStr;
        // 创建挂号记录
        RegVo regVo = createReg(orgId, clinicianId, deptCode, clinicDate, entity, visit, visitExtra, outpatient);
        Map<String, Object> regMap = saveRegNotPatientId(orgId, userId, aptNo, null, schedDuration, regVo);
        Long newRegId = Convert.toLong(regMap.get("regId"));
        Long newVisitId = Convert.toLong(regMap.get("visitId"));
        // 修改挂号的复诊标志
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, newRegId);
        regWrapper.set(RegEntity::getRevisitFlag, 1);
        regService.update(regWrapper);
        // 引用诊疗记录
        visitService.refVisit(userId, newVisitId, visitId);
        return regMap;
    }

    private RegVo createReg(Long orgId, Long clinicianId, String deptCode, Integer clinicDate, RegEntity entity, VisitEntity visit, VisitExtraEntity visitExtra, OutpatientEntity outpatient) {
        RegVo regVo = new RegVo();
        PatientEntity patient = patientService.getById(entity.getPatientId());
        regVo.setPatientId(entity.getPatientId());
        regVo.setPatientName(entity.getPatientName());
        regVo.setClinicTypeId(patient.getCertTypeId());
        regVo.setIdcertNo(patient.getIdcertNo());
        regVo.setBirthDate(patient.getBirthDate());
        regVo.setClinicDate(clinicDate);
        if (StrUtil.isNotBlank(deptCode)) {
            regVo.setDeptCode(deptCode);
        } else {
            regVo.setDeptCode(entity.getDeptCode());
        }
        regVo.setClinicianId(clinicianId);
        regVo.setSourceId(AptSource.clinicRoom.getValue());
        regVo.setOrgId(orgId);
        regVo.setRegTypeId(entity.getRegTypeId());
        regVo.setClinicTypeId(ClinicType.outpatient.getValue());
        regVo.setMedTypeId(MedicalType.normalOutpatient.getValue());
        regVo.setContactTel(patient.getTelNo());
        regVo.setGenderId(patient.getGenderId());
        regVo.setContactorName(visitExtra.getCompanionName());
        regVo.setContactorTel(visitExtra.getContactTel());
        regVo.setRelationshipId(visitExtra.getRelationshipId());
        regVo.setCompanionIdno(visitExtra.getCompanionIdno());
        regVo.setCompanionAddr(visitExtra.getCompanionAddr());
        regVo.setLivingZonecode(visitExtra.getLivingZonecode());
        regVo.setLivingAddr(visitExtra.getLivingAddr());
        regVo.setHeightCm(visitExtra.getHeightCm());
        regVo.setWeightKg(visitExtra.getWeightKg());
        regVo.setComplainOf(outpatient.getComplainedAs());
        regVo.setPatientStatement(outpatient.getPatientStatement());
        regVo.setPatientTypeId(entity.getPatientTypeId());
        regVo.setInsuranceTypeId(entity.getInsuranceTypeId());
        regVo.setCivilServantFlag(visit.getCivilServantFlag());
        regVo.setEmployerName(visitExtra.getEmployerName());
        regVo.setCreateRegBill(true);
        return regVo;
    }

    @Override
    @Transactional
    public void triageReg(Long orgId, Long userId, RegTriageVo regTriage) {
//        RegEntity regEntity = regService.getById(regTriage.getRegId());
//        if (regEntity.getRegStatus() < RegStatus.CONFIRMED.getValue()) {
//            throw new SaveFailureException("挂号记录就诊人还未付款确认，不能分诊。");
//        }
        // 保存分诊记录
        RegTriageEntity entity = regTriageService.getById(regTriage.getRegId());
        if (entity == null) {
            entity = new RegTriageEntity();
            BeanUtils.copyProperties(regTriage, entity);
            entity.setUserId(userId);
            entity.setPatientStatement(regTriage.getComplainOf());
            regTriageService.save(entity);
        } else {
            LambdaUpdateWrapper<RegTriageEntity> wrapper = Wrappers.lambdaUpdate(RegTriageEntity.class);
            wrapper.eq(RegTriageEntity::getRegId, regTriage.getRegId());
            wrapper.set(RegTriageEntity::getUserId, userId);
            if (StringUtil.isEmpty(entity.getPatientStatement())) {
                wrapper.set(RegTriageEntity::getPatientStatement, regTriage.getComplainOf());
            }
            wrapper.set(RegTriageEntity::getComplainOf, regTriage.getComplainOf());
            wrapper.set(RegTriageEntity::getWeightKg, regTriage.getWeightKg());
            wrapper.set(RegTriageEntity::getHeightCm, regTriage.getHeightCm());
            wrapper.set(RegTriageEntity::getTemperature, regTriage.getTemperature());
            wrapper.set(RegTriageEntity::getPulse, regTriage.getPulse());
            wrapper.set(RegTriageEntity::getRr, regTriage.getRr());
            wrapper.set(RegTriageEntity::getDbp, regTriage.getDbp());
            wrapper.set(RegTriageEntity::getSbp, regTriage.getSbp());
            regTriageService.update(wrapper);
        }
        // 修改挂号记录
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regTriage.getRegId());
        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.reg.getValue());
//        regWrapper.set(RegEntity::getRegStatus, RegStatus.CONFIRMED.getValue());
        if (StrUtil.isNotBlank(regTriage.getDeptCode())) {
            regWrapper.set(RegEntity::getDeptCode, regTriage.getDeptCode());
        }
        regWrapper.set(RegEntity::getClinicianId, regTriage.getClinicianId());
        regWrapper.set(RegEntity::getTimeArrival, new Date());
        if (regTriage.getClinicDate() != null) {
            regWrapper.set(RegEntity::getClinicDate, regTriage.getClinicDate());
        }
        regWrapper.set(RegEntity::getWristbandId, regTriage.getWristbandId());
        regWrapper.set(RegEntity::getNotes, regTriage.getNotes());
        regService.update(regWrapper);
        // 获取挂号记录
        RegEntity regEntity = regService.getById(regTriage.getRegId());
        // 保存未完结挂号记录
        RegPendingEntity regPending = regPendingService.getById(regEntity.getRegId());
        if (regPending == null) {
            regPendingService.save(RegPendingEntity.builder().regId(regEntity.getRegId()).clinicDate(regEntity.getClinicDate()).build());
        }
        if (regEntity.getVisitId() != null) {
            // 修改诊疗信息
            LambdaUpdateWrapper<VisitExtraEntity> extraWrapper = Wrappers.lambdaUpdate(VisitExtraEntity.class);
            extraWrapper.eq(VisitExtraEntity::getVisitId, regEntity.getVisitId());
            boolean flag = false;
            if (regTriage.getWeightKg() != null) {
                extraWrapper.set(VisitExtraEntity::getWeightKg, regTriage.getWeightKg());
                flag = true;
            }
            if (regTriage.getHeightCm() != null) {
                extraWrapper.set(VisitExtraEntity::getHeightCm, regTriage.getHeightCm());
                flag = true;
            }
            if (regTriage.getTemperature() != null) {
                extraWrapper.set(VisitExtraEntity::getTemperature, regTriage.getTemperature());
                flag = true;
            }
            if (regTriage.getPulse() != null) {
                extraWrapper.set(VisitExtraEntity::getPulse, regTriage.getPulse());
                flag = true;
            }
            if (regTriage.getRr() != null) {
                extraWrapper.set(VisitExtraEntity::getRr, regTriage.getRr());
                flag = true;
            }
            if (regTriage.getDbp() != null) {
                extraWrapper.set(VisitExtraEntity::getDbp, regTriage.getDbp());
                flag = true;
            }
            if (regTriage.getSbp() != null) {
                extraWrapper.set(VisitExtraEntity::getSbp, regTriage.getSbp());
                flag = true;
            }
            if (flag) {
                visitExtraService.update(extraWrapper);
            }
            // 修改主诉信息
            LambdaUpdateWrapper<OutpatientEntity> outpatientWrapper = Wrappers.lambdaUpdate(OutpatientEntity.class);
            outpatientWrapper.eq(OutpatientEntity::getVisitId, regEntity.getVisitId());
            outpatientWrapper.set(OutpatientEntity::getComplainedAs, regTriage.getComplainOf());
            outpatientService.update(outpatientWrapper);
            // 保存未完结诊疗记录
            VisitPendingEntity visitPending = visitPendingService.getById(regEntity.getVisitId());
            if (visitPending == null) {
                visitPendingService.save(VisitPendingEntity.builder()
                        .visitId(regEntity.getVisitId())
                        .clinicianId(regEntity.getClinicianId())
                        .patientId(regEntity.getPatientId())
                        .build());
            } else {
                visitPending.setClinicianId(regEntity.getClinicianId());
                visitPendingService.updateById(visitPending);
            }
        }
        // 查询是否已生成划价单
        List<BillEntity> billLs = billService.list(Wrappers.lambdaQuery(BillEntity.class).eq(BillEntity::getRegId, regEntity.getRegId())
                .eq(BillEntity::getBillTypeId, BillType.regBill.getValue()).eq(BillEntity::getMedTypeId, MedicalType.outpatientRegistration.getValue())
                .notIn(BillEntity::getPaidStatus, PaidStatus.refund.getValue(), PaidStatus.cancel.getValue()));
        if (billLs.isEmpty()) {
            createBill(orgId, userId, null, null, regEntity);
        }
        // 保存待呼叫
        miniqmsOrderService.saveEntity(MiniqmsOrderEntity.builder()
                .regId(regEntity.getRegId())
                .continuedFlag(0)
                .priority(QmsPriority.NORMAL.getCode())
                .orgId(regEntity.getOrgId())
                .deptCode(regEntity.getDeptCode())
                .build());
        // 推送转诊消息
        if (regEntity.getClinicianId() != null) {
            afterReg(userId, regEntity.getRegId(), regEntity.getOrgId(), regEntity.getClinicianId(), regEntity.getDeptCode(), ImMsgType.newReg);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rolloutReg(Long userId, Long regId, Long clinicianId, String deptCode, Long transferCid, Integer periodId,
                           Integer aptNo, String referralNotes) {
        log.info("Reg Rollout regId:{} clinicianId:{} deptCode:{} transferCid:{}", regId, clinicianId, deptCode, transferCid);
        RegDto reg = regService.findById(regId);
        if (reg == null) {
            throw new SaveFailureException("未找到挂号记录信息。");
        }
        if (reg.getRegStatus() != null && reg.getRegStatus().equals(RegStatus.WITHDRAWAL.getValue())) {
            throw new SaveFailureException("已退号，不能转诊。");
        }
        if (!reg.getClinicianId().equals(transferCid)) {
            throw new SaveFailureException("挂号医师不是您，您不能对此号进行转诊。");
        }
        /*if (reg.getClinicianId().equals(clinicianId)) {
            throw new SaveFailureException("您选择的转入的医师与现在的挂号医师同一个。");
        }*/
        if (reg.getVisitId() != null) {
            // 判断是否已经生成后付费费用
            VisitEntity visit = visitService.getById(reg.getVisitId());
            if (visit != null && visit.getPostpaidBseqid() != null
                    && billService.getPaidStatus(visit.getPostpaidBseqid()) < PaidStatus.paid.getValue()) {
                LambdaUpdateWrapper<BillEntity> updateWrapper = Wrappers.lambdaUpdate(BillEntity.class);
                updateWrapper.eq(BillEntity::getBseqid, visit.getPostpaidBseqid());
                updateWrapper.set(BillEntity::getPaidStatus, PaidStatus.cancel.getValue());
                billService.update(updateWrapper);
                billUnpaidService.removeById(visit.getPostpaidBseqid());
            }
            // 验证是否已开处方或已生成费用
            if (!visitService.validVisit(reg.getVisitId())){
                throw new SaveFailureException("您已开了处方或已生成了划价单费用，不能转诊。");
            }
        }
        // 如果是线上门诊不要转诊
        if (reg.getOcFlag() != null && reg.getOcFlag() == 1) {
            throw new SaveFailureException("这是线上互联网医院的问诊开方，不能转诊。");
        }
        // 修改挂号转诊
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getClinicianId, clinicianId);
        regWrapper.set(RegEntity::getDeptCode, deptCode);
        regWrapper.set(RegEntity::getTransferCid, transferCid);
        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.waiting.getValue());
        regWrapper.set(RegEntity::getRegStatus, RegStatus.TRANSFERRED.getValue());
        regWrapper.set(RegEntity::getQmsOrderId, null);
        regWrapper.set(RegEntity::getTimeArrival, null);
        regWrapper.set(RegEntity::getTimeAdmission, null);
        // 申请号源
        if (aptNo != null) {
            AptResp303 resp303 = schedService.getClinicianAptNo(reg.getOrgId(), clinicianId, reg.getClinicDate(), periodId, 2, aptNo, 1, null);
            if (resp303.getAPT_No() == null || resp303.getAPT_No() <= 0) {
                throw new SaveFailureException("号源申请失败。");
            }
            String now = DateUtil.dateFormat("yyyyMMdd");
            String startTime = DateUtil.dateFormat(DateUtil.toDateTime(now + StringUtils.leftPad(String.valueOf(resp303.getStart_Time()), 6, "0"), "yyyyMMddHHmmss"), "HH:mm");
            String endTime = DateUtil.dateFormat(DateUtil.toDateTime(now + StringUtils.leftPad(String.valueOf(resp303.getEnd_Time()), 6, "0"), "yyyyMMddHHmmss"), "HH:mm");
            regWrapper.set(RegEntity::getPeriodId, resp303.getPeriod_ID());
            regWrapper.set(RegEntity::getAptNo, resp303.getAPT_No());
            regWrapper.set(RegEntity::getSchedDuration, startTime + "-" + endTime);
        } else {
            String timeStr = DateUtil.dateFormat("HH:mm");
            String schedDuration = timeStr + "-" + timeStr;
            regWrapper.set(RegEntity::getSchedDuration, schedDuration);
        }
        regService.update(regWrapper);
        // 修改分诊信息
        LambdaUpdateWrapper<RegTriageEntity> triageWrapper = Wrappers.lambdaUpdate(RegTriageEntity.class);
        triageWrapper.eq(RegTriageEntity::getRegId, regId);
        triageWrapper.set(RegTriageEntity::getUserId, null);
        triageWrapper.set(RegTriageEntity::getReferralNotes, referralNotes);
        regTriageService.update(triageWrapper);
        // 如果已生成诊疗记录
        if (reg.getVisitId() != null) {
            LambdaUpdateWrapper<VisitEntity> visitWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
            visitWrapper.eq(VisitEntity::getVisitId, reg.getVisitId());
            visitWrapper.set(VisitEntity::getClinicianId, null);
            visitWrapper.set(VisitEntity::getDeptCode, null);
            visitWrapper.set(VisitEntity::getTimeAdmission, null);
            visitWrapper.set(VisitEntity::getVisitStatus, VisitStatus.outpatient.getValue());
            visitWrapper.set(VisitEntity::getPostpaidBseqid, null);
            visitService.update(visitWrapper);
            // 删除未结诊记录表
            visitPendingService.removeById(reg.getVisitId());
        }
        // 推送转诊消息
        if (clinicianId != null) {
            afterReg(userId, regId, reg.getOrgId(), clinicianId, deptCode, ImMsgType.newReg);
        }
    }

    /**
     * 转诊：如果已接诊则先完结，再转诊
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void newRolloutReg(Long userId, Long regId, Long clinicianId, String deptCode, Long transferCid, Integer periodId, Integer aptNo, String referralNotes) {
        log.info("新的转诊 regId:{} clinicianId:{} deptCode:{} transferCid:{}", regId, clinicianId, deptCode, transferCid);
        RegDto reg = regService.findById(regId);
        if (reg == null) {
            throw new SaveFailureException("未找到挂号记录信息。");
        }
        if (!reg.getClinicianId().equals(transferCid)) {
            throw new SaveFailureException("挂号医师不是您，您不能对此号进行转诊。");
        }
        if (reg.getRegStatus() != null && reg.getRegStatus().equals(RegStatus.WITHDRAWAL.getValue())) {
            throw new SaveFailureException("已退号，不能转诊。");
        }
        if (reg.getVisitId() == null) {
            rolloutReg(userId, regId, clinicianId, deptCode, transferCid, periodId, aptNo, referralNotes);
        }
        // 如果已接诊，先结诊
        VisitEntity visit = visitService.getById(reg.getVisitId());
        if (visit.getVisitStatus() == null || visit.getVisitStatus() < VisitStatus.planOut.getValue()) {
            rolloutReg(userId, regId, clinicianId, deptCode, transferCid, periodId, aptNo, referralNotes);
        } else if (visit.getVisitStatus().equals(VisitStatus.planOut.getValue())) {
            finishedReg(userId, regId, true);
            // 生成新科室的挂号单
            createReg(userId, clinicianId, deptCode, transferCid, periodId, aptNo, referralNotes, reg, visit);
            // 修改
            // 推送转诊消息
            if (clinicianId != null) {
                afterReg(userId, regId, reg.getOrgId(), clinicianId, deptCode, ImMsgType.newReg);
            }
        }
    }

    private void createReg(Long userId, Long clinicianId, String deptCode, Long transferCid, Integer periodId, Integer aptNo, String referralNotes, RegDto reg, VisitEntity visit) {
        VisitExtraEntity visitExtra = visitExtraService.getById(reg.getVisitId());
        OutpatientEntity outPatient = outpatientService.getById(reg.getVisitId());
        RegVo regVo = new RegVo();
        regVo.setPatientId(reg.getPatientId());
        regVo.setPatientName(visit.getPatientName());
        regVo.setCertTypeId(visitExtra.getCertTypeId());
        regVo.setIdcertNo(visitExtra.getIdcertNo());
        regVo.setBirthDate(reg.getPatientBirthDate());
        regVo.setClinicDate(Convert.toInt(DateUtil.dateFormat("yyyyMMdd")));
        regVo.setDeptCode(deptCode);
        regVo.setClinicianId(clinicianId);
        regVo.setSourceId(AptSource.clinicRoom.getValue());
        regVo.setOrgId(reg.getOrgId());
        regVo.setRegTypeId(reg.getRegTypeId());
        regVo.setClinicTypeId(visit.getClinicTypeId());
        regVo.setMedTypeId(visit.getMedTypeId());
        regVo.setTransferCid(transferCid);
        regVo.setPeriodId(periodId);
        regVo.setAptNo(aptNo);
        regVo.setContactTel(reg.getContactTel());
        regVo.setGenderId(visit.getGenderId());
        regVo.setAgeOfYears(visit.getAgeOfYears());
        regVo.setAgeOfDays(visit.getAgeOfDays());
        regVo.setAgesTypeId(visit.getAgesId());
        regVo.setContactorTel(visitExtra.getContactTel());
        regVo.setContactorName(visitExtra.getCompanionName());
        regVo.setCompanionIdno(visitExtra.getCompanionIdno());
        regVo.setCompanionAddr(visitExtra.getCompanionAddr());
        regVo.setRelationshipId(visitExtra.getRelationshipId());
        regVo.setLivingZonecode(visitExtra.getLivingZonecode());
        regVo.setLivingAddr(visitExtra.getLivingAddr());
        regVo.setNotes(referralNotes);
        regVo.setComplainOf(outPatient.getComplainedAs());
        regVo.setPatientStatement(outPatient.getPatientStatement());
        regVo.setPatientTypeId(reg.getPatientTypeId());
        regVo.setInsuranceTypeId(visit.getInsuranceTypeId());
        regVo.setCivilServantFlag(visit.getCivilServantFlag());
        if (visit.getPsnTypeId() != null) {
            PersonTypeEntity personType = personTypeService.getById(visit.getPsnTypeId());
            regVo.setPsnTypeCode(personType.getPsnTypeCode());
        }
        regVo.setEmployerName(visitExtra.getEmployerName());
        regVo.setCreateRegBill(false);
        Map<String, Object> map = saveAppointmentReg(reg.getOrgId(), userId, regVo);
        if (map == null) {
            throw new SaveFailureException("保存挂号失败。");
        }
        if (map.containsKey("regId")) {
            Long regId = Convert.toLong(map.get("regId"));
            RegTriageEntity oriRegTriage = regTriageService.getById(reg.getRegId());
            // 分诊
            RegTriageVo regTriageVo = new RegTriageVo();
            BeanUtils.copyProperties(oriRegTriage, regTriageVo);
            regTriageVo.setRegId(regId);
            regTriageVo.setOrgId(reg.getOrgId());
            regTriageVo.setClinicDate(regVo.getClinicDate());
            regTriageVo.setClinicianId(clinicianId);
            regTriageVo.setDeptCode(deptCode);
            triageReg(reg.getOrgId(), userId, regTriageVo);
            // 保存现病史
            if (map.containsKey("visitId")) {
                Long visitId = Convert.toLong(map.get("visitId"));
                LambdaUpdateWrapper<OutpatientEntity> wrapper = Wrappers.lambdaUpdate(OutpatientEntity.class)
                        .eq(OutpatientEntity::getVisitId, visitId)
                        .set(OutpatientEntity::getHpiDesc, outPatient.getHpiDesc());
                outpatientService.update(wrapper);
            }
        } else {
            throw new SaveFailureException("保存挂号失败。");
        }
    }

    @Async
    @Override
    public void afterReg(Long userId, Long regId, Long orgId, Long clinicianId, String deptCode, ImMsgType imMsgType) {
        // 发送消息给医生客户端
        try {
            String msg = "";
            if (imMsgType.getValue() == ImMsgType.newReg.getValue()) {
                msg = "ADD-REG_" + clinicianId + "_" + deptCode + "_" + regId;
            } else if (imMsgType.getValue() == ImMsgType.returnAptNo.getValue()) {
                msg = "DEL-REG_" + clinicianId + "_" + deptCode + "_" + regId;
            }
            if (clinicianId != null) {
                remoteImService.sendToDoctor(userId, orgId, clinicianId, imMsgType, msg);
            } else {
                remoteImService.sendToDept(userId, orgId, deptCode, imMsgType, msg);
            }
        } catch (Exception e) {
            log.error("发送消息给医生客户端失败", e);
        }
    }

    @Override
    @Transactional
    public void skipReg(Long regId) {
        log.info("Reg Suspend regId: {}", regId);
        RegDto reg = regService.findById(regId);
        if (reg == null) {
            throw new SaveFailureException("未找到挂号记录信息。");
        }
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.reg.getValue());
        regWrapper.set(RegEntity::getRegStatus, RegStatus.TIME_EXPIRED.getValue());
        regService.update(regWrapper);
        // 修改呼叫状态
        miniqmsOrderService.updateQmsCalledStatus(regId, QmsCallStatus.OVERCALLED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RegRefundData rejectedReg(Long userId, Long regId, String rejectedNotes) {
        log.info("Reg Rejected regId:{} rejectedNotes:{}", regId, rejectedNotes);
        RegEntity reg = regService.getById(regId);
        if (reg.getClinicStatus().equals(ClinicStatus.finish.getValue()) || reg.getClinicStatus().equals(ClinicStatus.rejected.getValue())) {
            throw new SaveFailureException("挂号记录是在【" + ClinicStatus.getName(reg.getClinicStatus()) + "】状态，不能拒诊");
        }
        if (reg.getVisitId() != null) {
            VisitEntity visit = visitService.getById(reg.getVisitId());
            if (visit != null && (visit.getVisitStatus().equals(VisitStatus.planOut.getValue())
                    || visit.getVisitStatus().equals(VisitStatus.outHospital.getValue()))) {
                throw new SaveFailureException("当前挂号记录已生成就诊记录，就诊状态为" + VisitStatus.getName(visit.getVisitStatus()) + "，不能拒诊");
            }
        }
        RegRefundData regRefundData = new RegRefundData();
        regRefundData.setRegId(regId);
        // 如果是AC线上
        if (reg.getOcFlag() != null && reg.getOcFlag() == 1) {
            handleRemoteAcService(userId, reg.getVisitId(), rejectedNotes);
        } else if (reg.getRegStatus().equals(RegStatus.WAITING_FOR_CONFIRMATION.getValue())
                || reg.getRegStatus().equals(RegStatus.PAYMENT_IN_PROGRESS.getValue())
                || reg.getRegStatus().equals(RegStatus.CONFIRMED.getValue())) {
            /**
             * 1. 如果是待支付状态，则直接取消划价单
             * 2. 如果是已支付状态，挂号金额为0，直接产生退费单
             * 3. 如果是已支付状态，挂号金额不为0。
             *    机构未设置允许部分退款，则退款金额为挂号金额。
             *    机构参数设置了允许部分退款，根据机构科室设置的退费比例，生成退费金额。
             *    机构科室未设置退费比例或退费比例为负数，则退款金额为挂号金额。
             *    并返回前端弹出窗口让输入退费金额。
             */
            RegAcctEntity regAcctEntity = regAcctService.findById(regId, RegAcctActionType.REG.getValue());
            if (regAcctEntity != null && regAcctEntity.getBseqid() != null) {
                BillEntity bill = billService.getById(regAcctEntity.getBseqid());
                if (bill != null && bill.getPaidStatus().equals(PaidStatus.paid.getValue())) {
                    regRefundData.setOriginalAmount(bill.getAmount());
                    regRefundData.setCashId(regAcctEntity.getCashId());
                    if (bill.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                        Long bseqid = createRedBill(regAcctEntity);
                        regRefundData.setBseqid(bseqid);
                        regRefundData.setRefundAmount(bill.getAmount());
                    } else {
                        regRefundData.setBseqid(regAcctEntity.getBseqid());
                        OrgSettingEntity orgSetting = orgSettingService.getById(reg.getOrgId());
                        if (Convert.toInt(orgSetting.getPartialAptRefund(), 0) == 1) {
                            OrgDeptEntity orgDept = orgDeptService.findById(reg.getOrgId(), reg.getDeptCode());
                            if (orgDept != null) {
                                if (orgDept.getAptRefundRatio() != null && orgDept.getAptRefundRatio().compareTo(BigDecimal.ZERO) > 0) {
                                    BigDecimal refundAmount = bill.getAmount().multiply(orgDept.getAptRefundRatio().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
                                    regRefundData.setShowRefundWin(1);
                                    regRefundData.setRefundAmount(refundAmount);
                                    regRefundData.setAptRefundRatio(orgDept.getAptRefundRatio());

                                    updateRegTriage(regId, rejectedNotes);
                                    return regRefundData;
                                }
                            }
                        }
                        regRefundData.setShowRefundWin(1);
                        regRefundData.setRefundAmount(bill.getAmount());
                        regRefundData.setAptRefundRatio(new BigDecimal(100));

                        updateRegTriage(regId, rejectedNotes);
                        return regRefundData;
                    }
                } else if (bill != null && bill.getPaidStatus() < PaidStatus.paid.getValue()) {
                    LambdaUpdateWrapper<BillEntity> updateWrapper = Wrappers.lambdaUpdate(BillEntity.class);
                    updateWrapper.eq(BillEntity::getBseqid, bill.getBseqid());
                    updateWrapper.set(BillEntity::getPaidStatus, PaidStatus.cancel.getValue());
                    billService.update(updateWrapper);
                    billUnpaidService.removeById(bill.getBseqid());
                }
            }
        }
        // 分诊
        updateRegTriage(regId, rejectedNotes);
        // 修改挂号信息
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.rejected.getValue());
        regWrapper.set(RegEntity::getRegStatus, RegStatus.WITHDRAWAL.getValue());
        regWrapper.set(RegEntity::getRejectedFlag, 1);
        regWrapper.set(RegEntity::getTimeDischarged, new Date());
        regService.update(regWrapper);
        // 如果已生成诊疗记录，则删除
        if (reg.getVisitId() != null) {
            visitService.rejectedVisit(Collections.singletonList(reg.getVisitId()));
        }
        regRefundData.setShowRefundWin(0);
        // 推送消息
        afterReg(userId, regId, reg.getOrgId(), reg.getClinicianId(), reg.getDeptCode(), ImMsgType.returnAptNo);
        return regRefundData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void returnReg(Long userId, Long regId, String returnNotes) {
        log.info("Reg Return regId:{} returnNotes:{}", regId, returnNotes);
        RegEntity reg = regService.getById(regId);
        if (reg.getClinicStatus().equals(ClinicStatus.finish.getValue()) || reg.getClinicStatus().equals(ClinicStatus.rejected.getValue())) {
            throw new SaveFailureException("挂号记录是在【" + ClinicStatus.getName(reg.getClinicStatus()) + "】状态，不能退诊");
        }
        if (reg.getVisitId() != null && !visitService.validVisit(reg.getVisitId())) {
//            VisitEntity visit = visitService.getById(reg.getVisitId());
//            if (visit != null && (visit.getVisitStatus().equals(VisitStatus.planOut.getValue())
//                    || visit.getVisitStatus().equals(VisitStatus.outHospital.getValue()))) {
//                throw new SaveFailureException("当前挂号记录已生成就诊记录，就诊状态为" + VisitStatus.getName(visit.getVisitStatus()) + "，不能退诊");
//            }
            throw new SaveFailureException("当前诊疗已接诊或已开处方、申请单，或已有划价单，不能退诊");
        }
        // 如果是AC线上
        if (reg.getOcFlag() != null && reg.getOcFlag() == 1) {
            handleRemoteAcService(userId, reg.getVisitId(), returnNotes);
            updateRejectedReg(regId, reg.getVisitId(), returnNotes);
        } else if (reg.getRegStatus().equals(RegStatus.WAITING_FOR_CONFIRMATION.getValue())
                || reg.getRegStatus().equals(RegStatus.PAYMENT_IN_PROGRESS.getValue())
                || reg.getRegStatus().equals(RegStatus.CONFIRMED.getValue())) {
            /**
             * 1. 如果是待支付状态，则直接取消划价单
             * 2. 如果是已支付状态，挂号金额为0，直接产生退费单
             * 3. 如果是已支付状态，挂号金额不为0，把挂号状态改了待接诊
             */
            RegAcctEntity regAcctEntity = regAcctService.findById(regId, RegAcctActionType.REG.getValue());
            if (regAcctEntity != null && regAcctEntity.getBseqid() != null) {
                BillEntity bill = billService.getById(regAcctEntity.getBseqid());
                if (bill != null && bill.getPaidStatus().equals(PaidStatus.paid.getValue())) {
                    if (bill.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                        createRedBill(regAcctEntity);
                        updateRejectedReg(regId, reg.getVisitId(), returnNotes);
                    } else {
                        // 修改挂号信息
                        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
                        regWrapper.eq(RegEntity::getRegId, regId);
                        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.waiting.getValue());
                        regService.update(regWrapper);

                        LambdaUpdateWrapper<VisitEntity> visitWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
                        visitWrapper.eq(VisitEntity::getVisitId, reg.getVisitId());
                        visitWrapper.set(VisitEntity::getVisitStatus, VisitStatus.outpatient.getValue());
                        visitService.update(visitWrapper);
                    }
                } else if (bill != null && bill.getPaidStatus() < PaidStatus.paid.getValue()) {
                    LambdaUpdateWrapper<BillEntity> updateWrapper = Wrappers.lambdaUpdate(BillEntity.class);
                    updateWrapper.eq(BillEntity::getBseqid, bill.getBseqid());
                    updateWrapper.set(BillEntity::getPaidStatus, PaidStatus.cancel.getValue());
                    billService.update(updateWrapper);
                    billUnpaidService.removeById(bill.getBseqid());

                    updateRejectedReg(regId, reg.getVisitId(), returnNotes);
                }
            } else {
                updateRejectedReg(regId, reg.getVisitId(), returnNotes);
            }
        }
        // 推送消息
        afterReg(userId, regId, reg.getOrgId(), reg.getClinicianId(), reg.getDeptCode(), ImMsgType.returnAptNo);
    }

    /**
     * 修改分诊拒诊原因
     */
    private void updateRegTriage(Long regId, String rejectedNotes) {
        // 修改挂号信息
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.rejected.getValue());
        regWrapper.set(RegEntity::getRejectedFlag, 1);
        regService.update(regWrapper);
        // 修改分诊信息
        RegTriageEntity regTriage = regTriageService.getById(regId);
        if (regTriage == null) {
            regTriageService.save(RegTriageEntity.builder().regId(regId).rejectedNotes(rejectedNotes).build());
        } else {
            LambdaUpdateWrapper<RegTriageEntity> triageWrapper = Wrappers.lambdaUpdate(RegTriageEntity.class);
            triageWrapper.eq(RegTriageEntity::getRegId, regId);
            triageWrapper.set(RegTriageEntity::getRejectedNotes, rejectedNotes);
            regTriageService.update(triageWrapper);
        }
        // 删除未完成
        regPendingService.removeById(regId);
    }

    private void updateRejectedReg(Long regId, Long visitId, String rejectedNotes) {
        // 分诊
        updateRegTriage(regId, rejectedNotes);
        // 修改挂号信息
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.rejected.getValue());
        regWrapper.set(RegEntity::getRegStatus, RegStatus.WITHDRAWAL.getValue());
        regWrapper.set(RegEntity::getRejectedFlag, 1);
        regWrapper.set(RegEntity::getTimeDischarged, new Date());
        regService.update(regWrapper);
        // 如果已生成诊疗记录，则删除
        if (visitId != null) {
            visitService.rejectedVisit(Collections.singletonList(visitId));
        }
    }

    private void handleRemoteAcService(Long userId, Long visitId, String rejectedNotes) {
        try {
            JSONArray jsonArray = remoteAcService.findLsByVisitIdLs(Collections.singletonList(visitId));
            if (jsonArray != null) {
                JSONObject svcObj = jsonArray.getJSONObject(0);
                Long svcId = svcObj.getLong("svcId");
                remoteAcService.rejectedReg(userId, svcId, rejectedNotes);
            }
        } catch (Exception e) {
            log.error("远程调用失败", e);
            throw new SaveFailureException("远程调用失败，请联系管理员");
        }
    }

    @Override
    @Transactional
    public void canceledRejectedReg(Long regId) {
        log.info("Reg Canceled Rejected regId: {}", regId);
        LambdaUpdateWrapper<RegTriageEntity> triageWrapper = Wrappers.lambdaUpdate(RegTriageEntity.class);
        triageWrapper.eq(RegTriageEntity::getRegId, regId);
        triageWrapper.set(RegTriageEntity::getRejectedNotes, null);
        regTriageService.update(triageWrapper);
        // 修改挂号信息
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.waiting.getValue());
        regWrapper.set(RegEntity::getRejectedFlag, 0);
        regService.update(regWrapper);
        // 保存未完成挂号记录
        if (regPendingService.count(Wrappers.lambdaQuery(RegPendingEntity.class).eq(RegPendingEntity::getRegId, regId)) == 0) {
            RegEntity reg = regService.getById(regId);
            regPendingService.save(RegPendingEntity.builder().regId(regId).clinicDate(reg.getClinicDate()).build());
        }
    }

    @Override
    @Transactional
    public void suspendReg(Long regId) {
        log.info("挂号暂挂 regId: {}", regId);
        RegDto reg = regService.findById(regId);
        if (reg == null) {
            throw new SaveFailureException("未找到挂号记录信息。");
        }
        if (reg.getClinicStatus().equals(ClinicStatus.finish.getValue())) {
            throw new SaveFailureException("诊疗状态为【" + ClinicStatus.getName(reg.getClinicStatus()) + "】不能做暂挂处理。");
        } else if (!reg.getClinicStatus().equals(ClinicStatus.visit.getValue())) {
            throw new SaveFailureException("只有诊疗状态为诊查中才能做暂挂处理。");
        }
        // 如果是已接诊状态改为待接诊
        if (reg.getClinicStatus().equals(ClinicStatus.visit.getValue())) {
            LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
            regWrapper.eq(RegEntity::getRegId, regId);
            regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.waiting.getValue());
            regWrapper.set(RegEntity::getRegStatus, RegStatus.CONFIRMED.getValue());
            regService.update(regWrapper);
            // 修改排队号
            LambdaUpdateWrapper<RegTriageEntity> wrapper = Wrappers.lambdaUpdate(RegTriageEntity.class);
            wrapper.eq(RegTriageEntity::getRegId, regId);
            wrapper.set(RegTriageEntity::getQueueNo, null);
            regTriageService.update(wrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishedReg(Long userId, Long regId, boolean autoFinished) {
        log.info("诊疗完结 userId:{} regId: {}", userId, regId);
        RegEntity reg = regService.getById(regId);
        if (reg == null) {
            throw new SaveFailureException("未找到挂号记录信息。");
        }
        if (reg.getClinicStatus().equals(ClinicStatus.finish.getValue()) || reg.getClinicStatus().equals(ClinicStatus.rejected.getValue())) {
            throw new SaveFailureException("诊疗状态为【" + ClinicStatus.getName(reg.getClinicStatus()) + "】不能做诊结处理。");
        }
        if (reg.getVisitId() != null && !autoFinished) {
            VisitDto visit = visitService.findById(reg.getVisitId(), true);
            if (StrUtil.isBlank(visit.getComplainedAs()) || visit.getVisitDiagLs() == null || visit.getVisitDiagLs().isEmpty()) {
                throw new SaveFailureException("诊疗诊结时，请填写完善病例信息信息。");
            }
            // 判断是否还有处方申请单未签名提交
            List<RecipeEntity> recipeLs = recipeService.list(Wrappers.lambdaQuery(RecipeEntity.class).eq(RecipeEntity::getVisitId, reg.getVisitId())
                    .and(p -> p.isNull(RecipeEntity::getExecStatus).or().lt(RecipeEntity::getExecStatus, ExecStatus.finish.getValue())));
            if (ObjectUtil.isNotEmpty(recipeLs)) {
                List<Long> recipeIdLs = recipeLs.stream().map(RecipeEntity::getRecipeId).distinct().collect(Collectors.toList());
                long count = recipeExtraService.count(Wrappers.lambdaQuery(RecipeExtraEntity.class).in(RecipeExtraEntity::getRecipeId, recipeIdLs)
                        .isNull(RecipeExtraEntity::getTimeSigned));
                if (count > 0) {
                    throw new SaveFailureException("还有处方或申请单未提交签名，请先完成处方或申请单签名提交。");
                }
            }
        }
        // 诊疗诊结
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.finish.getValue());
        regWrapper.set(RegEntity::getRegStatus, RegStatus.CONFIRMED.getValue());
        regWrapper.set(RegEntity::getTimeDischarged, new Date());
        // 修改机构日问诊统计
        OrgDailyOpcStatEntity orgDaily = orgDailyOpcStatService.getOne(Wrappers.lambdaQuery(OrgDailyOpcStatEntity.class)
                .eq(OrgDailyOpcStatEntity::getOrgId, reg.getOrgId()).eq(OrgDailyOpcStatEntity::getClinicDate, reg.getClinicDate()));
        int dailyNo; // 日门诊序号
        if (orgDaily == null) {
            dailyNo = 1;
            orgDailyOpcStatService.save(OrgDailyOpcStatEntity.builder()
                    .orgId(reg.getOrgId()).clinicDate(reg.getClinicDate()).opcCount(dailyNo).build());
        } else {
            dailyNo = orgDaily.getOpcCount() == null ? 1 : orgDaily.getOpcCount() + 1;
            LambdaUpdateWrapper<OrgDailyOpcStatEntity> wrapper = Wrappers.lambdaUpdate(OrgDailyOpcStatEntity.class);
            wrapper.eq(OrgDailyOpcStatEntity::getOrgId, reg.getOrgId());
            wrapper.eq(OrgDailyOpcStatEntity::getClinicDate, reg.getClinicDate());
            wrapper.setSql("OPC_Count = IFNULL(OPC_Count, 0) + 1");
            orgDailyOpcStatService.update(wrapper);
        }
        regWrapper.set(RegEntity::getDailyNo, dailyNo);
        regService.update(regWrapper);
        // 删除未结诊挂号记录表
        regPendingService.removeById(regId);
        // 诊疗记录表
        if (reg.getVisitId() != null) {
            hsdVisitService.finishedVisit(reg.getVisitId());
        }
        // 如果是线上门诊推送到AC完结
        if (reg.getOcFlag() != null && reg.getOcFlag() == 1) {
            remoteAcService.finished(reg.getVisitId(), userId);
        } else if (reg.getClinicDate() != null && reg.getPatientId() != null) { // 不是线上门诊，修改患者诊疗次数统计的最近诊疗日期
            PatientVisitStatEntity patientVisitStat = patientVisitStatService.getOne(Wrappers.lambdaQuery(PatientVisitStatEntity.class)
                    .eq(PatientVisitStatEntity::getOrgId, reg.getOrgId()).eq(PatientVisitStatEntity::getPatientId, reg.getPatientId()));
            if (patientVisitStat == null) {
                patientVisitStat = PatientVisitStatEntity.builder()
                        .orgId(reg.getOrgId()).patientId(reg.getPatientId()).lastClinicDate(reg.getClinicDate()).build();
                patientVisitStatService.save(patientVisitStat);
            } else {
                patientVisitStat.setLastClinicDate(reg.getClinicDate());
                patientVisitStatService.update(patientVisitStat, Wrappers.lambdaQuery(PatientVisitStatEntity.class)
                        .eq(PatientVisitStatEntity::getOrgId, reg.getOrgId()).eq(PatientVisitStatEntity::getPatientId, reg.getPatientId()));
            }
        }
    }

    @Override
    @Transactional
    public void batchFinishedReg(Long userId, List<Long> regIds) {
        log.info("批量完结 userId:{} regIds: {}", userId, regIds);
        if (ObjectUtil.isNotEmpty(regIds)) {
            regIds.forEach(regId -> {
                try {
                    finishedReg(userId, regId, true);
                } catch (Exception e) {
                    log.error("完结挂号异常 regId:{} msg: {}", regId, e.getMessage());
                }
            });
        }
    }

    @Override
    @Transactional
    public void recallReg(Long regId) {
        log.info("挂号召回 regId: {}", regId);
        RegDto reg = regService.findById(regId);
        if (reg == null) {
            throw new SaveFailureException("未找到挂号记录信息。");
        }
        if (!reg.getClinicStatus().equals(ClinicStatus.finish.getValue())) {
            throw new SaveFailureException("该患者的诊疗状态为【未诊结】不用召回，可以直接看诊。");
        }
        // 修改诊疗状态为在看诊
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.visit.getValue());
        regWrapper.set(RegEntity::getRegStatus, RegStatus.CONFIRMED.getValue());
        regWrapper.set(RegEntity::getTimeDischarged, null);
        regWrapper.set(RegEntity::getDailyNo, null);
        regService.update(regWrapper);
        // 添加未结诊挂号记录表
        regPendingService.save(RegPendingEntity.builder().regId(regId).clinicDate(reg.getClinicDate()).build());
        // 修改机构日问诊统计
        OrgDailyOpcStatEntity orgDaily = orgDailyOpcStatService.getOne(Wrappers.lambdaQuery(OrgDailyOpcStatEntity.class)
                .eq(OrgDailyOpcStatEntity::getOrgId, reg.getOrgId()).eq(OrgDailyOpcStatEntity::getClinicDate, reg.getClinicDate()));
        if (orgDaily != null) {
            LambdaUpdateWrapper<OrgDailyOpcStatEntity> wrapper = Wrappers.lambdaUpdate(OrgDailyOpcStatEntity.class);
            wrapper.eq(OrgDailyOpcStatEntity::getOrgId, reg.getOrgId());
            wrapper.eq(OrgDailyOpcStatEntity::getClinicDate, reg.getClinicDate());
            wrapper.setSql("OPC_Count = IFNULL(OPC_Count, 0) - 1");
            orgDailyOpcStatService.update(wrapper);
        }
        // 添加未结诊记录表
        if (reg.getVisitId() != null) {
            LambdaUpdateWrapper<VisitEntity> visitWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
            visitWrapper.eq(VisitEntity::getVisitId, reg.getVisitId());
            visitWrapper.set(VisitEntity::getVisitStatus, VisitStatus.planOut.getValue());
            visitService.update(visitWrapper);

            visitPendingService.save(VisitPendingEntity.builder()
                    .visitId(reg.getVisitId())
                    .patientId(reg.getPatientId())
                    .clinicianId(reg.getClinicianId())
                    .build());
            opcDiagService.remove(Wrappers.lambdaQuery(OpcDiagEntity.class).eq(OpcDiagEntity::getVisitId, reg.getVisitId()));
            // 修改患者状态
            if (reg.getPatientId() != null) {
                PatientStateEntity patientState = patientStateService.getById(reg.getPatientId());
                if (patientState == null) {
                    patientStateService.save(PatientStateEntity.builder().patientId(reg.getPatientId()).pendingVisitId(reg.getVisitId()).build());
                } else {
                    LambdaUpdateWrapper<PatientStateEntity> patientStateWrapper = Wrappers.lambdaUpdate(PatientStateEntity.class);
                    patientStateWrapper.eq(PatientStateEntity::getPatientId, reg.getPatientId());
                    patientStateWrapper.set(PatientStateEntity::getPendingVisitId, reg.getVisitId());
                    patientStateService.update(patientStateWrapper);
                }
            }
        }
    }

    @Override
    public Map<String, Object> getPayCodeParams(Long visitId) {
        Map<String, Object> map = new HashMap<>();
        if (visitId != null) {
            VisitDto visit = visitService.findById(visitId, true);
            if (visit != null) {
                map.put("visitId", visit.getVisitId());
                map.put("orgId", visit.getOrgId());
                map.put("patientId", visit.getPatientId());
                map.put("clinicianId", visit.getClinicianId());
                map.put("deptCode", visit.getDeptCode());
                map.put("clinicDate", visit.getClinicDate());
                map.put("orgCode", Convert.toStr(schedService.getHospitalNo(visit.getOrgId())));
                map.put("clinicianCode", schedService.getClinicianCode(visit.getClinicianId()));
                map.put("patientName", visit.getPatientName());
                map.put("outpatientNo", visit.getOutpatientNo());
                map.put("deptName", visit.getDeptName());
            }
        }
        return map;
    }

    @Override
    public void closeUnFinishReg() {
        int nowDate = Convert.toInt(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        List<RegEntity> list = regService.list(Wrappers.lambdaQuery(RegEntity.class).lt(RegEntity::getClinicDate, nowDate)
                .in(RegEntity::getClinicStatus, ClinicStatus.waiting.getValue(), ClinicStatus.reg.getValue(), ClinicStatus.visit.getValue())
                .and(p -> p.isNull(RegEntity::getOcFlag).or().eq(RegEntity::getOcFlag, 0)));
        if (!list.isEmpty()) {
            List<Long> clinicianIdLs = list.stream().map(RegEntity::getClinicianId).collect(Collectors.toList());
            List<UserCodeEntity> userCodeLs = userCodeService.list(Wrappers.lambdaQuery(UserCodeEntity.class)
                    .in(UserCodeEntity::getClinicianId, clinicianIdLs).ne(UserCodeEntity::getUserId, 0));
            for (RegEntity entity : list) {
                Optional<UserCodeEntity> userCode = userCodeLs.stream().filter(p -> p.getClinicianId().equals(entity.getClinicianId())).findFirst();
                finishedReg(userCode.map(UserCodeEntity::getUserId).orElse(null), entity.getRegId(), true);
//                if (entity.getClinicStatus().equals(ClinicStatus.visit.getValue())) {
//                    finishedReg(userCode.map(UserCodeEntity::getUserId).orElse(null), entity.getRegId(), true);
//                } else {
//                    rejectedReg(userCode.map(UserCodeEntity::getUserId).orElse(null), entity.getRegId(), "超时未接诊，自动拒诊。");
//                }
            }
        }
    }

    @Override
    public void closeUnFinishOcRegAfter24H() {
        Date expiryDate = DateUtils.addDays(new Date(), -1);
        List<RegEntity> list = regService.list(Wrappers.lambdaQuery(RegEntity.class).lt(RegEntity::getTimeCreated, expiryDate)
                .in(RegEntity::getClinicStatus, ClinicStatus.waiting.getValue(), ClinicStatus.reg.getValue(), ClinicStatus.visit.getValue())
                .eq(RegEntity::getOcFlag, 1));
        if (!list.isEmpty()) {
            List<Long> clinicianIdLs = list.stream().map(RegEntity::getClinicianId).collect(Collectors.toList());
            List<UserCodeEntity> userCodeLs = userCodeService.list(Wrappers.lambdaQuery(UserCodeEntity.class)
                    .in(UserCodeEntity::getClinicianId, clinicianIdLs).ne(UserCodeEntity::getUserId, 0));
            for (RegEntity entity : list) {
                Long userId = remoteAcService.getClinicianUidByVisitId(entity.getVisitId());
                if (userId == null) {
                    Optional<UserCodeEntity> userCode = userCodeLs.stream().filter(p -> p.getClinicianId().equals(entity.getClinicianId())).findFirst();
                    userId = userCode.map(UserCodeEntity::getUserId).orElse(null);
                }
                if (entity.getClinicStatus().equals(ClinicStatus.visit.getValue())) {
                    finishedReg(userId, entity.getRegId(), true);
                } else {
                    rejectedReg(userId, entity.getRegId(), "24小时超时未接诊，自动拒诊。");
                }
            }
        }
    }

    private Integer getPsnTypeIdByCode(String psnTypeCode) {
        if (StrUtil.isNotBlank(psnTypeCode)) {
            List<PersonTypeEntity> list = personTypeService.list(Wrappers.lambdaQuery(PersonTypeEntity.class).eq(PersonTypeEntity::getPsnTypeCode, psnTypeCode));
            return list.isEmpty() ? null : list.get(0).getPsnTypeId();
        }
        return null;
    }
}
