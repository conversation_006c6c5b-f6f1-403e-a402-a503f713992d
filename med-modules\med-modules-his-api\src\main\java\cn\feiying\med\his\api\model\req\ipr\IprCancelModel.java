package cn.feiying.med.his.api.model.req.ipr;

import lombok.Data;

import java.util.List;

/**
 * 电子住院证作废请求模型
 */
@Data
public class IprCancelModel {
    
    /**
     * 作废人编码
     */
    private String userCode;
    
    /**
     * 作废人名称
     */
    private String userName;
    
    /**
     * 作废时间
     */
    private String cancelTime;
    
    /**
     * 数据来源 1-内部系统，2-外部系统
     */
    private Integer sourceId;

    private List<IprItem> iprList;

    @Data
    public static class IprItem {
        /**
         * 住院申请流水号
         */
        private Long iprId;

        /**
         * 作废原因
         */
        private String cancelCause;
    }
} 