<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.feiying.med.clinics_wm.dao.ArtSnTrackDao">

    <select id="findLsByArtSnIdLs" resultType="cn.feiying.med.clinics_wm.entity.ArtSnTrackEntity">
        SELECT *
        from microhis_clinics_wm.t_art_sn_track
        WHERE (Art_ID, SN_No) IN
        <foreach collection="artSnIdLs" item="item" separator="," open="(" close=")">
            (#{item.artId}, #{item.snNo})
        </foreach>
    </select>
</mapper>