package cn.feiying.med.his.api.controller;

import cn.feiying.med.his.api.model.req.ApiReqModel;
import cn.feiying.med.his.api.model.req.ipr.IprCancelModel;
import cn.feiying.med.his.api.model.req.ipr.IprModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.IprRespModel;
import cn.feiying.med.his.api.service.IprApiService;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 电子住院证相关API控制器
 */
@Slf4j
@RestController
@RequestMapping("/his/api/ipr")
public class IprApiController extends BaseApiController {
    
    @Resource
    private IprApiService iprApiService;
    
    /**
     * 电子住院证数据保存及修改
     */
    @PostMapping("/saveOrUpdate")
    public ApiResultModel<IprRespModel> saveOrUpdateIpr(HttpServletRequest request, @RequestBody ApiReqModel apiReq) {
        try {
            // 解密请求数据
            IprModel iprReq = decryptRequest(request, apiReq, IprModel.class);
            log.info("电子住院证数据保存及修改请求: {}", iprReq);

            // 调用服务处理业务逻辑
            return iprApiService.saveOrUpdateIpr(iprReq);
        } catch (Exception e) {
            log.error("电子住院证数据保存及修改失败", e);
            return ApiResultModel.error("电子住院证数据保存及修改失败: " + e.getMessage());
        }
    }
    
    /**
     * 电子住院证作废
     */
    @PostMapping("/cancel")
    public ApiResultModel<?> cancelIpr(HttpServletRequest request, @RequestBody ApiReqModel apiReq) {
        try {
            // 解密请求数据
            IprCancelModel iprModel = decryptRequest(request, apiReq, IprCancelModel.class);
            log.info("电子住院证作废请求: {}", iprModel);
            
            // 调用服务处理业务逻辑
            return iprApiService.cancelIpr(iprModel);
        } catch (Exception e) {
            log.error("电子住院证作废失败", e);
            return ApiResultModel.error("电子住院证作废失败: " + e.getMessage());
        }
    }
} 