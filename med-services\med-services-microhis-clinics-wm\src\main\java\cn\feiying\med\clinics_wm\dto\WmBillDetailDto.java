package cn.feiying.med.clinics_wm.dto;

import cn.feiying.med.clinics_wm.entity.WmBillDetailEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class WmBillDetailDto extends WmBillDetailEntity {
    /**
     * 条目编码
     */
    private String artCode;
    /**
     * 条目名称
     */
    private String artName;
    private String artNameFull;
    /**
     * 规格型号
     */
    private String artSpec;
    /**
     * 生产企业
     */
    private String producer;
    /**
     * 基本包装单位
     */
    private String packUnit;
    /**
     * 制剂单位
     */
    private String cellUnit;
    /**
     * 包装单位制剂数
     */
    private Integer packCells;
    /**
     * 医保标准编码
     */
    private String miCode;
    /**
     * 批准文号
     */
    private String approvalNo;
    /**
     * 追溯码应采集数量
     */
    private Integer unCollectedCount;
    /**
     * 追溯码已采集数量
     */
    private Integer collectedCount;
    /**
     * 仓库库存包装数量
     */
    private Integer deptTotalPacks;
    /**
     * 仓库库存拆零数量
     */
    private BigDecimal deptTotalCells;
    private Long recipeId;
    /**
     * 出入库状态
     */
    private Integer billStatus;
    /**
     * 相关出入库流水号
     */
    private Long relativeWbseqid;
    /**
     * 整包销售单价
     */
    private BigDecimal salePackPrice;
    /**
     * 拆零销售单价
     */
    private BigDecimal saleCellPrice;
    /**
     * 销售加成比例
     */
    private BigDecimal salePctAdd;
    /**
     * 基础整包单价（最小损失售价方式下使用)
     */
    private BigDecimal orgPackPrice;
    /**
     * 基础拆零单价（最小损失售价方式下使用)
     */
    private BigDecimal orgCellPrice;
    /**
     * 金额
     */
    private BigDecimal saleAmount;



    /**
     * 数量
     */
    private BigDecimal total;
    /**
     * 单价
     */
    private BigDecimal price;
    private BigDecimal salePrice;
    private String unit;
    private BigDecimal costPrice;
    /**
     * 无追溯码(零或空表示有,1-无)
     */
    private Integer noTrackCode;
    /**
     * 医嘱号
     */
    private Integer oeNo;
    /**
     * 诊疗流水号
     */
    private Long visitId;
    /**
     * 申领单ID
     */
    private Long drugReqid;
    private Long patientId;
    private String patientName;
    private String validatorUname;
    private String applyDeptName;
    private String rxNo;
    private String clinicianName;
    private Date timeValidated;
    private Integer genderId;
    private String genderName;
    private Integer ageOfYears;
    private Integer ageOfDays;
    private String bedNo;
    /**
     * 短缺补充制剂数
     */
    private BigDecimal shortCells;
    /**
     * 已锁定制剂数量
     */
    private BigDecimal cellsReserved;
    /**
     * 已发拆制剂数
     */
    private BigDecimal cellsDelivered;
    private String totalAndUnit;
    private String reservedTotalAndUnit;
    private String shortTotalAndUnit;

    /**
     * 包装材质
     */
    private String packMaterial;

    /**
     * 出入库单流水号集合
     */
    private String wbSeqids;

    /**
     * 拆零成本价
     */
    private BigDecimal  costCellPrice;
    /**
     * 业务类型(1-采购,2-调拨,3-销售,4-损溢,5-采购直调)
     */
    private Integer bsnType;
    /**
     * 出入库单类型代号
     */
    private Integer wmbillTypeId;
    /**
     * 出入库状态
     */
    private Integer status;
    /**
     * 业务摘要
     */
    private String bsnAbstract;
    /**
     * 诊疗类型代号
     */
    private Integer clinicTypeId;
    /**
     * 批次库存包装数量
     */
    private Integer deptStockTotalPacks;
    /**
     * 批次库存拆零数量
     */
    private BigDecimal deptStockTotalCells;
    /**
     * 冲红包装数量
     */
    private Integer redRushTotalPacks;
    /**
     * 冲红拆零数量
     */
    private BigDecimal redRushTotalCells;
    /**
     * 条目亚类
     */
    private String artSubTypeName;
    /**
     *  批次加成整包单价
     */
    private BigDecimal pctPrice;
    /**
     *  批次加成拆零单价
     */
    private BigDecimal pctCellPrice;
    /**
     *  批次加成金额
     */
    private BigDecimal pctAmount;
    /**
     *  额定售价金额
     */
    private BigDecimal ratedAmount;
    /**
     * 有效期至
     */
    private Integer artExpiry;
    /**
     * 生产日期
     */
    private Integer artDateManufactured;

    private Integer artIsDisassembled;
    /**
     * 条目类别代号
     */
    private Integer artTypeId;
    /**
     * 速查码1
     */
    private String qsCode1;
    /**
     * 速查码2
     */
    private String qsCode2;

    private List<Integer> prodNoLs;
}
