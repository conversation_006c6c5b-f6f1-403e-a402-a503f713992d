package cn.feiying.med.clinics_wm.service.impl;

import cn.feiying.med.clinics_wm.dao.WmBillTrackcodeDao;
import cn.feiying.med.clinics_wm.dto.WmBillTrackcodeDto;
import cn.feiying.med.clinics_wm.entity.*;
import cn.feiying.med.clinics_wm.enums.WmBillBsnType;
import cn.feiying.med.clinics_wm.enums.WmBillType;
import cn.feiying.med.clinics_wm.enums.WmTrackCodeStatus;
import cn.feiying.med.clinics_wm.model.ArtSnId;
import cn.feiying.med.clinics_wm.service.*;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.GQueryWrapper;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.common.utils.Query;
import cn.feiying.med.common.utils.wm.TrackCodeUtil;
import cn.feiying.med.hip.mdi.entity.ArticleEntity;
import cn.feiying.med.hip.mdi.service.ArticleService;
import cn.feiying.med.saas.api.service.RemoteMcispService;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 追溯码信息表
 *
 * <AUTHOR> 16:30:02
 */
@Slf4j
@Service("wmBillTrackcodeService")
public class WmBillTrackcodeServiceImpl extends ServiceImpl<WmBillTrackcodeDao, WmBillTrackcodeEntity> implements WmBillTrackcodeService {
    @Resource
    private WmBillService wmBillService;
    @Resource
    private ArtTrackcodeProdService artTrackcodeProdService;
    @Resource
    private ArtSnTrackService artSnTrackService;
    @Resource
    private RemoteMcispService remoteMcispService;
    @Resource
    private WmBillDetailService wmBillDetailService;
    @Resource
    private ArticleService articleService;

    @Override
    public PageUtils queryPage(Long wbSeqid, Integer lineNo, Map<String, Object> params) {
        QueryWrapper<WmBillTrackcodeDto> wrapper = new GQueryWrapper<WmBillTrackcodeDto>().getWrapper(params);
        wrapper.eq("t_wm_bill_trackcode.WB_SeqID", wbSeqid);
        if (lineNo != null) {
            wrapper.eq("t_wm_bill_trackcode.Line_No", lineNo);
        }
        IPage<WmBillTrackcodeDto> page = this.baseMapper.queryDtoPage(new Query<WmBillTrackcodeDto>().getPage(params), wrapper);

        return new PageUtils(page);
    }

    @Override
    public List<WmBillTrackcodeDto> findLsByWbSeqIds(Map<String, Object> params) {
        Long wbSeqId = Convert.toLong(params.get("wbSeqid"));
        List<Long> wbSeqIdLs = Convert.toList(Long.class, params.get("wbSeqids"));
        if (ObjectUtil.isEmpty(wbSeqId) && ObjectUtil.isEmpty(wbSeqIdLs)) {
            return new ArrayList<>();
        }

        QueryWrapper<WmBillTrackcodeDto> wrapper = new QueryWrapper<>();
        if (wbSeqId != null) {
            wrapper.eq("t_wm_bill_trackcode.WB_SeqID", wbSeqId);
        }
        if (ObjectUtil.isNotEmpty(wbSeqIdLs)) {
            wrapper.in("t_wm_bill_trackcode.WB_SeqID", wbSeqIdLs);
        }
        List<WmBillTrackcodeDto> list = baseMapper.findLsByWrapper(wrapper);
        if (Convert.toBool(params.get("validatorUsed"), false)) {
            {
                validatorUsed(list);
            }
        }
        return list;
    }

    private void validatorUsed(List<WmBillTrackcodeDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<ArtSnId> artSnIdLs = new ArrayList<>();

            // 遍历列表，获取artId和snNo，构建artSnIdLs
            list.forEach(dto -> {
                String trackCode = dto.getTrackCode();
                String snNoStr = TrackCodeUtil.getSnNoInfo(null, trackCode);
                if (StrUtil.isNotBlank(snNoStr)) {
                    try {
                        Long snNo = Long.valueOf(snNoStr);
                        ArtSnId artSnId = ArtSnId.builder()
                                .artId(dto.getArtId().intValue())
                                .snNo(snNo)
                                .build();
                        // 判断artSnIdLs不存在的才增加
                        boolean exists = artSnIdLs.stream()
                                .anyMatch(existing ->
                                        existing.getArtId().equals(artSnId.getArtId()) &&
                                                existing.getSnNo().equals(artSnId.getSnNo()));
                        if (!exists) {
                            artSnIdLs.add(artSnId);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("追溯码格式错误，无法解析snNo: {}", trackCode);
                    }
                }
            });

            if (ObjectUtil.isNotEmpty(artSnIdLs)) {
                // 调用服务获取已分发的列表
                List<ArtSnTrackEntity> distributedList = artSnTrackService.findLsByArtSnIdLs(artSnIdLs);

                // 判断list中的artId和snNo是否包含在已分发数据中，设置used状态
                list.forEach(dto -> {
                    String trackCode = dto.getTrackCode();
                    String snNoStr = TrackCodeUtil.getSnNoInfo(null, trackCode);
                    if (StrUtil.isNotBlank(snNoStr)) {
                        try {
                            Long snNo = Long.valueOf(snNoStr);
                            boolean isUsed = distributedList.stream()
                                    .anyMatch(distributed ->
                                            dto.getArtId().intValue() == distributed.getArtId() &&
                                                    snNo.equals(distributed.getSnNo()));
                            dto.setUsed(isUsed);
                        } catch (NumberFormatException e) {
                            log.warn("追溯码格式错误，无法解析snNo: {}", trackCode);
                        }
                    }
                });
            }
        }
    }

    @Override
    @Transactional
    public void addCode(WmBillTrackcodeEntity entity) {
        log.info("addCode obj:{}", JSONUtil.toJsonStr(entity));
        if (entity.getWbSeqid() == null || entity.getLineNo() == null) {
            throw new SaveFailureException("未找到单据信息,请刷新页面重试");
        }
        WmBillEntity wmBill = wmBillService.getById(entity.getWbSeqid());
        if (wmBill == null) {
            throw new SaveFailureException("参数错误，未找到单据信息");
        }
        if (wmBill.getTrackcodeStatus() == null || wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.NOT_COLLECTED.getValue())) {
            wmBillService.update(new LambdaUpdateWrapper<WmBillEntity>()
                    .eq(WmBillEntity::getWbSeqid, entity.getWbSeqid())
                    .set(WmBillEntity::getTrackcodeStatus, WmTrackCodeStatus.COLLECTING.getValue())
            );
            wmBill.setTrackcodeStatus(WmTrackCodeStatus.COLLECTING.getValue());
        }
        if (!wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.COLLECTING.getValue()) && !wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.COLLECTED.getValue())) {
            throw new SaveFailureException("状态错误，不是采集中状态，不允许添加追溯码");
        }
        Integer displayOrder = this.baseMapper.queryNextDisplayOrder(entity.getWbSeqid(), entity.getLineNo());
        entity.setDisplayOrder(displayOrder);
        WmBillDetailEntity wmBillDetailEntity = wmBillDetailService.findById(entity.getWbSeqid(), entity.getLineNo());
        entity.setArtId(wmBillDetailEntity.getArtId());
        if (StrUtil.isBlank(entity.getBatchNo())) {
            entity.setBatchNo(wmBillDetailEntity.getBatchNo());
        }
        entity.setTotal(Convert.toInt(entity.getIsDisassembled(), 0) == 0 ? 1 : 0);
        this.save(entity);
        wmBillService.updateTrackcodeCollected(entity.getWbSeqid());
    }

    @Override
    @Transactional
    public void batchAddCode(Long wbSeqid, Integer lineNo, List<String> codeList) {
        WmBillEntity wmBill = wmBillService.getById(wbSeqid);
        if (wmBill == null) {
            throw new SaveFailureException("参数错误，未找到单据信息");
        }
        if (wmBill.getTrackcodeStatus() == null || wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.NOT_COLLECTED.getValue())) {
            wmBillService.update(new LambdaUpdateWrapper<WmBillEntity>()
                    .eq(WmBillEntity::getWbSeqid, wbSeqid)
                    .set(WmBillEntity::getTrackcodeStatus, WmTrackCodeStatus.COLLECTING.getValue())
            );
            wmBill.setTrackcodeStatus(WmTrackCodeStatus.COLLECTING.getValue());
        }
        if (!wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.COLLECTING.getValue()) && !wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.COLLECTED.getValue())) {
            throw new SaveFailureException("状态错误，不是采集中状态，不允许添加追溯码");
        }
        Integer displayOrder = this.baseMapper.queryNextDisplayOrder(wbSeqid, lineNo);

        List<WmBillTrackcodeEntity> list = new ArrayList<>();
        for (String code : codeList) {
            WmBillTrackcodeEntity entity = new WmBillTrackcodeEntity();
            entity.setWbSeqid(wbSeqid);
            entity.setLineNo(lineNo);
            entity.setTrackCode(code);
            entity.setDisplayOrder(displayOrder++);
//            entity.setBatchNo();
//            entity.setPackLevel();
//            entity.setTotal();
            list.add(entity);
        }
        this.saveBatch(list);
        wmBillService.updateTrackcodeCollected(wbSeqid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delCode(Long wbSeqid, Integer lineNo, String trackCode) {
        WmBillEntity wmBill = wmBillService.getById(wbSeqid);
        if (wmBill == null) {
            throw new SaveFailureException("参数错误，未找到单据信息");
        }
        if (wmBill.getTrackcodeStatus() == null) {
            throw new SaveFailureException("状态错误，不是采集中状态，不允许移除追溯码");
        }
        if (!wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.COLLECTING.getValue()) && !wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.COLLECTED.getValue())) {
            throw new SaveFailureException("状态错误，不是采集中状态，不允许移除追溯码");
        }
        this.remove(new LambdaQueryWrapper<WmBillTrackcodeEntity>()
                .eq(WmBillTrackcodeEntity::getWbSeqid, wbSeqid)
                .eq(lineNo != null, WmBillTrackcodeEntity::getLineNo, lineNo)
                .eq(StrUtil.isNotBlank(trackCode), WmBillTrackcodeEntity::getTrackCode, trackCode)
        );
        wmBillService.updateTrackcodeCollected(wbSeqid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delCodeByWbSeqId(Long wbSeqId, List<Long> wbSeqIdLs) {
        if (wbSeqId != null) {
            delCode(wbSeqId, null, null);
        }
        if (ObjectUtil.isNotEmpty(wbSeqIdLs)) {
            wbSeqIdLs.forEach(id -> {
                delCode(id, null, null);
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBill(Long wbSeqid, Integer inventoryChangeType) {
//        if (inventoryChangeType == null) {
//            throw new SaveFailureException("参数错误，未指定库存变化类型");
//        }
        WmBillEntity wmBill = wmBillService.getById(wbSeqid);
        if (wmBill == null) {
            throw new SaveFailureException("参数错误，未找到单据信息");
        }
        if (wmBill.getTrackcodeStatus() == null) {
            throw new SaveFailureException("状态错误，不是采集中状态，不允许修改库存变化类型");
        }
        if (!wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.COLLECTING.getValue()) && !wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.COLLECTED.getValue())) {
            throw new SaveFailureException("状态错误，不是采集中状态，不允许修改库存变化类型");
        }
        wmBillService.update(new LambdaUpdateWrapper<WmBillEntity>()
                .eq(WmBillEntity::getWbSeqid, wbSeqid)
                .set(WmBillEntity::getInventoryChangeType, inventoryChangeType)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitBill(Long wbSeqid, Integer inventoryChangeType, long userId) {
        WmBillEntity wmBill = wmBillService.getById(wbSeqid);
        if (wmBill == null) {
            throw new SaveFailureException("参数错误，未找到单据信息");
        }
        if (wmBill.getTrackcodeStatus() == null || wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.NOT_COLLECTED.getValue())) {
            // 判断是否明细都是无追溯码状态
            List<WmBillDetailEntity> detailEntities = wmBillDetailService.list(new LambdaQueryWrapper<WmBillDetailEntity>().eq(WmBillDetailEntity::getWbSeqid, wbSeqid));
            List<Long> artIds = detailEntities.stream().map(WmBillDetailEntity::getArtId).collect(Collectors.toList());
            List<ArticleEntity> articleEntities = articleService.listByIds(artIds);
            boolean allNoTrack = articleEntities.stream().allMatch(article -> article.getNoTrackCode() != null && article.getNoTrackCode().equals(1));
            if (!allNoTrack) {
                throw new SaveFailureException("状态错误，还未采集，不允许提交");
            }
        }
        if (wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.REPORTED.getValue())) {
            throw new SaveFailureException("状态错误，已经上报，不允许提交");
        }
        if (!wmBill.getBsnType().equals(WmBillBsnType.SALE_OUT.getValue()) && inventoryChangeType == null) {
            throw new SaveFailureException("参数错误，库存变化类型不能为空");
        }
        wmBillService.update(new LambdaUpdateWrapper<WmBillEntity>()
                .eq(WmBillEntity::getWbSeqid, wbSeqid)
                .set(WmBillEntity::getInventoryChangeType, inventoryChangeType)
                .set(WmBillEntity::getTrackcodeStatus, WmTrackCodeStatus.COLLECTED.getValue())
        );
//        this.report(wmBill, userId);
    }

    @Override
    public void reportBill(Long wbSeqid, long userId) {
        WmBillEntity wmBill = wmBillService.getById(wbSeqid);
        if (wmBill == null) {
            throw new SaveFailureException("参数错误，未找到单据信息");
        }
        if (wmBill.getTrackcodeStatus() == null || wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.NOT_COLLECTED.getValue())) {
            throw new SaveFailureException("状态错误，还未采集，不允许提交");
        }
        if (wmBill.getTrackcodeStatus().equals(WmTrackCodeStatus.REPORTED.getValue())) {
            throw new SaveFailureException("状态错误，已经上报，不允许提交");
        }
        report(wmBill, userId);
    }

    private void report(WmBillEntity wmBill, long userId) {
        Long wbSeqid = wmBill.getWbSeqid();
        if (wmBill.getRecipeId() != null) {
            // 销售出库单
            if (WmBillType.bsnSaleOutLs.contains(wmBill.getWmbillTypeId()) && wmBill.getBsnType().equals(WmBillBsnType.SALE_OUT.getValue())) {
                JSONObject jsonObject = remoteMcispService.artSale(wbSeqid, userId);
                if (jsonObject != null && jsonObject.getInt("code") == 0) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    if (data.getInt("infcode") == 0) {
                        wmBillService.update(new LambdaUpdateWrapper<WmBillEntity>()
                                .eq(WmBillEntity::getWbSeqid, wbSeqid)
                                .set(WmBillEntity::getTrackcodeStatus, WmTrackCodeStatus.REPORTED.getValue())
                        );
                    } else {
                        throw new SaveFailureException("上报商品销售失败," + data.getStr("err_msg"));
                    }
                } else {
                    throw new SaveFailureException("上报商品销售失败," + jsonObject.getStr("msg"));
                }
            } else {
                throw new SaveFailureException("上报商品销售失败，单据类型错误");
            }
        } else {
            if (wmBill.getInventoryChangeType() == null) {
                throw new SaveFailureException("上报失败，未指定库存变化类型");
            }
            JSONObject jsonObject = remoteMcispService.stockChange(wbSeqid, userId);
            if (jsonObject != null && jsonObject.getInt("code") == 0) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (data.getInt("infcode") == 0) {
                    wmBillService.update(new LambdaUpdateWrapper<WmBillEntity>()
                            .eq(WmBillEntity::getWbSeqid, wbSeqid)
                            .set(WmBillEntity::getTrackcodeStatus, WmTrackCodeStatus.REPORTED.getValue())
                    );
                } else {
                    throw new SaveFailureException("上报库存变化失败," + data.getStr("err_msg"));
                }
            } else {
                throw new SaveFailureException("上报库存变化失败," + jsonObject.getStr("msg"));
            }
        }
    }

    @Override
    public void reportCancel(Long wbSeqid, long userId) {
        JSONObject jsonObject = remoteMcispService.trackCodeCancel(wbSeqid, userId);
        if (jsonObject != null && jsonObject.getInt("code") == 0) {
            JSONObject data = jsonObject.getJSONObject("data");
            if (data.getInt("infcode") == 0) {
                wmBillService.update(new LambdaUpdateWrapper<WmBillEntity>()
                        .eq(WmBillEntity::getWbSeqid, wbSeqid)
                        .set(WmBillEntity::getTrackcodeStatus, WmTrackCodeStatus.COLLECTED.getValue())
                );
            } else {
                throw new SaveFailureException("删除失败," + data.getStr("err_msg"));
            }
        } else {
            throw new SaveFailureException("删除失败," + jsonObject.getStr("msg"));
        }
    }

    @Override
    public void setNoTrackCode(Long wbSeqid, Integer lineNo) {
        Long artId = getArtIdFromBillDetailId(wbSeqid, lineNo);
        articleService.update(new LambdaUpdateWrapper<ArticleEntity>()
                .eq(ArticleEntity::getArtId, artId)
                .set(ArticleEntity::getNoTrackCode, 1)
        );
    }

    @Override
    public void clearNoTrackCode(Long wbSeqid, Integer lineNo) {
        Long artId = getArtIdFromBillDetailId(wbSeqid, lineNo);
        articleService.update(new LambdaUpdateWrapper<ArticleEntity>()
                .eq(ArticleEntity::getArtId, artId)
                .set(ArticleEntity::getNoTrackCode, null)
        );
    }

    @Override
    public void setDisassembled(Long wbSeqid, Integer lineNo) {
        Long artId = getArtIdFromBillDetailId(wbSeqid, lineNo);
        articleService.update(new LambdaUpdateWrapper<ArticleEntity>()
                .eq(ArticleEntity::getArtId, artId)
                .set(ArticleEntity::getIsDisassembled, 1)
        );
    }

    @Override
    public void clearDisassembled(Long wbSeqid, Integer lineNo) {
        Long artId = getArtIdFromBillDetailId(wbSeqid, lineNo);
        articleService.update(new LambdaUpdateWrapper<ArticleEntity>()
                .eq(ArticleEntity::getArtId, artId)
                .set(ArticleEntity::getIsDisassembled, null)
        );
    }

    private @NotNull Long getArtIdFromBillDetailId(Long wbSeqid, Integer lineNo) {
        Long artId = wmBillDetailService.getArtIdFromDetailId(wbSeqid, lineNo);
        if (artId == null) {
            throw new SaveFailureException("参数错误，未找到明细信息");
        }
        return artId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allConfirmByWbSeqId(Long wbSeqid) {
        List<WmBillTrackcodeEntity> list = list(new LambdaQueryWrapper<WmBillTrackcodeEntity>().eq(WmBillTrackcodeEntity::getWbSeqid, wbSeqid));
        List<ArtTrackcodeProdEntity> artTrackCodeProdLs = new ArrayList<>();
        list.forEach(wmBillTrackCode -> {
            String trackCode = wmBillTrackCode.getTrackCode();
            Integer prodNo = TrackCodeUtil.getProdNo(null, trackCode);
            if (prodNo != null && artTrackCodeProdLs.stream().noneMatch(e -> prodNo.equals(e.getProdNo()))) {
                artTrackCodeProdLs.add(ArtTrackcodeProdEntity.builder().artId(wmBillTrackCode.getArtId()).prodNo(prodNo).build());
            }
        });

        // 1. 判断非空
        if (ObjectUtil.isNotEmpty(artTrackCodeProdLs)) {
            // 2. 抽取prodNo
            List<Integer> prodNoList = artTrackCodeProdLs.stream()
                    .map(ArtTrackcodeProdEntity::getProdNo)
                    .collect(Collectors.toList());

            // 3. 查询已存在的prodNo
            List<ArtTrackcodeProdEntity> existList = artTrackcodeProdService.list(new LambdaQueryWrapper<ArtTrackcodeProdEntity>().in(ArtTrackcodeProdEntity::getProdNo, prodNoList));

            // 4. 过滤掉已存在的（同时比较prodNo和artId双主键）
            List<ArtTrackcodeProdEntity> saveList = artTrackCodeProdLs.stream()
                    .filter(e -> existList.stream().noneMatch(exist ->
                            exist.getProdNo().equals(e.getProdNo()) && exist.getArtId().equals(e.getArtId())))
                    .collect(Collectors.toList());

            // 5. 再次判断非空，保存
            if (ObjectUtil.isNotEmpty(saveList)) {
                artTrackcodeProdService.saveBatch(saveList);
            }
        }
    }
}
