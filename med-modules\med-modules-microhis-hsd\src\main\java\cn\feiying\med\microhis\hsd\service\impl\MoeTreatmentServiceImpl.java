package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.entity.ClinicianEntity;
import cn.feiying.med.hip.mdi.entity.OrgSettingEntity;
import cn.feiying.med.hip.mdi.service.ClinicianService;
import cn.feiying.med.hip.mdi.service.OrderTypeService;
import cn.feiying.med.hip.mdi.service.OrgSettingService;
import cn.feiying.med.hip.vo.TreatmentVo;
import cn.feiying.med.his.moe.service.TreatmentService;
import cn.feiying.med.microhis.bcs.dto.BillDetailDto;
import cn.feiying.med.microhis.bcs.entity.BillEntity;
import cn.feiying.med.microhis.bcs.service.BillDetailService;
import cn.feiying.med.microhis.bcs.service.BillService;
import cn.feiying.med.microhis.hsd.dto.RecipeDetailDto;
import cn.feiying.med.microhis.hsd.entity.*;
import cn.feiying.med.microhis.hsd.service.*;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024/6/7 18:01
 */
@Slf4j
@Service
public class MoeTreatmentServiceImpl implements MoeTreatmentService {

    @Resource
    private RecipeService recipeService;
    @Resource
    private RecipeDetailService recipeDetailService;
    @Resource
    private VisitService visitService;
    @Resource
    private OrderService orderService;
    @Resource
    private ClinicianService clinicianService;
    @Resource
    private TreatmentService treatmentService;
    @Resource
    private BillService billService;
    @Resource
    private BillDetailService billDetailService;
    @Resource
    private OrgSettingService orgSettingService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notifyTreatment(List<Long> recipeIdLs) {
        log.info("notifyTreatment recipeIdLs:{}", JSONUtil.toJsonStr(recipeIdLs));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (ObjectUtil.isEmpty(recipeIdLs)) {
            throw new SaveFailureException("请指定处方编号");
        }
        List<TreatmentVo> treatmentLs = new ArrayList<>();
        List<OrderEntity> orderLs = new ArrayList<>();
        // 只处理检验
        List<Integer> mtaRecipeTypeLs = ListUtil.of(RecipeType.sys.getValue());
        List<RecipeEntity> recipeLs = recipeService.listByIds(recipeIdLs);
        String rxNos = recipeLs.stream().filter(p -> p.getPaidStatus() == null || !p.getPaidStatus().equals(PaidStatus.paid.getValue())).map(RecipeEntity::getRxNo).collect(Collectors.joining(StrUtil.COMMA));
        if (StrUtil.isNotBlank(rxNos)) {
            throw new SaveFailureException("处方：" + rxNos + "未处于支付完结状态");
        }
        List<RecipeDetailDto> detailLs = recipeDetailService.findLsByRecipeIdLs(recipeIdLs);
        List<Long> visitIdLs = recipeLs.stream().map(RecipeEntity::getVisitId).distinct().collect(Collectors.toList());
        List<VisitEntity> visitLs = visitService.listByIds(visitIdLs);
        List<Long> orderRecipeIdLs = recipeLs.stream().filter(p -> mtaRecipeTypeLs.contains(p.getRecipeTypeId())).map(RecipeEntity::getRecipeId).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(orderRecipeIdLs)) {
            orderLs = orderService.list(new LambdaQueryWrapper<OrderEntity>().in(OrderEntity::getRecipeId, orderRecipeIdLs));
        }
        List<Long> clinicianIdLs = recipeLs.stream().filter(p -> p.getClinicianId() != null).map(RecipeEntity::getClinicianId).distinct().collect(Collectors.toList());
        if (ObjectUtil.isEmpty(clinicianIdLs)) {
            clinicianIdLs = visitLs.stream().map(VisitEntity::getClinicianId).distinct().collect(Collectors.toList());
        }
        List<ClinicianEntity> clinicianLs = clinicianService.listByIds(clinicianIdLs);
        List<BillEntity> billLs = billService.list(new LambdaQueryWrapper<BillEntity>().in(BillEntity::getRecipeId, recipeIdLs));
        for (RecipeEntity recipe : recipeLs) {
            OrgSettingEntity orgSetting = orgSettingService.getById(recipe.getOrgId());
            // consumableAppendToCure = 1,绑定耗材合并到治疗单时没有处置划价单 不会触发RecipeEventType.PAY_DISPOSAL处方划价单支付产生的MOE处置记录
            boolean consumableAppendToCure = orgSetting != null && Convert.toInt(orgSetting.getConsumableAppendToCure(), 0) == 1;
            VisitEntity visit = visitLs.stream().filter(p -> p.getVisitId().equals(recipe.getVisitId())).findFirst().orElse(null);
            if (visit == null) {
                throw new SaveFailureException("处方：" + recipe.getRxNo() + "未关联就诊信息");
            }
            OrderEntity order = orderLs.stream().filter(p -> p.getRecipeId().equals(recipe.getRecipeId())).findFirst().orElse(null);
            if (mtaRecipeTypeLs.contains(recipe.getRecipeTypeId()) && order == null) {
                throw new SaveFailureException("医技处方：" + recipe.getRxNo() + "未关联申请单");
            }
            ClinicianEntity clinician = clinicianLs.stream().filter(p -> p.getClinicianId().equals(recipe.getClinicianId() == null ? visit.getClinicianId() : recipe.getClinicianId())).findFirst().orElse(null);
            if (order != null && clinician == null) {
                throw new SaveFailureException("处方：" + recipe.getRxNo() + "未关联医生信息");
            }
            List<RecipeDetailDto> recipeDetailLs = detailLs.stream().filter(p -> p.getRecipeId().equals(recipe.getRecipeId())).collect(Collectors.toList());
            BillEntity treatmentBill = billLs.stream().filter(p -> p.getBillTypeId().equals(BillType.treatmentBill.getValue()) && p.getRecipeId().equals(recipe.getRecipeId())).findFirst().orElse(null);
            List<TreatmentVo.TreatmentDetail> treatmentDetailLs = new ArrayList<>();
            for (RecipeDetailDto recipeDetail : recipeDetailLs) {
                if (order != null || (recipeDetail.getFeedMethod() != null && recipeDetail.getFeedMethod().equals(FeedMethod.injection.getValue()))) {
                    TreatmentVo.TreatmentDetail treatmentDetail = new TreatmentVo.TreatmentDetail();
                    treatmentDetail.setRecipeLineNo(recipeDetail.getLineNo());
                    treatmentDetail.setArtId(recipeDetail.getArtId());
                    treatmentDetail.setRouteId(recipeDetail.getRouteId());
                    treatmentDetail.setFreqCode(recipeDetail.getFreqCode());
                    treatmentDetail.setTotal(recipeDetail.getTotal());
                    treatmentDetail.setUnit(recipeDetail.getUnit());
                    treatmentDetail.setUnitType(recipeDetail.getUnitType());
                    treatmentDetail.setStRequired(recipeDetail.getStRequired());
                    treatmentDetail.setGroupNo(recipeDetail.getGroupNo());
                    treatmentDetail.setGroupMark(recipeDetail.getGroupMark());
                    treatmentDetail.setSpecimenTypeId(recipeDetail.getSpecimenTypeId());
                    treatmentDetail.setBodypartDesc(recipeDetail.getBodypartDesc());
                    treatmentDetail.setBodypartCount(recipeDetail.getBodypartCount());
                    treatmentDetail.setLabId(order == null ? null : order.getLabId());
                    treatmentDetail.setNotice(recipeDetail.getNotice());
                    treatmentDetail.setDpm(recipeDetail.getDpm());
                    treatmentDetail.setMealCells(recipeDetail.getMealCells());
                    treatmentDetail.setMealDoses(recipeDetail.getMealDoses());
                    treatmentDetail.setPaidCycles(recipeDetail.getPaidCycles());
                    treatmentDetail.setHeadingTimes(recipeDetail.getHeadingTimes());
                    treatmentDetail.setFeedMethod(recipeDetail.getFeedMethod());
                    treatmentDetail.setFreqCycleBeats(recipeDetail.getFreqCycleBeats());
                    treatmentDetail.setFreqCycleSeconds(recipeDetail.getFreqCycleSeconds());
                    treatmentDetailLs.add(treatmentDetail);
                }
            }
            // 只处理有数据业务
            if (ObjectUtil.isEmpty(treatmentDetailLs)) {
                continue;
            }
            TreatmentVo treatment = new TreatmentVo();
            treatment.setRecipeId(recipe.getRecipeId());
            treatment.setRecipeTypeId(recipe.getRecipeTypeId());
            treatment.setOrgId(recipe.getOrgId());
            treatment.setVisitId(recipe.getVisitId());
            treatment.setApplyDeptcode(recipe.getApplyDeptcode());
            treatment.setExceDeptcode(recipe.getExceDeptcode());
            treatment.setTimeApplied(recipe.getTimeSubmitted());
            treatment.setSectionId(recipe.getSectionId());
            treatment.setClinicTypeId(visit.getClinicTypeId());
            treatment.setPatientName(visit.getPatientName());
            treatment.setAgeOfYears(visit.getAgeOfYears());
            treatment.setAgeOfDays(Convert.toInt(visit.getAgeOfDays()));
            treatment.setPatientId(visit.getPatientId());
            treatment.setGenderId(visit.getGenderId());
            if (order != null) {
                treatment.setOrderId(order.getOrderId());
                treatment.setOrderTypeId(order.getOrderTypeId());
                treatment.setPurposeDesc(order.getPurposeDesc());
                treatment.setMedicalHistory(order.getMedicalHistory());
            }
            treatment.setClinicianCode(clinician.getClinicianNo());
            treatment.setClinicianName(clinician.getClinicianName());
            treatment.setAccessionNo(recipe.getRxNo());
            treatment.setRecipeDate(Convert.toInt(sdf.format(recipe.getTimeCreated())));
            treatment.setSubjectId(recipe.getSubjectId());
            treatment.setTimes(recipe.getTimes());
            treatment.setPaidStatus(PaidStatus.paid.getValue());
            treatment.setBseqid(treatmentBill == null ? null : treatmentBill.getBseqid());
            treatment.setDetailLs(treatmentDetailLs);
            if (!consumableAppendToCure) {
                setConsumable(treatmentBill, treatment);
            }
            treatmentLs.add(treatment);
        }
        if (ObjectUtil.isNotEmpty(treatmentLs)) {
            treatmentService.notifyTreatment(treatmentLs);
        }
    }

    private void setConsumable(BillEntity treatmentBill, TreatmentVo treatment) {
        if (treatmentBill != null) {
            List<TreatmentVo.TreatmentConsumable> consumableLs = new ArrayList<>();
            Map<String, Object> params = new HashMap<>(2);
            params.put("bSeqId", treatmentBill.getBseqid());
            params.put("stockReq", 1);
            //获取耗材
            List<BillDetailDto> stockArtLs = billDetailService.findLsByParams(params);
            if (ObjectUtil.isNotEmpty(stockArtLs)) {
                for (BillDetailDto stockArt : stockArtLs) {
                    TreatmentVo.TreatmentConsumable consumable = new TreatmentVo.TreatmentConsumable();
                    consumable.setArtId(stockArt.getArtId());
                    consumable.setArtName(stockArt.getArtName());
                    consumable.setArtSpec(stockArt.getArtSpec());
                    consumable.setPackCells(stockArt.getPackCells());
                    consumable.setPackTotal(stockArt.getPackTotal());
                    consumable.setCellTotal(stockArt.getCellTotal());
                    consumableLs.add(consumable);
                }
                treatment.setConsumableLs(consumableLs);
            }
        }
    }
}
