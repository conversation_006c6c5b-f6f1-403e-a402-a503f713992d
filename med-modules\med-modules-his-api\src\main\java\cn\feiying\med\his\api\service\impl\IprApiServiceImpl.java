package cn.feiying.med.his.api.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.mdi.entity.ClinicianEntity;
import cn.feiying.med.hip.mdi.entity.DiseaseEntity;
import cn.feiying.med.hip.mdi.entity.UserCodeEntity;
import cn.feiying.med.hip.mdi.service.ClinicianService;
import cn.feiying.med.hip.mdi.service.UserCodeService;
import cn.feiying.med.his.api.model.req.ipr.IprCancelModel;
import cn.feiying.med.his.api.model.req.ipr.IprModel;
import cn.feiying.med.his.api.model.req.visit.VisitModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.IprRespModel;
import cn.feiying.med.his.api.service.IprApiService;
import cn.feiying.med.saas.api.service.RemoteHsdService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 电子住院证相关服务实现类
 */
@Slf4j
@Service
public class IprApiServiceImpl implements IprApiService {

    @Resource
    private ClinicianService clinicianService;
    @Resource
    private UserCodeService userCodeService;
    @Resource
    private RemoteHsdService remoteHsdService;

    @Override
    public ApiResultModel<IprRespModel> saveOrUpdateIpr(IprModel iprModel) {
        log.info("电子住院证数据保存及修改开始处理");
        try {
            // 实现电子住院证数据保存及修改逻辑
            validateModel(iprModel);
            JSONObject jsonObject = toJSONObject(iprModel);
            Long iprId = remoteHsdService.saveIprRecord(jsonObject);
            // 返回成功的住院申请流水号信息
            IprRespModel responseModel = new IprRespModel();
            // 这里应该返回新增的住院申请流水号，临时用789表示
            responseModel.setIprId(iprId);
            
            return ApiResultModel.success(responseModel);
        } catch (Exception e) {
            log.error("电子住院证数据保存及修改处理异常", e);
            return ApiResultModel.error("电子住院证数据保存及修改失败：" + e.getMessage());
        }
    }

    @Override
    public ApiResultModel<?> cancelIpr(IprCancelModel iprModel) {
        log.info("电子住院证作废开始处理");
        try {
            if (iprModel == null || ObjectUtil.isEmpty(iprModel.getIprList())) {
                throw new SaveFailureException("待作废的电子住院证不能为空");
            }
            
            // 获取用户信息
            Long userId = null;
            if (StrUtil.isNotBlank(iprModel.getUserCode())) {
                List<UserCodeEntity> userCodeList = userCodeService.list(
                        Wrappers.<UserCodeEntity>lambdaQuery().eq(UserCodeEntity::getUserCode, iprModel.getUserCode()));
                userId = ObjectUtil.isNotEmpty(userCodeList) ? userCodeList.get(0).getUserId() : null;
            }
            
            // 获取住院申请流水号列表
            List<Long> iprIdList = iprModel.getIprList().stream()
                    .map(IprCancelModel.IprItem::getIprId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (ObjectUtil.isEmpty(iprIdList)) {
                throw new SaveFailureException("待作废的电子住院证不能为空");
            }
            
            // 调用远程服务作废电子住院证
            remoteHsdService.cancelIprRecord(userId, iprIdList);
            
            return ApiResultModel.success();
        } catch (Exception e) {
            log.error("电子住院证作废处理异常", e);
            return ApiResultModel.error("电子住院证作废失败：" + e.getMessage());
        }
    }

    private void validateModel(IprModel iprModel) {
        if (ObjectUtil.isEmpty(iprModel)) {
            throw new SaveFailureException("入参不能为空");
        }
        if (iprModel.getVisitId() == null && iprModel.getPatientId() == null && StrUtil.isBlank(iprModel.getIdcertNo())) {
            throw new SaveFailureException("诊疗ID和患者ID和身份证号不能同时为空");
        }
        if (iprModel.getVisitId() == null && iprModel.getOrgId() == null) {
            throw new SaveFailureException("诊疗ID为空是，机构ID不能为空");
        }
        if (StrUtil.isBlank(iprModel.getPatientName())) {
            throw new SaveFailureException("患者姓名不能为空");
        }
        if (StrUtil.isBlank(iprModel.getDeptCode())) {
            throw new SaveFailureException("入院科室不能为空");
        }
        if (StrUtil.isBlank(iprModel.getClinicianCode())) {
            throw new SaveFailureException("申请医师不能为空");
        }
        if (iprModel.getClinicDate() == null) {
            throw new SaveFailureException("入院日期不能为空");
        }
    }

    private JSONObject toJSONObject(IprModel iprModel) {
        JSONObject jsonObject = JSONUtil.parseObj(iprModel);
        // 医师
        if (StrUtil.isNotBlank(iprModel.getClinicianCode())) {
            ClinicianEntity clinician = clinicianService.findByClinicianNo(iprModel.getClinicianCode());
            if (clinician != null) {
                jsonObject.set("clinicianId", clinician.getClinicianId());
            }
        }
        return jsonObject;
    }
} 