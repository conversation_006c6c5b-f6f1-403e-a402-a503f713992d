package cn.feiying.med.clinics_wm.controller;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.feiying.med.clinics_wm.dto.WmBillDetailDto;
import cn.feiying.med.clinics_wm.dto.WmBillDto;
import cn.feiying.med.clinics_wm.entity.*;
import cn.feiying.med.clinics_wm.service.*;
import cn.feiying.med.clinics_wm.utils.ExcelImportCalculateUtil;
import cn.feiying.med.clinics_wm.vo.ChangeBatchNoVo;
import cn.feiying.med.clinics_wm.vo.WmBillForm;
import cn.feiying.med.clinics_wm.vo.WmBillCheckVo;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.ArticleUtil;
import cn.feiying.med.hip.mdi.dto.OrgFormularyDto;
import cn.feiying.med.hip.mdi.entity.ArticleEntity;
import cn.feiying.med.hip.mdi.service.ArticleService;
import cn.feiying.med.hip.mdi.service.OrgFormularyService;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.common.utils.R;
import cn.feiying.med.common.annotation.ReSubmitCheck;
import cn.feiying.med.aspect.annotation.SysLog;
import cn.feiying.med.common.annotation.SucPermissions;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;
/**
 * 出入库单表
 *
 * <AUTHOR> @email
 * @date 2024-05-25 11:30:06
 */
@RestController
@RequestMapping("/clinics_wm/wmbill")
public class WmBillController extends AbstractController {
    @Resource
    private WmBillService wmBillService;
    @Resource
    private WmBillDetailService wmBillDetailService;
    @Resource
    private ArticleService articleService;
    @Resource
    private OrgFormularyService orgFormularyService;
    @Resource
    private ReqService reqService;
    @Resource
    private OrgArtService orgArtService;
    @Resource
    private WmService wmService;

    @Resource
    private DeptArtService deptArtService;
    /**
     * 分页列表
     */
    @PostMapping("/page")
    @SucPermissions("clinics_wm:wmbill:page")
    public R page(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        PageUtils page = wmBillService.queryPage(orgId, params);
        return R.ok(page);
    }


    @PostMapping("/reservedPage")
    @SucPermissions("clinics_wm:wmbill:reservedPage")
    public R<PageUtils<WmBillDto>> reservedPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        PageUtils<WmBillDto> page = wmService.queryReservedPage(orgId, params);
        return R.ok(page);
    }

    /**
     * 查询处方列表
     */
    @PostMapping("/recipePage")
    @SucPermissions("clinics_wm:wmbill:recipePage")
    public R recipePage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        PageUtils page = wmBillDetailService.queryRecipePage(orgId, params);
        return R.ok(page);
    }

    /**
     * 处方发药记录分页列表
     */
    @PostMapping("/recipeDeliverPage")
    @SucPermissions("clinics_wm:wmbill:recipeDeliverPage")
    public R recipeDeliverPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        PageUtils page = wmBillDetailService.queryRecipeDeliverPage(orgId, params);
        return R.ok(page);
    }

    /**
     * 明细分页列表
     */
    @PostMapping("/detailPage")
    @SucPermissions("clinics_wm:wmbill:detailPage")
    public R detailPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        Long wbSeqid = Convert.toLong(params.get("wbSeqid"));
        Long orgId = Convert.toLong(params.get("orgId"));
        if (orgId == null) {
            orgId = getOrgId(request);
        }
        PageUtils page = wmBillDetailService.queryPage(wbSeqid, params, orgId);
        return R.ok(page);
    }

    @PostMapping("/detailLsByIds")
    @SucPermissions("clinics_wm:wmbill:detailLsByIds")
    public R<List<WmBillDetailDto>> detailLsByIds(@RequestBody Map<String, Object> params) {
        List<WmBillDetailDto> list = wmBillDetailService.findLsByIds(params);
        return R.ok(list);
    }

    @PostMapping("/detailAndStockNoPage")
    @SucPermissions("clinics_wm:wmbill:detailAndStockNoPage")
    public R detailAndStockNoPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        Long wbSeqid = Convert.toLong(params.get("wbSeqid"));
        Long orgId = Convert.toLong(params.get("orgId"));
        if (orgId == null) {
            orgId = getOrgId(request);
        }
        List<WmBillDetailDto> detailDtoList = wmBillDetailService.detailAndStockNoPage(wbSeqid, params, orgId);
        return R.ok(detailDtoList);
    }

    /**
     * 明细分页列表
     */
    @PostMapping("/detailGroupByArtPage")
    @SucPermissions("clinics_wm:wmbill:detailPage")
    public R detailGroupByArtPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        Long wbSeqid = Convert.toLong(params.get("wbSeqid"));
        PageUtils page = wmBillDetailService.queryGroupByArtPage(wbSeqid, params);
        return R.ok(page);
    }

    /**
     * 明细分页列表
     */
    @PostMapping("/detailGroupByArtPageByIds")
    @SucPermissions("clinics_wm:wmbill:detailPage")
    public R detailGroupByArtPageByIds(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        List<Long> wbSeqids = Convert.toList(Long.class, params.get("wbSeqids"));
        PageUtils page = wmBillDetailService.queryGroupByArtPageByIds(wbSeqids, params);
        return R.ok(page);
    }

    /**
     * 明细分页列表
     */
    @PostMapping("/recipeDetailPageByVisit")
    @SucPermissions("clinics_wm:wmbill:detailPage")
    public R recipeDetailPageByVisit(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        Long visitId = Convert.toLong(params.get("visitId"));
        PageUtils page = wmBillDetailService.queryRecipePageByVisitId(visitId, params);
        return R.ok(page);
    }

    /**
     * 明细分页列表
     */
    @PostMapping("/detailTrackCodePage")
    @SucPermissions("clinics_wm:wmbill:detailTrackCodePage")
    public R detailTrackCodePage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        Long wbSeqid = Convert.toLong(params.get("wbSeqid"));
        PageUtils page = wmBillDetailService.queryTrackCodePage(wbSeqid, params);
        return R.ok(page);
    }

    /**
     * 明细分页列表
     */
    @PostMapping("/detailList")
    @SucPermissions("clinics_wm:wmbill:detailList")
    public R detailList(HttpServletRequest request, @RequestBody WmBillEntity params) {
        List<WmBillDetailDto> list = wmBillDetailService.queryList(params.getWbSeqid(), true);
        return R.ok(list);
    }


    /**
     * 信息
     */
    @PostMapping("/info")
    @SucPermissions("clinics_wm:wmbill:info")
    public R info(HttpServletRequest request, @RequestBody WmBillEntity params) {
        WmBillDto entity = wmBillService.findDtoById(params.getWbSeqid());

        return R.ok(entity);
    }

    /**
     * 新增
     * 采购制单-保存
     */
    @SysLog("wmbill新增")
    @ReSubmitCheck
    @PostMapping("/save")
    @SucPermissions("clinics_wm:wmbill:save")
    public R save(HttpServletRequest request, @RequestBody WmBillForm form) {
        long userId = getUserId(request);
        long orgId = getOrgId(request);
        long wbSeqid = wmBillService.saveBill(orgId, userId, form.getWmBill(), form.getDetails());

        return R.ok(wbSeqid);
    }

    /**
     * 提交
     * 采购制单-提交
     */
    @SysLog("wmbill提交")
    @ReSubmitCheck
    @PostMapping("/submit")
    @SucPermissions("clinics_wm:wmbill:submit")
    public R submit(HttpServletRequest request, @RequestBody WmBillForm form) {
        long userId = getUserId(request);
        long orgId = getOrgId(request);
        long wbSeqid = wmBillService.submitBill(orgId, userId, form.getWmBill(), form.getDetails(), true);

        return R.ok(wbSeqid);
    }

    /**
     * 作废
     */
    @SysLog("wmbill作废")
    @ReSubmitCheck
    @PostMapping("/cancel")
    @SucPermissions("clinics_wm:wmbill:cancel")
    public R cancel(HttpServletRequest request, @RequestBody WmBillCheckVo params) {
        long userId = getUserId(request);
        long orgId = getOrgId(request);
        wmBillService.cancelBill(orgId, userId, params.getWbSeqid());
        return R.ok();
    }

    /**
     * wmBill释放加作废，只针对处方以及购销单
     */
    @SysLog("wmBill释放加作废，只针对处方以及购销单")
    @ReSubmitCheck
    @PostMapping("/releaseAndCancel")
    @SucPermissions("clinics_wm:wmbill:releaseAndCancel")
    public R releaseAndCancel(HttpServletRequest request, @RequestBody WmBillCheckVo params) {
        long userId = getUserId(request);
        long orgId = getOrgId(request);
        wmService.releaseAndCancel(orgId, userId, params.getWbSeqid());
        return R.ok();
    }

    /**
     * 审核
     */
    @SysLog("wmbill审核")
    @ReSubmitCheck
    @PostMapping("/check")
    @SucPermissions("clinics_wm:wmbill:check")
    public R check(HttpServletRequest request, @RequestBody WmBillCheckVo params) {
        long userId = getUserId(request);
        long orgId = getOrgId(request);
        wmBillService.validateBill(orgId, userId, params.getWbSeqid(), params.getIsPass() != null && params.getIsPass().equals(1));

        return R.ok();
    }

    /**
     * 审核
     */
    @SysLog("wmbill批量审核")
    @ReSubmitCheck
    @PostMapping("/batchCheck")
    @SucPermissions("clinics_wm:wmbill:check")
    public R batchCheck(HttpServletRequest request, @RequestBody WmBillCheckVo params) {
        long userId = getUserId(request);
        long orgId = getOrgId(request);
        wmBillService.batchValidateBill(orgId, userId, params.getWbdSeqids(), params.getIsPass() != null && params.getIsPass().equals(1));

        return R.ok();
    }

    /**
     * 保存批号调整请求
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/saveChangeBatchNo")
    @SucPermissions("clinics_wm:deptstock:saveChangeBatchNo")
    public R saveChangeBatchNo(HttpServletRequest request, @RequestBody ChangeBatchNoVo params) {
        long orgId = getOrgId(request);
        long userId = getUserId(request);
        wmBillService.changeBatchNo(orgId, userId, params.getDeptCode(), params.getArtId(),
                params.getOldStockNo(), params.getNewStockNo(), params.getTotalPacks(), params.getTotalCells());
        return R.ok();
    }

    /**
     * 票据冲红
     *
     * @param request
     * @param wmBill
     * @return
     */
    @PostMapping("/wmBillReturn")
    public R wmBillReturn(HttpServletRequest request, @RequestBody WmBillEntity wmBill) {
        long userId = getUserId(request);
        long orgId = getOrgId(request);
        reqService.wmBillReturn(orgId, userId, wmBill.getWbSeqid());
        return R.ok();
    }


    /**
     * 采购冲红
     * 可明细冲红，可反复冲红
     *
     * @param request
     * @param form
     * @return
     */
    @PostMapping("/inStoreWmBillReturn")
    public R inStoreWmBillReturn(HttpServletRequest request, @RequestBody WmBillForm form) {
        long userId = getUserId(request);
        long orgId = getOrgId(request);
        reqService.inStoreWmBillReturn(orgId, userId, form.getWmBill(), form.getDetails());
        return R.ok();
    }

    /**
     * 修改票据明细批次号
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/updateStockNo")
    public R updateStockNo(HttpServletRequest request, @RequestBody WmBillDetailEntity params) {
        long userId = getUserId(request);
        long orgId = getOrgId(request);
        wmBillDetailService.updateStockNo(orgId, params.getWbSeqid(), params.getLineNo(), params.getStockNo());
        return R.ok();
    }

    @RequestMapping("/impExcel")
    public R<?> impExcel(HttpServletRequest request, MultipartRequest multiRequest) throws IOException {
        long orgId = getOrgId(request);
        MultipartFile mpf = multiRequest.getFile("file");
        String originalFilename = mpf.getOriginalFilename();
        String[] filename = originalFilename.split("\\.");
        if (!"xlsx".equals(filename[filename.length - 1]) && !"xls".equals(filename[filename.length - 1])) {
            return R.error("请上传excel文件");
        }
        InputStream inputStream = mpf.getInputStream();
        ExcelReader reader = ExcelUtil.getReader(inputStream);

        // 智能识别表头所在行
        int headerRowIndex = -1;
        int maxRows = Math.min(10, reader.getRowCount()); // 最多检查前10行
        for (int i = 0; i < maxRows; i++) {
            List<Object> row = reader.readRow(i);
            if (row != null && !row.isEmpty()) {
                String firstCell = Convert.toStr(row.get(0));
                if ("商品编码".equals(firstCell)) {
                    headerRowIndex = i;
                    break;
                }
            }
        }

        if (headerRowIndex == -1) {
            return R.error("未找到包含商品编码的表头行");
        }

        // readExcel
//		List<Map<String, Object>> dataList = reader.read(2, 3, Integer.MAX_VALUE);
        List<Map<String, Object>> dataList = reader.read(headerRowIndex, headerRowIndex + 1, Integer.MAX_VALUE);

        //List<Map<String, Object>> dataList = reader.read(1, 2, Integer.MAX_VALUE);
        if (dataList.size() < 1) {
            return R.error("没有读取到数据，请检查Excel文件是否正确");
        }
//		商品编码	医保编码	商品名称	商品规格	生产厂家	整包单位	拆零单位	拆零系数	生产批号 生产日期	有效期至	整包数量 	整包单价	拆零数量	拆零单价	金额

        boolean useArtId = true;
        List<Long> artCodeList = dataList.stream().map(p -> Convert.toLong(p.get("商品编码"))).collect(Collectors.toList());
        artCodeList = artCodeList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());


        List<ArticleEntity> articleEntities = new ArrayList<>();
        List<ArticleEntity> miCodeArticleEntities = new ArrayList<>();
        if (!artCodeList.isEmpty()) {
            articleEntities = articleService.list(new LambdaQueryWrapper<ArticleEntity>().in(ArticleEntity::getArtId, artCodeList));
        }

        if (artCodeList.isEmpty()) {
            useArtId = false;
            List<String> miCodeList = dataList.stream().map(p -> Convert.toStr(p.get("医保编码"))).collect(Collectors.toList());
            miCodeList = miCodeList.stream().filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            miCodeArticleEntities = articleService.list(new LambdaQueryWrapper<ArticleEntity>().in(ArticleEntity::getMiCode, miCodeList));
        }
        if (articleEntities.isEmpty() && miCodeArticleEntities.isEmpty()) {
            return R.error("根据商品编码和医保编码都没有匹配到任何品种，请检查Excel文件是否正确");
        }

        int lineNo = 1;
        List<WmBillDetailDto> list = new ArrayList<>();
        List<OrgArtEntity> orgArtEntities = new ArrayList<>();
        Map<Long, OrgArtEntity> orgArtMap = new HashMap<>();
        for (Map<String, Object> data : dataList) {
            WmBillDetailDto dto = new WmBillDetailDto();
            dto.setLineNo(lineNo++);

            Long artId = Convert.toLong(data.get("商品编码"));
            String miCode = Convert.toStr(data.get("医保编码"));
            ArticleEntity articleEntity = null;
            if (useArtId) {
                articleEntity = articleEntities.stream().filter(p -> p.getArtId().equals(artId)).findFirst().orElse(null);
            } else {
                if (StrUtil.isNotBlank(miCode)) {
                    ArticleEntity miCodeArticleEntity = miCodeArticleEntities.stream().filter(p -> p.getMiCode().equals(miCode)).findFirst().orElse(null);
                    if (miCodeArticleEntity != null) {
                        articleEntity = miCodeArticleEntity;
                    }
                }
            }

            if (articleEntity == null) {
//				dto.setArtId(artId);
                dto.setMiCode(Convert.toStr(data.get("医保编码")));
                dto.setArtName(Convert.toStr(data.get("商品名称")));
                dto.setArtSpec(Convert.toStr(data.get("商品规格")));
                dto.setProducer(Convert.toStr(data.get("生产厂家")));
                dto.setPackUnit(Convert.toStr(data.get("整包单位")));
                dto.setCellUnit(Convert.toStr(data.get("拆零单位")));
                dto.setPackCells(Convert.toInt(data.get("拆零系数")));
                dto.setNotes("未匹配到商品编码对应的商品信息");
            } else {
//				String artName = Convert.toStr(data.get("商品名称"));
//				if (!articleEntity.getArtName().equals(artName)) {
//					return R.error(artName + "，与系统中匹配到的商品名【" + articleEntity.getArtName() + "】不一致，请检查数据是否正确");
//				}
                dto.setArtId(articleEntity.getArtId());
                dto.setArtCode(articleEntity.getArtCode());
                dto.setMiCode(articleEntity.getMiCode());
                dto.setArtName(articleEntity.getArtName());
                dto.setArtSpec(articleEntity.getArtSpec());
                dto.setProducer(articleEntity.getProducer());
                dto.setPackUnit(articleEntity.getPackUnit());
                dto.setCellUnit(articleEntity.getCellUnit());
                if (articleEntity.getPackCells() == null) {
                    articleEntity.setPackCells(1);
                }
                dto.setPackCells(articleEntity.getPackCells());
                if(!dto.getPackCells().equals(Convert.toInt(data.get("拆零系数"))))
                {
                    return R.error(dto.getArtName()+"与系统的系数不匹配，请检查！！！");
                }
                dto.setBatchNo(Convert.toStr(data.get("生产批号")));
                if (StrUtil.isBlank(dto.getBatchNo())) {
                    return R.error("生产批号数据不能为空");
                }
                dto.setDateManufactured(Convert.toInt(data.get("生产日期")));
                if (dto.getDateManufactured() == null) {
                    return R.error("生产日期数据不能为空，格式为YYYYMMDD");
                }
                dto.setExpiry(Convert.toInt(data.get("有效期至")));
                if (dto.getExpiry() == null) {
                    return R.error("有效期至数据不能为空，格式为YYYYMMDD");
                }
                dto.setTotalPacks(Convert.toInt(data.get("整包数量")));
                dto.setPackPrice(Convert.toBigDecimal(data.get("整包单价")));
                dto.setTotalCells(Convert.toBigDecimal(data.get("拆零数量")));
//				dto.setCellPrice(Convert.toBigDecimal(data.get("拆零单价")));
//				if (dto.getPackPrice() == null || dto.getPackPrice().compareTo(BigDecimal.ZERO) == 0) {
//					if (dto.getCellPrice() == null || dto.getCellPrice().compareTo(BigDecimal.ZERO) == 0) {
//						dto.setNotes("整包单价与拆零单位不能同时为空");
//					} else {
//						dto.setPackPrice(dto.getCellPrice().multiply(BigDecimal.valueOf(dto.getPackCells())));
//					}
//				}
                if ((dto.getTotalPacks() == null || dto.getTotalPacks().equals(0))
                        && (dto.getTotalCells() == null || dto.getTotalCells().compareTo(BigDecimal.ZERO) == 0)) {
                    throw new SaveFailureException(dto.getArtId() + "整包数量与拆零数量不能同时为空");
                }
                if (dto.getPackPrice() == null || dto.getPackPrice().compareTo(BigDecimal.ZERO) == 0) {
//                    if (detail.getTotalPacks() != null) {
//                        throw new SaveFailureException(detail.getArtId() + "整包单价不能为空");
//                    } else {
//                        detail.setPackPrice(detail.getCellPrice().multiply(Convert.toBigDecimal(articleEntity.getPackCells(), BigDecimal.ONE)));
//                    }
                    throw new SaveFailureException(dto.getArtId() + "整包单价不能为空");
                }
                // 使用公用方法设置基础价格信息
                ExcelImportCalculateUtil.setBasicPriceInfo(dto, dto.getPackCells());
//				dto.setTrackCode(Convert.toStr(data.get("商品追溯码")));
                //BigDecimal pctAdd = Convert.toBigDecimal(data.get("加成比例"));
                BigDecimal pctAdd = Convert.toBigDecimal(data.get("加成比例"));
                String splitFlag = Convert.toStr(data.get("是否拆零"));
                if (pctAdd != null || StrUtil.isNotBlank(splitFlag)) {
                    // 使用公用方法计算基础零售价
                    ExcelImportCalculateUtil.SaleAmountResult result = ExcelImportCalculateUtil.calculateSaleAmount(dto, pctAdd);

                    // 设置计算结果到DTO
                    dto.setSaleAmount(result.getSaleAmount());
                    dto.setSalePctAdd(result.getPctAdd());
                    dto.setSalePackPrice(result.getSalePackPrice());
                    dto.setSaleCellPrice(result.getSaleCellPrice());
                    Long artIds = articleEntity.getArtId();
                    if (!orgArtMap.containsKey(artIds)) {
                        OrgArtEntity orgArtEntity = new OrgArtEntity();
                        orgArtEntity.setOrgId(orgId);
                        orgArtEntity.setArtId(artIds);
                        orgArtEntity.setPctAdd(pctAdd);
                        orgArtEntity.setSplittable(StrUtil.isNotBlank(splitFlag) && splitFlag.equals("是") ? 1 : 0);
                        orgArtMap.put(artIds, orgArtEntity);
                    }
//                    OrgArtEntity orgArtEntity = new OrgArtEntity();
//                    orgArtEntity.setOrgId(orgId);
//                    orgArtEntity.setArtId(articleEntity.getArtId());
//                    orgArtEntity.setPctAdd(pctAdd);
//                    orgArtEntity.setSplittable(StrUtil.isNotBlank(splitFlag) && splitFlag.equals("是") ? 1 : 0);
//                    orgArtEntities.add(orgArtEntity);
//					OrgArtEntity orgArtEntity = orgArtService.findById(orgId, articleEntity.getArtId());
//					if (orgArtEntity == null) {
//						orgArtEntity = new OrgArtEntity();
//						orgArtEntity.setOrgId(orgId);
//						orgArtEntity.setArtId(articleEntity.getArtId());
//						orgArtEntity.setPctAdd(pctAdd);
//						orgArtEntity.setSplittable(splitFlag.equals("是")? 1: 0);
//						orgArtService.saveEntity(orgArtEntity);
//					}
                } else {
                    return R.error("加成比例 和 是否可拆零不能同时为空");
                }

                if (dto.getArtId() != null) {
                    Optional<WmBillDetailDto> itemOptional = list.stream().filter(p -> p.getArtId() != null && p.getArtId().equals(dto.getArtId()) && p.getBatchNo().equals(dto.getBatchNo())).findFirst();
                    if (itemOptional.isPresent()) {
                        dto.setNotes("存在相同批号的记录");
                    }
                }
            }
            list.add(dto);
        }
        orgArtEntities.addAll(orgArtMap.values());
        orgArtService.batchSaveOrUpdate(orgId, orgArtEntities);
        return R.ok(list);
    }



    private void setExcelStyle(BigExcelWriter writer, String title, String notes) throws IOException {
        writer.setColumnWidth(0, 20);
        writer.setColumnWidth(1, 20);
        writer.setColumnWidth(2, 40);
        writer.setColumnWidth(3, 20);
        writer.setColumnWidth(4, 40);
        writer.setColumnWidth(5, 15);
        writer.setColumnWidth(6, 15);
        writer.setColumnWidth(7, 15);
        writer.setColumnWidth(8, 15);
        writer.setColumnWidth(9, 15);
        writer.setColumnWidth(10, 15);
        writer.setColumnWidth(11, 15);
        writer.setColumnWidth(12, 15);
        writer.setColumnWidth(13, 15);
        writer.setColumnWidth(14, 15);

        writer.merge(14, title);

        writer.merge(14);
        writer.writeRow(ListUtil.of("备注：" + Convert.toStr(notes, "")));

        CellStyle cellStyle = writer.getOrCreateCellStyle(0, 1);
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
    }

    private List<Object> getExcelTitleRow() {
        List<Object> titleRow = new ArrayList<>();
        titleRow.add("商品编码");
        titleRow.add("医保编码");
        titleRow.add("商品名称");
        titleRow.add("商品规格");
        titleRow.add("生产厂家");
        titleRow.add("整包单位");
        titleRow.add("拆零单位");
        titleRow.add("拆零系数");
        titleRow.add("生产批号");
        titleRow.add("生产日期");
        titleRow.add("有效期至");
        titleRow.add("整包单价");
        titleRow.add("整包数量");
        titleRow.add("拆零数量");
//		titleRow.add("拆零单价");
        titleRow.add("金额");
        titleRow.add("加成比例");
        titleRow.add("是否拆零");
        return titleRow;
    }

    /**
     * 下载单据明细
     *
     * @param request
     * @param response
     * @param params
     * @throws Exception
     */
    @PostMapping("/downloadExcelById")
    @SucPermissions("spd:orgart:downloadExcelById")
    public void downloadExcelById(HttpServletRequest request, HttpServletResponse response, @RequestBody Map<String, Object> params) throws Exception {
        Long orgId = getOrgId(request);
        ServletOutputStream out = response.getOutputStream();
        BigExcelWriter writer = new BigExcelWriter();
        Long wbSeqid = Convert.toLong(params.get("wbSeqid"));
        WmBillDto wmBillDto = wmBillService.findDtoById(wbSeqid);
        String notes = "票据ID：" + Convert.toStr(params.get("wbSeqid")) + ",往来单位:" + wmBillDto.getCustName();
        if (StrUtil.isNotBlank(wmBillDto.getBsnAbstract())) {
            notes = notes + "。" + wmBillDto.getBsnAbstract();
        }
        setExcelStyle(writer, wmBillDto.getDeptName() + "单据明细", notes);
        List<List<Object>> dataRows = new ArrayList<>();
        List<Object> titleRow = new ArrayList<>();
        titleRow.add("商品编码");
        titleRow.add("医保编码");
        titleRow.add("商品名称");
        titleRow.add("商品规格");
        titleRow.add("生产厂家");
        titleRow.add("拆零系数");
        titleRow.add("生产批号");
        titleRow.add("生产日期");
        titleRow.add("有效期至");
        titleRow.add("整包数量");
        titleRow.add("整包单位");
        titleRow.add("拆零数量");
        titleRow.add("拆零单位");
        titleRow.add("整包单价");
//		titleRow.add("拆零单价");
        titleRow.add("金额");
        titleRow.add("加成比例");
        titleRow.add("是否拆零");
        dataRows.add(titleRow);

        List<WmBillDetailDto> list = wmBillDetailService.queryList(wbSeqid, true);
        for (WmBillDetailDto dto : list) {
            List<Object> dataRow = new ArrayList<>();
            dataRow.add(Convert.toStr(dto.getArtId()));
            dataRow.add(dto.getMiCode());
            dataRow.add(dto.getArtName());
            dataRow.add(dto.getArtSpec());
            dataRow.add(dto.getProducer());
            dataRow.add(dto.getPackCells());
            dataRow.add(dto.getBatchNo());
            dataRow.add(dto.getDateManufactured());
            dataRow.add(dto.getExpiry());
            dataRow.add(dto.getTotalPacks());
            dataRow.add(dto.getPackUnit());
            dataRow.add(dto.getTotalCells());
            dataRow.add(dto.getCellUnit());
            dataRow.add(dto.getPackPrice());
//			dataRow.add(dto.getCellPrice());
            dataRow.add(dto.getAmount());
//			dataRow.add("加成比例");
//			dataRow.add("是否拆零");
            dataRows.add(dataRow);
        }
        writer.write(dataRows);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("单据明细", "UTF-8") + ".xlsx");

        writer.flush(out, true);
        writer.close();
        IoUtil.close(out);
    }

    /**
     * 下载一个空的Excel
     *
     * @param request
     * @param response
     * @param params
     * @throws Exception
     */
    @PostMapping("/downloadEmptyExcel")
    @SucPermissions("spd:orgart:downloadEmptyExcel")
    public void downloadEmptyExcel(HttpServletRequest request, HttpServletResponse response, @RequestBody Map<String, Object> params) throws Exception {
        ServletOutputStream out = response.getOutputStream();
        BigExcelWriter writer = new BigExcelWriter();
        setExcelStyle(writer, "商品明细导入模板", null);
        List<List<Object>> dataRows = new ArrayList<>();
        dataRows.add(getExcelTitleRow());
        List<Object> dataRow = new ArrayList<>();
        dataRow.add("10000001");
        dataRow.add("测试医保编码001");
        dataRow.add("测试商品，导入前请删除此商品");
        dataRow.add("0.5mg*50粒");
        dataRow.add("xx公司");
        dataRow.add("盒");
        dataRow.add("粒");
        dataRow.add("50");
        dataRow.add("20240101");
        dataRow.add("20240101");
        dataRow.add("20261231");
        dataRow.add("50");
        dataRow.add("100");
        dataRow.add("");
        dataRow.add("");
        dataRow.add("15");
        dataRow.add("");
        dataRows.add(dataRow);

        writer.write(dataRows);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("门诊库房导入模板", "UTF-8") + ".xlsx");

        writer.flush(out, true);
        writer.close();
        IoUtil.close(out);
    }

    /**
     * 下载一个含所有机构处方集商品的Excel
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @PostMapping("/downloadAllArtExcel")
    @SucPermissions("spd:orgart:downloadAllArtExcel")
    public void downloadAllArtExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Long orgId = getOrgId(request);
        ServletOutputStream out = response.getOutputStream();
        BigExcelWriter writer = new BigExcelWriter();
        setExcelStyle(writer, "商品明细导入模板", null);
        List<List<Object>> dataRows = new ArrayList<>();
        dataRows.add(getExcelTitleRow());

        Map<String, Object> params = new HashMap<>();
        params.put("S_IN_t_article__Cat_Type_ID", "101,102,103,104,105");
        params.put("S_EQ_t_article__Stock_req", "1");
        List<OrgFormularyDto> list = orgFormularyService.queryDtoList(orgId, params);
        for (OrgFormularyDto dto : list) {
            List<Object> dataRow = new ArrayList<>();
            dataRow.add(Convert.toStr(dto.getArtId()));
            dataRow.add(dto.getMiCode());
            dataRow.add(dto.getArtName());
            dataRow.add(dto.getArtSpec());
            dataRow.add(dto.getProducer());
            dataRow.add(dto.getPackUnit());
            dataRow.add(dto.getCellUnit());
            dataRow.add(dto.getPackCells());
//			dataRow.add("生产批号");
//			dataRow.add("有效期至");
//			dataRow.add("整包数量");
//			dataRow.add("整包单价");
//			dataRow.add("拆零数量");
//			dataRow.add("拆零单价");
//			dataRow.add("金额");
            dataRows.add(dataRow);
        }
        writer.write(dataRows);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("门诊库房导入模板", "UTF-8") + ".xlsx");

        writer.flush(out, true);
        writer.close();
        IoUtil.close(out);
    }

    /**
     * 病区已发药列表-按发药记录
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/sectionDeliveredPage")
    public R sectionDeliveredPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        PageUtils page = wmBillService.querySectionDeliveredPage(orgId, params);
        return R.ok(page);
    }

    /**
     * 病区已发药列表-按发药记录-汇总
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/sectionDeliveredSummaryPage")
    public R sectionDeliveredSummaryPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        Long orgId = Convert.toLong(params.get("orgId"));
        if (orgId == null) {
            orgId = getOrgId(request);
        }
        PageUtils page = wmBillService.querySectionDeliveredSummaryPage(orgId, params);
        return R.ok(page);
    }

    /**
     * 病区发药明细-按发药记录
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/sectionDeliveredDetailPage")
    public R sectionDeliveredDetailPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        PageUtils page = wmBillDetailService.querySectionDeliveredDetailPage(params);
        return R.ok(page);
    }

    /**
     * 病区发药明细-按发药记录
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/sectionDeliveredDetailList")
    public R sectionDeliveredDetailList(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        List<WmBillDetailDto> list = wmBillDetailService.querySectionDeliveredDetailList(params);
        return R.ok(list);
    }

    /**
     * 病区已发药列表-按病区汇总
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/sectionDeliveredPageBySection")
    public R sectionDeliveredPageBySection(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        PageUtils page = wmBillService.querySectionDeliveredPageBySection(orgId, params);
        return R.ok(page);
    }

    /**
     * 病区发药明细-按病区汇总
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/sectionDeliveredDetailPageBySection")
    public R sectionDeliveredDetailPageBySection(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        PageUtils page = wmBillDetailService.querySectionDeliveredDetailPageBySection(orgId, params);
        return R.ok(page);
    }

    /**
     * 病区发药明细-按病区汇总（返回List）
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/sectionDeliveredDetailListBySection")
    public R sectionDeliveredDetailListBySection(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        List<WmBillDetailDto> list = wmBillDetailService.querySectionDeliveredDetailListBySection(orgId, params);
        return R.ok(list);
    }

    /**
     * 病区已发药列表-按患者汇总
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/sectionDeliveredPageByPatient")
    public R sectionDeliveredPageByPatient(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        PageUtils page = wmBillService.querySectionDeliveredPageByPatient(orgId, params);
        return R.ok(page);
    }

    /**
     * 病区发药明细-按患者汇总
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/sectionDeliveredDetailPageByPatient")
    public R sectionDeliveredDetailPageByPatient(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        PageUtils page = wmBillDetailService.querySectionDeliveredDetailPageByPatient(orgId, params);
        return R.ok(page);
    }

    /**
     * 病区发药明细-按患者汇总（返回List）
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/sectionDeliveredDetailListByPatient")
    public R sectionDeliveredDetailListByPatient(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        long orgId = getOrgId(request);
        List<WmBillDetailDto> list = wmBillDetailService.querySectionDeliveredDetailListByPatient(orgId, params);
        return R.ok(list);
    }

    /**
     * 病区-待退药-退药列表汇总
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/returingMedicationSumPage")
    @SucPermissions("clinics_wm:wmbill:returingMedicationSumPage")
    public R returingMedicationSumPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        Long orgId = Convert.toLong(params.get("orgId"));
        if (orgId == null) {
            orgId = getOrgId(request);
        }
        PageUtils page = wmBillService.returingMedicationSumPage(orgId, params);
        return R.ok(page);
    }

    /**
     * 病区-待退药-退药列表明细
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/returnBillPage")
    @SucPermissions("clinics_wm:wmbill:returnBillPage")
    public R returnBillPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        Long orgId = Convert.toLong(params.get("orgId"));
        if (orgId == null) {
            orgId = getOrgId(request);
        }
        PageUtils page = wmBillService.returnBillPage(orgId, params);
        return R.ok(page);
    }

    /**
     * 病区-待退药-药品明细
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/pendingDetailByWbSeqidsPage")
    @SucPermissions("clinics_wm:wmbill:pendingDetailByWbSeqidsPage")
    public R pendingDetailByWbSeqidsPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        PageUtils page = wmBillDetailService.pendingDetailByWbSeqidsPage(params);
        return R.ok(page);
    }

    /**
     * 病区-已退药-退药记录-按病区汇总
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/returnedBillBySectionPage")
    @SucPermissions("clinics_wm:wmbill:returnedBillBySectionPage")
    public R returnedBillBySectionPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        Long orgId = Convert.toLong(params.get("orgId"));
        if (orgId == null) {
            orgId = getOrgId(request);
        }
        PageUtils page = wmBillService.returnedBillBySectionPage(orgId, params);
        return R.ok(page);
    }

    /**
     * 病区-已退药-退药记录/按病区汇总-药品明细
     *
     * @param request
     * @param params
     * @return
     */
    @PostMapping("/returnDetailByWbSeqidsPage")
    public R returnDetailByWbSeqidsPage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        List<WmBillDetailDto> list = wmBillDetailService.returnDetailByWbSeqidsPage(params);
        return R.ok(list);
    }
    /**
     * 拆零盒整操作
     * 处理JSON对象转换和拆零盒整计算
     *
     * @param request HTTP请求
     * @param params 参数，包含record的JSON字符串
     * @return 操作结果
     */
    @SysLog("拆零盒整操作")
    @ReSubmitCheck
    @PostMapping("/performSplitPack")
    @SucPermissions("clinics_wm:wmbill:performSplitPack")
    public R performSplitPack(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        Long orgId = Convert.toLong(params.get("orgId"));
        if (orgId == null) {
            orgId = getOrgId(request);
        }

        try {
            wmBillService.performSplitPack(orgId, params);
            return R.ok("拆零盒整操作执行成功");
        } catch (Exception e) {
            return R.error("拆零盒整操作失败: " + e.getMessage());
        }
    }

}
