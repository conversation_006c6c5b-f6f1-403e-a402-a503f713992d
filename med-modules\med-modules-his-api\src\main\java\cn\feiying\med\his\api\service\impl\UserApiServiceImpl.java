package cn.feiying.med.his.api.service.impl;

import cn.feiying.med.his.api.model.req.user.UserReqModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.OrgRespModel;
import cn.feiying.med.his.api.model.resp.TokenRespModel;
import cn.feiying.med.his.api.service.UserApiService;
import cn.feiying.med.saas.api.service.RemoteEamService;
import cn.feiying.med.saas.api.service.RemoteIdmService;
import cn.feiying.med.saas.api.vo.EamOrgVo;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.RegEx;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户API服务实现类
 */
@Service
@Slf4j
public class UserApiServiceImpl implements UserApiService {

    @Resource
    private RemoteEamService remoteEamService;
    @Resource
    private RemoteIdmService remoteIdmService;

    @Override
    public ApiResultModel<TokenRespModel> getToken(UserReqModel userReq) {
        log.info("获取用户token，用户ID: {}", userReq.getUserId());
        
        try {
            // 模拟业务逻辑，生成token
            TokenRespModel tokenResp = new TokenRespModel();
            JSONObject tokenJson = remoteIdmService.getTokenByUserId(userReq.getUserId(), userReq.getOrgId());
            if (ObjectUtils.isNotEmpty(tokenJson)) {
                tokenResp = JSONUtil.toBean(tokenJson, TokenRespModel.class);
            }
            
            return ApiResultModel.success(tokenResp);
        } catch (Exception e) {
            log.error("获取用户token失败", e);
            return ApiResultModel.error("获取用户token失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResultModel<List<OrgRespModel>> getOrgList(UserReqModel userReq) {
        log.info("获取用户机构列表，用户ID: {}", userReq.getUserId());
        
        try {
            List<EamOrgVo> orgLs = remoteEamService.findOrgLsByUserId(userReq.getUserId());
            if (ObjectUtils.isEmpty(orgLs)) {
                return ApiResultModel.success(new ArrayList<>());
            }
            List<OrgRespModel> orgList = orgLs.stream().map(org -> {
                OrgRespModel orgResp = new OrgRespModel();
                BeanUtils.copyProperties(org, orgResp);
                return orgResp;
            }).collect(Collectors.toList());
            
            return ApiResultModel.success(orgList);
        } catch (Exception e) {
            log.error("获取用户机构列表失败", e);
            return ApiResultModel.error("获取用户机构列表失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResultModel<?> chooseOrg(UserReqModel userReq) {
        log.info("切换用户当前机构，用户ID: {}, 机构ID: {}", userReq.getUserId(), userReq.getOrgId());
        
        try {
            remoteEamService.chooseOrg(userReq.getUserId(), userReq.getOrgId());
            
            return ApiResultModel.success();
        } catch (Exception e) {
            log.error("切换用户当前机构失败", e);
            return ApiResultModel.error("切换用户当前机构失败: " + e.getMessage());
        }
    }
} 