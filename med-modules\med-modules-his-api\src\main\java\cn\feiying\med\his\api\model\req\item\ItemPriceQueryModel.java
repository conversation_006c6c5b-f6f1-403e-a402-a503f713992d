package cn.feiying.med.his.api.model.req.item;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目价格查询请求模型
 */
@Data
public class ItemPriceQueryModel {
    
    /**
     * 申请单号
     */
    private String orderId;

    /**
     * 机构ID
     */
    private Long orgId;
    
    /**
     * 项目明细列表
     */
    private List<Item> itemLs;
    
    /**
     * 项目价格查询明细项
     */
    @Data
    public static class Item {
        /**
         * 行号
         */
        private Integer lineNo;
        
        /**
         * 项目编码
         */
        private String artCode;
        
        /**
         * 项目名称
         */
        private String artName;
        
        /**
         * 数量
         */
        private BigDecimal total;
        
        /**
         * 诊疗类型 1-门诊，2-住院
         */
        private Integer clinicType;
    }
} 