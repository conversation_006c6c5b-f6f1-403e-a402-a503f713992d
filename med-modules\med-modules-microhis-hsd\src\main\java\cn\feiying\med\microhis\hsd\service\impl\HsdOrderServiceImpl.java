package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.*;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.dto.*;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.hip.mdi.vo.*;
import cn.feiying.med.hip.mpi.entity.*;
import cn.feiying.med.hip.mpi.service.*;
import cn.feiying.med.hip.ucc.dto.OrgParamDto;
import cn.feiying.med.hip.ucc.enums.UccParamKey;
import cn.feiying.med.hip.ucc.service.OrgParamService;
import cn.feiying.med.hip.vo.TreatmentVo;
import cn.feiying.med.hip.vo.pacs.sdjn.req.InspectReportDataReq;
import cn.feiying.med.hip.vo.pacs.sdjn.resp.InspectReportDataResp;
import cn.feiying.med.his.moe.dto.TestResultDto;
import cn.feiying.med.his.moe.entity.*;
import cn.feiying.med.his.moe.service.*;
import cn.feiying.med.his.moe.vo.*;
import cn.feiying.med.microhis.bcs.dto.BillDetailDto;
import cn.feiying.med.microhis.bcs.entity.*;
import cn.feiying.med.microhis.bcs.service.*;
import cn.feiying.med.microhis.hsd.dto.*;
import cn.feiying.med.microhis.hsd.entity.*;
import cn.feiying.med.microhis.hsd.service.*;
import cn.feiying.med.microhis.hsd.vo.OrderReportVo;
import cn.feiying.med.saas.api.model.CvrEventModel;
import cn.feiying.med.saas.api.model.OrderReportModel;
import cn.feiying.med.saas.api.service.*;
import cn.feiying.med.saas.api.vo.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HsdOrderServiceImpl implements HsdOrderService {

    @Value("${sys.aes.secret}")
    private String aesSecret;

    @Resource
    private SysSequenceService sequenceService;
    @Resource
    private RecipeService recipeService;
    @Resource
    private RecipeExtraService recipeExtraService;
    @Resource
    private OrderService orderService;
    @Resource
    private VisitService visitService;
    @Resource
    private VisitExtraService visitExtraService;
    @Resource
    private HsdRecipeService hsdRecipeService;
    @Resource
    private PatientService patientService;
    @Resource
    private OrgDeptService orgDeptService;
    @Resource
    private SchedService schedService;
    @Resource
    private MtBodypartService mtBodypartService;
    @Resource
    private RemotePrService remotePrService;
    @Resource
    private RemoteMtaService remoteMtaService;
    @Resource
    private ArticleService articleService;
    @Resource
    private OrderTypeService orderTypeService;
    @Resource
    private PackageDetailService packageDetailService;
    @Resource
    private OrgItemPriceService orgItemPriceService;
    @Resource
    private BillDetailService billDetailService;
    @Resource
    private MoeOrderService moeOrderService;
    @Resource
    private OrderReportService orderReportService;
    @Resource
    private ReportService reportService;
    @Resource
    private ReportExtraService reportExtraService;
    @Resource
    private IdService idService;
    @Resource
    private StudyService studyService;
    @Resource
    private OrderPendingService orderPendingService;
    @Resource
    private OrderItemPendingService orderItemPendingService;
    @Resource
    private GenderService genderService;
    @Resource
    private OrgParamService orgParamService;
    @Resource
    private UserCodeService userCodeService;
    @Resource
    private CvrEventService cvrEventService;
    @Resource
    private TreatmentService treatmentService;
    @Resource
    private ClinicianService clinicianService;
    @Resource
    private LisService lisService;
    @Resource
    private LisTestService lisTestService;
    @Resource
    private TestResultService testResultService;

    @Override
    public List<OrderDto> findOrderLs(Map<String, Object> params) {
        List<OrderDto> list = orderService.findLsByVisit(params);
        if (ObjectUtil.isNotEmpty(list)) {
            List<OrderDetailDto> orderDetailLs = orderService.findOrderDetailLs(params);
            setArticle(orderDetailLs);
            setReport(list);
            list.forEach(order -> {
                List<OrderDetailDto> detailLs = orderDetailLs.stream().filter(d -> d.getOrderId().equals(order.getOrderId())).collect(Collectors.toList());
//                BigDecimal price = detailLs.stream().map(OrderDetailDto::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal amount = detailLs.stream().map(OrderDetailDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal price = amount.divide(Convert.toBigDecimal(order.getTotal(), BigDecimal.ONE), 2, RoundingMode.HALF_UP);
                order.getRecipeDetailLs().forEach(detail -> {
                    Optional<OrderDetailDto> orderDetail = detailLs.stream().filter(d -> d.getRecipeId().equals(detail.getRecipeId())
                            && d.getLineNo().equals(detail.getLineNo()) && d.getArtId().equals(detail.getArtId())).findFirst();
                    if (orderDetail.isPresent()) {
                        detail.setCellPrice(orderDetail.get().getPrice());
                        detail.setAmount(orderDetail.get().getAmount());
                        detail.setPackageDetailLs(orderDetail.get().getPackageDetailLs());
                    }
                });
                if (ObjectUtil.isNotEmpty(detailLs)) {
                    order.setDiseaseDiagCode(detailLs.get(0).getDiseaseDiagCode());
                    order.setDiseaseDiagName(detailLs.get(0).getDiseaseDiagName());
                    order.setSymptomDiagCode(detailLs.get(0).getSymptomDiagCode());
                    order.setSymptomDiagName(detailLs.get(0).getSymptomDiagName());
                    order.setRecipeDiagLs(detailLs.get(0).getRecipeDiagLs());
                }
                order.setPrice(price);
                order.setAmount(amount);
            });
        }
        return list;
    }

    @Override
    public List<OrderDetailDto> findOrderDetailLs(Map<String, Object> params) {
        List<OrderDetailDto> list = orderService.findOrderDetailLs(params);
        setArticle(list);
        return list;
    }

    private void setArticle(List<OrderDetailDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<Long> artIdLs = list.stream().map(OrderDetailDto::getArtId).distinct().collect(Collectors.toList());
            List<ArticleEntity> articleLs = articleService.listByIds(artIdLs);
            List<Integer> orderTypeIdLs = articleLs.stream().map(ArticleEntity::getOrderTypeId).filter(Objects::nonNull).collect(Collectors.toList());
            List<OrderTypeEntity> orderTypeLs = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(orderTypeIdLs)) {
                orderTypeLs = orderTypeService.listByIds(orderTypeIdLs);
            }
            for (OrderDetailDto item : list) {
                ArticleEntity article = articleLs.stream().filter(a -> a.getArtId().equals(item.getArtId())).findFirst().orElse(new ArticleEntity());
                item.setArtCode(article.getArtCode());
                item.setArtName(article.getArtName());
                item.setArtSpec(article.getArtSpec());
                item.setProducer(article.getProducer());
                item.setIsPackage(article.getIsPackage());
                OrderTypeEntity orderType = orderTypeLs.stream().filter(a -> a.getOrderTypeId().equals(article.getOrderTypeId())).findFirst().orElse(new OrderTypeEntity());
                item.setOrderTypeCode(orderType.getTypeCode());
                item.setOrderTypeName(orderType.getTypeName());
            }
            // 获取价格
//            List<Long> packageIdLs = articleLs.stream().filter(p -> p.getIsPackage() != null && p.getIsPackage() == 1).map(ArticleEntity::getArtId).distinct().collect(Collectors.toList());
//            List<PackageDetailEntity> packageDetailLs = new ArrayList<>();
//            if (ObjectUtil.isNotEmpty(packageIdLs)) {
//                packageDetailLs = packageDetailService.list(Wrappers.lambdaQuery(PackageDetailEntity.class).in(PackageDetailEntity::getPackageId, packageIdLs));
//            }
            List<Long> detailArtIdLs = articleLs.stream().filter(p -> p.getIsPackage() == null || p.getIsPackage() == 0).map(ArticleEntity::getArtId).collect(Collectors.toList());
            List<Long> orgIdLs = list.stream().map(OrderDetailDto::getOrgId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<Long> bseqidLs = list.stream().map(OrderDetailDto::getBseqid).filter(Objects::nonNull).collect(Collectors.toList());
            // 治疗处方的bseqid
            Set<Long> cureBseqidLs = list.stream().filter(d -> d.getCureBseqid() != null).map(OrderDetailDto::getCureBseqid).collect(Collectors.toSet());
            if (ObjectUtil.isNotEmpty(cureBseqidLs)) {
                bseqidLs.addAll(cureBseqidLs);
            }

            List<BillDetailEntity> billDetailLs = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(bseqidLs)) {
                billDetailLs = billDetailService.list(Wrappers.lambdaQuery(BillDetailEntity.class).in(BillDetailEntity::getBseqid, bseqidLs));
            }
            if (ObjectUtil.isNotEmpty(orgIdLs)) {
                for (Long orgId : orgIdLs) {
                    List<OrderDetailDto> orderDetailLs = list.stream().filter(d -> d.getOrgId().equals(orgId)).collect(Collectors.toList());
                    List<PackageDetailVo> packageDetailVoLs = orderDetailLs.stream().map(d -> {
                        ArticleEntity article = articleLs.stream().filter(a -> a.getArtId().equals(d.getArtId())).findFirst().orElse(null);
                        if (article != null && article.getIsPackage() != null && article.getIsPackage() == 1) {
                            return PackageDetailVo.builder()
                                    .recipeId(d.getRecipeId())
                                    .lineNo(d.getLineNo())
                                    .orderId(d.getOrderId())
                                    .packageId(d.getArtId())
                                    .total(d.getTotal())
                                    .clinicTypeId(d.getClinicTypeId())
                                    .build();
                        }
                        return null;
                    }).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
                    List<PackagePriceDto> packagePriceLs = new ArrayList<>();
                    List<ArticleEntity> packageArtLs = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(packageDetailVoLs)) {
                        try {
                            packagePriceLs = packageDetailService.findPackagePriceIgnoreNoPrice(orgId, null, null, packageDetailVoLs);
                        } catch (Exception e) {
                            log.error("获取套餐价格失败", e);
                            e.printStackTrace();
                            packagePriceLs = new ArrayList<>();
                        }
                        List<Long> packageArtIdLs = packagePriceLs.stream().map(PackagePriceDto::getArtId).distinct().collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(packageArtIdLs)) {
                            packageArtLs = articleService.listByIds(packageArtIdLs);
                        }
                    }
                    List<OrgItemPriceEntity> orgItemPriceLs = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(detailArtIdLs)) {
                        orgItemPriceLs = orgItemPriceService.list(Wrappers.lambdaQuery(OrgItemPriceEntity.class).in(OrgItemPriceEntity::getArtId, detailArtIdLs).eq(OrgItemPriceEntity::getOrgId, orgId));
                    }
                    for (OrderDetailDto item : orderDetailLs) {
                        List<PackageDetailDto> packageDetailLs = new ArrayList<>();
                        BigDecimal price = BigDecimal.ZERO;
                        BigDecimal amount = BigDecimal.ZERO;
                        if (item.getIsPackage() != null && item.getIsPackage() == 1) {
                            if (item.getBseqid() != null || item.getCureBseqid() != null) {
                                List<BillDetailEntity> billLs = billDetailLs.stream().filter(b -> ((item.getBseqid() != null && b.getBseqid().equals(item.getBseqid())) || (item.getCureBseqid() != null && item.getCureBseqid().equals(b.getBseqid())))
                                        && b.getPackageId() != null && b.getPackageId().equals(item.getArtId())).collect(Collectors.toList());
                                for (BillDetailEntity bill : billLs) {
                                    ArticleEntity article = packageArtLs.stream().filter(a -> a.getArtId().equals(bill.getArtId())).findFirst().orElse(new ArticleEntity());
                                    PackageDetailDto packageDetail = new PackageDetailDto();
                                    packageDetail.setPackageId(item.getArtId());
                                    packageDetail.setArtId(bill.getArtId());
                                    packageDetail.setArtCode(article.getArtCode());
                                    packageDetail.setArtName(article.getArtName());
                                    packageDetail.setUnit(bill.getUnit());
                                    packageDetail.setPrice(bill.getPrice() == null ? BigDecimal.ZERO : bill.getPrice());
                                    packageDetail.setTotal(Convert.toBigDecimal(bill.getTotal(), BigDecimal.ONE));
                                    packageDetail.setAmount(bill.getAmount() == null ? BigDecimal.ZERO : bill.getAmount());
                                    packageDetail.setArtTypeId(article.getArtTypeId());
                                    packageDetail.setCatTypeId(article.getCatTypeId());
                                    packageDetail.setForTreatment(article.getForTreatment());
                                    amount = amount.add(Convert.toBigDecimal(packageDetail.getAmount(), BigDecimal.ZERO));
                                    packageDetailLs.add(packageDetail);
                                }
                            } else {
                                // todo Convert.toInt(detail.getStockReq(), 0) == 1 从sectionArt取价格
                                List<PackagePriceDto> priceLs = packagePriceLs.stream().filter(p -> p.getPackageId().equals(item.getArtId())
                                        && p.getRecipeId().equals(item.getRecipeId()) && p.getLineNo().equals(item.getLineNo())).collect(Collectors.toList());
                                for (PackagePriceDto detail : priceLs) {
                                    ArticleEntity article = packageArtLs.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(new ArticleEntity());
                                    PackageDetailDto packageDetail = new PackageDetailDto();
                                    packageDetail.setPackageId(item.getArtId());
                                    packageDetail.setArtId(detail.getArtId());
                                    packageDetail.setArtCode(article.getArtCode());
                                    packageDetail.setArtName(article.getArtName());
                                    packageDetail.setUnit(detail.getUnit());
                                    packageDetail.setUnitType(detail.getUnitType());
                                    packageDetail.setPrice(detail.getPrice());
                                    packageDetail.setArtTypeId(article.getArtTypeId());
                                    packageDetail.setCatTypeId(article.getCatTypeId());
                                    packageDetail.setForTreatment(article.getForTreatment());
                                    packageDetail.setIsUnchargeable(detail.getIsUnchargeable());
//                                    packageDetail.setTotal(detail.getTotal());
                                    if (Convert.toInt(detail.getIsUnchargeable(), 0) == 0) {
                                        packageDetail.setAmount(detail.getAmount());
                                    } else {
                                        packageDetail.setAmount(BigDecimal.ZERO);
                                    }
//                                if (item.getBseqid() == null) {
//                                    OrgItemPriceEntity orgItemPrice = orgItemPriceLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(new OrgItemPriceEntity());
//                                    price = price.add(orgItemPrice.getUnitPrice() == null ? BigDecimal.ZERO : orgItemPrice.getUnitPrice());
//                                    packageDetail.setPrice(orgItemPrice.getUnitPrice() == null ? BigDecimal.ZERO : orgItemPrice.getUnitPrice());
//                                } else
                                    amount = amount.add(Convert.toBigDecimal(packageDetail.getAmount(), BigDecimal.ZERO));
                                    packageDetailLs.add(packageDetail);
                                }
                            }
                            if (item.getCureRecipeId() != null && item.getCureBseqid() == null) {
                                List<PackagePriceDto> priceLs = packagePriceLs.stream().filter(p -> p.getPackageId().equals(item.getArtId())
                                        && p.getRecipeId().equals(item.getRecipeId()) && p.getLineNo().equals(item.getLineNo())).collect(Collectors.toList());
                                for (PackagePriceDto detail : priceLs) {
                                    PackageDetailDto packageDetail = packageDetailLs.stream().filter(p -> p.getPackageId().equals(item.getArtId()) && p.getArtId().equals(detail.getArtId())).findFirst().orElse(null);
                                    if (packageDetail == null) {
                                        ArticleEntity article = packageArtLs.stream().filter(a -> a.getArtId().equals(detail.getArtId())).findFirst().orElse(new ArticleEntity());
                                        packageDetail = new PackageDetailDto();
                                        packageDetail.setPackageId(item.getArtId());
                                        packageDetail.setArtId(detail.getArtId());
                                        packageDetail.setArtCode(article.getArtCode());
                                        packageDetail.setArtName(article.getArtName());
                                        packageDetail.setUnit(detail.getUnit());
                                        packageDetail.setUnitType(detail.getUnitType());
                                        packageDetail.setPrice(detail.getPrice());
                                        packageDetail.setArtTypeId(article.getArtTypeId());
                                        packageDetail.setCatTypeId(article.getCatTypeId());
                                        packageDetail.setForTreatment(article.getForTreatment());
                                        packageDetail.setIsUnchargeable(detail.getIsUnchargeable());
                                        if (Convert.toInt(detail.getIsUnchargeable(), 0) == 0) {
                                            packageDetail.setAmount(detail.getAmount());
                                        } else {
                                            packageDetail.setAmount(BigDecimal.ZERO);
                                        }
                                        amount = amount.add(Convert.toBigDecimal(packageDetail.getAmount(), BigDecimal.ZERO));
                                        packageDetailLs.add(packageDetail);
                                    }
                                }
                            }
                            price = amount.divide(item.getTotal() == null || item.getTotal().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : item.getTotal(), 2, RoundingMode.HALF_UP);
                        } else if (item.getBseqid() != null) {
                            List<BillDetailEntity> bdLs = billDetailLs.stream().filter(p -> p.getBseqid().equals(item.getBseqid()) && p.getArtId().equals(item.getArtId())).collect(Collectors.toList());
                            if (ObjectUtil.isNotEmpty(bdLs)) {
                                price = bdLs.stream().map(BillDetailEntity::getPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                amount = bdLs.stream().map(BillDetailEntity::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                            }
                        } else {
                            OrgItemPriceEntity orgItemPrice = orgItemPriceLs.stream().filter(p -> p.getArtId().equals(item.getArtId())).findFirst().orElse(null);
                            if (orgItemPrice != null) {
                                price = orgItemPrice.getUnitPrice() == null ? BigDecimal.ZERO : orgItemPrice.getUnitPrice();
                            }
                            amount = amount.add(price.multiply(item.getTotal() == null || item.getTotal().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : item.getTotal()));
                        }
                        item.setPrice(price);
                        item.setAmount(amount);
                        item.setPackageDetailLs(packageDetailLs);
                    }
                }
            }
        }
    }

    private void setReport(List<OrderDto> orderLs) {
        if (ObjectUtil.isNotEmpty(orderLs)) {
            List<Long> orderIdLs = orderLs.stream().map(OrderDto::getOrderId).distinct().collect(Collectors.toList());
            List<OrderReportDto> orderReportLs = orderReportService.findLsByOrderIdLs(orderIdLs);
            List<ReportEntity> reportLs = reportService.list(Wrappers.lambdaQuery(ReportEntity.class).in(ReportEntity::getOrderId, orderIdLs));
            List<ReportExtraEntity> reportExtraLs = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(reportLs)) {
                List<Long> reportIdLs = reportLs.stream().map(ReportEntity::getReportId).distinct().collect(Collectors.toList());
                reportExtraLs = reportExtraService.list(Wrappers.lambdaQuery(ReportExtraEntity.class).in(ReportExtraEntity::getReportId, reportIdLs));
            }
            List<LisTestEntity> lisTestLs = lisTestService.list(Wrappers.lambdaQuery(LisTestEntity.class).in(LisTestEntity::getOrderId, orderIdLs));
            List<TestResultDto> testResultLs = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(lisTestLs)) {
                List<Long> testIdLs = lisTestLs.stream().map(LisTestEntity::getTestId).distinct().collect(Collectors.toList());
                testResultLs = testResultService.findLsByTestIdLs(testIdLs);
            }
            for (OrderDto order : orderLs) {
                List<OrderReportDto> newOrderReportLs = orderReportLs.stream().filter(p -> p.getOrderId().equals(order.getOrderId())).collect(Collectors.toList());
                if (ObjectUtil.isEmpty(newOrderReportLs)) {
                    List<ReportEntity> newReportLs = reportLs.stream().filter(p -> p.getOrderId().equals(order.getOrderId())).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(newReportLs)) {
                        int reportNo = 1;
                        for (ReportEntity report : newReportLs) {
                            OrderReportDto orderReportDto = new OrderReportDto();
                            orderReportDto.setOrderId(order.getOrderId());
                            orderReportDto.setReportNo(reportNo);
                            orderReportDto.setReportId(report.getReportId());
                            orderReportDto.setTimeReported(report.getTimeReported());
                            orderReportDto.setIsSecret(report.getIsSecret());
                            newOrderReportLs.add(orderReportDto);
                            reportNo++;
                        }
                    }
                }
                if (ObjectUtil.isNotEmpty(newOrderReportLs)) {
                    for (OrderReportDto orderReport : newOrderReportLs) {
                        ReportEntity report = reportLs.stream().filter(p -> p.getReportId().equals(orderReport.getReportId())).findFirst().orElse(null);
                        if (report != null) {
                            orderReport.setInspectorCode(report.getInspectorCode());
                            orderReport.setInspectorName(report.getInspectorName());
                            orderReport.setQcCode(report.getQcCode());
                            orderReport.setQcName(report.getQcName());
                        }
                        Optional<ReportExtraEntity> reportExtraOpt = reportExtraLs.stream().filter(p -> p.getReportId().equals(orderReport.getReportId())).findFirst();
                        if (reportExtraOpt.isPresent()) {
                            orderReport.setReportUri(reportExtraOpt.get().getReportUri());
                            orderReport.setInspectionDesc(reportExtraOpt.get().getInspectionDesc());
                            orderReport.setConclusionDesc(reportExtraOpt.get().getConclusionDesc());
                            orderReport.setAbnormalDesc(reportExtraOpt.get().getAbnormalDesc());
                        }
                        List<LisTestEntity> newLisTestLs = lisTestLs.stream().filter(p -> p.getOrderId().equals(orderReport.getOrderId())).collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(newLisTestLs)) {
                            for (LisTestEntity lisTest : newLisTestLs) {
                                List<TestResultDto> newTestResultLs = testResultLs.stream().filter(p -> p.getTestId().equals(lisTest.getTestId())).collect(Collectors.toList());
                                if (ObjectUtil.isNotEmpty(newTestResultLs)) {
                                    List<ReportItemResultDto> itemResultLs = new ArrayList<>();
                                    for (TestResultDto testResult : newTestResultLs) {
                                        ReportItemResultDto itemResult = new ReportItemResultDto();
                                        BeanUtils.copyProperties(testResult, itemResult);
                                        itemResultLs.add(itemResult);
                                    }
                                    orderReport.setItemResultLs(itemResultLs);
                                }
                            }
                        }
                    }
                }
                order.setOrderReportLs(newOrderReportLs);
            }
        }
    }

    @Override
    @Transactional
    public R<?> signedOrder(Long orgId, Long userId, Long certId, String storeId, Integer isSlefPaid, List<Long> orderIdLs) {
        log.info("申请单签名提交 userId:{} orderIdLs:{} ", userId, JSONUtil.toJsonStr(orderIdLs));
        List<OrderEntity> orderLs = orderService.list(Wrappers.lambdaQuery(OrderEntity.class).in(OrderEntity::getOrderId, orderIdLs));
        if (orderLs == null || orderLs.isEmpty()) {
            throw new SaveFailureException("未找到该申请记录");
        }
        List<Long> recipeIdLs = orderLs.stream().map(OrderEntity::getRecipeId).distinct().collect(Collectors.toList());
        if (!recipeIdLs.isEmpty()) {
            // 处方提交接受
//            LambdaUpdateWrapper<RecipeEntity> recipeWrapper = Wrappers.lambdaUpdate(RecipeEntity.class);
//            recipeWrapper.in(RecipeEntity::getRecipeId, recipeIdLs);
//            recipeWrapper.set(RecipeEntity::getPaidStatus, PaidStatus.toPaid.getValue());
//            recipeWrapper.set(RecipeEntity::getExecStatus, ExecStatus.waiting.getValue());
//            recipeWrapper.set(RecipeEntity::getTimeSubmitted, new Date());
//            recipeService.update(recipeWrapper);
            // 处方签名
            R<?> result = hsdRecipeService.clinicianSign(orgId, userId, certId, storeId, null, null, isSlefPaid, true, true, recipeIdLs, null);
            if (result.getCode() != 0) {
                return result;
            }
            // 如果没有划价单则直接推送到moe 发送执行科室申请单
            List<RecipeDto> recipeLs = recipeService.findLsByIdLs(recipeIdLs);
            if (ObjectUtil.isNotEmpty(recipeLs)) {
                List<RecipeDto> noBillRecipeLs = recipeLs.stream().filter(p -> p.getBseqid() == null).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(noBillRecipeLs)) {
                    for (RecipeDto recipeDto : noBillRecipeLs) {
                        if (recipeDto.getRecipeTypeId().equals(RecipeType.sys.getValue())) {
                            saveOrderExec(recipeDto);
                            notifyTreatment(recipeDto);
                            // 上报云Lis
                            OrderEntity order = orderLs.stream().filter(p -> p.getRecipeId().equals(recipeDto.getRecipeId())).findFirst().orElse(null);
                            if (order != null) {
                                lisService.uploadLisOrder(recipeDto.getOrgId(), ListUtil.of(order.getOrderId()));
                            }
                        } if (recipeDto.getRecipeTypeId().equals(RecipeType.yj.getValue())) {
                            saveOrderExec(recipeDto);
                            orderService.orderSend(Collections.singletonList(recipeDto.getRecipeId()));
                        } else if (recipeDto.getRecipeTypeId().equals(RecipeType.zl.getValue())) {
                            saveOrderExec(recipeDto);
                        }

                        LambdaUpdateWrapper<RecipeEntity> recipeWrapper = Wrappers.lambdaUpdate(RecipeEntity.class);
                        recipeWrapper.eq(RecipeEntity::getRecipeId, recipeDto.getRecipeId());
                        recipeWrapper.set(RecipeEntity::getExecStatus, ExecStatus.waiting.getValue());
                        recipeWrapper.set(RecipeEntity::getPaidStatus, PaidStatus.paid.getValue());
                        recipeService.update(recipeWrapper);

                        LambdaUpdateWrapper<OrderEntity> orderWrapper = Wrappers.lambdaUpdate(OrderEntity.class);
                        orderWrapper.eq(OrderEntity::getRecipeId, recipeDto.getRecipeId());
                        orderWrapper.set(OrderEntity::getTimeSent, new Date());
                        orderService.update(orderWrapper);
                    }
                }
            }
        }
        return R.ok();
    }

    @Override
    @Transactional
    public void withdrawSingedOrder(List<Long> orderIdLs) {
        log.info("撤回已签名的申请单 orderIdLs:{}", JSONUtil.toJsonStr(orderIdLs));
        if (ObjectUtil.isNotEmpty(orderIdLs)) {
            List<OrderEntity> orderLs = orderService.list(Wrappers.lambdaQuery(OrderEntity.class).in(OrderEntity::getOrderId, orderIdLs));
            List<Long> recipeIdLs = orderLs.stream().map(OrderEntity::getRecipeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(recipeIdLs)) {
                hsdRecipeService.withdrawRecipeSign(recipeIdLs);
            }
            // 撤回申请单
        }
    }

    /**
     * 发送到MOE处置
     */
    private void notifyTreatment(RecipeDto recipe) {
        log.info("notifyTreatment recipeId:{}", recipe.getRecipeId());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        List<TreatmentVo> treatmentLs = new ArrayList<>();
        List<OrderEntity> orderLs = new ArrayList<>();
        List<Integer> mtaRecipeTypeLs = ListUtil.of(RecipeType.sys.getValue());
        List<RecipeDetailDto> detailLs = recipe.getRecipeDetailLs();
        VisitEntity visit = visitService.getById(recipe.getVisitId());
        if (visit == null) {
            throw new SaveFailureException("处方：" + recipe.getRxNo() + "未关联就诊信息");
        }
        if (mtaRecipeTypeLs.contains(recipe.getRecipeTypeId())) {
            orderLs = orderService.list(new LambdaQueryWrapper<OrderEntity>().eq(OrderEntity::getRecipeId, recipe.getRecipeId()));
        }
        Long clinicianId = recipe.getClinicianId();
        if (clinicianId == null) {
            clinicianId = visit.getClinicianId();
        }
        OrderEntity order = orderLs.stream().filter(p -> p.getRecipeId().equals(recipe.getRecipeId())).findFirst().orElse(null);
        if (mtaRecipeTypeLs.contains(recipe.getRecipeTypeId()) && order == null) {
            throw new SaveFailureException("医技处方：" + recipe.getRxNo() + "未关联申请单");
        }
        ClinicianEntity clinician = clinicianService.getById(clinicianId);
        if (order != null && clinician == null) {
            throw new SaveFailureException("处方：" + recipe.getRxNo() + "未关联医生信息");
        }
        List<TreatmentVo.TreatmentDetail> treatmentDetailLs = new ArrayList<>();
        for (RecipeDetailDto recipeDetail : detailLs) {
            if (order != null || (recipeDetail.getFeedMethod() != null && recipeDetail.getFeedMethod().equals(FeedMethod.injection.getValue()))) {
                TreatmentVo.TreatmentDetail treatmentDetail = new TreatmentVo.TreatmentDetail();
                treatmentDetail.setRecipeLineNo(recipeDetail.getLineNo());
                treatmentDetail.setArtId(recipeDetail.getArtId());
                treatmentDetail.setRouteId(recipeDetail.getRouteId());
                treatmentDetail.setFreqCode(recipeDetail.getFreqCode());
                treatmentDetail.setTotal(recipeDetail.getTotal());
                treatmentDetail.setUnit(recipeDetail.getUnit());
                treatmentDetail.setUnitType(recipeDetail.getUnitType());
                treatmentDetail.setStRequired(recipeDetail.getStRequired());
                treatmentDetail.setGroupNo(recipeDetail.getGroupNo());
                treatmentDetail.setGroupMark(recipeDetail.getGroupMark());
                treatmentDetail.setSpecimenTypeId(recipeDetail.getSpecimenTypeId());
                treatmentDetail.setBodypartDesc(recipeDetail.getBodypartDesc());
                treatmentDetail.setBodypartCount(recipeDetail.getBodypartCount());
                treatmentDetail.setLabId(order == null ? null : order.getLabId());
                treatmentDetail.setDpm(recipeDetail.getDpm());
                treatmentDetail.setNotice(recipeDetail.getNotice());
                treatmentDetail.setMealCells(recipeDetail.getMealCells());
                treatmentDetail.setMealDoses(recipeDetail.getMealDoses());
                treatmentDetail.setPaidCycles(recipeDetail.getPaidCycles());
                treatmentDetail.setHeadingTimes(recipeDetail.getHeadingTimes());
                treatmentDetail.setFeedMethod(recipeDetail.getFeedMethod());
                treatmentDetail.setFreqCycleBeats(recipeDetail.getFreqCycleBeats());
                treatmentDetail.setFreqCycleSeconds(recipeDetail.getFreqCycleSeconds());
                treatmentDetailLs.add(treatmentDetail);
            }
        }
        if (ObjectUtil.isNotEmpty(treatmentDetailLs)) {
            TreatmentVo treatment = new TreatmentVo();
            treatment.setRecipeId(recipe.getRecipeId());
            treatment.setRecipeTypeId(recipe.getRecipeTypeId());
            treatment.setOrgId(recipe.getOrgId());
            treatment.setVisitId(recipe.getVisitId());
            treatment.setApplyDeptcode(recipe.getApplyDeptcode());
            treatment.setExceDeptcode(recipe.getExceDeptcode());
            treatment.setTimeApplied(recipe.getTimeSubmitted());
            treatment.setSectionId(recipe.getSectionId());
            treatment.setClinicTypeId(visit.getClinicTypeId());
            treatment.setPatientName(visit.getPatientName());
            treatment.setAgeOfYears(visit.getAgeOfYears());
            treatment.setAgeOfDays(Convert.toInt(visit.getAgeOfDays()));
            treatment.setPatientId(visit.getPatientId());
            treatment.setGenderId(visit.getGenderId());
            if (order != null) {
                treatment.setOrderId(order.getOrderId());
                treatment.setOrderTypeId(order.getOrderTypeId());
                treatment.setPurposeDesc(order.getPurposeDesc());
                treatment.setMedicalHistory(order.getMedicalHistory());
            }
            treatment.setClinicianCode(clinician.getClinicianNo());
            treatment.setClinicianName(clinician.getClinicianName());
            treatment.setAccessionNo(recipe.getRxNo());
            treatment.setRecipeDate(Convert.toInt(sdf.format(recipe.getTimeCreated())));
            treatment.setSubjectId(recipe.getSubjectId());
            treatment.setTimes(recipe.getTimes());
            treatment.setPaidStatus(PaidStatus.paid.getValue());
            treatment.setDetailLs(treatmentDetailLs);
            treatmentLs.add(treatment);
            treatmentService.notifyTreatment(treatmentLs);
        }
    }

    /**
     * 保存执行科室申请单 治疗暂时不推送
     */
    private void saveOrderExec(RecipeDto recipe) {
        if (recipe != null && (recipe.getRecipeTypeId().equals(RecipeType.sys.getValue())
                || recipe.getRecipeTypeId().equals(RecipeType.yj.getValue())
                || recipe.getRecipeTypeId().equals(RecipeType.zl.getValue()))) {
            List<OrderEntity> orderLs = orderService.list(Wrappers.lambdaQuery(OrderEntity.class).eq(OrderEntity::getRecipeId, recipe.getRecipeId()));
            List<BillDetailEntity> billDetailLs = new ArrayList<>();
            if (recipe.getBseqid() != null) {
                billDetailLs = billDetailService.list(Wrappers.lambdaQuery(BillDetailEntity.class).eq(BillDetailEntity::getBseqid, recipe.getBseqid()));
            }
            List<OrderExecVo> orderExecLs = new ArrayList<>();
            OrderEntity order = orderLs.stream().filter(o -> o.getRecipeId().equals(recipe.getRecipeId())).findFirst().orElse(new OrderEntity());
            List<RecipeDetailDto> recipeDetailLs = recipe.getRecipeDetailLs();
            List<Long> artIdLs = recipeDetailLs.stream().map(RecipeDetailDto::getArtId).collect(Collectors.toList());
            List<ArticleEntity> articleLs = articleService.list(Wrappers.lambdaQuery(ArticleEntity.class).in(ArticleEntity::getArtId, artIdLs));
            Integer artTypeId = articleLs.get(0).getArtTypeId();
            if (recipe.getRecipeTypeId().equals(RecipeType.zl.getValue()) || artTypeId.equals(ArtType.MoeLab.getValue()) || artTypeId.equals(ArtType.MoeExam.getValue())) {
                OrderExecVo orderExec = OrderExecVo.builder()
                        .orderId(order.getOrderId())
                        .orgId(recipe.getOrgId())
                        .visitId(recipe.getVisitId())
                        .recipeId(recipe.getRecipeId())
                        .clinicTypeId(recipe.getClinicTypeId())
                        .orderTypeId(order.getOrderTypeId())
                        .applyDeptcode(recipe.getApplyDeptcode())
                        .exceDeptcode(recipe.getExceDeptcode())
                        .patientId(recipe.getPatientId())
                        .genderId(recipe.getPatientGenderId())
                        .patientName(recipe.getPatientName())
                        .ageOfYears(recipe.getAgeOfYears())
                        .ageOfDays(recipe.getAgeOfDays())
                        .creatorUid(recipe.getUserId())
                        .timeApplied(recipe.getTimeCreated())
                        .purposeDesc(order.getPurposeDesc())
                        .medicalHistory(order.getMedicalHistory())
                        .clinicianCode(recipe.getClinicianNo())
                        .clinicianName(recipe.getClinicianName())
                        .accessionNo(order.getAccessionNo())
                        .artTypeId(articleLs.get(0).getArtTypeId())
                        .sectionId(recipe.getSectionId())
                        .bseqid(recipe.getBseqid())
                        .build();
                List<OrderDetailVo> orderDetailLs = new ArrayList<>();
                for (RecipeDetailDto recipeDetail : recipeDetailLs) {
                    if (Convert.toInt(recipeDetail.getIsNhItem(), 0) == 0) {
                        List<Integer> bodypartIdLs = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(recipeDetail.getRecipeDetailPosLs())) {
                            bodypartIdLs = recipeDetail.getRecipeDetailPosLs().stream().map(RecipeDetailPosDto::getBodypartId).collect(Collectors.toList());
                        }
                        BigDecimal amount = BigDecimal.ZERO;
                        if (recipe.getBseqid() != null) {
                            List<BillDetailEntity> bdLs;
                            if (recipeDetail.getIsPackage() != null && recipeDetail.getIsPackage() == 1) {
                                bdLs = billDetailLs.stream().filter(p -> p.getBseqid().equals(recipe.getBseqid()) && p.getPackageId() != null && p.getPackageId().equals(recipeDetail.getArtId())).collect(Collectors.toList());
                            } else {
                                bdLs = billDetailLs.stream().filter(p -> p.getBseqid().equals(recipe.getBseqid()) && p.getArtId().equals(recipeDetail.getArtId())).collect(Collectors.toList());
                            }
                            if (ObjectUtil.isNotEmpty(bdLs)) {
                                amount = bdLs.stream().map(BillDetailEntity::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                            }
                        }
                        orderDetailLs.add(OrderDetailVo.builder()
                                .times(1)
                                .packageId(recipeDetail.getArtId())
                                .total(Convert.toBigDecimal(recipeDetail.getTotal(), BigDecimal.ZERO))
                                .specimenTypeId(recipeDetail.getSpecimenTypeId())
                                .bodypartCount(recipeDetail.getBodypartCount())
                                .bodypartDesc(recipeDetail.getBodypartDesc())
                                .labId(order.getLabId())
                                .amount(amount)
                                .isUnchargeable(amount.compareTo(BigDecimal.ZERO) == 0 ? 1 : Convert.toInt(recipeDetail.getIsUnchargeable(), 0))
                                .bodypartIdLs(bodypartIdLs)
                                .build());
                    }
                }
                if (ObjectUtil.isNotEmpty(orderDetailLs)) {
                    orderExec.setOrderDetailLs(orderDetailLs);
                    orderExecLs.add(orderExec);
                }
            }
            if (ObjectUtil.isNotEmpty(orderExecLs)) {
                moeOrderService.saveBatchOrderLs(orderExecLs);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawOrder(Long userId, Long orderId) {
        log.info("申请单撤销 orderId:{}", orderId);
        OrderEntity order = orderService.getById(orderId);
        if (order == null) {
            throw new SaveFailureException("未找到该申请记录");
        }
        if (order.getRecipeId() != null) {
            RecipeEntity recipe = recipeService.getById(order.getRecipeId());
            if (recipe.getExecStatus().equals(ExecStatus.finish.getValue())
                    || recipe.getExecStatus().equals(ExecStatus.cancel.getValue())) {
                throw new SaveFailureException("申请单状态为【" + ExecStatus.getName(recipe.getExecStatus()) + "】不能撤销。");
            }
            RecipeExtraEntity recipeExtra = recipeExtraService.getById(recipe.getRecipeId());
            if (recipeExtra != null && recipeExtra.getTimeSigned() != null) {
                // 先到Moe撤销
                moeOrderService.stopOrderExec(orderId);
            }
            // 处方撤销
            hsdRecipeService.cancel(userId, recipe.getRecipeId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchWithdrawOrder(Long userId, List<Long> orderIdLs) {
        for (Long orderId : orderIdLs) {
            withdrawOrder(userId, orderId);
        }
    }

    @Override
    public List<ReferralDestData> findReferralDestDeptLs(Integer type, Long orgId) {
        List<ReferralDestData> deptLs = new ArrayList<>();
        Integer hospitalNo = schedService.getHospitalNo(orgId);
        if (type == 1) {
            deptLs = remotePrService.findDestDeptLs(hospitalNo);
        } else if (type == 2) {
            deptLs = remotePrService.findMtaDestDeptLs(hospitalNo);
        }
        return deptLs;
    }

    @Override
    public JSONArray findOrgOrderTypeLs(Long orgId, List<Integer> mtTypeIdLs) {
        return remotePrService.findOrgOrderTypeLs(orgId, mtTypeIdLs);
    }

    @Override
    public JSONArray findOrgItemLs(Map<String, Object> params) {
        return remotePrService.findOrgItemLs(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReferral(Long userId, ReferralVo referral) {
        // 诊疗记录
        VisitDto visit = visitService.findById(referral.getVisitId(), true);
        String age = "";
        if (visit.getAgeOfYears() != null) {
            age = visit.getAgeOfYears() + "Y";
        }
        if (visit.getAgeOfDays() != null) {
            age = Convert.toInt(visit.getAgeOfDays()) + "D";
        }
        // 患者信息
        PatientEntity patientEntity = patientService.getById(referral.getPatientId());
        if (patientEntity!= null) {
            referral.setPatientName(patientEntity.getPatientName());
            referral.setTelNo(patientEntity.getTelNo());
            referral.setCertTypeId(patientEntity.getCertTypeId());
            referral.setCertIdno(patientEntity.getIdcertNo());
        }
//        MpiPatientMapEntity mpiPatientMapEntity = mpiPatientMapService.findById(referral.getPatientId(), referral.getTransferOrgId());
//        if (mpiPatientMapEntity!= null) {
//            referral.setPatientNo(Convert.toStr(mpiPatientMapEntity.getPatientNo()));
//        }
        referral.setPatientNo(Convert.toStr(referral.getPatientId()));
        VisitExtraEntity visitExtra = visitExtraService.getById(referral.getVisitId());
        if (visitExtra!= null) {
            if (StringUtil.isNotEmpty(referral.getContactTel()) && !ValidatorUtil.isMobile(referral.getContactTel())) {
                referral.setContactTel(visitExtra.getContactTel());
            }
            referral.setCertTypeId(visitExtra.getCertTypeId());
            referral.setCertIdno(visitExtra.getIdcertNo());
        }
        if (StringUtil.isNotEmpty(referral.getTransferDeptcode())) {
            OrgDeptEntity orgDeptEntity = orgDeptService.findById(referral.getTransferOrgId(), referral.getTransferDeptcode());
            if (orgDeptEntity != null) {
                referral.setTransferDeptname(orgDeptEntity.getDeptName());
            }
        }
        if (StrUtil.isNotEmpty(referral.getContactTel()) && !ValidatorUtil.isMobile(referral.getContactTel())) {
            referral.setContactTel(visit.getContactTel());
        }
        referral.setClinicianCode(schedService.getClinicianCode(referral.getClinicianId()));
        referral.setHospitalNo(schedService.getHospitalNo(referral.getTransferOrgId()));
        if (referral.getCreatorUid() == null) {
            referral.setCreatorUid(userId);
        }
        if (referral.getOrderLs() != null && !referral.getOrderLs().isEmpty()) {
            for (MtaOrderData order : referral.getOrderLs()) {
                order.setPurposeDesc(referral.getDiagDesc());
                order.setVisitId(Convert.toStr(referral.getVisitId()));
                order.setPatientName(referral.getPatientName());
                order.setGender(visit.getGenderName());
                order.setAge(age);
            }
        }
        log.info("保存转诊转检申请到:{}", JSONUtil.toJsonStr(referral));
        remotePrService.saveReferral(referral);
    }

    @Override
    public JSONArray findReferralLsByVisitId(Long visitId) {
        return remotePrService.findReferralLsByVisitId(visitId);
    }

    @Override
    public void saveMtaOrder(Long recipeId) {
        log.debug("推送医技申请到MTA recipeId:{}", recipeId);
        if (recipeId != null) {
            RecipeDto recipe = recipeService.findById(recipeId);
            boolean psyCtRecipe = recipe.getRecipeDetailLs().stream().allMatch(p -> p.getCatTypeId() != null && p.getCatTypeId().equals(ArtCatType.PsychologicalCT.getValue()));
            if (recipe.getRecipeTypeId().equals(RecipeType.yj.getValue()) && !psyCtRecipe) {
                String age = "";
                if (recipe.getAgeOfYears() != null) {
                    age = recipe.getAgeOfYears() + "Y";
                }
                if (recipe.getAgeOfDays() != null) {
                    age = recipe.getAgeOfDays() + "D";
                }
                OrderEntity order = orderService.getOne(Wrappers.lambdaQuery(OrderEntity.class).eq(OrderEntity::getRecipeId, recipeId));
                //
                MtaOrderData mtaOrder = new MtaOrderData();
                mtaOrder.setHisOrderId(Convert.toStr(order.getOrderId()));
                mtaOrder.setApplyUid(recipe.getUserId());
                mtaOrder.setMedicalHistory(order.getMedicalHistory());
                mtaOrder.setPurposeDesc(order.getPurposeDesc());
                mtaOrder.setOrderTypeId(order.getOrderTypeId());
                mtaOrder.setVisitId(Convert.toStr(recipe.getVisitId()));
                mtaOrder.setClinicType(recipe.getClinicTypeId());
                mtaOrder.setSpecimenTypeId(order.getSpecimenTypeId());
                mtaOrder.setHospitalNo(Convert.toInt(recipe.getOrgCode()));
                mtaOrder.setPatientNo(Convert.toStr(recipe.getPatientId()));
                mtaOrder.setPatientName(recipe.getPatientName());
                mtaOrder.setGender(recipe.getPatientGenderName());
                mtaOrder.setAge(age);
                mtaOrder.setIsSecret(order.getIsSecret());
                mtaOrder.setApplyDeptcode(recipe.getApplyDeptcode());
                mtaOrder.setApplyDeptname(recipe.getApplyDeptname());
                mtaOrder.setExecDeptcode(recipe.getExceDeptcode());
                mtaOrder.setExecDeptname(recipe.getExceDeptname());
                mtaOrder.setApplierCode(recipe.getClinicianNo());
                mtaOrder.setApplierName(recipe.getClinicianName());
                VisitExtraEntity visitExtra = visitExtraService.getById(recipe.getVisitId());
                if (visitExtra!= null) {
                    mtaOrder.setContactTel(visitExtra.getContactTel());
                    mtaOrder.setCertTypeId(visitExtra.getCertTypeId());
                    mtaOrder.setCertIdno(visitExtra.getIdcertNo());
                }
                // 患者信息
                PatientEntity patientEntity = patientService.getById(recipe.getPatientId());
                if (patientEntity!= null) {
                    mtaOrder.setTelNo(patientEntity.getTelNo());
                }
                // 明细
                List<RecipeDetailDto> recipeDetailLs = recipe.getRecipeDetailLs();
                List<MtaOrderDetailData> mtaOrderDetailLs = new ArrayList<>();
                recipeDetailLs.forEach(detail -> {
                   MtaOrderDetailData mtaDetail = new MtaOrderDetailData();
                   mtaDetail.setItemId(detail.getArtId());
                   mtaDetail.setBodypartCount(detail.getBodypartCount());
                   // 部位
                   List<MtaOrderDetailBodypartData> detailBodypartDataLs = new ArrayList<>();
                   if (detail.getRecipeDetailPosLs() != null && !detail.getRecipeDetailPosLs().isEmpty()) {
                       List<Integer> bodypartIdLs = detail.getRecipeDetailPosLs().stream().map(RecipeDetailPosDto::getBodypartId).distinct().collect(Collectors.toList());
                       List<MtBodypartEntity> bodypartLs = mtBodypartService.listByIds(bodypartIdLs);
                       detail.getRecipeDetailPosLs().forEach(pos -> {
                           MtaOrderDetailBodypartData detailBodypartData = new MtaOrderDetailBodypartData();
                           Optional<MtBodypartEntity> bodypart = bodypartLs.stream().filter(p -> p.getBodypartId().equals(pos.getBodypartId())).findFirst();
                           bodypart.ifPresent(p -> {
                               detailBodypartData.setBodypartCode(p.getBodypartCode());
                               detailBodypartData.setBodypartName(p.getBodypartName());
                           });
                           detailBodypartDataLs.add(detailBodypartData);
                       });
                   }
                   mtaDetail.setBodypartLs(detailBodypartDataLs);
                   mtaOrderDetailLs.add(mtaDetail);
                });
                mtaOrder.setOrderDetailLs(mtaOrderDetailLs);
                // 诊断
                List<MtaOrderDiagData> mtaOrderDiagLs = new ArrayList<>();
                List<RecipeDiagDto> recipeDiagLs = recipe.getRecipeDiagLs();
                if (ObjectUtil.isNotEmpty(recipeDiagLs)) {
                    recipeDiagLs.forEach(diag -> {
                        MtaOrderDiagData mtaOrderDiag = new MtaOrderDiagData();
                        mtaOrderDiag.setDiagCode(diag.getDiagCode());
                        mtaOrderDiag.setDiagName(diag.getDiagName());
                        mtaOrderDiagLs.add(mtaOrderDiag);
                    });
                }
                remoteMtaService.saveMtaOrder(mtaOrder, mtaOrderDiagLs);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(Long orderId, Integer status) {
        if (orderId == null) {
            throw new SaveFailureException("请选择申请单ID");
        }
        if (status == null) {
            throw new SaveFailureException("状态为空");
        }
        OrderEntity order = orderService.getById(orderId);
        if (order == null) {
            throw new SaveFailureException("申请单" + orderId + "不存在");
        }
        // 修改处方状态
        if (order.getRecipeId() != null) {
            LambdaUpdateWrapper<RecipeEntity> wrapper = Wrappers.lambdaUpdate(RecipeEntity.class);
            wrapper.eq(RecipeEntity::getRecipeId, order.getRecipeId());
            wrapper.set(RecipeEntity::getExecStatus, status);
            recipeService.update(wrapper);
        }
        // 修改申请单状态
        LambdaUpdateWrapper<MoeOrderEntity> orderWrapper = Wrappers.lambdaUpdate(MoeOrderEntity.class);
        orderWrapper.eq(MoeOrderEntity::getOrderId, orderId);
        orderWrapper.set(MoeOrderEntity::getExecStatus, status);
        moeOrderService.update(orderWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(List<OrderReportVo> orderLs) {
        if (ObjectUtil.isNotEmpty(orderLs)) {
            for (OrderReportVo orderReportVo : orderLs) {
                // 如果有检查报告
                if (ObjectUtil.isNotEmpty(orderReportVo.getPacsList())) {
                    for (OrderReportModel orderReportModel : orderReportVo.getPacsList()) {
                        orderReportModel.setOrderId(orderReportVo.getOrderId());
                        saveInspectionOrderReport(orderReportModel);
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCvrEvent(CvrEventModel cvrEventModel) {
        if (cvrEventModel.getOrderId() == null) {
            throw new SaveFailureException("申请单ID为空");
        }
        OrderEntity order = orderService.getById(cvrEventModel.getOrderId());
        if (order.getRecipeId() != null) {
            RecipeEntity recipe = recipeService.getById(order.getRecipeId());
            OrderTypeEntity orderType = orderTypeService.getById(order.getOrderTypeId());
            Long reporterUid = null;
            if (StrUtil.isNotBlank(cvrEventModel.getReporterCode())) {
                List<UserCodeEntity> userCodeLs = userCodeService.list(Wrappers.lambdaQuery(UserCodeEntity.class)
                        .eq(UserCodeEntity::getUserCode, cvrEventModel.getReporterCode()));
                if (ObjectUtil.isNotEmpty(userCodeLs)) {
                    UserCodeEntity userCode = userCodeLs.get(0);
                    reporterUid = userCode.getUserId();
                }
            }
            cvrEventService.saveCvrEvent(recipe.getVisitId(), recipe.getClinicianId(), orderType.getMtTypeId(), 1, reporterUid, cvrEventModel.getReportTime(), "");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInspectionOrderReport(OrderReportModel orderReport) {
        log.debug("保存检查报告 {}", JSONUtil.toJsonStr(orderReport));
        if (orderReport.getOrderId() == null) {
            throw new SaveFailureException("申请单ID为空");
        }
        OrderEntity order = orderService.getById(orderReport.getOrderId());
        List<InspectReportDataResp> list = new ArrayList<>();
        InspectReportDataResp resp = new InspectReportDataResp();
        resp.setSubmitDoctorCode(orderReport.getReporter());
        resp.setSubmitDateTime(DateUtil.dateFormat(orderReport.getReportdate(), DatePattern.NORM_DATETIME_PATTERN));
        resp.setCheckModality(orderReport.getModality());
        resp.setImageView(orderReport.getExamreport());
        resp.setImageSee(orderReport.getConclusion());
        resp.setReportUrl(orderReport.getReportUrl());
        list.add(resp);
        List<Long> reportIdLs = saveReport(order, list);
        List<OrderReportEntity> orderReportLs = new ArrayList<>();
        for (Long reportId : reportIdLs) {
            orderReportLs.add(OrderReportEntity.builder()
                    .orderId(order.getOrderId())
                    .reportId(reportId)
                    .timeReported(new Date())
                    .isSecret(order.getIsSecret())
                    .reportStatus(1)
                    .unreadFlag(1)
                    .build());
        }
        orderReportService.saveBatchEntity(orderReportLs);
        updateExecStatus(order.getOrderId(), order.getRecipeId(), reportIdLs.size());
    }

    private void updateExecStatus(Long orderId, Long recipeId, Integer reportCount) {
        // 刷新未读报告数量
        LambdaUpdateWrapper<OrderEntity> wrapper = Wrappers.lambdaUpdate(OrderEntity.class);
        wrapper.eq(OrderEntity::getOrderId, orderId);
        wrapper.set(OrderEntity::getReportStatus, 1);
        wrapper.setSql("report_count = IFNULL(report_count, 0) + " + reportCount);
        wrapper.setSql("unread_report_count = IFNULL(unread_report_count, 0) + " + reportCount);
        orderService.update(wrapper);
        // 修改申请单状态
        LambdaUpdateWrapper<MoeOrderEntity> moeOrderWrapper = Wrappers.lambdaUpdate(MoeOrderEntity.class);
        moeOrderWrapper.eq(MoeOrderEntity::getOrderId, orderId);
        moeOrderWrapper.set(MoeOrderEntity::getExecStatus, ExecStatus.finish.getValue());
        moeOrderWrapper.set(MoeOrderEntity::getTimeFinished, new Date());
        moeOrderService.update(moeOrderWrapper);
        // 修改处方状态
        if (recipeId != null) {
            LambdaUpdateWrapper<RecipeEntity> recipeWrapper = Wrappers.lambdaUpdate();
            recipeWrapper.eq(RecipeEntity::getRecipeId, recipeId);
            recipeWrapper.set(RecipeEntity::getExecStatus, ExecStatus.finish.getValue());
            recipeService.update(recipeWrapper);
        }
    }

    @Override
    public List<InspectReportDataResp> getInspectReportData(Long orgId, Long orderId) {
        OrderEntity order = orderService.getById(orderId);
        if (order != null) {
            List<OrderReportEntity> orderReportLs = orderReportService.list(Wrappers.lambdaQuery(OrderReportEntity.class).eq(OrderReportEntity::getOrderId, orderId)
                    .orderByAsc(OrderReportEntity::getReportNo));
            if (ObjectUtil.isEmpty(orderReportLs)) {
                List<OrgParamDto> orgParamLs = orgParamService.findCanNullByCode(orgId, ListUtil.of(UccParamKey.PACS_JOINT_MODE.getCode()));
                boolean isPost = ObjectUtil.isEmpty(orgParamLs) || Convert.toInt(orgParamLs.get(0).getParamVal(), 1) == 1;
                if (!isPost) {
                    throw new SaveFailureException("报告正在生成中");
                }
                Map<String, Object> param = getOrgParam(orgId, ListUtil.of(UccParamKey.PACS_HOSPITAL_CODE.getCode(), UccParamKey.PACS_ORDER_REPORT.getCode()));
                String hospitalCode = Convert.toStr(param.get(UccParamKey.PACS_HOSPITAL_CODE.getCode()));
                String pacsOrderReportUrl = Convert.toStr(param.get(UccParamKey.PACS_ORDER_REPORT.getCode()));

                InspectReportDataReq dateReq = InspectReportDataReq.builder()
                        .hospitalCode(hospitalCode)
                        .hisOrderCode(Convert.toStr(orderId))
                        .build();
                String result = MedApiUtil.postEncryptSign(dateReq, pacsOrderReportUrl, aesSecret);
                ApiResult<List<InspectReportDataResp>> apiResp = JSONUtil.toBean(result, ApiResult.class, false);
                if (apiResp != null && apiResp.getResp() != null) {
                    if (MedApiUtil.apiRespCodeSuccess(apiResp.getResultCode())) {
                        List<InspectReportDataResp> list = Convert.toList(InspectReportDataResp.class, apiResp.getResp());
                        if (ObjectUtil.isNotEmpty(list)) {
                            List<Long> reportIdLs = saveReport(order, list);
                            orderReportLs = new ArrayList<>();
                            for (Long reportId : reportIdLs) {
                                orderReportLs.add(OrderReportEntity.builder()
                                        .orderId(orderId)
                                        .reportId(reportId)
                                        .timeReported(new Date())
                                        .isSecret(order.getIsSecret())
                                        .reportStatus(1)
                                        .unreadFlag(1)
                                        .build());
                            }
                            orderReportService.saveBatchEntity(orderReportLs);
                            updateExecStatus(orderId, order.getRecipeId(), reportIdLs.size());
                        }
                        /*LambdaUpdateWrapper<OrderEntity> wrapper = Wrappers.lambdaUpdate(OrderEntity.class);
                        wrapper.eq(OrderEntity::getOrderId, orderId);
                        wrapper.set(OrderEntity::getReportCount, order.getReportCount() == null ? 1 : order.getReportCount() + 1);
                        wrapper.set(OrderEntity::getReportStatus, 1);
                        wrapper.set(OrderEntity::getUnreadReportCount, order.getUnreadReportCount() == null ? 1 : order.getUnreadReportCount() + 1);
                        update(wrapper);*/
                        return apiResp.getResp();
                    } else {
                        throw new SaveFailureException(apiResp.getResultMsg());
                    }
                } else {
                    if (apiResp != null) {
                        throw new SaveFailureException(apiResp.getResultMsg());
                    } else {
                        throw new SaveFailureException("获取报告数据失败");
                    }
                }
            } else {
                Map<String, Object> param = getOrgParam(orgId, ListUtil.of(UccParamKey.PACS_HOSPITAL_CODE.getCode()));
                String hospitalCode = Convert.toStr(param.get(UccParamKey.PACS_HOSPITAL_CODE.getCode()));

                List<InspectReportDataResp> list = new ArrayList<>();
                List<Long> reportIdLs = orderReportLs.stream().map(OrderReportEntity::getReportId).distinct().collect(Collectors.toList());
                List<ReportEntity> reportLs = reportService.list(Wrappers.lambdaQuery(ReportEntity.class).in(ReportEntity::getReportId, reportIdLs));
                List<ReportExtraEntity> reportExtraLs = reportExtraService.list(Wrappers.lambdaQuery(ReportExtraEntity.class).in(ReportExtraEntity::getReportId, reportIdLs));
                List<Long> studyIdLs = reportLs.stream().map(ReportEntity::getStudyId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                List<StudyEntity> studyLs = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(studyIdLs)) {
                    studyLs = studyService.list(Wrappers.lambdaQuery(StudyEntity.class).in(StudyEntity::getStudyId, studyIdLs));
                }
                for (ReportEntity report : reportLs) {
                    InspectReportDataResp resp = new InspectReportDataResp();
                    resp.setSubmitDoctorCode(report.getInspectorCode());
                    resp.setSubmitDoctorName(report.getInspectorName());
                    resp.setSubmitDateTime(DateUtil.dateFormat(report.getTimeReported(), "yyyy-MM-dd"));
                    ReportExtraEntity reportExtra = reportExtraLs.stream().filter(e -> e.getReportId().equals(report.getReportId())).findFirst().orElse(null);
                    if (reportExtra != null) {
                        resp.setImageSee(reportExtra.getConclusionDesc());
                        resp.setImageView(reportExtra.getInspectionDesc());
                        resp.setReportUrl(reportExtra.getReportUri());
                    }
                    if (ObjectUtil.isNotEmpty(studyLs)) {
                        StudyEntity study = studyLs.stream().filter(e -> e.getStudyId().equals(report.getStudyId())).findFirst().orElse(null);
                        if (study != null) {
                            String imageUrl = study.getDcmPath() + "?hosCode=" + hospitalCode + "&studyIuid=" + study.getStudyUid();
                            resp.setImageUrl(imageUrl);
                        }
                    }
                    list.add(resp);
                }
                // 刷新未读报告数量
                List<OrderReportEntity> unreadReportLs = orderReportLs.stream().filter(e -> e.getUnreadFlag() != null && e.getUnreadFlag() == 1).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(unreadReportLs)) {
                    LambdaUpdateWrapper<OrderEntity> wrapper = Wrappers.lambdaUpdate(OrderEntity.class);
                    wrapper.eq(OrderEntity::getOrderId, orderId);
                    wrapper.setSql("unread_report_count = IFNULL(unread_report_count, 0) - " + reportIdLs.size());
                    orderService.update(wrapper);

                    List<Integer> reportNoLs = unreadReportLs.stream().map(OrderReportEntity::getReportNo).collect(Collectors.toList());
                    LambdaUpdateWrapper<OrderReportEntity> reportWrapper = Wrappers.lambdaUpdate(OrderReportEntity.class);
                    reportWrapper.eq(OrderReportEntity::getOrderId, orderId);
                    reportWrapper.in(OrderReportEntity::getReportNo, reportNoLs);
                    reportWrapper.set(OrderReportEntity::getUnreadFlag, 0);
                    orderReportService.update(reportWrapper);
                }
                return list;
            }
        }
        return null;
    }

    private List<Long> saveReport(OrderEntity order, List<InspectReportDataResp> list) {
        List<Long> reportIdLs = new ArrayList<>();
        RecipeEntity recipe = recipeService.getById(order.getRecipeId());
        VisitEntity visit = visitService.getById(recipe.getVisitId());
        for (InspectReportDataResp item : list) {
            // 检查登记
            Long studyId = sequenceService.getLongNextValue(StudyEntity.class.getSimpleName());
            StudyEntity study = StudyEntity.builder()
                    .studyId(studyId)
                    .orgId(visit.getOrgId())
                    .orderId(order.getOrderId())
                    .deptCode(recipe.getExceDeptcode())
                    .patientId(visit.getPatientId())
                    .patientName(visit.getPatientName())
                    .accessionNo(order.getAccessionNo())
                    .registratorUid(recipe.getUserId())
                    .timeRegistration(order.getTimeSched())
                    .schedStatus(1)
                    .studyDesc(item.getImageSee())
                    .timeArchived(new Date())
                    .build();
            if (visit.getGenderId() != null) {
                GenderEntity gender = genderService.getById(visit.getGenderId());
                study.setGender(gender.getGenderName());
            }
            if (visit.getPatientId() != null) {
                PatientEntity patient = patientService.getById(visit.getPatientId());
                if (patient != null && patient.getBirthDate() != null) {
                    study.setAge(DateUtil.getAgeStrByBirth(DateUtil.toDateTime(patient.getBirthDate() + "", "yyyyMMdd")));
                }
            }
            if (StrUtil.isNotBlank(item.getImageUrl())) {
                String[] imageUrlArr = item.getImageUrl().split("\\?");
                study.setDcmPath(imageUrlArr[0]);
                String[] params = imageUrlArr[1].split("&");
                Map<String, String> map = new HashMap<>();
                for (String param : params) {
                    String[] kv = param.split("=");
                    map.put(kv[0], kv[1]);
                }
                if (map.containsKey("studyIuid")) {
                    study.setStudyUid(map.get("studyIuid"));
                }
            }
            studyService.save(study);
            // 检查报告
            Long reportId = idService.getSnowflakeId();
            ReportEntity report = ReportEntity.builder()
                    .reportId(reportId)
                    .orderId(order.getOrderId())
                    .orgId(visit.getOrgId())
                    .studyId(studyId)
                    .timeReported(StrUtil.isNotBlank(item.getSubmitDateTime()) ? DateUtil.toDateTime(item.getSubmitDateTime(), "yyyy-MM-dd") : new Date())
                    .inspectorCode(item.getSubmitDoctorCode())
                    .inspectorName(item.getSubmitDoctorName())
                    .isSecret(order.getIsSecret())
                    .build();
            reportService.save(report);
            // 检查报告补充信息
            ReportExtraEntity reportExtra = ReportExtraEntity.builder()
                    .reportId(reportId)
                    .inspectionDesc(item.getImageView())
                    .conclusionDesc(item.getImageSee())
                    .reportUri(item.getReportUrl())
                    .build();
            reportExtraService.save(reportExtra);

            reportIdLs.add(reportId);
        }
        orderPendingService.remove(Wrappers.lambdaQuery(OrderPendingEntity.class).eq(OrderPendingEntity::getOrderId, order.getOrderId()));
        orderItemPendingService.remove(Wrappers.lambdaQuery(OrderItemPendingEntity.class).eq(OrderItemPendingEntity::getOrderId, order.getOrderId()));
        return reportIdLs;
    }

    private Map<String, Object> getOrgParam(Long orgId, List<String> paramCodeLs) {
        List<OrgParamDto> orgParam = orgParamService.findByCode(orgId, paramCodeLs);
        Map<String, Object> map = new HashMap<>();
        for (OrgParamDto param : orgParam) {
            Object value = null;
            if (StrUtil.isNotBlank(param.getParamText())) {
                value = param.getParamText();
            } else if (param.getParamVal() != null) {
                value = param.getParamVal();
            }
            if (value != null) {
                map.put(param.getParamCode(), value);
            } else {
                throw new SaveFailureException("参数代码【" + param.getParamCode() + "】未配置参数值");
            }
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrder(Long userId, List<Long> orderIdLs) {
        if (ObjectUtil.isNotEmpty(orderIdLs)) {
            List<OrderEntity> orderLs = orderService.listByIds(orderIdLs);
            List<Long> recipeIdLs = orderLs.stream().map(OrderEntity::getRecipeId).filter(Objects::nonNull).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(recipeIdLs)) {
                hsdRecipeService.batchCancelRecipe(userId, recipeIdLs);
            }
            orderService.delete(orderIdLs);
        }
    }
}
