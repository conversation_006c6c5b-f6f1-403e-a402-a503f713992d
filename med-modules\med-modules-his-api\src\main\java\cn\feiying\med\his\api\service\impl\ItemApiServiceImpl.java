package cn.feiying.med.his.api.service.impl;

import cn.feiying.med.common.utils.DateUtil;
import cn.feiying.med.his.api.model.req.item.ItemPriceQueryModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.ItemPriceRespModel;
import cn.feiying.med.his.api.service.ItemApiService;
import cn.feiying.med.saas.api.service.RemoteHipBaseService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目相关服务实现类
 */
@Slf4j
@Service
public class ItemApiServiceImpl implements ItemApiService {

    @Resource
    private RemoteHipBaseService remoteHipBaseService;

    @Override
    public ApiResultModel<ItemPriceRespModel> queryPrice(ItemPriceQueryModel itemPriceQuery) {
        ApiResultModel<ItemPriceRespModel> result = new ApiResultModel<>();
        
        try {
            // 检查项目明细列表是否为空
            if (itemPriceQuery.getItemLs() == null || itemPriceQuery.getItemLs().isEmpty()) {
                result.setCode(-1);
                result.setMsg("项目明细列表不能为空");
                return result;
            }

            // 调用远程服务查询价格
            JSONObject params = new JSONObject();
            params.set("clinicDate", DateUtil.getTodayInt());
            params.set("orgId", itemPriceQuery.getOrgId());
            for (ItemPriceQueryModel.Item item : itemPriceQuery.getItemLs()) {
                JSONObject itemParams = new JSONObject();
                itemParams.set("orderId",  itemPriceQuery.getOrderId());
                itemParams.set("lineNo", item.getLineNo());
                itemParams.set("packageId", Convert.toLong(item.getArtCode()));
                itemParams.set("artName", item.getArtName());
                itemParams.set("total", item.getTotal());
                itemParams.set("clinicTypeId", item.getClinicType());
                params.set("itemLs", itemParams);
            }
            JSONArray priceResponse = remoteHipBaseService.findItemPriceLs(params);
            // 封装结果
            ItemPriceRespModel resultModel = new ItemPriceRespModel();
            resultModel.setOrderId(itemPriceQuery.getOrderId());
            if (priceResponse != null && ObjectUtil.isNotEmpty(priceResponse)) {
                List<ItemPriceRespModel.Item> itemLs = new ArrayList<>();
                for (JSONObject price : priceResponse.toList(JSONObject.class)) {
                    ItemPriceRespModel.Item item = new ItemPriceRespModel.Item();
                    BeanUtil.copyProperties(price, item);
                    item.setArtCode(Convert.toStr(price.get("artId")));
                    itemLs.add(item);
                }
                resultModel.setItemLs(itemLs);
            }
            
            result.setCode(0);
            result.setMsg("操作成功");
            result.setData(resultModel);
            
        } catch (Exception e) {
            log.error("项目价格查询失败", e);
            result.setCode(-1);
            result.setMsg("操作失败：" + e.getMessage());
        }
        
        return result;
    }
} 