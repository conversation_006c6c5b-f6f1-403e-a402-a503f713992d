package cn.feiying.med.clinics_wm.service;

import cn.feiying.med.clinics_wm.entity.ArtSnTrackEntity;
import cn.feiying.med.clinics_wm.model.ArtSnId;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 追溯码分发明细表
 *
 * <AUTHOR>
 * 2025-05-20 11:13:14
 */
public interface ArtSnTrackService extends IService<ArtSnTrackEntity> {

    List<ArtSnTrackEntity> findLsByArtSnIdLs(List<ArtSnId> artSnIdLs);
}

