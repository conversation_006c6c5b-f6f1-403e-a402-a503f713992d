package cn.feiying.med.clinics_wm.dto;

import cn.feiying.med.clinics_wm.entity.WmBillEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WmBillDto extends WmBillEntity {

    /**
     * 往来单位名称
     */
    private String custName;
    /**
     * 确认用户
     */
    private String validatorUname;
    /**
     * 制单用户
     */
    private String creatorUname;

    private String deptName;
    private String recvDeptName;

    private String tmTypeName;
    /**
     * 出入库单类型名称
     */
    private String wmbillTypeName;
    private String sectionName;
    private String orgName;
    private String userName;
    /**
     * 业务类型
     */
    private String bsnTypeName;
    /**
     * 出入库状态
     */
    private String statusName;
    /**
     * 追溯码状态(0-未采集,1-采集中,2-已采集,3-已上报)
     */
    private String trackcodeStatusName;
    private Long visitId;
    private Long patientId;
    private String patientName;
    private String rxNo;
    private Long recipeId;
    private String genderName;
    private Integer ageOfYears;
    private Integer ageOfDays;
    private String bedNo;
    private String reqNotes;
    /**
     * 出入库单流水号集合
     */
    private String wbSeqids;

    /**
     * 申请科室编号
     */
    private String applyDeptcode;
    /**
     * 申请科室名称
     */
    private String applyDeptname;
    private String transferDeptName;
    /**
     * BillDetail明细集合
     */
    private List<WmBillDetailDto> wmBillDetailDtoList;
    /**
     * 冲红标志 1 冲红 0 非冲红
     */
    private Boolean isRedRush;

    private Integer bcsPaidStatus;

    private Date bcsBillTimeCreated;
}
