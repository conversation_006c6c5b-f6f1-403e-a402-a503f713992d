package cn.feiying.med.his.api.controller;

import cn.feiying.med.his.api.model.req.ApiReqModel;
import cn.feiying.med.his.api.model.req.item.ItemPriceQueryModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.ItemPriceRespModel;
import cn.feiying.med.his.api.service.ItemApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 项目相关API控制器
 */
@Slf4j
@RestController
@RequestMapping("/his/api/item")
public class ItemApiController extends BaseApiController {
    
    @Resource
    private ItemApiService itemApiService;
    
    /**
     * 项目价格查询
     */
    @PostMapping("/queryPrice")
    public ApiResultModel<ItemPriceRespModel> queryPrice(HttpServletRequest request, @RequestBody ApiReqModel apiReq) {
        try {
            // 解密请求数据
            ItemPriceQueryModel itemPriceQuery = decryptRequest(request, apiReq, ItemPriceQueryModel.class);
            log.info("项目价格查询请求: {}", itemPriceQuery);
            
            // 调用服务处理业务逻辑
            return itemApiService.queryPrice(itemPriceQuery);
        } catch (Exception e) {
            log.error("项目价格查询失败", e);
            return ApiResultModel.error("项目价格查询失败: " + e.getMessage());
        }
    }
} 