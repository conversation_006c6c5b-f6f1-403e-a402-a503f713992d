package cn.feiying.med.microhis.hsd.vo;

import cn.feiying.med.common.annotation.TrimStr;
import cn.feiying.med.common.validator.group.DefaultGroup;
import cn.feiying.med.common.validator.group.EditGroup;
import cn.feiying.med.common.validator.group.SaveGroup;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Data
public class RegVo {

    /**
     * 挂号ID
     */
    @Null(message = "挂号ID必须为空", groups = SaveGroup.class)
    @NotNull(message = "挂号ID不能为空", groups = EditGroup.class)
    private Long regId;

    /**
     * 线上挂号记录ID
     */
    private Long acRegId;

    /**
     * 患者ID
     */
//    @NotNull(message = "患者ID不能为空", groups = DefaultGroup.class)
    private Long patientId;

    /**
     * 患者名称
     */
    @NotEmpty(message = "患者名称不能为空", groups = DefaultGroup.class)
    @TrimStr
    private String patientName;

    /**
     * 证件类型代号
     */
    private Integer certTypeId;

    /**
     * 证件号码
     */
    @TrimStr
    private String idcertNo;

    /**
     * 出生日期
     */
    private Integer birthDate;

    /**
     * 就诊日期
     */
    @NotNull(message = "就诊日期不能为空", groups = DefaultGroup.class)
    private Integer clinicDate;

    /**
     * 预计就诊时间
     */
    private String schedDuration;

    /**
     * 挂号号数
     */
    private Integer aptNo;

    /**
     * 科室编码
     */
    private String deptCode;

    /**
     * 挂号医生ID
     */
//    @NotNull(message = "医生ID不能为空", groups = DefaultGroup.class)
    private Long clinicianId;

    /**
     * 预约挂号渠道代号
     */
    @NotNull(message = "预约挂号渠道代号不能为空", groups = DefaultGroup.class)
    private Integer sourceId;

    /**
     * 机构ID
     */
    @NotNull(message = "机构ID不能为空", groups = DefaultGroup.class)
    private Long orgId;

    /**
     * 挂号类型代号
     */
    private Integer regTypeId;

    /**
     * 诊疗类型
     */
    private Integer clinicTypeId;

    /**
     * 医疗类别代号
     */
    private Integer medTypeId;

    /**
     * 急诊标志位
     */
    private Integer isEmergency;

    /**
     * 午别代号
     */
    private Integer periodId;

    /**
     * 转出医生ID
     */
    private Long transferCid;

    /**
     * 联系电话
     */
    private String contactTel;

    /**
     * 性别
     */
    @NotNull(message = "性别不能为空", groups = DefaultGroup.class)
    private Integer genderId;

    /**
     * 年龄
     */
    private Integer ageOfYears;

    /**
     * 日龄
     */
    private BigDecimal ageOfDays;

    /**
     * 年龄类型
     */
    private Integer agesTypeId;

    /**
     * 联系人
     */
    @TrimStr
    private String contactorName;

    /**
     * 联系人电话
     */
    @TrimStr
    private String contactorTel;

    /**
     * 人员关系代号
     */
    private Integer relationshipId;

    /**
     * 陪诊人证件号码
     */
    @TrimStr
    private String companionIdno;

    /**
     * 陪诊人地址
     */
    @TrimStr
    private String companionAddr;

    /**
     * 现居地区代号
     */
    private String livingZonecode;

    /**
     * 现居详细地址
     */
    @Size(message = "请输入80字以内的详细地址", max = 80, groups = DefaultGroup.class)
    @TrimStr
    private String livingAddr;

    /**
     * 体温
     */
    private BigDecimal temperature;

    /**
     * 身高cm
     */
    private Integer heightCm;

    /**
     * 体重Kg
     */
    private BigDecimal weightKg;

    /**
     * 舒张压
     */
    private Integer dbp;

    /**
     * 收缩压
     */
    private Integer sbp;

    /**
     * 脉搏率
     */
    private Integer pulse;

    /**
     * 呼吸率
     */
    private Integer rr;

    /**
     * 说明备注
     */
    @Size(message = "请输入60字以内的说明备注", max = 60, groups = DefaultGroup.class)
    @TrimStr
    private String notes;

    /**
     * 主诉
     */
    @Size(message = "请输入60字以内的主诉", max = 60, groups = DefaultGroup.class)
    @TrimStr
    private String complainOf;

    /**
     * 病情陈述
     */
    @TrimStr
    private String patientStatement;

    /**
     * 腕带标识
     */
    private String wristbandId;
    /**
     * 是否接诊
     */
    private Integer isReceive;
    /**
     * 患者类型代号
     */
    private Integer patientTypeId;
    /**
     * 保险类型代号
     */
    private Integer insuranceTypeId;
    /**
     * 人群类别代号
     */
    private String psnTypeCode;
    /**
     * 保险业务类别代码
     */
    private String businessCode;
    /**
     * 保险人群类别代码
     */
    private String insuredCode;
    /**
     * 是否为公务员
     */
    private Integer civilServantFlag;
    /**
     * 参保单位名称
     */
    private String employerName;
    /**
     * 就诊凭证类型代号
     */
    private Integer mdtrtCertTypeId;
    /**
     * 就诊凭证内容
     */
    private String mdtrtCertText;
    /**
     * 是否创建Reg Bill
     */
    private boolean createRegBill = true;
}
