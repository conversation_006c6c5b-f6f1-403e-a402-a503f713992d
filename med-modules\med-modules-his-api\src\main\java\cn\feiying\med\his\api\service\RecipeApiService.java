package cn.feiying.med.his.api.service;

import cn.feiying.med.his.api.model.req.recipe.RecipeCancelModel;
import cn.feiying.med.his.api.model.req.recipe.RecipeSaveReqModel;
import cn.feiying.med.his.api.model.req.recipe.RecipeSignReqModel;
import cn.feiying.med.his.api.model.req.recipe.RecipeStatusUpdateModel;
import cn.feiying.med.his.api.model.resp.ApiResultModel;
import cn.feiying.med.his.api.model.resp.RecipeRespModel;
import cn.feiying.med.his.api.model.resp.RecipeSignRespModel;

import java.util.List;

/**
 * 处方相关服务接口
 */
public interface RecipeApiService {
    
    /**
     * 处方保存及修改
     * 
     * @param recipeReq 处方保存请求
     * @return 处方保存结果
     */
    ApiResultModel<RecipeRespModel> saveOrUpdateRecipe(RecipeSaveReqModel recipeReq);

    /**
     * 处方签名
     * 
     * @param recipeSignReq 处方签名请求
     * @return 处方签名结果
     */
    ApiResultModel<RecipeSignRespModel> signRecipe(RecipeSignReqModel recipeSignReq);

    /**
     * 处方作废
     * 
     * @param recipeLs 处方作废请求列表
     * @return 处方作废结果
     */
    ApiResultModel<?> cancelRecipe(List<RecipeCancelModel> recipeLs);
    
    /**
     * 处方状态回写
     * 
     * @param recipeLs 处方状态回写请求列表
     * @return 处方状态回写结果
     */
    ApiResultModel<?> updateRecipeStatus(List<RecipeStatusUpdateModel> recipeLs);
} 