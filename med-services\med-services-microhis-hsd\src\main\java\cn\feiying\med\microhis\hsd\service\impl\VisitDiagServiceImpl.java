package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.StringUtil;
import cn.feiying.med.hip.enums.DiagCat;
import cn.feiying.med.hip.enums.DiagStatus;
import cn.feiying.med.hip.enums.DiagType;
import cn.feiying.med.hip.enums.ExecStatus;
import cn.feiying.med.hip.mdi.entity.DiagnoseEntity;
import cn.feiying.med.hip.mdi.service.DiagnoseService;
import cn.feiying.med.microhis.hsd.dto.VisitDiagDto;
import cn.feiying.med.microhis.hsd.entity.*;
import cn.feiying.med.microhis.hsd.service.*;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.feiying.med.common.utils.GQueryWrapper;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.common.utils.Query;

import cn.feiying.med.microhis.hsd.dao.VisitDiagDao;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 诊疗记录诊断表
 *
 * <AUTHOR> 2023-09-21 15:15:55
 */
@Slf4j
@Service("visitDiagService")
public class VisitDiagServiceImpl extends ServiceImpl<VisitDiagDao, VisitDiagEntity> implements VisitDiagService {

    @Resource
    private DiagnoseService diagnoseService;
    @Resource
    private OpcDiagService opcDiagService;
    @Resource
    private RecipeDiagService recipeDiagService;
    @Resource
    private VisitDiagDescService visitDiagDescService;
    @Resource
    private VisitStateService visitStateService;
    @Resource
    private AdmDiagService admDiagService;
    @Resource
    private DisDiagService disDiagService;

    @Override
    public PageUtils<VisitDiagDto> queryPage(Map<String, Object> params) {
        QueryWrapper<VisitDiagDto> wrapper = new GQueryWrapper<VisitDiagDto>().getWrapper(params);
        String keyword = Convert.toStr(params.get("keyword"));
        if (StringUtil.isNotEmpty(keyword)) {
            wrapper.and(p -> p.like("t_visit_diag.Diag_Code", keyword.trim()).or().like("t_visit_diag.Diag_Name", keyword.trim()));
        }
        wrapper.orderByAsc("t_visit_diag.Display_Order");
        IPage<VisitDiagDto> page = baseMapper.queryPage(new Query<VisitDiagDto>().getPage(params), wrapper);
        setDiag(page.getRecords());
        return new PageUtils<>(page);
    }

    @Override
    public List<VisitDiagDto> findByWrapper(QueryWrapper<VisitDiagDto> wrapper) {
        wrapper.orderByAsc("t_visit_diag.Display_Order");
        List<VisitDiagDto> list = baseMapper.queryPage(wrapper);
        setDiag(list);
        return list;
    }

    private void setDiag(List<VisitDiagDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<String> diagCodeLs = list.stream().map(VisitDiagDto::getDiagCode).distinct().collect(Collectors.toList());
            List<DiagnoseEntity> diagLs = diagnoseService.list(Wrappers.lambdaQuery(DiagnoseEntity.class).in(DiagnoseEntity::getDiagCode, diagCodeLs));
            for (VisitDiagDto visitDiagDto : list) {
                diagLs.stream().filter(p -> p.getDiagCode().equals(visitDiagDto.getDiagCode())).findFirst().ifPresent(diag -> visitDiagDto.setIsChronic(diag.getIsChronic()));
            }
        }
    }

    @Override
    public VisitDiagDto findById(Long visitId, Integer diagNo) {
        VisitDiagDto entity = baseMapper.findById(visitId, diagNo);
        DiagnoseEntity diagnose = diagnoseService.getById(entity.getDiagCode());
        if (diagnose != null) {
            entity.setIsChronic(diagnose.getIsChronic());
        }
        return entity;
    }

    @Override
    @Transactional
    public void saveEntity(VisitDiagEntity entity) {
        save(entity);
    }

    @Override
    @Transactional
    public List<VisitDiagDto> saveBatchEntity(Long visitId, Long userId, Long clinicianId, String deptCode, List<VisitDiagEntity> visitDiagLs) {
        if (visitDiagLs != null && !visitDiagLs.isEmpty()) {
            // 校验中医诊断
            List<VisitDiagEntity> tcmMainDiagLs = visitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmMainDiag.getValue())).collect(Collectors.toList());
            List<VisitDiagEntity> tcmOtherDiagLs = visitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmOtherDiag.getValue())).collect(Collectors.toList());
            if ((!tcmMainDiagLs.isEmpty() && tcmOtherDiagLs.isEmpty()) || (tcmMainDiagLs.isEmpty() && !tcmOtherDiagLs.isEmpty())) {
                throw new SaveFailureException("中医诊断主病和主证不能为空。");
            }
            List<VisitDiagEntity> originalVisitDiagLs = this.list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId));
            if (!originalVisitDiagLs.isEmpty()) {
                // 西药
                List<VisitDiagEntity> westDiagLs = originalVisitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.westernDiag.getValue())
                        || p.getDiagTypeId().equals(DiagType.medicalDiag.getValue())).collect(Collectors.toList());
                if (!westDiagLs.isEmpty()) {
                    List<String> diagCodeLs = westDiagLs.stream().map(VisitDiagEntity::getDiagCode).collect(Collectors.toList());
                    List<VisitDiagEntity> updateVisitDiagLs = visitDiagLs.stream().filter(p -> (p.getDiagTypeId().equals(DiagType.westernDiag.getValue()) || p.getDiagTypeId().equals(DiagType.medicalDiag.getValue()))
                            && diagCodeLs.contains(p.getDiagCode())).collect(Collectors.toList());
                    List<VisitDiagEntity> addVisitDiagLs = visitDiagLs.stream().filter(p -> (p.getDiagTypeId().equals(DiagType.westernDiag.getValue()) || p.getDiagTypeId().equals(DiagType.medicalDiag.getValue()))
                            && !diagCodeLs.contains(p.getDiagCode())).collect(Collectors.toList());
                    // 修改已有的诊断
                    if (!updateVisitDiagLs.isEmpty()) {
                        for (VisitDiagEntity entity : updateVisitDiagLs) {
                            updateVisitDiag(visitId, entity, westDiagLs);
                        }
                    }
                    // 新增诊断
                    if (!addVisitDiagLs.isEmpty()) {
                        for (VisitDiagEntity visitDiag : addVisitDiagLs) {
                            saveVisitDiag(visitId, userId, clinicianId, deptCode, visitDiag);
                        }
                    }
                } else {
                    List<VisitDiagEntity> addVisitDiagLs = visitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.westernDiag.getValue()) || p.getDiagTypeId().equals(DiagType.medicalDiag.getValue())).collect(Collectors.toList());
                    if (!addVisitDiagLs.isEmpty()) {
                        for (VisitDiagEntity visitDiag : addVisitDiagLs) {
                            saveVisitDiag(visitId, userId, clinicianId, deptCode, visitDiag);
                        }
                    }
                }
                // 中药
                List<VisitDiagEntity> tcmDiagLs = originalVisitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmMainDiag.getValue()) || p.getDiagTypeId().equals(DiagType.tcmOtherDiag.getValue())).collect(Collectors.toList());
                if (!tcmDiagLs.isEmpty()) {
                    // 主病
                    List<VisitDiagEntity> originalMainDiagLs = originalVisitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmMainDiag.getValue())).collect(Collectors.toList());
                    updateTcmDiag(visitId, userId, clinicianId, deptCode, tcmMainDiagLs, originalMainDiagLs);
                    // 主症
                    List<VisitDiagEntity> originalOtherDiagLs = originalVisitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmOtherDiag.getValue())).collect(Collectors.toList());
                    updateTcmDiag(visitId, userId, clinicianId, deptCode, tcmOtherDiagLs, originalOtherDiagLs);
                } else {
                    if (!tcmMainDiagLs.isEmpty()) {
                        for (VisitDiagEntity visitDiag : tcmMainDiagLs) {
                            saveVisitDiag(visitId, userId, clinicianId, deptCode, visitDiag);
                        }
                    }
                    if (!tcmOtherDiagLs.isEmpty()) {
                        for (VisitDiagEntity visitDiag : tcmOtherDiagLs) {
                            saveVisitDiag(visitId, userId, clinicianId, deptCode, visitDiag);
                        }
                    }
                }
            } else {
                for (VisitDiagEntity visitDiag : visitDiagLs) {
                    saveVisitDiag(visitId, userId, clinicianId, deptCode, visitDiag);
                }
            }
        }
        QueryWrapper<VisitDiagDto> diagWrapper = new GQueryWrapper<VisitDiagDto>().getWrapper();
        diagWrapper.eq("t_visit_diag.Visit_ID", visitId);
        List<VisitDiagDto> list = findByWrapper(diagWrapper);
        setDiag(list);
        
        // 保存诊断描述信息
        saveVisitDiagDesc(visitId);
        
        return list;
    }

    private void updateTcmDiag(Long visitId, Long userId, Long clinicianId, String deptCode, List<VisitDiagEntity> visitDiagLs, List<VisitDiagEntity> originalVisitDiagLs) {
        List<String> diagCodeLs = originalVisitDiagLs.stream().map(VisitDiagEntity::getDiagCode).collect(Collectors.toList());
        List<VisitDiagEntity> addVisitDiagLs = visitDiagLs.stream().filter(p -> !diagCodeLs.contains(p.getDiagCode())).collect(Collectors.toList());
        if (!addVisitDiagLs.isEmpty()) {
            // 新增诊断
            for (VisitDiagEntity visitDiag : addVisitDiagLs) {
                saveVisitDiag(visitId, userId, clinicianId, deptCode, visitDiag);
            }
            // 修改已有的诊断
            if (!originalVisitDiagLs.isEmpty()) {
                for (VisitDiagEntity entity : originalVisitDiagLs) {
                    updateDiagRemoved(visitId, entity.getDiagNo(), entity.getDiagCode());
                }
            }
        } else {
            List<String> diagCodeLs1 = visitDiagLs.stream().map(VisitDiagEntity::getDiagCode).collect(Collectors.toList());
            List<VisitDiagEntity> updateVisitDiagLs = originalVisitDiagLs.stream().filter(p -> !diagCodeLs1.contains(p.getDiagCode())).collect(Collectors.toList());
            // 修改已有的诊断
            if (!updateVisitDiagLs.isEmpty()) {
                for (VisitDiagEntity entity : updateVisitDiagLs) {
                    updateDiagRemoved(visitId, entity.getDiagNo(), entity.getDiagCode());
                }
            }

            for (VisitDiagEntity entity : visitDiagLs) {
                VisitDiagEntity originalVisitDiag = originalVisitDiagLs.stream().filter(p -> p.getDiagCode().equals(entity.getDiagCode())).findFirst().orElse(null);
                LambdaUpdateWrapper<VisitDiagEntity> updateWrapper = Wrappers.lambdaUpdate(VisitDiagEntity.class);
                updateWrapper.eq(VisitDiagEntity::getVisitId, visitId);
                updateWrapper.eq(VisitDiagEntity::getDiagCode, entity.getDiagCode());
                updateWrapper.set(VisitDiagEntity::getDiagnosorUid, userId);
                updateWrapper.set(VisitDiagEntity::getClinicianId, clinicianId);
                updateWrapper.set(VisitDiagEntity::getDiagStatus, entity.getDiagStatus());
                if (entity.getDiagStatus().equals(DiagStatus.confirmed.getValue())) {
                    updateWrapper.set(VisitDiagEntity::getTimeDenied, null);
                    updateWrapper.set(VisitDiagEntity::getTimeConfirmed, entity.getTimeConfirmed() == null ? new Date() : entity.getTimeConfirmed());
                } else {
                    updateWrapper.set(VisitDiagEntity::getTimeDenied, new Date());
                    updateWrapper.set(VisitDiagEntity::getTimeConfirmed, null);
                }
                if (originalVisitDiag != null && entity.getDisplayOrder() == null) {
                    updateWrapper.set(VisitDiagEntity::getDisplayOrder, originalVisitDiag.getDisplayOrder());
                } else if (entity.getDisplayOrder() != null) {
                    updateWrapper.set(VisitDiagEntity::getDisplayOrder, entity.getDisplayOrder());
                }
                updateWrapper.set(VisitDiagEntity::getDiagCatId, entity.getDiagCatId());
                updateWrapper.set(VisitDiagEntity::getLastUpdated, new Date());
                update(updateWrapper);
            }
        }
    }

    private void saveVisitDiag(Long visitId, Long userId, Long clinicianId, String deptCode, VisitDiagEntity visitDiag) {
        if (visitDiag.getDiagNo() == null) {
            visitDiag.setDiagNo(baseMapper.getDiagNo(visitId));
        }
        visitDiag.setVisitId(visitId);
        if (visitDiag.getDiagStatus() == null) {
            visitDiag.setDiagStatus(DiagStatus.suspected.getValue());
        }
        // 排除
        if (visitDiag.getDiagStatus().equals(DiagStatus.removed.getValue())) {
            visitDiag.setTimeDenied(new Date());
        }
        if (visitDiag.getDisplayOrder() == null) {
            visitDiag.setDisplayOrder(baseMapper.getDisplayOrder(visitId));
        }
        visitDiag.setDiagnosorUid(userId);
        visitDiag.setClinicianId(clinicianId);
        visitDiag.setDeptCode(deptCode);
        visitDiag.setTimeDiagnosed(new Date());
        if (visitDiag.getDiagStatus().equals(DiagStatus.confirmed.getValue())) {
            visitDiag.setTimeConfirmed(visitDiag.getTimeConfirmed() == null ? new Date() : visitDiag.getTimeConfirmed());
        }
        save(visitDiag);
        // 保存门诊诊断
        if (!Convert.toInt(visitDiag.getDiagStatus()).equals(DiagStatus.removed.getValue())) {
            OpcDiagEntity opcDiag = OpcDiagEntity.builder()
                    .visitId(visitId)
                    .diagNo(visitDiag.getDiagNo())
                    .displayOrder(visitDiag.getDisplayOrder())
                    .build();
            opcDiagService.save(opcDiag);
        }
    }

    private void updateVisitDiag(Long visitId, VisitDiagEntity entity, List<VisitDiagEntity> originalVisitDiagLs) {
        Optional<VisitDiagEntity> originalVisitDiag = originalVisitDiagLs.stream().filter(p -> p.getDiagCode().equals(entity.getDiagCode())).findFirst();
        if (originalVisitDiag.isPresent()) {
            LambdaUpdateWrapper<VisitDiagEntity> updateWrapper = Wrappers.lambdaUpdate(VisitDiagEntity.class);
            updateWrapper.eq(VisitDiagEntity::getVisitId, visitId);
            updateWrapper.eq(VisitDiagEntity::getDiagCode, entity.getDiagCode());
            updateWrapper.set(VisitDiagEntity::getDiagStatus, entity.getDiagStatus());
            if (!originalVisitDiag.get().getDiagStatus().equals(DiagStatus.confirmed.getValue()) && entity.getDiagStatus().equals(DiagStatus.confirmed.getValue())) {
                updateWrapper.set(VisitDiagEntity::getTimeConfirmed, entity.getTimeConfirmed() == null ? new Date() : entity.getTimeConfirmed());
            } else if (originalVisitDiag.get().getDiagStatus().equals(DiagStatus.confirmed.getValue()) && !entity.getDiagStatus().equals(DiagStatus.confirmed.getValue())) {
                updateWrapper.set(VisitDiagEntity::getTimeConfirmed, null);
            }
            updateWrapper.set(VisitDiagEntity::getDisplayOrder, entity.getDisplayOrder() == null ? originalVisitDiag.get().getDisplayOrder() : entity.getDisplayOrder());
            updateWrapper.set(VisitDiagEntity::getDiagCatId, entity.getDiagCatId());
            updateWrapper.set(VisitDiagEntity::getLastUpdated, new Date());
            update(updateWrapper);
        }
    }

    private void updateDiagRemoved(Long visitId, Integer diagNo, String diagCode) {
        LambdaUpdateWrapper<VisitDiagEntity> updateWrapper = Wrappers.lambdaUpdate(VisitDiagEntity.class);
        updateWrapper.eq(VisitDiagEntity::getVisitId, visitId);
        updateWrapper.eq(VisitDiagEntity::getDiagCode, diagCode);
        updateWrapper.set(VisitDiagEntity::getDiagStatus, DiagStatus.removed.getValue());
        updateWrapper.set(VisitDiagEntity::getTimeDenied, new Date());
        updateWrapper.set(VisitDiagEntity::getTimeConfirmed, null);
        updateWrapper.set(VisitDiagEntity::getLastUpdated, new Date());
        update(updateWrapper);
        // 删除门诊诊断
        opcDiagService.remove(Wrappers.lambdaQuery(OpcDiagEntity.class).eq(OpcDiagEntity::getVisitId, visitId).eq(OpcDiagEntity::getDiagNo, diagNo));
    }

    @Override
    @Transactional
    public void delVisitDiagById(Long visitId, List<Integer> diagNoLs) {
        if (ObjectUtil.isNotEmpty(diagNoLs)) {
            String sql = "select * from microhis_hsd.t_recipe where t_recipe_diag.Recipe_ID = t_recipe.Recipe_ID and t_recipe.Visit_ID = " + visitId + " and t_recipe.Exec_Status <> " + ExecStatus.cancel.getValue();
            if (recipeDiagService.count(Wrappers.lambdaQuery(RecipeDiagEntity.class).in(RecipeDiagEntity::getDiagNo, diagNoLs).exists(sql)) > 0) {
                throw new SaveFailureException("该诊断已在处方或申请单中引用，不能删除");
            }
            List<VisitDiagEntity> visitDiagLs = list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId)
                    .in(VisitDiagEntity::getDiagNo, diagNoLs));
            if (ObjectUtil.isNotEmpty(visitDiagLs)) {
                // 如果删除主病诊，则找到对应的主症
                List<VisitDiagEntity> tcmDiagLs = visitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmMainDiag.getValue()) || p.getDiagTypeId().equals(DiagType.tcmOtherDiag.getValue())).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(tcmDiagLs)) {
                    List<VisitDiagEntity> tcmMainDiagLs = tcmDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmMainDiag.getValue())).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(tcmMainDiagLs)) {
                        List<String> compDiagCodeLs = tcmMainDiagLs.stream().map(VisitDiagEntity::getComplicationDiagCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(compDiagCodeLs)) {
                            List<VisitDiagEntity> otherDiagLs = list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId)
                                   .in(VisitDiagEntity::getDiagCode, compDiagCodeLs).eq(VisitDiagEntity::getDiagTypeId, DiagType.tcmOtherDiag.getValue()));
                            List<VisitDiagEntity> compDiagLs = list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId)
                                  .in(VisitDiagEntity::getComplicationDiagCode, compDiagCodeLs).eq(VisitDiagEntity::getDiagTypeId, DiagType.tcmMainDiag.getValue()));
                            if (ObjectUtil.isNotEmpty(otherDiagLs)) {
                                for (VisitDiagEntity otherDiag : otherDiagLs) {
                                    List<VisitDiagEntity> diagLs = compDiagLs.stream().filter(p -> p.getComplicationDiagCode().equals(otherDiag.getDiagCode())).collect(Collectors.toList());
                                    if (diagLs.size() == 1) {
                                        diagNoLs.add(otherDiag.getDiagNo());
                                    }
                                }
                            }
                        }
                    }
                    // 如果是主症，则找到对应的主病
                    List<VisitDiagEntity> otherDiagLs = tcmDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmOtherDiag.getValue())).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(otherDiagLs)) {
                        List<String> diagCodeLs = otherDiagLs.stream().map(VisitDiagEntity::getDiagCode).collect(Collectors.toList());
                        List<VisitDiagEntity> mainDiagLs = list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId)
                                .in(VisitDiagEntity::getComplicationDiagCode, diagCodeLs).eq(VisitDiagEntity::getDiagTypeId, DiagType.tcmMainDiag.getValue()));
                        if (ObjectUtil.isNotEmpty(mainDiagLs)) {
                            diagNoLs.addAll(mainDiagLs.stream().map(VisitDiagEntity::getDiagNo).collect(Collectors.toList()));
                        }
                    }
                }
                // 删除关联的 recipe_diag
                String delSql = "select * from microhis_hsd.t_recipe where t_recipe_diag.Recipe_ID = t_recipe.Recipe_ID and t_recipe.Visit_ID = " + visitId + " and t_recipe.Exec_Status = " + ExecStatus.cancel.getValue();
                List<RecipeDiagEntity> recipeDiagLs = recipeDiagService.list(Wrappers.lambdaQuery(RecipeDiagEntity.class).in(RecipeDiagEntity::getDiagNo, diagNoLs).exists(delSql));
                if (ObjectUtil.isNotEmpty(recipeDiagLs)) {
                    recipeDiagLs.forEach(p -> recipeDiagService.remove(Wrappers.lambdaQuery(RecipeDiagEntity.class).eq(RecipeDiagEntity::getRecipeId, p.getRecipeId()).eq(RecipeDiagEntity::getDiagNo, p.getDiagNo())));
                }
                // 删除 visit_diag
                remove(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId).in(VisitDiagEntity::getDiagNo, diagNoLs));
                // 删除门诊诊断
                opcDiagService.remove(Wrappers.lambdaQuery(OpcDiagEntity.class).eq(OpcDiagEntity::getVisitId, visitId).in(OpcDiagEntity::getDiagNo, diagNoLs));
                // 删除入院诊断
                admDiagService.remove(Wrappers.lambdaQuery(AdmDiagEntity.class).eq(AdmDiagEntity::getVisitId, visitId).in(AdmDiagEntity::getDiagNo, diagNoLs));
                // 删除出院诊断
                disDiagService.remove(Wrappers.lambdaQuery(DisDiagEntity.class).eq(DisDiagEntity::getVisitId, visitId).in(DisDiagEntity::getDiagNo, diagNoLs));
                // 删除诊断后重新保存诊断描述信息
                QueryWrapper<VisitDiagDto> diagWrapper = new GQueryWrapper<VisitDiagDto>().getWrapper();
                diagWrapper.eq("t_visit_diag.Visit_ID", visitId);
                List<VisitDiagDto> remainingDiagList = findByWrapper(diagWrapper);
                // 更新诊断数量，和排除诊断数
                long diagCount = remainingDiagList.size();
                long deniedCount = remainingDiagList.stream().filter(p -> p.getDiagStatus().equals(DiagStatus.removed.getValue())).count();
                LambdaUpdateWrapper<VisitStateEntity> visitStateWrapper = Wrappers.lambdaUpdate(VisitStateEntity.class);
                visitStateWrapper.eq(VisitStateEntity::getVisitId, visitId);
                visitStateWrapper.set(VisitStateEntity::getDiagCount, diagCount);
                visitStateWrapper.set(VisitStateEntity::getDiagDenied, deniedCount);
                visitStateService.update(visitStateWrapper);
                // 保存诊断描述信息
                saveVisitDiagDesc(visitId);
            }
        }
    }

    @Override
    @Transactional
    public void delBatchVisitDiag(List<VisitDiagEntity> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            // 根据visitId分诊
            Map<Long, List<VisitDiagEntity>> visitDiagMap = list.stream().collect(Collectors.groupingBy(VisitDiagEntity::getVisitId));
            for (Long visitId : visitDiagMap.keySet()) {
                // 根据visitId获取诊断列表
                List<VisitDiagEntity> originalDiagLs = this.list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId));
                // 要删除的诊断列表
                List<VisitDiagEntity> daigList = visitDiagMap.get(visitId);
                // 根据diagCode获取出诊断序号
                List<String> diagCodeLs = daigList.stream().map(VisitDiagEntity::getDiagCode).filter(Objects::nonNull).collect(Collectors.toList());
                List<Integer> diagNoLs = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(diagCodeLs)) {
                    List<VisitDiagEntity> diagLs = originalDiagLs.stream().filter(p -> diagCodeLs.contains(p.getDiagCode())).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(diagLs)) {
                        diagNoLs.addAll(diagLs.stream().map(VisitDiagEntity::getDiagNo).distinct().collect(Collectors.toList()));
                    }
                }
                // 如果真的编码为空或者编码数量不等着要删除的诊断数量，则根据要删除的诊断列表获取诊断序号diagNo
                if (ObjectUtil.isEmpty(diagCodeLs) || diagCodeLs.size() != diagNoLs.size()) {
                    List<Integer> noLs = daigList.stream().map(VisitDiagEntity::getDiagNo).filter(Objects::nonNull).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(noLs)) {
                        diagNoLs.addAll(noLs);
                     }
                }
                if (ObjectUtil.isNotEmpty(diagNoLs)) {
                    delVisitDiagById(visitId, diagNoLs);
                }
            }
        }
    }

    @Override
    @Transactional
    public void delete(Integer... idLs) {
        for(Integer id : idLs) {
            removeById(id);
        }
    }

    /**
     * 保存诊疗记录诊断描述信息
     * @param visitId 诊疗ID
     */
    private void saveVisitDiagDesc(Long visitId) {
        if (visitId != null) {
            // 过滤掉排除诊断，获取有效诊断列表
            List<VisitDiagEntity> validDiagList = list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId)
                    .ne(VisitDiagEntity::getDiagStatus, DiagStatus.removed.getValue()).orderByAsc(VisitDiagEntity::getDisplayOrder));
            
            if (ObjectUtil.isNotEmpty(validDiagList)) {
                // 入院诊断
                List<AdmDiagEntity> admDiagLs = admDiagService.list(Wrappers.lambdaQuery(AdmDiagEntity.class).eq(AdmDiagEntity::getVisitId, visitId).orderByAsc(AdmDiagEntity::getDisplayOrder));
                // 出院诊断
                List<DisDiagEntity> disDiagLs = disDiagService.list(Wrappers.lambdaQuery(DisDiagEntity.class).eq(DisDiagEntity::getVisitId, visitId).orderByAsc(DisDiagEntity::getDisplayOrder));
                // 诊断描述
                VisitDiagDescEntity visitDiagDescEntity = VisitDiagDescEntity.builder()
                        .visitId(visitId)
                        .build();
                // 门诊诊断
                List<VisitDiagEntity> opcDiagLs = validDiagList.stream()
                        .filter(diag -> diag.getDiagCatId().equals(DiagCat.outpatient.getValue()))
                        .collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(opcDiagLs)) {
                    visitDiagDescEntity.setOpcDiagcode(getDiagCodeStr(opcDiagLs));
                    visitDiagDescEntity.setOpcDiagname(getDiagNameStr(opcDiagLs));
                }
                // 入院诊断
                if (ObjectUtil.isNotEmpty(admDiagLs)) {
                    List<Integer> diagNoLs = admDiagLs.stream().map(AdmDiagEntity::getDiagNo).collect(Collectors.toList());
                    List<VisitDiagEntity> diagLs = validDiagList.stream()
                            .filter(diag -> diagNoLs.contains(diag.getDiagNo()))
                            .collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(diagLs)) {
                        visitDiagDescEntity.setAdmDiagcode(getDiagCodeStr(diagLs));
                        visitDiagDescEntity.setAdmDiagname(getDiagNameStr(diagLs));
                    }
                }
                // 出院诊断
                if (ObjectUtil.isNotEmpty(disDiagLs)) {
                    List<Integer> diagNoLs = disDiagLs.stream().map(DisDiagEntity::getDiagNo).collect(Collectors.toList());
                    List<VisitDiagEntity> diagLs = validDiagList.stream()
                            .filter(diag -> diagNoLs.contains(diag.getDiagNo()))
                            .collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(diagLs)) {
                        visitDiagDescEntity.setDscDiagcode(getDiagCodeStr(diagLs));
                        visitDiagDescEntity.setDscDiagname(getDiagNameStr(diagLs));
                        // 主诊断
                        VisitDiagEntity mainDiag = validDiagList.stream().filter(diag -> diag.getDiagNo().equals(diagNoLs.get(0))).findFirst().orElse(null);
                        if (mainDiag != null) {
                            visitDiagDescEntity.setMainDiagcode(mainDiag.getDiagCode());
                            visitDiagDescEntity.setMainDiagname(mainDiag.getDiagName());
                        }
                    }
                } else {
                    visitDiagDescEntity.setMainDiagcode(validDiagList.get(0).getDiagCode());
                    visitDiagDescEntity.setMainDiagname(validDiagList.get(0).getDiagName());
                }
                // 术前诊断
                List<VisitDiagEntity> preopDiagLs = validDiagList.stream()
                        .filter(diag -> diag.getDiagCatId().equals(DiagCat.surgeryBefore.getValue()))
                       .collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(preopDiagLs)) {
                    visitDiagDescEntity.setPreopDiagcode(getDiagCodeStr(preopDiagLs));
                    visitDiagDescEntity.setPreopDiagname(getDiagNameStr(preopDiagLs));
                }
                // 术后诊断
                List<VisitDiagEntity> postopDiagLs = validDiagList.stream()
                        .filter(diag -> diag.getDiagCatId().equals(DiagCat.surgeryAfter.getValue()))
                        .collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(postopDiagLs)) {
                    visitDiagDescEntity.setPostopDiagcode(getDiagCodeStr(postopDiagLs));
                    visitDiagDescEntity.setPostopDiagname(getDiagNameStr(postopDiagLs));
                }
                // 保存或更新诊断描述信息
                visitDiagDescService.saveOrUpdateDiagDesc(visitDiagDescEntity);
            }
        }
    }

    private String getDiagCodeStr(List<VisitDiagEntity> list) {
        return list.stream()
                .map(VisitDiagEntity::getDiagCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(";"));
    }

    private String getDiagNameStr(List<VisitDiagEntity> list) {
        return list.stream()
                .map(VisitDiagEntity::getDiagName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(";"));
    }
}
