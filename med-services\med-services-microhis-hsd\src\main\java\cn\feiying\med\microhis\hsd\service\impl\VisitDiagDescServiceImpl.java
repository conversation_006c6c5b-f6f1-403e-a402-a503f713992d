package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.microhis.hsd.dao.VisitDiagDescDao;
import cn.feiying.med.microhis.hsd.entity.VisitDiagDescEntity;
import cn.feiying.med.microhis.hsd.service.VisitDiagDescService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 诊疗记录诊断描述表
 *
 * <AUTHOR> 2025-05-29 10:30:00
 */
@Service("visitDiagDescService")
public class VisitDiagDescServiceImpl extends ServiceImpl<VisitDiagDescDao, VisitDiagDescEntity> implements VisitDiagDescService {

    @Override
    @Transactional
    public void saveOrUpdateDiagDesc(VisitDiagDescEntity entity) {
        if (entity != null && entity.getVisitId() != null) {
            saveOrUpdate(entity);
        }
    }

} 