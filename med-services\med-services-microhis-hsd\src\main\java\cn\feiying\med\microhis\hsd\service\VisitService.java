package cn.feiying.med.microhis.hsd.service;

import cn.feiying.med.hip.enums.VisitStatus;
import cn.feiying.med.microhis.hsd.dto.VisitDto;
import cn.feiying.med.microhis.hsd.vo.UpdatePatientVo;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.feiying.med.common.utils.PageUtils;
import cn.feiying.med.microhis.hsd.entity.VisitEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 诊疗记录表
 *
 * <AUTHOR> @email 
 * @date 2023-09-21 15:15:54
 * 2023-09-21 15:15:54
 */
public interface VisitService extends IService<VisitEntity> {

    PageUtils<VisitDto> queryPage(Map<String, Object> params);

    List<VisitDto> findLs(Map<String, Object> params);

    List<VisitDto> findOutpatientVisitLs(Long orgId, Integer startDate, Integer endDate, String deptCode, String patientName, Integer normalFinish, boolean isDesensitized);

    List<VisitDto> findLsByIdLs(List<Long> visitIdLs);

    VisitDto findById(Long visitId, boolean isDesensitized);

    Integer getVisitNo(Integer clinicDate);

    void updateVisitStatus(Long visitId, VisitStatus status);

    VisitDto saveVisitByRegId(Long orgId, Long userId, Long regId, Long clinicianId, String deptCode, Integer clinicTypeId, Integer medTypeId);

    Long saveEntity(Long orgId, Long patientId, Long clinicianId, Long regId, String patientName, Integer genderId, Integer ageOfYears, BigDecimal ageOfDays,
                    Integer agesTypeId, String contactorName, String contactorTel, Integer relationshipId, String companionIdno, String companionAddr,
                    Integer clinicTypeId, Integer clinicDate, Integer insuranceTypeId, Integer psnTypeId, Integer medTypeId, Integer status, String deptCode, String complainOf,
                    String patientStatement, BigDecimal temperature, Integer heightCm, BigDecimal weightKg, Integer pulse, Integer rr,
                    Integer dbp, Integer sbp, String livingZonecode, String livingAddr, Integer patientTypeId, Integer civilServantFlag,
                    String employerName, Integer mdtrtCertTypeId, String mdtrtCertText);

    Map<String, Object> refVisit(Long userId, Long visitId, Long refVisitId);

    void updateVisitPatient(UpdatePatientVo entity);

    void updateVisitPatientById(Long visitId, Long patientId);

    void updateGeneralInspection(Long visitId, String generalInspection, String auxiliaryInspection);

    /**
     * 计算年龄和天数
     * @param birthDate 生日
     */
    BigDecimal[] calculateAgeAndDayCount(Integer birthDate, Integer birthTime);

    /**
     * 获取年龄id
     * @param ageOfDays   日龄
     * @param ageOfYears  年龄
     */
    Integer getAgesId(Integer ageOfDays, Integer ageOfYears);

    List<VisitEntity> findInHospitalByPatientId(Long patientId);

    /**
     * 验证是否诊疗信息
     * @param visitId   诊疗流水号
     */
    boolean validVisit(Long visitId);

    void delete(List<Long> visitIdLs);

    void rejectedVisit(List<Long> visitIdLs);

    List<VisitEntity> findByKeyword(String keyword);

    String getDisplayVisitNo(VisitEntity visit);

    /**
     * 获取患者最近的诊疗记录
     */
    VisitDto findLastVisitByPatientId(Long patientId);

    /**
     * 诊疗统计
     */
    List<Map<String, Object>> findVisitStats(Long orgId);
}


