package cn.feiying.med.clinics_wm.service.impl;

import cn.feiying.med.clinics_wm.service.DeptArtService;
import cn.feiying.med.clinics_wm.service.OrgArtService;
import cn.feiying.med.hip.mdi.entity.ArticleEntity;
import cn.feiying.med.hip.mdi.service.ArticleService;
import cn.hutool.json.JSONUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WmBillServiceImpl 拆零盒整操作测试类
 */
class WmBillServiceImplTest {

    @Mock
    private ArticleService articleService;
    
    @Mock
    private DeptArtService deptArtService;
    
    @Mock
    private OrgArtService orgArtService;
    
    @InjectMocks
    private WmBillServiceImpl wmBillService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testPerformSplitPack_成功案例() {
        // 准备测试数据
        Map<String, Object> recordData = new HashMap<>();
        recordData.put("artId", 1084374L);
        recordData.put("totalPacks", 23);
        recordData.put("totalCells", new BigDecimal("80"));
        recordData.put("orgId", 370811001L);
        recordData.put("deptCode", "000013");
        recordData.put("stockNo", 1);
        recordData.put("batchNo", "BM24111603");
        recordData.put("dateManufactured", 20241116);
        recordData.put("expiry", 20261115);
        recordData.put("packPrice", new BigDecimal("35"));
        recordData.put("cellPrice", new BigDecimal("0.35"));
        recordData.put("salePackPrice", new BigDecimal("35"));
        recordData.put("saleCellPrice", new BigDecimal("0.35"));
        recordData.put("timeCreated", "2025-03-24 09:35:47");
        recordData.put("custName", "华润济宁医药有限公司");
        recordData.put("lastBuyPrice", new BigDecimal("35"));
        recordData.put("basePackPrice", new BigDecimal("35"));
        recordData.put("baseCellPrice", new BigDecimal("0.35"));
        recordData.put("splittable", 1);
        recordData.put("deptTotalPacks", 123);
        recordData.put("deptTotalCells", new BigDecimal("80"));
        recordData.put("expiryDays", 534);
        recordData.put("costAmount", new BigDecimal("833"));
        recordData.put("saleAmount", new BigDecimal("833"));

        String recordJson = JSONUtil.toJsonStr(recordData);
        
        Map<String, Object> params = new HashMap<>();
        params.put("record", recordJson);

        // Mock ArticleEntity
        ArticleEntity articleEntity = new ArticleEntity();
        articleEntity.setArtId(1084374L);
        articleEntity.setPackCells(100); // 假设拆零系数为100
        
        when(articleService.getById(1084374L)).thenReturn(articleEntity);

        // 执行测试
        wmBillService.performSplitPack(370811001L, params);

        // 验证调用
        verify(articleService, times(1)).getById(1084374L);
        verify(deptArtService, times(1)).remakeDeptArt(
            eq(370811001L), eq("000013"), eq(1084374L), 
            anyInt(), any(BigDecimal.class), any(BigDecimal.class));
        verify(orgArtService, times(1)).remakeOrgArt(
            eq(370811001L), eq("000013"), eq(1084374L), 
            anyInt(), any(BigDecimal.class), any(BigDecimal.class));
    }

    @Test
    void testCalculatePackAndCellConversion_拆零系数100() {
        // 测试数据：
        // 仓库总库存：123整包 + 80拆零
        // 批次库存：23整包 + 80拆零
        // 拆零系数：100
        
        // 计算过程：
        // 总拆零数 = 80 + 80 = 160
        // 总整包数 = 123 + 23 = 146
        // 总制剂数 = 160 + (146 * 100) = 160 + 14600 = 14760
        // 最终整包数 = 14760 / 100 = 147（取整）
        // 最终拆零数 = 14760 % 100 = 60（取余）
        
        Integer deptTotalPacks = 123;
        BigDecimal deptTotalCells = new BigDecimal("80");
        Integer totalPacks = 23;
        BigDecimal totalCells = new BigDecimal("80");
        Integer packCells = 100;

        // 使用反射调用私有方法进行测试
        // 这里只是展示测试思路，实际测试中可能需要将方法设为包级别可见或使用反射
        
        // 预期结果：
        // 最终整包数：147
        // 最终拆零数：60
        
        System.out.println("测试拆零盒整计算逻辑：");
        System.out.println("输入 - 仓库总库存：" + deptTotalPacks + "整包 + " + deptTotalCells + "拆零");
        System.out.println("输入 - 批次库存：" + totalPacks + "整包 + " + totalCells + "拆零");
        System.out.println("输入 - 拆零系数：" + packCells);
        
        // 手动计算验证
        BigDecimal totalCellsSum = deptTotalCells.add(totalCells);
        Integer totalPacksSum = deptTotalPacks + totalPacks;
        BigDecimal allCellsCount = totalCellsSum.add(
            BigDecimal.valueOf(totalPacksSum).multiply(BigDecimal.valueOf(packCells)));
        
        Integer finalTotalPacks = allCellsCount.divide(
            BigDecimal.valueOf(packCells), 0, BigDecimal.ROUND_DOWN).intValue();
        BigDecimal finalTotalCells = allCellsCount.subtract(
            BigDecimal.valueOf(finalTotalPacks).multiply(BigDecimal.valueOf(packCells)));
        
        System.out.println("计算 - 总制剂数：" + allCellsCount);
        System.out.println("结果 - 最终整包数：" + finalTotalPacks);
        System.out.println("结果 - 最终拆零数：" + finalTotalCells);
        
        // 断言验证
        assert finalTotalPacks.equals(147) : "整包数计算错误，期望147，实际" + finalTotalPacks;
        assert finalTotalCells.equals(new BigDecimal("60")) : "拆零数计算错误，期望60，实际" + finalTotalCells;
    }
}
