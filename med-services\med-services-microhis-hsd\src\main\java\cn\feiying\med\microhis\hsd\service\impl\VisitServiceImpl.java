package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.hip.enums.*;
import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.mysql.AESEncryptHandler;
import cn.feiying.med.common.utils.*;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.hip.mpi.entity.*;
import cn.feiying.med.hip.mpi.service.*;
import cn.feiying.med.microhis.bcs.dto.BillDetailDto;
import cn.feiying.med.microhis.bcs.entity.BillEntity;
import cn.feiying.med.microhis.bcs.entity.CashPaymentEntity;
import cn.feiying.med.microhis.bcs.entity.PrepaidBalanceEntity;
import cn.feiying.med.microhis.bcs.service.BillDetailService;
import cn.feiying.med.microhis.bcs.service.BillService;
import cn.feiying.med.microhis.bcs.service.CashPaymentService;
import cn.feiying.med.microhis.hsd.dto.*;
import cn.feiying.med.microhis.hsd.entity.*;
import cn.feiying.med.microhis.hsd.service.*;
import cn.feiying.med.microhis.hsd.vo.*;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.feiying.med.microhis.hsd.dao.VisitDao;
import cn.feiying.med.hip.mdi.service.SysSequenceService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 诊疗记录表
 *
 * <AUTHOR> 2023-09-21 15:15:54
 */
@Slf4j
@Service("visitService")
public class VisitServiceImpl extends ServiceImpl<VisitDao, VisitEntity> implements VisitService {

    @Resource
    private SysSequenceService sequenceService;
    @Resource
    private PatientService patientService;
    @Resource
    private MpiPatientMapService mpiPatientMapService;
    @Resource
    private MpiPatientnoService mpiPatientnoService;
    @Resource
    private AgesService agesService;
    @Resource
    private VisitExtraService visitExtraService;
    @Resource
    private OutpatientService outpatientService;
    @Resource
    private VisitStateService visitStateService;
    @Resource
    private VisitPendingService visitPendingService;
    @Resource
    private VisitDiagService visitDiagService;
    @Resource
    private RegService regService;
    @Resource
    private RegTriageService regTriageService;
    @Resource
    private MedicalTypeService medicalTypeService;
    @Resource
    private OrgSettingService orgSettingService;
    @Resource
    private PatientStateService patientStateService;
    @Resource
    private GbZoneService gbZoneService;
    @Resource
    private VisitAllergyService visitAllergyService;
    @Resource
    private VisitIllnessService visitIllnessService;
    @Resource
    private BillService billService;
    @Resource
    private BillDetailService billDetailService;
    @Resource
    private RecipeService recipeService;
    @Resource
    private PatientVisitStatService patientVisitStatService;
    @Resource
    private OrgService orgService;
    @Resource
    private PatientMiDiseaseService patientMiDiseaseService;
    @Resource
    private CashPaymentService cashPaymentService;

    @Override
    public PageUtils<VisitDto> queryPage(Map<String, Object> params) {
        QueryWrapper<VisitDto> wrapper = buildWrapper(params);
        Integer isDesensitized = Convert.toInt(params.get("isDesensitized"), 1);
        IPage<VisitDto> page = baseMapper.queryPage(new Query<VisitDto>().getPage(params), wrapper);
        setVisitNoStr(page.getRecords());
        if (isDesensitized == 1) {
            desensitizedVisit(page.getRecords());
        }
        setVisitDiagLs(page.getRecords());
        setVisitAllergyLs(page.getRecords());
        setVisitIllnessLs(page.getRecords());
        setZoneCodeList(page.getRecords());
        setZoneName(page.getRecords());
        setVisitAmount(page.getRecords());
        ageFormatYearDays(page.getRecords());
        setMiDisease(page.getRecords());
        return new PageUtils<>(page);
    }

    @Override
    public List<VisitDto> findLs(Map<String, Object> params) {
        QueryWrapper<VisitDto> wrapper = buildWrapper(params);
        if (params.containsKey("sidx")) {
            wrapper.orderBy(true, params.get("order").equals("asc"), Convert.toStr(params.get("sidx")));
        } else {
            wrapper.orderByDesc("t_visit.Clinic_Date");
        }
        List<VisitDto> visitLs = baseMapper.queryPage(wrapper);
        setVisitNoStr(visitLs);
        Integer desensitized = Convert.toInt(params.get("isDesensitized"), 1);
        if (desensitized == 1) {
            desensitizedVisit(visitLs);
        }
        setVisitDiagLs(visitLs);
        setVisitAllergyLs(visitLs);
        setVisitIllnessLs(visitLs);
        setZoneCodeList(visitLs);
        setZoneName(visitLs);
        setVisitAmount(visitLs);
        ageFormatYearDays(visitLs);
        setMiDisease(visitLs);
        return visitLs;
    }

    @Override
    public List<VisitDto> findOutpatientVisitLs(Long orgId, Integer startDate, Integer endDate, String deptCode, String patientName, Integer normalFinish, boolean isDesensitized) {
        QueryWrapper<VisitDto> wrapper = new GQueryWrapper<VisitDto>().getWrapper();
        wrapper.eq("t_visit.Org_ID", orgId);
        if (startDate != null) {
            wrapper.ge("t_visit.Clinic_Date", startDate);
        }
        if (endDate != null) {
            wrapper.le("t_visit.Clinic_Date", endDate);
        }
        wrapper.and(andWrapper -> andWrapper.isNull("t_visit.Clinic_Type_ID").or().in("t_visit.Clinic_Type_ID", ListUtil.of(ClinicType.outpatient.getValue(), ClinicType.emergency.getValue())));
        if (StrUtil.isNotBlank(deptCode) && !deptCode.equals("0")) {
            wrapper.eq("t_visit.Dept_Code", deptCode);
        }
        if (StrUtil.isNotBlank(patientName) && !patientName.equals("0")) {
            wrapper.like("t_visit.Patient_Name", patientName);
        }
        // 添加正常完结的参数，不正常完结的条件是没有门诊诊断t_opr_diag表数据为空，或者金额为0
        if (normalFinish != null) {
            String diagSql = "select 1 from microhis_hsd.t_opc_diag where t_opc_diag.Visit_ID = t_visit.Visit_ID";
            String recipeSql = "select 1 from microhis_hsd.t_recipe where t_recipe.Visit_ID = t_visit.Visit_ID and t_recipe.Exec_Status not in (" + ExecStatus.cancel.getValue() + "," + ExecStatus.rejected.getValue() + ")";
            if (normalFinish == 1) {
                wrapper.exists(diagSql);
                wrapper.exists(recipeSql);
            } else {
                wrapper.and(andWrapper -> andWrapper.notExists(diagSql).or().notExists(recipeSql));
            }
        }
        wrapper.orderByAsc("t_visit.Clinic_Date");
        List<VisitDto> list = baseMapper.findOutpatientVisitLs(wrapper);
        if (isDesensitized) {
            desensitizedVisit(list);
        }
        setMiDisease(list);
        ageFormatYearDays(list);
        return list;
    }

    private QueryWrapper<VisitDto> buildWrapper(Map<String, Object> params) {
        QueryWrapper<VisitDto> wrapper = new GQueryWrapper<VisitDto>().getWrapper(params);
        String diagName = Convert.toStr(params.get("diagName"));
        if (StrUtil.isNotBlank(diagName)) {
            String sql = "select 1 from microhis_hsd.t_visit_diag where t_visit_diag.Visit_ID = t_visit.Visit_ID " +
                    "and (t_visit_diag.Diag_Code like '%" + diagName.trim() + "%' or t_visit_diag.Diag_Name like '%" + diagName.trim() + "%')";
            wrapper.exists(sql);
        }
        String searchName = Convert.toStr(params.get("searchName"), StrUtil.EMPTY).trim();
        if (StrUtil.isNotBlank(searchName)) {
            String sql = "select 1 from microhis_hsd.t_visit_diag where t_visit_diag.Visit_ID = t_visit.Visit_ID " +
                    "and (t_visit_diag.Diag_Code like '%" + searchName.trim() + "%' or t_visit_diag.Diag_Name like '%" + searchName.trim() + "%')";
            wrapper.and(p -> p.like("t_org.Org_Name", searchName.trim()).or().like("t_clinician.Clinician_Name", searchName.trim())
                    .or().like("t_org_dept.Dept_Name", searchName.trim()).or().like("t_outpatient.Complained_As", searchName.trim())
                    .or().exists(sql));
        }
        String patientName = Convert.toStr(params.get("patientName"), StrUtil.EMPTY).trim();
        if (StrUtil.isNotBlank(patientName)) {
            wrapper.and(p -> p.like("t_visit.Patient_Name", patientName));
        }
        String clinicianName = Convert.toStr(params.get("clinicianName"), StrUtil.EMPTY).trim();
        if (StrUtil.isNotBlank(clinicianName)) {
            wrapper.and(p -> p.like("t_clinician.Clinician_Name", clinicianName));
        }
        Integer isChronic = Convert.toInt(params.get("isChronic"));
        if (isChronic != null && isChronic == 1) {
            String sql = "select 1 from microhis_hsd.t_recipe where t_visit.Visit_ID = t_recipe.Visit_ID and t_recipe.is_Chronic = 1 and t_recipe.Exec_Status < " + ExecStatus.cancel.getValue();
            wrapper.exists(sql);
        }
        Integer startDate = Convert.toInt(params.get("startDate"));
        if (startDate != null) {
            wrapper.ge("t_visit.Clinic_Date", startDate);
        }
        Integer endDate = Convert.toInt(params.get("endDate"));
        if (endDate != null) {
            wrapper.le("t_visit.Clinic_Date", endDate);
        }
        Integer ocFlag = Convert.toInt(params.get("ocFlag"));
        if (ocFlag != null) {
            if (ocFlag == 1) {
                wrapper.eq("t_reg.oc_flag", 1);
            } else {
                wrapper.and(i -> i.eq("t_reg.oc_flag", 0).or().isNull("t_reg.oc_flag"));
            }
        }
        boolean inpatient = Convert.toBool(params.get("inpatient"), false);
        if (inpatient) {
            wrapper.and(andWrapper -> andWrapper.isNull("t_visit.Clinic_Type_ID").or().eq("t_visit.Clinic_Type_ID", ClinicType.inpatient.getValue()));
        }
        boolean outpatient = Convert.toBool(params.get("outpatient"), false);
        if (outpatient) {
            wrapper.and(andWrapper -> andWrapper.isNull("t_visit.Clinic_Type_ID").or().in("t_visit.Clinic_Type_ID", ListUtil.of(ClinicType.outpatient.getValue(), ClinicType.emergency.getValue())));
        }
        // 添加正常完结的参数，不正常完结的条件是没有门诊诊断t_opr_diag表数据为空，或者金额为0
        Integer normalFinish = Convert.toInt(params.get("normalFinish"));
        if (normalFinish != null) {
            String diagSql = "select 1 from microhis_hsd.t_opc_diag where t_opc_diag.Visit_ID = t_visit.Visit_ID";
            String recipeSql = "select 1 from microhis_hsd.t_recipe where t_recipe.Visit_ID = t_visit.Visit_ID and t_recipe.Exec_Status not in (" + ExecStatus.cancel.getValue() + "," + ExecStatus.rejected.getValue() + ")";
            if (normalFinish == 1) {
                wrapper.exists(diagSql);
                wrapper.exists(recipeSql);
            } else {
                wrapper.and(andWrapper -> andWrapper.notExists(diagSql).or().notExists(recipeSql));
            }
        }
        return wrapper;
    }

    @Override
    public List<VisitDto> findLsByIdLs(List<Long> visitIdLs) {
        QueryWrapper<VisitDto> wrapper = new GQueryWrapper<VisitDto>().getWrapper();
        wrapper.in("t_visit.Visit_ID", visitIdLs);
        List<VisitDto> visitLs = baseMapper.queryPage(wrapper);
        setVisitNoStr(visitLs);
        desensitizedVisit(visitLs);
        setVisitDiagLs(visitLs);
        setVisitAllergyLs(visitLs);
        setVisitIllnessLs(visitLs);
        setZoneCodeList(visitLs);
        setZoneName(visitLs);
        setVisitAmount(visitLs);
        ageFormatYearDays(visitLs);
        setMiDisease(visitLs);
        return visitLs;
    }

    @Override
    public VisitDto findById(Long visitId, boolean isDesensitized) {
        VisitDto visit = baseMapper.findById(visitId);
        if (visit != null) {
            visit.setVisitNoStr(getDisplayVisitNo(visit));
            List<VisitDto> list = ListUtil.of(visit);
            if (isDesensitized) {
                desensitizedVisit(list);
            }
            setVisitDiagLs(list);
            setVisitAllergyLs(list);
            setVisitIllnessLs(list);
            setZoneCodeList(list);
            setZoneName(list);
            setVisitAmount(list);
            ageFormatYearDays(list);
            setMiDisease(list);
            if (visit.getPatientId() != null) {
                PatientVisitStatEntity patientVisitStat = patientVisitStatService.getOne(Wrappers.lambdaQuery(PatientVisitStatEntity.class)
                        .eq(PatientVisitStatEntity::getOrgId, visit.getOrgId()).eq(PatientVisitStatEntity::getPatientId, visit.getPatientId()));
                if (patientVisitStat != null) {
                    visit.setLastClinicDate(patientVisitStat.getLastClinicDate());
                }
            }
//            QueryWrapper<VisitDiagDto> diagWrapper = new GQueryWrapper<VisitDiagDto>().getWrapper();
//            diagWrapper.eq("t_visit_diag.Visit_ID", visit.getVisitId());
//            List<VisitDiagDto> diagLs = visitDiagService.findByWrapper(diagWrapper);
//            if (!diagLs.isEmpty()) {
//                String diagNames = diagLs.stream().filter(p -> !p.getDiagStatus().equals(DiagStatus.removed.getValue()))
//                        .map(VisitDiagDto::getDiagName)
//                        .collect(Collectors.joining(StrUtil.COMMA));
//                visit.setDiagNames(diagNames);
//                visit.setVisitDiagLs(diagLs);
//            }
//            if (StrUtil.isBlank(visit.getComplainedAs()) || StrUtil.isBlank(visit.getHpiDesc()) || diagLs.isEmpty()) {
//                visit.setIsPrefect(0);
//            }
        }
        return visit;
    }

    private void setVisitNoStr(List<VisitDto> list) {
        if (!list.isEmpty()) {
            list.forEach(visit -> visit.setVisitNoStr(getDisplayVisitNo(visit)));
        }
    }

    private void desensitizedVisit(List<VisitDto> list) {
        if (!list.isEmpty()) {
            list.forEach(visit -> {
                visit.setIdcertNo(StrUtil.desensitized(visit.getIdcertNo(), DesensitizedUtil.DesensitizedType.ID_CARD));
                visit.setTelNo(StrUtil.desensitized(visit.getTelNo(), DesensitizedUtil.DesensitizedType.MOBILE_PHONE));
                visit.setCompanionIdno(StrUtil.desensitized(visit.getCompanionIdno(), DesensitizedUtil.DesensitizedType.ID_CARD));
                visit.setContactTel(StrUtil.desensitized(visit.getContactTel(), DesensitizedUtil.DesensitizedType.MOBILE_PHONE));
            });
        }
    }

    private void setVisitDiagLs(List<VisitDto> list) {
        if (!list.isEmpty()) {
            List<Long> visitIdLs = list.stream().map(VisitDto::getVisitId).collect(Collectors.toList());
            QueryWrapper<VisitDiagDto> diagWrapper = new GQueryWrapper<VisitDiagDto>().getWrapper();
            diagWrapper.in("t_visit_diag.Visit_ID", visitIdLs);
            List<VisitDiagDto> diagLs = visitDiagService.findByWrapper(diagWrapper);
            list.forEach(visit -> {
                List<VisitDiagDto> visitDiagLs = diagLs.stream().filter(p -> p.getVisitId().equals(visit.getVisitId())).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(visitDiagLs)) {
                    String diagNames = visitDiagLs.stream().filter(p -> !p.getDiagStatus().equals(DiagStatus.removed.getValue()))
                            .map(VisitDiagDto::getDiagName)
                            .limit(3)
                            .collect(Collectors.joining(StrUtil.COMMA));
                    if (visitDiagLs.size() > 3) {
                        diagNames += StrUtil.SPACE + "等";
                    }
                    visit.setDiagNames(diagNames);
                    visit.setIsInfection((int) visitDiagLs.stream().filter(p -> p.getIsInfection() != null && p.getIsInfection() == 1).count());
                    visit.setIsFoodborne((int) visitDiagLs.stream().filter(p -> p.getIsFoodborne() != null && p.getIsFoodborne() == 1).count());
                    visit.setVisitDiagLs(visitDiagLs);
                }
                if (StrUtil.isBlank(visit.getComplainedAs()) || StrUtil.isBlank(visit.getHpiDesc()) || visitDiagLs.isEmpty()) {
                    visit.setIsPrefect(0);
                }
            });
        }
    }

    private void setVisitAllergyLs(List<VisitDto> list) {
        if (!list.isEmpty()) {
            List<Long> visitIdLs = list.stream().map(VisitDto::getVisitId).collect(Collectors.toList());
            List<VisitAllergyDto> allergyLs = visitAllergyService.findLsByIds(visitIdLs);
            list.forEach(visit -> visit.setVisitAllergyLs(allergyLs.stream().filter(p -> p.getVisitId().equals(visit.getVisitId())).collect(Collectors.toList())));
        }
    }

    private void setVisitIllnessLs(List<VisitDto> list) {
        if (!list.isEmpty()) {
            List<Long> visitIdLs = list.stream().map(VisitDto::getVisitId).collect(Collectors.toList());
            List<VisitIllnessDto> illnessLs = visitIllnessService.findByIdLs(visitIdLs);
            list.forEach(visit -> visit.setVisitIllnessLs(illnessLs.stream().filter(p -> p.getVisitId().equals(visit.getVisitId())).collect(Collectors.toList())));
        }
    }

    private void setVisitAmount(List<VisitDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<Long> visitIdLs = list.stream().map(VisitDto::getVisitId).distinct().collect(Collectors.toList());
            // 获取划价单列表
            Map<String, Object> params = new HashMap<>();
            params.put("S_IN_t_bill__Visit_ID", visitIdLs.stream().map(String::valueOf).collect(Collectors.joining(",")));
            params.put("S_IN_t_bill__Paid_Status", StrUtil.join(",", PaidStatus.toPaid.getValue(), PaidStatus.inPayment.getValue(), PaidStatus.paid.getValue(), PaidStatus.refund.getValue()));
            List<BillDetailDto> billLs = billDetailService.findLsByParams(params);
            if (ObjectUtil.isNotEmpty(billLs)) {
                for (VisitDto dto : list) {
                    List<BillDetailDto> billDetailLs = billLs.stream().filter(p -> p.getVisitId().equals(dto.getVisitId())).collect(Collectors.toList());
                    if (ObjectUtil.isNotEmpty(billDetailLs)) {
                        dto.setAmount(billDetailLs.stream().map(BillDetailDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        List<BillDetailDto> unUploadBillDetails = billDetailLs.stream().filter(p -> (p.getUploadedFlag() == null || !p.getUploadedFlag())
                                && (p.getNonMedicalFlag() == null || !p.getNonMedicalFlag())).collect(Collectors.toList());
                        dto.setUnUploadedAmt(unUploadBillDetails.stream().map(BillDetailDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        BigDecimal nonMedicalAmt = billDetailLs.stream().filter(p -> p.getNonMedicalFlag() != null && p.getNonMedicalFlag()).map(BillDetailDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        dto.setNonMedicalAmt(nonMedicalAmt);
                        dto.setMedicalAmt((dto.getAmount() == null ? BigDecimal.ZERO : dto.getAmount()).subtract(dto.getNonMedicalAmt()));

                        // 自付
                        List<Long> cashIdLs = billDetailLs.stream().map(BillDetailDto::getCashId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(cashIdLs)) {
                            List<CashPaymentEntity> cashPaymentLs = cashPaymentService.list(Wrappers.lambdaQuery(CashPaymentEntity.class)
                                    .in(CashPaymentEntity::getCashId, cashIdLs).in(CashPaymentEntity::getStatus, CashTransStatus.success.getValue(), CashTransStatus.cancel.getValue()));
                            BigDecimal totalAmount = cashPaymentLs.stream().map(CashPaymentEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal ybAmount = cashPaymentLs.stream().filter(p -> p.getPaymentId() != null && p.getPaymentId() == PaymentType.miFund.getValue()).map(CashPaymentEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal selfPaidAmount = totalAmount.subtract(ybAmount);
                            dto.setSelfPaidAmount(selfPaidAmount);
                        }
                        // 首页费用
                        List<InpatientFeeDto> inpatientFeeLs = new ArrayList<>();
                        // 根据feeTypeId分组
                        Map<Integer, List<BillDetailDto>> map = billDetailLs.stream().collect(Collectors.groupingBy(BillDetailDto::getFeeTypeId));
                        for (Map.Entry<Integer, List<BillDetailDto>> entry : map.entrySet()) {
                            List<BillDetailDto> detailLs = entry.getValue();
                            if (ObjectUtil.isNotEmpty(detailLs)) {
                                InpatientFeeDto inpatientFee = InpatientFeeDto.builder()
                                        .feeTypeId(entry.getKey())
                                        .amount(detailLs.stream().map(BillDetailDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                                        .selfPaidAmount(detailLs.stream().filter(p -> p.getSelfpaidPct() != null).map(p -> p.getAmount().multiply(p.getSelfpaidPct())).reduce(BigDecimal.ZERO, BigDecimal::add))
                                        .build();
                                inpatientFeeLs.add(inpatientFee);
                            }
                        }
                        dto.setInpatientFeeLs(inpatientFeeLs);
                    }
                }
            }
        }
    }

    private void setMiDisease(List<VisitDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<Long> patientIdLs = list.stream().map(VisitDto::getPatientId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(patientIdLs)) {
                List<PatientMiDiseaseEntity> miDiseaseLs = patientMiDiseaseService.list(Wrappers.lambdaQuery(PatientMiDiseaseEntity.class).in(PatientMiDiseaseEntity::getPatientId, patientIdLs));
                list.forEach(visit -> {
                    if (visit.getPatientId() != null) {
                        List<PatientMiDiseaseEntity> diseaseLs = miDiseaseLs.stream().filter(p -> p.getPatientId().equals(visit.getPatientId())).collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(diseaseLs)) {
                            visit.setPatientDiseaseName(diseaseLs.stream().map(PatientMiDiseaseEntity::getDiseaseName).collect(Collectors.joining(StrUtil.COMMA)));
                            visit.setPatientMiDiseaseLs(diseaseLs);
                        }
                    }
                });
            }
        }
    }

    private void ageFormatYearDays(List<VisitDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            for (VisitDto visit : list) {
                Integer ageOfYears = visit.getAgeOfYears();
                BigDecimal ageOfDays = visit.getAgeOfDays();
                if (ageOfDays == null || ageOfDays.compareTo(BigDecimal.ZERO) < 0) {
                    ageOfDays = BigDecimal.ZERO;
                }

                int year = ageOfYears != null && ageOfYears >= 0 ? ageOfYears : 0;
                // 获取小数点前面的数
                ageOfDays = ageOfDays.setScale(0, RoundingMode.DOWN);
                int month = Math.floorDiv(Convert.toInt(ageOfDays), 30);
                int day = Convert.toInt(ageOfDays) % 30;

                StringBuilder result = new StringBuilder();
                if (year > 6) {
                    result.append(year).append("岁");
                } else if (year > 0) {
                    result.append(year).append("岁").append(month).append("月");
                } else if (month > 0 && day > 0) {
                    result.append(month).append("月").append(day).append("天");
                } else if (month > 0) {
                    result.append(month).append("月");
                } else if (day > 0) {
                    result.append(day).append("天");
                }
                visit.setAge(result.toString());
            }
        }
    }

    // 设置省市区三级编码
    private void setZoneCodeList(List<VisitDto> list) {
        if (!list.isEmpty()) {
            Map<String, String> zoneMap = gbZoneService.findZoneMap();
            list.forEach(visit -> {
                visit.setFamilyZoneCodeLs(getZoneCodeList(visit.getFamilyZoneCode(), zoneMap));
                visit.setLivingZoneCodeLs(getZoneCodeList(visit.getLivingZoneCode(), zoneMap));
            });
        }
    }

    private void setZoneName(List<VisitDto> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            List<String> zoneCodeLs = new ArrayList<>();
            list.forEach(visit -> {
                if (StrUtil.isNotBlank(visit.getFamilyZoneCode()) && !zoneCodeLs.contains(visit.getFamilyZoneCode())) {
                    zoneCodeLs.add(visit.getFamilyZoneCode());
                }
                if (StrUtil.isNotBlank(visit.getLivingZoneCode()) && !zoneCodeLs.contains(visit.getLivingZoneCode())) {
                    zoneCodeLs.add(visit.getLivingZoneCode());
                }
            });
            if (ObjectUtil.isNotEmpty(zoneCodeLs)) {
                List<GbZoneEntity> zoneLs = gbZoneService.list(Wrappers.lambdaQuery(GbZoneEntity.class).in(GbZoneEntity::getZoneCode, zoneCodeLs));
                list.forEach(visit -> {
                    if (StrUtil.isNotBlank(visit.getFamilyZoneCode())) {
                        visit.setFamilyZoneName(zoneLs.stream().filter(p -> p.getZoneCode().equals(visit.getFamilyZoneCode())).findFirst().orElse(new GbZoneEntity()).getZoneFullname());
                    }
                    if (StrUtil.isNotBlank(visit.getLivingZoneCode())) {
                        visit.setLivingZoneName(zoneLs.stream().filter(p -> p.getZoneCode().equals(visit.getLivingZoneCode())).findFirst().orElse(new GbZoneEntity()).getZoneFullname());
                    }
                });
            }
        }
    }

    /**
     * 返回省、市、区三级区域编码，前台没存数据，在后台过滤只有两级如北京天津的区域数组
     */
    private List<String> getZoneCodeList(String zoneCode, Map<String, String> zoneMap) {
        List<String> zoneCodeList = new ArrayList<>();
        if (StrUtil.isNotBlank(zoneCode)) {
            String districtCode = gbZoneService.getDistrictCode(zoneCode);
            if (StrUtil.isNotBlank(districtCode)) {
                String provinceCode = gbZoneService.getProvinceCode(zoneCode);
                String cityCode = gbZoneService.getCityCode(zoneCode);
                zoneCodeList.add(provinceCode);
                if (StrUtil.isNotBlank(zoneMap.get(cityCode))) {
                    zoneCodeList.add(cityCode);
                }
                zoneCodeList.add(districtCode);
            }
        }
        return zoneCodeList;
    }

    @Override
    public Integer getVisitNo(Integer clinicDate) {
        return baseMapper.getVisitNo(clinicDate);
    }

    @Override
    @Transactional
    public void updateVisitStatus(Long visitId, VisitStatus status) {
        LambdaUpdateWrapper<VisitEntity> wrapper = Wrappers.lambdaUpdate(VisitEntity.class);
        wrapper.eq(VisitEntity::getVisitId, visitId);
        wrapper.set(VisitEntity::getVisitStatus, status.getValue());
        if (status.getValue() == VisitStatus.planOut.getValue()) {
            wrapper.set(VisitEntity::getTimeAdmission, new Date());
        }
        update(wrapper);
    }

    // 修改诊疗记录其他信息的诊断
    private void updateVisitExtraDiag(Long visitId, List<VisitDiagEntity> visitDiagLs) {
        List<VisitDiagEntity> westDiagLs = visitDiagLs.stream().filter(p ->
                (p.getDiagTypeId().equals(DiagType.westernDiag.getValue()) || p.getDiagTypeId().equals(DiagType.medicalDiag.getValue())
                        && !p.getDiagStatus().equals(DiagStatus.removed.getValue()))).collect(Collectors.toList());
        List<VisitDiagEntity> tcmMainDiagLs = visitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmMainDiag.getValue())
                && !p.getDiagStatus().equals(DiagStatus.removed.getValue())).collect(Collectors.toList());
        List<VisitDiagEntity> tcmOtherDiagLs = visitDiagLs.stream().filter(p -> p.getDiagTypeId().equals(DiagType.tcmOtherDiag.getValue())
                && !p.getDiagStatus().equals(DiagStatus.removed.getValue())).collect(Collectors.toList());
        // 病理学诊断
        List<VisitDiagEntity> pathologicalDiagLs = visitDiagLs.stream().filter(p -> p.getDiagCatId() != null && p.getDiagCatId().equals(DiagCat.pathology.getValue())
                && !p.getDiagStatus().equals(DiagStatus.removed.getValue())).collect(Collectors.toList());

        String opDiseaseCode = "";
        String opDiseaseName = "";
        String opTcmDiseaseCode = "";
        String opTcmDiseaseName = "";
        String opTcmSymptomCode = "";
        String opTcmSymptomName = "";
        String pathologicalDiagCode = "";
        String pathologicalDiagName = "";
        String pathologyNo = "";
        if (!westDiagLs.isEmpty()) {
            opDiseaseCode = westDiagLs.get(0).getDiagCode();
            opDiseaseName = westDiagLs.get(0).getDiagName();
        }
        if (!tcmMainDiagLs.isEmpty()) {
            opTcmDiseaseCode = tcmMainDiagLs.get(0).getDiagCode();
            opTcmDiseaseName = tcmMainDiagLs.get(0).getDiagName();
        }
        if (!tcmOtherDiagLs.isEmpty()) {
            opTcmSymptomCode = tcmOtherDiagLs.get(0).getDiagCode();
            opTcmSymptomName = tcmOtherDiagLs.get(0).getDiagName();
        }
        if (ObjectUtil.isNotEmpty(pathologicalDiagLs)) {
            pathologicalDiagCode = pathologicalDiagLs.get(0).getDiagCode();
            pathologicalDiagName = pathologicalDiagLs.get(0).getDiagName();
            pathologyNo = pathologicalDiagLs.get(0).getSpecimenNo();
        }
        LambdaUpdateWrapper<VisitExtraEntity> wrapper = Wrappers.lambdaUpdate(VisitExtraEntity.class);
        wrapper.eq(VisitExtraEntity::getVisitId, visitId);
        wrapper.set(VisitExtraEntity::getOpDiseaseCode, opDiseaseCode);
        wrapper.set(VisitExtraEntity::getOpDiseaseName, opDiseaseName);
        wrapper.set(VisitExtraEntity::getOpTcmDiseaseCode, opTcmDiseaseCode);
        wrapper.set(VisitExtraEntity::getOpTcmDiseaseName, opTcmDiseaseName);
        wrapper.set(VisitExtraEntity::getOpTcmSymptomCode, opTcmSymptomCode);
        wrapper.set(VisitExtraEntity::getOpTcmSymptomName, opTcmSymptomName);
        wrapper.set(VisitExtraEntity::getPathologicalDiagCode, pathologicalDiagCode);
        wrapper.set(VisitExtraEntity::getPathologicalDiagName, pathologicalDiagName);
        wrapper.set(VisitExtraEntity::getPathologyNo, pathologyNo);
        visitExtraService.update(wrapper);
    }

    @Override
    @Transactional
    public VisitDto saveVisitByRegId(Long orgId, Long userId, Long regId, Long clinicianId, String deptCode, Integer clinicTypeId, Integer medTypeId) {
        log.info("根据挂号ID保存诊疗记录 orgId:{} userId:{} regId:{} clinicianId: {} deptCode:{}", orgId, userId, regId, clinicianId, deptCode);
        // 获取挂号记录
        RegDto reg = regService.findById(regId);
        if (reg == null) {
            throw new SaveFailureException("没有挂号记录。");
        }
        RegTriageEntity regTriage = regTriageService.getById(regId);
        if (regTriage == null) {
            regTriage = RegTriageEntity.builder().regId(regId).userId(userId).build();
            regTriageService.save(regTriage);
        }
        // 保存诊疗信息
        Long visitId = reg.getVisitId();
        if (visitId == null) {
            List<VisitEntity> visitLs = list(Wrappers.lambdaQuery(VisitEntity.class).eq(VisitEntity::getRegId, regId));
            if (!visitLs.isEmpty()) {
                visitId = visitLs.get(0).getVisitId();
            }
        }
        if (visitId == null) {
            visitId = saveEntity(orgId, reg.getPatientId(), clinicianId, regId, "", null, null,
                    null, null, "", "", null, "",
                    "", clinicTypeId, reg.getClinicDate(), reg.getInsuranceTypeId(), null, medTypeId,
                    VisitStatus.outpatient.getValue(), deptCode, reg.getComplainOf(), reg.getPatientStatement(), reg.getTemperature(), reg.getHeightCm(),
                    reg.getWeightKg(), reg.getPulse(), reg.getRr(), reg.getDbp(), reg.getSbp(), "", "", reg.getPatientTypeId(),
                    null, "", null, "");
        } else {
            LambdaUpdateWrapper<VisitEntity> visitWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
            visitWrapper.eq(VisitEntity::getVisitId, visitId);
            visitWrapper.set(VisitEntity::getClinicianId, clinicianId);
            visitWrapper.set(VisitEntity::getDeptCode, deptCode);
            visitWrapper.set(VisitEntity::getClinicTypeId, clinicTypeId);
            visitWrapper.set(VisitEntity::getMedTypeId, medTypeId);
            visitWrapper.set(VisitEntity::getVisitStatus, VisitStatus.inHospital.getValue());
            visitWrapper.set(VisitEntity::getTimeAdmission, new Date());
            update(visitWrapper);
            // 保存未完结诊疗记录
            VisitPendingEntity visitPending = visitPendingService.getById(visitId);
            if (visitPending == null) {
                visitPendingService.save(VisitPendingEntity.builder().visitId(visitId).clinicianId(clinicianId).patientId(reg.getPatientId()).build());
            } else {
                visitPendingService.update(Wrappers.lambdaUpdate(VisitPendingEntity.class).eq(VisitPendingEntity::getVisitId, visitId)
                        .set(VisitPendingEntity::getClinicianId, clinicianId));
            }
        }
        // 修改诊疗状态
        LambdaUpdateWrapper<RegEntity> regWrapper = Wrappers.lambdaUpdate(RegEntity.class);
        regWrapper.eq(RegEntity::getRegId, regId);
        regWrapper.set(RegEntity::getVisitId, visitId);
        regWrapper.set(RegEntity::getClinicStatus, ClinicStatus.visit.getValue());
        regWrapper.set(RegEntity::getRegStatus, RegStatus.CONFIRMED.getValue());
        regWrapper.set(RegEntity::getClinicianId, clinicianId);
        regWrapper.set(RegEntity::getTimeAdmission, new Date());
        if (reg.getTimeArrival() == null) {
            regWrapper.set(RegEntity::getTimeArrival, new Date());
        }
        regService.update(regWrapper);

        return findById(visitId, true);
    }

    @Override
    @Transactional
    public Long saveEntity(Long orgId, Long patientId, Long clinicianId, Long regId, String patientName, Integer genderId, Integer ageOfYears, BigDecimal ageOfDays,
                           Integer agesTypeId, String contactorName, String contactorTel, Integer relationshipId, String companionIdno, String companionAddr,
                           Integer clinicTypeId, Integer clinicDate, Integer insuranceTypeId, Integer psnTypeId, Integer medTypeId, Integer status, String deptCode, String complainOf,
                           String patientStatement, BigDecimal temperature, Integer heightCm, BigDecimal weightKg, Integer pulse, Integer rr, Integer dbp,
                           Integer sbp, String livingZonecode, String livingAddr, Integer patientTypeId, Integer civilServantFlag,
                           String employerName, Integer mdtrtCertTypeId, String mdtrtCertText) {
        log.info("SaveVisit orgId:{} patientId:{} clinicianId:{} regId:{} clinicDate:{}", orgId, patientId, clinicianId, regId, clinicDate);
        // 保存诊疗记录
        VisitEntity visit = new VisitEntity();
        visit.setOrgId(orgId);
        if (status != null && status.equals(VisitStatus.planOut.getValue())) {
            visit.setTimeAdmission(new Date());
        }
        visit.setPatientId(patientId);
        visit.setClinicianId(clinicianId);
        visit.setClinicDate(clinicDate);
        visit.setRegId(regId);
        visit.setClinicTypeId(clinicTypeId);
        visit.setVisitStatus(status);
        visit.setInsuranceTypeId(insuranceTypeId);
        visit.setPsnTypeId(psnTypeId);
        visit.setMedTypeId(medTypeId);
        visit.setDeptCode(deptCode);
        PatientEntity patient = getPatient(visit, orgId, patientId, patientName, genderId, ageOfYears, ageOfDays, medTypeId);
        Long visitId = sequenceService.getLongNextValue(VisitEntity.class.getSimpleName());
        visit.setVisitId(visitId);
        visit.setPatientTypeId(patientTypeId);
        visit.setCivilServantFlag(civilServantFlag);
        visit.setMdtrtCertTypeId(mdtrtCertTypeId);
        save(visit);
        // 保存诊疗记录其他信息
        buildVisitExtra(visitId, regId, patientId, contactorName, contactorTel, relationshipId, companionIdno, companionAddr,
                temperature, heightCm, weightKg, pulse, rr, dbp, sbp, livingZonecode, livingAddr, employerName, mdtrtCertText,
                patient);
        // 保存门诊病例
        buildOutpatient(visitId, patientId, complainOf, patientStatement);
        // 保存诊疗记录状态
        visitStateService.save(VisitStateEntity.builder().visitId(visitId).build());
        // 保存未完结诊疗记录
        visitPendingService.save(VisitPendingEntity.builder().visitId(visitId).clinicianId(clinicianId).patientId(patientId).build());
        // 保存患者状态
        if (patientId != null) {
            PatientStateEntity patientState = patientStateService.getById(patientId);
            if (patientState == null) {
                patientStateService.save(PatientStateEntity.builder().patientId(patientId).pendingVisitId(visitId).build());
            } else {
                patientStateService.update(Wrappers.lambdaUpdate(PatientStateEntity.class).eq(PatientStateEntity::getPatientId, patientId)
                        .set(PatientStateEntity::getPendingVisitId, visitId));
            }
        }
        return visitId;
    }

    private void buildVisitExtra(Long visitId, Long regId, Long patientId, String contactorName, String contactorTel, Integer relationshipId, String companionIdno,
                                             String companionAddr, BigDecimal temperature, Integer heightCm, BigDecimal weightKg, Integer pulse, Integer rr, Integer dbp,
                                             Integer sbp, String livingZonecode, String livingAddr, String employerName, String mdtrtCertText, PatientEntity patient) {
        VisitExtraEntity visitExtra = VisitExtraEntity.builder()
                .visitId(visitId)
                .temperature(temperature)
                .heightCm(heightCm)
                .weightKg(weightKg)
                .pulse(pulse)
                .rr(rr)
                .dbp(dbp)
                .sbp(sbp)
                .companionName(contactorName)
                .relationshipId(relationshipId)
                .companionAddr(companionAddr)
                .livingZonecode(livingZonecode)
                .livingAddr(livingAddr)
                .employerName(employerName)
                .mdtrtCertText(mdtrtCertText)
                .build();
        if (StrUtil.isNotBlank(contactorTel) && ValidatorUtil.isMobile(contactorTel)) {
            visitExtra.setContactTel(contactorTel);
        }
        if (StrUtil.isNotBlank(companionIdno) && ValidatorUtil.isIdCard(companionIdno)) {
            visitExtra.setCompanionIdno(companionIdno);
        }
        if (patientId != null) {
            // 获取最近的诊疗记录其他信息
            VisitExtraEntity lastVisitExtra = visitExtraService.findLastByPatientId(patientId, ClinicType.outpatient.getValue());
            if (lastVisitExtra != null) {
                if (visitExtra.getRelationshipId() == null) {
                    visitExtra.setRelationshipId(lastVisitExtra.getRelationshipId());
                }
                if (StrUtil.isBlank(visitExtra.getCompanionName())) {
                    visitExtra.setCompanionName(lastVisitExtra.getCompanionName());
                }
                if (StrUtil.isNotBlank(visitExtra.getCompanionName()) && StrUtil.isNotBlank(lastVisitExtra.getCompanionName())
                        && visitExtra.getCompanionName().equals(lastVisitExtra.getCompanionName())) {
                    visitExtra.setCompanionIdno(lastVisitExtra.getCompanionIdno());
                }
                if (StrUtil.isBlank(visitExtra.getContactTel())) {
                    visitExtra.setContactTel(lastVisitExtra.getContactTel());
                }
                if (StrUtil.isBlank(visitExtra.getCompanionIdno())) {
                    visitExtra.setCompanionIdno(lastVisitExtra.getCompanionIdno());
                }
                if (StrUtil.isBlank(visitExtra.getCompanionAddr())) {
                    visitExtra.setCompanionAddr(lastVisitExtra.getCompanionAddr());
                }
                if (visitExtra.getHeightCm() == null) {
                    visitExtra.setHeightCm(lastVisitExtra.getHeightCm());
                }
                if (visitExtra.getWeightKg() == null) {
                    visitExtra.setWeightKg(lastVisitExtra.getWeightKg());
                }
                visitExtra.setAboBloodTypeId(lastVisitExtra.getAboBloodTypeId());
                visitExtra.setRhBloodTypeId(lastVisitExtra.getRhBloodTypeId());
            } else {
                if (StrUtil.isBlank(visitExtra.getCompanionName())) {
                    visitExtra.setCompanionName(patient.getContactorName());
                }
                if (StrUtil.isBlank(visitExtra.getContactTel())) {
                    visitExtra.setContactTel(patient.getContactorTel());
                }
                if (visitExtra.getRelationshipId() == null) {
                    visitExtra.setRelationshipId(patient.getRelationshipId());
                }
            }
            visitExtra.setCertTypeId(patient.getCertTypeId());
            visitExtra.setIdcertNo(patient.getIdcertNo());
            visitExtra.setFamilyZonecode(patient.getFamilyZonecode());
            visitExtra.setFamilyAddr(patient.getFamilyAddr());
            visitExtra.setFamilyZipcode(patient.getFamilyZipcode());
            if (StrUtil.isBlank(visitExtra.getLivingZonecode())) {
                visitExtra.setLivingZonecode(patient.getLivingZonecode());
            }
            if (StrUtil.isBlank(visitExtra.getLivingAddr())) {
                visitExtra.setLivingAddr(patient.getLivingAddr());
            }
            visitExtra.setLivingZipcode(patient.getLivingZipcode());
        }
        // 获取分诊记录信息
        if (regId != null) {
            RegTriageEntity regTriage = regTriageService.getById(regId);
            if (regTriage != null) {
                boolean isUpdate = false;
                if (regTriage.getHeightCm() != null) {
                    visitExtra.setHeightCm(Convert.toInt(regTriage.getHeightCm()));
                } else if (visitExtra.getHeightCm() != null){
                    regTriage.setHeightCm(Convert.toBigDecimal(visitExtra.getHeightCm()));
                    isUpdate = true;
                }
                if (regTriage.getWeightKg() != null) {
                    visitExtra.setWeightKg(regTriage.getWeightKg());
                } else if (visitExtra.getWeightKg() != null) {
                    regTriage.setWeightKg(visitExtra.getWeightKg());
                    isUpdate = true;
                }
                visitExtra.setTemperature(regTriage.getTemperature());
                visitExtra.setDbp(regTriage.getDbp());
                visitExtra.setSbp(regTriage.getSbp());
                visitExtra.setPulse(regTriage.getPulse());
                visitExtra.setRr(regTriage.getRr());
                // 修改分诊记录信息
                if (isUpdate) {
                    regTriageService.updateById(regTriage);
                }
            } else if (visitExtra.getWeightKg() != null || visitExtra.getHeightCm() != null) {
                regTriage = RegTriageEntity.builder()
                        .regId(regId)
                        .weightKg(visitExtra.getWeightKg())
                        .heightCm(Convert.toBigDecimal(visitExtra.getHeightCm()))
                        .temperature(visitExtra.getTemperature())
                        .pulse(visitExtra.getPulse())
                        .rr(visitExtra.getRr())
                        .sbp(visitExtra.getSbp())
                        .dbp(visitExtra.getDbp())
                        .build();
                regTriageService.save(regTriage);
            }
        }
        visitExtraService.save(visitExtra);
    }

    private void buildOutpatient(Long visitId, Long patientId, String complainOf, String patientStatement) {
        OutpatientEntity outpatient = OutpatientEntity.builder()
                .visitId(visitId)
                .complainedAs(complainOf)
                .patientStatement(patientStatement)
                .build();
        if (patientId != null) {
            // 获取最近的诊疗记录其他信息
            VisitExtraEntity lastVisitExtra = visitExtraService.findLastByPatientId(patientId, ClinicType.outpatient.getValue());
            if (lastVisitExtra != null) {
                // 门诊病例
                OutpatientEntity lastOp = outpatientService.getById(lastVisitExtra.getVisitId());
                if (lastOp != null) {
//                    outpatient.setHpiDesc(lastOp.getHpiDesc());
                    outpatient.setPastHistory(lastOp.getPastHistory());
                    outpatient.setFamilyHistory(lastOp.getFamilyHistory());
                    outpatient.setAllergicHistory(lastOp.getAllergicHistory());
                }
                // 获取过敏史列表
                List<VisitAllergyEntity> allergyLs = visitAllergyService.list(Wrappers.lambdaQuery(VisitAllergyEntity.class).eq(VisitAllergyEntity::getVisitId, lastVisitExtra.getVisitId()));
                if (ObjectUtil.isNotEmpty(allergyLs)) {
                    allergyLs.forEach(entity -> entity.setVisitId(visitId));
                    visitAllergyService.saveBatch(allergyLs);
                }
                // 获取病史列表
                List<VisitIllnessEntity> illnessLs = visitIllnessService.list(Wrappers.lambdaQuery(VisitIllnessEntity.class).eq(VisitIllnessEntity::getVisitId, lastVisitExtra.getVisitId()));
                if (ObjectUtil.isNotEmpty(illnessLs)) {
                    illnessLs.forEach(entity -> entity.setVisitId(visitId));
                    visitIllnessService.saveBatch(illnessLs);
                }
            }
        }
        outpatientService.save(outpatient);
    }

    private PatientEntity getPatient(VisitEntity visit, Long orgId, Long patientId, String patientName, Integer genderId,
                                     Integer ageOfYears, BigDecimal ageOfDays, Integer medTypeId) {
        PatientEntity patient = null;
        ageOfYears = ageOfYears == null ? 0 : ageOfYears;
        ageOfDays = ageOfDays == null ? BigDecimal.ZERO : ageOfDays;
        if (patientId != null) {
            patient = patientService.getById(patientId);
            if (patient.getBirthDate() != null) {
                BigDecimal[] ageAndDayCount = calculateAgeAndDayCount(patient.getBirthDate(), patient.getBirthTime());
                ageOfYears = Convert.toInt(ageAndDayCount[0]);
                ageOfDays = ageAndDayCount[1];
                visit.setAgeOfYears(ageOfYears);
                visit.setAgeOfDays(ageOfDays);
                OrgSettingEntity orgSetting = orgSettingService.getById(orgId);
                if (orgSetting != null) {
                    // 是否儿童病例
                    if (orgSetting.getChildrenMaxAge() != null &&  ageOfYears <= orgSetting.getChildrenMaxAge()) {
                        visit.setIsChildren(1);
                    } else {
                        visit.setIsChildren(0);
                    }
                    // 是否老年病例
                    if (orgSetting.getAgedMinAge() != null && ageOfYears >= orgSetting.getAgedMinAge()) {
                        visit.setIsAged(1);
                    } else {
                        visit.setIsAged(0);
                    }
                }
                if (ageOfYears <= 0) {
                    // 是否新生儿 小于28天是新生儿
                    if (Convert.toInt(ageOfDays) <= 28) {
                        visit.setIsNewborn(1);
                    } else {
                        visit.setIsNewborn(0);
                    }
                    visit.setAgesId(getAgesId(Convert.toInt(ageOfDays), 0));
                } else {
                    visit.setAgesId(getAgesId(0, ageOfYears));
                }
            }
            visit.setPatientName(patient.getPatientName());
            visit.setGenderId(patient.getGenderId());
            visit.setZoneCode(patient.getZoneCode());
            visit.setCareerId(patient.getCareerId());
            visit.setMarriageStatusId(patient.getMarriageStatusId());
            // 患者机构映射
            MedicalTypeEntity medicalType = medicalTypeService.getById(medTypeId);
            if (medicalType == null) {
                throw new SaveFailureException("未找到医疗类别代码信息。");
            }
            MpiPatientMapEntity patientMap = mpiPatientMapService.findById(patientId, orgId);
            if (medicalType.getForOpc() == 1 && (patientMap == null || patientMap.getOutpatientNo() == null)) {
                Integer outpatientNo = mpiPatientnoService.getNextOutpatientNo(orgId);
                mpiPatientMapService.updateOutpatientNo(orgId, patientId, outpatientNo);
            } else if (medicalType.getForIpc() == 1 && (patientMap == null || patientMap.getInpatientNo() == null)) {
                Integer inpatientNo = mpiPatientnoService.getNextInpatientNo(orgId);
                mpiPatientMapService.updateInpatientNo(orgId, patientId, inpatientNo);
            }
        } else {
            visit.setPatientName(patientName);
            visit.setGenderId(genderId);
            visit.setAgeOfYears(ageOfYears);
            visit.setAgeOfDays(ageOfDays);
            OrgSettingEntity orgSetting = orgSettingService.getById(orgId);
            if (orgSetting != null) {
                // 是否儿童病例
                if (orgSetting.getChildrenMaxAge() != null &&  ageOfYears <= orgSetting.getChildrenMaxAge()) {
                    visit.setIsChildren(1);
                } else {
                    visit.setIsChildren(0);
                }
                // 是否老年病例
                if (orgSetting.getAgedMinAge() != null && ageOfYears >= orgSetting.getAgedMinAge()) {
                    visit.setIsAged(1);
                } else {
                    visit.setIsAged(0);
                }
            }
            if (ageOfYears <= 0) {
                // 是否新生儿 小于28天是新生儿
                if (Convert.toInt(ageOfDays) <= 28) {
                    visit.setIsNewborn(1);
                } else {
                    visit.setIsNewborn(0);
                }
                visit.setAgesId(getAgesId(Convert.toInt(ageOfDays), 0));
            } else {
                visit.setAgesId(getAgesId(0, ageOfYears));
            }
        }
        return patient;
    }

    @Override
    public BigDecimal[] calculateAgeAndDayCount(Integer birthDate, Integer birthTime) {
        if (birthDate == null || birthDate <= 0 || birthDate.toString().length() != 8) {
            return new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO};
        }

        // 解析出生日期
        int year = Integer.parseInt(birthDate.toString().substring(0, 4));
        int month = Integer.parseInt(birthDate.toString().substring(4, 6));
        int day = Integer.parseInt(birthDate.toString().substring(6, 8));

        // 解析出生时间，默认为 00:00:00
        int hour = 0;
        int minute = 0;
        int second = 0;
        if (birthTime != null && birthTime.toString().length() == 6) {
            hour = Integer.parseInt(birthTime.toString().substring(0, 2));
            minute = Integer.parseInt(birthTime.toString().substring(2, 4));
            second = Integer.parseInt(birthTime.toString().substring(4, 6));
        }

        // 创建出生日期时间对象
        LocalDateTime birthDateTime = LocalDateTime.of(year, month, day, hour, minute, second);
        LocalDateTime now = LocalDateTime.now();

        // 计算年龄
        Period age = Period.between(birthDateTime.toLocalDate(), now.toLocalDate());
        int years = age.getYears();

        // 获取今年的生日日期
        LocalDate thisYearsBirthday = birthDateTime.toLocalDate().withYear(now.getYear());
        if (thisYearsBirthday.isAfter(now.toLocalDate())) {
            thisYearsBirthday = thisYearsBirthday.minusYears(1);
        }

        // 计算今年的日龄
        long days = ChronoUnit.DAYS.between(thisYearsBirthday, now.toLocalDate());
        BigDecimal dayCount;
        if (days < 3) {
            // 当日龄小于 3 时，精确到时分秒
            long totalSeconds = ChronoUnit.SECONDS.between(LocalDateTime.of(thisYearsBirthday, birthDateTime.toLocalTime()), now);
            dayCount = BigDecimal.valueOf(totalSeconds).divide(BigDecimal.valueOf(86400), 6, RoundingMode.HALF_UP);
        } else {
            dayCount = BigDecimal.valueOf(days);
        }
        // 将 dayCount 转换为天、时、分、秒
//        long totalSecondsLong = dayCount.multiply(BigDecimal.valueOf(86400)).longValue();
//        long dayPart = totalSecondsLong / 86400;
//        long hourPart = (totalSecondsLong % 86400) / 3600;
//        long minutePart = (totalSecondsLong % 3600) / 60;
//        long secondPart = totalSecondsLong % 60;
//        System.out.printf("天: %d, 时: %d, 分: %d, 秒: %d%n", dayPart, hourPart, minutePart, secondPart);
        return new BigDecimal[]{BigDecimal.valueOf(years), dayCount};
    }

    @Override
    public Integer getAgesId(Integer ageOfDays, Integer ageOfYears) {
        if (ageOfYears == null) ageOfYears = 0;
        if (ageOfDays == null) ageOfDays = 0;
        List<AgesEntity> agesLs = agesService.list(Wrappers.lambdaQuery(AgesEntity.class).orderByAsc(AgesEntity::getDisplayOrder));
        if (ageOfYears < 0 && ageOfDays < 0) {
            return 0;
        }
        if (ageOfYears >= 90) {
            Integer finalAgeOfYears = ageOfYears;
            List<AgesEntity> list = agesLs.stream().filter(p -> p.getMinAge() != null && p.getMinAge() <= finalAgeOfYears).collect(Collectors.toList());
            if (!list.isEmpty()) {
                return list.get(0).getAgesId();
            } else {
                return 0;
            }
        }
        List<AgesEntity> list;
        if (ageOfYears <= 0) {
            Integer finalAgeOfDays = ageOfDays;
            list = agesLs.stream().filter(p -> p.getMinDays() != null && p.getMaxDays() != null
                    && p.getMinDays() <= finalAgeOfDays && p.getMaxDays() >= finalAgeOfDays).collect(Collectors.toList());
        } else {
            Integer finalAgeOfYears = ageOfYears;
            list = agesLs.stream().filter(p -> p.getMinAge() != null && p.getMaxAge() != null
                    && p.getMinAge() <= finalAgeOfYears && p.getMaxAge() >= finalAgeOfYears).collect(Collectors.toList());
        }
        if (!list.isEmpty()) {
            return list.get(0).getAgesId();
        } else {
            return 0;
        }
    }

    @Override
    @Transactional
    public Map<String, Object> refVisit(Long userId, Long visitId, Long refVisitId) {
        log.info("诊疗引用 userId:{} visitId:{} refVisitId:{}", userId, visitId, refVisitId);
        Map<String, Object> result = new HashMap<>();
        if (visitId != null) {
            // 获取诊疗信息
            VisitEntity visit = getById(visitId);
            // 获取诊疗病例信息
            OutpatientEntity outpatient = outpatientService.getById(visitId);
            OutpatientEntity refOutpatient = outpatientService.getById(refVisitId);
            LambdaUpdateWrapper<OutpatientEntity> updateWrapper = Wrappers.lambdaUpdate(OutpatientEntity.class);
            updateWrapper.eq(OutpatientEntity::getVisitId, visitId);
            updateWrapper.set(OutpatientEntity::getComplainedAs, StrUtil.isBlank(outpatient.getComplainedAs()) ? refOutpatient.getComplainedAs() : outpatient.getComplainedAs());
            updateWrapper.set(OutpatientEntity::getGeneralInspection, StrUtil.isBlank(outpatient.getGeneralInspection()) ? refOutpatient.getGeneralInspection() : outpatient.getGeneralInspection());
            updateWrapper.set(OutpatientEntity::getHealthEducation, StrUtil.isBlank(outpatient.getHealthEducation()) ? refOutpatient.getHealthEducation() : outpatient.getHealthEducation());
            updateWrapper.set(OutpatientEntity::getTreatAbstract, StrUtil.isBlank(outpatient.getTreatAbstract()) ? refOutpatient.getTreatAbstract() : outpatient.getTreatAbstract());
            updateWrapper.set(OutpatientEntity::getAllergicHistory, StrUtil.isBlank(outpatient.getAllergicHistory()) ? refOutpatient.getAllergicHistory() : outpatient.getAllergicHistory());
            updateWrapper.set(OutpatientEntity::getHpiDesc, StrUtil.isBlank(outpatient.getHpiDesc()) ? refOutpatient.getHpiDesc() : outpatient.getHpiDesc());
            updateWrapper.set(OutpatientEntity::getPastHistory, StrUtil.isBlank(outpatient.getPastHistory()) ? refOutpatient.getPastHistory() : outpatient.getPastHistory());
            updateWrapper.set(OutpatientEntity::getMaritalHistory, StrUtil.isBlank(outpatient.getMaritalHistory())? refOutpatient.getMaritalHistory() : outpatient.getMaritalHistory());
            updateWrapper.set(OutpatientEntity::getChildbearingHistory, StrUtil.isBlank(outpatient.getChildbearingHistory())? refOutpatient.getChildbearingHistory() : outpatient.getChildbearingHistory());
            updateWrapper.set(OutpatientEntity::getMenstrualHistory, StrUtil.isBlank(outpatient.getMenstrualHistory())? refOutpatient.getMenstrualHistory() : outpatient.getMenstrualHistory());
            outpatientService.update(updateWrapper);
            // 过敏史
            List<VisitAllergyEntity> visitAllergyLs = visitAllergyService.list(Wrappers.lambdaQuery(VisitAllergyEntity.class).eq(VisitAllergyEntity::getVisitId, visitId));
            if (ObjectUtil.isEmpty(visitAllergyLs)) {
                List<VisitAllergyEntity> refVisitAllergyLs = visitAllergyService.list(Wrappers.lambdaQuery(VisitAllergyEntity.class).eq(VisitAllergyEntity::getVisitId, refVisitId));
                if (ObjectUtil.isNotEmpty(refVisitAllergyLs)) {
                    visitAllergyService.saveBatch(visitId, refVisitAllergyLs.stream().map(VisitAllergyEntity::getAllergyTypeId).collect(Collectors.toList()));
                }
            }
            // 获取诊疗疾病列表
            List<VisitIllnessEntity> visitIllnessLs = visitIllnessService.list(Wrappers.lambdaQuery(VisitIllnessEntity.class).eq(VisitIllnessEntity::getVisitId, visitId));
            if (ObjectUtil.isEmpty(visitIllnessLs)) {
                List<VisitIllnessEntity> refVisitIllnessLs = visitIllnessService.list(Wrappers.lambdaQuery(VisitIllnessEntity.class).eq(VisitIllnessEntity::getVisitId, refVisitId));
                if (ObjectUtil.isNotEmpty(refVisitIllnessLs)) {
                    visitIllnessService.saveBatch(visitId, refVisitIllnessLs.stream().map(VisitIllnessEntity::getIllnessTypeId).collect(Collectors.toList()));
                }
            }
            // 获取诊疗诊断列表
            List<VisitDiagEntity> srcVisitDiagLs = visitDiagService.list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, visitId));
            List<VisitDiagEntity> refVisitDiagLs = visitDiagService.list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, refVisitId).ne(VisitDiagEntity::getDiagStatus, DiagStatus.removed.getValue()));
            List<String> existDiagLs = srcVisitDiagLs.stream().map(VisitDiagEntity::getDiagCode).collect(Collectors.toList());
            List<VisitDiagEntity> visitDiagLs = new ArrayList<>();
            Integer diagNo = 1;
            for (VisitDiagEntity visitDiag : srcVisitDiagLs) {
                if (diagNo < visitDiag.getDiagNo()) {
                    diagNo = visitDiag.getDiagNo();
                }
                visitDiagLs.add(visitDiag);
            }
            diagNo++;
            if (refVisitDiagLs != null && !refVisitDiagLs.isEmpty()) {
                for (VisitDiagEntity visitDiag : refVisitDiagLs) {
                    if (!existDiagLs.contains(visitDiag.getDiagCode())) {
                        visitDiag.setDiagNo(diagNo);
                        visitDiag.setDiagStatus((visitDiag.getDiagTypeId().equals(DiagType.westernDiag.getValue()) || visitDiag.getDiagTypeId().equals(DiagType.medicalDiag.getValue())) ? DiagStatus.suspected.getValue() : DiagStatus.confirmed.getValue());
                        visitDiagLs.add(visitDiag);
                        diagNo++;
                    }
                }
            }
            visitDiagService.saveBatchEntity(visitId, userId, visit.getClinicianId(), visit.getDeptCode(), visitDiagLs);
            // 修改诊疗诊断信息
            updateVisitExtraDiag(visitId, visitDiagLs);
            // 修改诊疗状态诊断数量
            Integer diagCode = visitDiagLs.size();
            Integer diagDenied = (int) visitDiagLs.stream().filter(p -> p.getDiagStatus().equals(DiagStatus.removed.getValue())).count();
            LambdaUpdateWrapper<VisitStateEntity> stateWrapper = Wrappers.lambdaUpdate(VisitStateEntity.class);
            stateWrapper.eq(VisitStateEntity::getVisitId, visitId);
            stateWrapper.set(VisitStateEntity::getDiagCount, diagCode);
            stateWrapper.set(VisitStateEntity::getDiagDenied, diagDenied);
            visitStateService.update(stateWrapper);
        } else {
            OutpatientEntity refOutpatient = outpatientService.getById(refVisitId);
            List<VisitDiagEntity> visitDiagLs = visitDiagService.list(Wrappers.lambdaQuery(VisitDiagEntity.class).eq(VisitDiagEntity::getVisitId, refVisitId).ne(VisitDiagEntity::getDiagStatus, DiagStatus.removed.getValue()));
            List<VisitAllergyEntity> visitAllergyLs = visitAllergyService.list(Wrappers.lambdaQuery(VisitAllergyEntity.class).eq(VisitAllergyEntity::getVisitId, visitId));
            List<VisitIllnessEntity> visitIllnessLs = visitIllnessService.list(Wrappers.lambdaQuery(VisitIllnessEntity.class).eq(VisitIllnessEntity::getVisitId, visitId));
            result.put("outpatient", refOutpatient);
            result.put("visitAllergyLs", visitAllergyLs);
            result.put("visitIllnessLs", visitIllnessLs);
            result.put("visitDiagLs", visitDiagLs);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVisitPatient(UpdatePatientVo entity) {
        try {
            VisitEntity visit = getById(entity.getVisitId());
            // 修改诊疗信息
            updateVisitEntity(entity);
            // 修改诊疗其他信息
            updateVisitExtraEntity(entity);
            // 修改患者信息
            updatePatientEntity(visit.getPatientId(), entity);
            // 修改患者其他诊疗记录信息
            updateOtherVisitExtraEntity(visit.getPatientId(), entity);
        } catch (Exception e) {
            log.error("修改患者信息失败", e);
            throw new SaveFailureException("修改患者信息失败");
        }
    }

    private void updateVisitEntity(UpdatePatientVo entity) {
        LambdaUpdateWrapper<VisitEntity> visitWrapper = Wrappers.lambdaUpdate(VisitEntity.class);
        visitWrapper.eq(VisitEntity::getVisitId, entity.getVisitId());
        visitWrapper.set(VisitEntity::getMarriageStatusId, entity.getMarriageStatusId());
        visitWrapper.set(VisitEntity::getCareerId, entity.getCareerId());
        update(visitWrapper);
    }

    private void updateVisitExtraEntity(UpdatePatientVo entity) {
        LambdaUpdateWrapper<VisitExtraEntity> extraWrapper = Wrappers.lambdaUpdate(VisitExtraEntity.class);
        extraWrapper.eq(VisitExtraEntity::getVisitId, entity.getVisitId());
        extraWrapper.set(VisitExtraEntity::getRelationshipId, entity.getRelationshipId());
        extraWrapper.set(VisitExtraEntity::getCompanionName, entity.getCompanionName());
        if (StrUtil.isNotBlank(entity.getCompanionIdno()) && ValidatorUtil.isIdCard(entity.getCompanionIdno())) {
            extraWrapper.set(VisitExtraEntity::getCompanionIdno, AESEncryptHandler.getEncryptStr(entity.getCompanionIdno()));
        } else if (StrUtil.isBlank(entity.getCompanionIdno())) {
            extraWrapper.set(VisitExtraEntity::getCompanionIdno, null);
        }
        if (StrUtil.isNotBlank(entity.getContactTel()) && ValidatorUtil.isMobile(entity.getContactTel())) {
            extraWrapper.set(VisitExtraEntity::getContactTel, AESEncryptHandler.getEncryptStr(entity.getContactTel()));
        } else if (StrUtil.isBlank(entity.getContactTel())) {
            extraWrapper.set(VisitExtraEntity::getContactTel, null);
        }
        extraWrapper.set(VisitExtraEntity::getCompanionAddr, entity.getCompanionAddr());
        extraWrapper.set(VisitExtraEntity::getHeightCm, entity.getHeightCm());
        extraWrapper.set(VisitExtraEntity::getWeightKg, entity.getWeightKg());
        extraWrapper.set(VisitExtraEntity::getMiDiseaseCode, entity.getMiDiseaseCode());
        extraWrapper.set(VisitExtraEntity::getMiDiseaseName, entity.getMiDiseaseName());
        extraWrapper.set(VisitExtraEntity::getFamilyAddr, entity.getFamilyAddr());
        extraWrapper.set(VisitExtraEntity::getLivingAddr, entity.getLivingAddr());
        extraWrapper.set(VisitExtraEntity::getAboBloodTypeId, entity.getAboBloodTypeId());
        extraWrapper.set(VisitExtraEntity::getRhBloodTypeId, entity.getRhBloodTypeId());
        visitExtraService.update(extraWrapper);
    }

    private void updatePatientEntity(Long patientId, UpdatePatientVo entity) {
        if (patientId != null) {
            LambdaUpdateWrapper<PatientEntity> patientWrapper = Wrappers.lambdaUpdate(PatientEntity.class);
            patientWrapper.eq(PatientEntity::getPatientId, patientId);
            patientWrapper.set(PatientEntity::getFamilyAddr, entity.getFamilyAddr());
            patientWrapper.set(PatientEntity::getLivingAddr, entity.getLivingAddr());
            patientWrapper.set(PatientEntity::getContactorName, entity.getCompanionName());
            if (StrUtil.isNotBlank(entity.getContactTel()) && ValidatorUtil.isMobile(entity.getContactTel())) {
                patientWrapper.set(PatientEntity::getContactorTel, AESEncryptHandler.getEncryptStr(entity.getContactTel()));
            } else if (StrUtil.isBlank(entity.getContactTel())) {
                patientWrapper.set(PatientEntity::getContactorTel, null);
            }
            patientWrapper.set(PatientEntity::getRelationshipId, entity.getRelationshipId());
            patientWrapper.set(PatientEntity::getBirthPlace, entity.getBirthPlace());
            patientWrapper.set(PatientEntity::getNationalityCode, entity.getNationalityCode());
            patientWrapper.set(PatientEntity::getMarriageStatusId, entity.getMarriageStatusId());
            patientWrapper.set(PatientEntity::getCareerId, entity.getCareerId());
            patientWrapper.set(PatientEntity::getLastUpdated, new Date());
            patientService.update(patientWrapper);
        }
    }

    private void updateOtherVisitExtraEntity(Long patientId, UpdatePatientVo entity) {
        if (patientId != null) {
            // 修改患者其他诊疗记录的患者信息
            List<VisitEntity> visitLs = list(Wrappers.lambdaQuery(VisitEntity.class).eq(VisitEntity::getPatientId, patientId)
                    .ne(VisitEntity::getVisitId, entity.getVisitId()));
            if (!visitLs.isEmpty()) {
                List<Long> visitIdLs = visitLs.stream().map(VisitEntity::getVisitId).distinct().collect(Collectors.toList());
                List<VisitExtraEntity> visitExtraLs = visitExtraService.list(Wrappers.lambdaQuery(VisitExtraEntity.class).in(VisitExtraEntity::getVisitId, visitIdLs));
                if (!visitExtraLs.isEmpty()) {
                    for (VisitExtraEntity visitExtra : visitExtraLs) {
                        LambdaUpdateWrapper<VisitExtraEntity> wrapper = Wrappers.lambdaUpdate(VisitExtraEntity.class);
                        wrapper.eq(VisitExtraEntity::getVisitId, visitExtra.getVisitId());
                        boolean isUpdate = false;
                        if (visitExtra.getHeightCm() == null) {
                            isUpdate = true;
                            wrapper.set(VisitExtraEntity::getHeightCm, entity.getHeightCm());
                        }
                        if (visitExtra.getWeightKg() == null) {
                            isUpdate = true;
                            wrapper.set(VisitExtraEntity::getWeightKg, entity.getWeightKg());
                        }
                        if (entity.getAboBloodTypeId() != null) {
                            isUpdate = true;
                            wrapper.set(VisitExtraEntity::getAboBloodTypeId, entity.getAboBloodTypeId());
                        }
                        if (entity.getRhBloodTypeId()!= null) {
                            isUpdate = true;
                            wrapper.set(VisitExtraEntity::getRhBloodTypeId, entity.getRhBloodTypeId());
                        }
                        if (StrUtil.isBlank(visitExtra.getFamilyAddr())) {
                            isUpdate = true;
                            wrapper.set(VisitExtraEntity::getFamilyAddr, entity.getFamilyAddr());
                        }
                        if (StrUtil.isBlank(visitExtra.getLivingAddr())) {
                            isUpdate = true;
                            wrapper.set(VisitExtraEntity::getLivingAddr, entity.getLivingAddr());
                        }
                        if (isUpdate) {
                            visitExtraService.update(wrapper);
                        }
                    }
                }
            }
        }
    }

    @Override
    @Transactional
    public void updateVisitPatientById(Long visitId, Long patientId) {
        PatientEntity patient = patientService.getById(patientId);
        VisitEntity visit = getById(visitId);
        if (visit != null) {
            LambdaUpdateWrapper<VisitEntity> wrapper = Wrappers.lambdaUpdate(VisitEntity.class);
            wrapper.eq(VisitEntity::getVisitId, visitId);
            wrapper.set(VisitEntity::getPatientId, patientId);
            wrapper.set(VisitEntity::getMarriageStatusId, patient.getMarriageStatusId());
            wrapper.set(VisitEntity::getCareerId, patient.getCareerId());
            update(wrapper);
        }
        VisitExtraEntity visitExtra = visitExtraService.getById(visitId);
        if (visitExtra != null) {
            LambdaUpdateWrapper<VisitExtraEntity> extraWrapper = Wrappers.lambdaUpdate(VisitExtraEntity.class);
            extraWrapper.eq(VisitExtraEntity::getVisitId, visitId);
            boolean isUpdate = false;
            if (StrUtil.isBlank(visitExtra.getCompanionName())) {
                isUpdate = true;
                extraWrapper.set(VisitExtraEntity::getCompanionName, patient.getContactorName());
            }
            if (StrUtil.isBlank(visitExtra.getContactTel())) {
                isUpdate = true;
                extraWrapper.set(VisitExtraEntity::getContactTel, AESEncryptHandler.getEncryptStr(patient.getContactorTel()));
            }
            if (visitExtra.getRelationshipId() == null) {
                isUpdate = true;
                extraWrapper.set(VisitExtraEntity::getRelationshipId, patient.getRelationshipId());
            }
            if (StrUtil.isBlank(visitExtra.getFamilyAddr())) {
                isUpdate = true;
                extraWrapper.set(VisitExtraEntity::getFamilyZonecode, patient.getFamilyZonecode());
                extraWrapper.set(VisitExtraEntity::getFamilyAddr, patient.getFamilyAddr());
                extraWrapper.set(VisitExtraEntity::getFamilyZipcode, patient.getFamilyZipcode());
            }
            if (StrUtil.isBlank(visitExtra.getLivingAddr())) {
                isUpdate = true;
                extraWrapper.set(VisitExtraEntity::getLivingZonecode, patient.getLivingZonecode());
                extraWrapper.set(VisitExtraEntity::getLivingAddr, patient.getLivingAddr());
                extraWrapper.set(VisitExtraEntity::getLivingZipcode, patient.getLivingZipcode());
            }
            if (visitExtra.getCertTypeId() == null) {
                isUpdate = true;
                extraWrapper.set(VisitExtraEntity::getCertTypeId, patient.getCertTypeId());
                extraWrapper.set(VisitExtraEntity::getIdcertNo, AESEncryptHandler.getEncryptStr(patient.getIdcertNo()));
            }
            if (isUpdate) {
                visitExtraService.update(extraWrapper);
            }
        }
    }

    @Override
    @Transactional
    public void updateGeneralInspection(Long visitId, String generalInspection, String auxiliaryInspection) {
        OutpatientEntity outpatient = outpatientService.getById(visitId);
        if (outpatient != null) {
            LambdaUpdateWrapper<OutpatientEntity> wrapper = Wrappers.lambdaUpdate(OutpatientEntity.class);
            wrapper.eq(OutpatientEntity::getVisitId, visitId);
            boolean isUpdate = false;
            if (StrUtil.isNotBlank(generalInspection)) {
                if (StrUtil.isNotBlank(outpatient.getGeneralInspection())) {
                    generalInspection = outpatient.getGeneralInspection() + "\n" + generalInspection;
                }
                if (StrUtil.isNotBlank(generalInspection) && generalInspection.length() > 1024) {
                    generalInspection = generalInspection.substring(0, 1024);
                }
                wrapper.set(OutpatientEntity::getGeneralInspection, generalInspection);
                isUpdate = true;
            }
            if (StrUtil.isNotBlank(auxiliaryInspection)) {
                if (StrUtil.isNotBlank(outpatient.getAuxiliaryInspection())) {
                    auxiliaryInspection = outpatient.getAuxiliaryInspection() + "\n" + auxiliaryInspection;
                }
                if (StrUtil.isNotBlank(auxiliaryInspection) && auxiliaryInspection.length() > 1024) {
                    auxiliaryInspection = auxiliaryInspection.substring(0, 1024);
                }
                wrapper.set(OutpatientEntity::getAuxiliaryInspection, auxiliaryInspection);
                isUpdate = true;
            }
            if (isUpdate) {
                outpatientService.update(wrapper);
            }
        }
    }

    @Override
    public List<VisitEntity> findInHospitalByPatientId(Long patientId) {
        // 返回当天的或当天之前未诊结的记录
        return this.list(
                new LambdaQueryWrapper<VisitEntity>()
                        .eq(VisitEntity::getPatientId, patientId)
                        .and(p -> p.eq(VisitEntity::getClinicDate, DateUtil.getTodayInt()).or()
                                .ne(VisitEntity::getVisitStatus, VisitStatus.outHospital.getValue()))
                        .orderByDesc(VisitEntity::getVisitId)
        );
    }

    @Override
    public boolean validVisit(Long visitId) {
        VisitEntity visit = getById(visitId);
        if (visit == null) {
            return false;
        }
        if (visit.getVisitStatus().equals(VisitStatus.outHospital.getValue())) {
            return false;
        }
        List<RecipeEntity> recipeLs = recipeService.list(Wrappers.lambdaQuery(RecipeEntity.class).eq(RecipeEntity::getVisitId, visitId)
                .ne(RecipeEntity::getExecStatus, ExecStatus.cancel.getValue()));
        if (ObjectUtil.isNotEmpty(recipeLs)) {
            return false;
        }
        List<BillEntity> billLs = billService.list(Wrappers.lambdaQuery(BillEntity.class).eq(BillEntity::getVisitId, visitId)
                .isNull(BillEntity::getRegId).ne(BillEntity::getPaidStatus, PaidStatus.cancel.getValue()));
        if (ObjectUtil.isEmpty(billLs)) {
            return true;
        }
        BigDecimal amt = billLs.stream().map(BillEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return amt.compareTo(BigDecimal.ZERO) == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> visitIdLs) {
        try {
            List<VisitEntity> visitLs = listByIds(visitIdLs);
            if (visitLs.stream().anyMatch(v -> v.getVisitStatus().equals(VisitStatus.outHospital.getValue()))) {
                throw new RuntimeException("已出院的诊疗记录，不能删除");
            }
            List<RecipeEntity> recipeLs = recipeService.list(Wrappers.lambdaQuery(RecipeEntity.class).in(RecipeEntity::getVisitId, visitIdLs)
                    .ne(RecipeEntity::getExecStatus, ExecStatus.cancel.getValue()));
            if (ObjectUtil.isNotEmpty(recipeLs)) {
                throw new RuntimeException("已开立了处方或申请单，不能删除。");
            }
            List<BillEntity> billLs = billService.list(Wrappers.lambdaQuery(BillEntity.class).in(BillEntity::getVisitId, visitIdLs).isNull(BillEntity::getRegId).ne(BillEntity::getPaidStatus, PaidStatus.cancel.getValue()));
            if (ObjectUtil.isNotEmpty(billLs)) {
                throw new RuntimeException("已生成了划价单费用信息，不能删除。");
            }
            for (Long visitId : visitIdLs) {
                VisitEntity entity = visitLs.stream().filter(v -> v.getVisitId().equals(visitId)).findFirst().orElse(new VisitEntity());
                recipeService.update(Wrappers.lambdaUpdate(RecipeEntity.class).eq(RecipeEntity::getVisitId, visitId)
                        .set(RecipeEntity::getVisitId, null).set(RecipeEntity::getExecStatus, ExecStatus.cancel.getValue()));
                visitExtraService.removeById(visitId);
                outpatientService.removeById(visitId);
                visitStateService.removeById(visitId);
                visitPendingService.removeById(visitId);
                visitIllnessService.removeById(visitId);
                visitAllergyService.removeById(visitId);
                visitDiagService.removeById(visitId);
                LambdaUpdateWrapper<RegEntity> wrapper = Wrappers.lambdaUpdate(RegEntity.class);
                wrapper.eq(RegEntity::getVisitId, visitId);
                wrapper.set(RegEntity::getVisitId, null);
                regService.update(wrapper);
                if (entity.getPatientId() != null) {
                    PatientStateEntity patientState = patientStateService.getById(entity.getPatientId());
                    if (patientState != null && patientState.getPendingVisitId() != null && patientState.getPendingVisitId().equals(visitId)) {
                        LambdaUpdateWrapper<PatientStateEntity> patientStateWrapper = Wrappers.lambdaUpdate(PatientStateEntity.class);
                        patientStateWrapper.eq(PatientStateEntity::getPatientId, entity.getPatientId());
                        patientStateWrapper.set(PatientStateEntity::getPendingVisitId, null);
                        patientStateService.update(patientStateWrapper);
                    }
                    if (patientState != null && patientState.getLastOpVisitId() != null && patientState.getLastOpVisitId().equals(visitId)) {
                        LambdaUpdateWrapper<PatientStateEntity> patientStateWrapper = Wrappers.lambdaUpdate(PatientStateEntity.class);
                        patientStateWrapper.eq(PatientStateEntity::getPatientId, entity.getPatientId());
                        patientStateWrapper.set(PatientStateEntity::getLastOpVisitId, null);
                        patientStateService.update(patientStateWrapper);
                    }
                }
                removeById(visitId);
            }
        } catch (Exception e) {
            throw new SaveFailureException("数据已被外部引用，删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void rejectedVisit(List<Long> visitIdLs) {
        if (ObjectUtil.isNotEmpty(visitIdLs)) {
            List<VisitEntity> visitLs = listByIds(visitIdLs);
            for (VisitEntity entity : visitLs) {
                entity.setVisitStatus(VisitStatus.outHospital.getValue());
                entity.setTimeCompleted(new Date());
                entity.setVisitNo(null);
                updateById(entity);

                Long visitId = entity.getVisitId();
                if (entity.getPatientId() != null) {
                    PatientStateEntity patientState = patientStateService.getById(entity.getPatientId());
                    if (patientState != null && patientState.getPendingVisitId() != null && patientState.getPendingVisitId().equals(visitId)) {
                        LambdaUpdateWrapper<PatientStateEntity> patientStateWrapper = Wrappers.lambdaUpdate(PatientStateEntity.class);
                        patientStateWrapper.eq(PatientStateEntity::getPatientId, entity.getPatientId());
                        patientStateWrapper.set(PatientStateEntity::getPendingVisitId, null);
                        patientStateService.update(patientStateWrapper);
                    }
                    if (patientState != null && patientState.getLastOpVisitId() != null && patientState.getLastOpVisitId().equals(visitId)) {
                        LambdaUpdateWrapper<PatientStateEntity> patientStateWrapper = Wrappers.lambdaUpdate(PatientStateEntity.class);
                        patientStateWrapper.eq(PatientStateEntity::getPatientId, entity.getPatientId());
                        patientStateWrapper.set(PatientStateEntity::getLastOpVisitId, null);
                        patientStateService.update(patientStateWrapper);
                    }
                }
            }
        }
    }

    @Override
    public List<VisitEntity> findByKeyword(String keyword) {
        return this.list(
                new LambdaQueryWrapper<VisitEntity>()
                        .like(VisitEntity::getPatientName, keyword)
                        .and(p -> p.eq(VisitEntity::getClinicDate, DateUtil.getTodayInt()).or()
                                .ne(VisitEntity::getVisitStatus, VisitStatus.outHospital.getValue()))
        );
    }

    @Override
    public String getDisplayVisitNo(VisitEntity visit) {
        if (visit.getVisitNo() != null) {
            return visit.getClinicDate() + String.format("%05d", visit.getVisitNo());
        }
        return "";
    }

    @Override
    public VisitDto findLastVisitByPatientId(Long patientId) {
        // 获取最近的诊疗记录其他信息
        VisitExtraEntity lastVisitExtra = visitExtraService.findLastByPatientId(patientId, ClinicType.outpatient.getValue());
        if (lastVisitExtra != null) {
            VisitDto visit = new VisitDto();
            visit.setCompanionName(lastVisitExtra.getCompanionName());
            //visit.setCompanionIdno(StrUtil.desensitized(lastVisitExtra.getCompanionIdno(), DesensitizedUtil.DesensitizedType.ID_CARD));
            //visit.setContactTel(StrUtil.desensitized(lastVisitExtra.getContactTel(), DesensitizedUtil.DesensitizedType.MOBILE_PHONE));
            visit.setCompanionIdno(lastVisitExtra.getCompanionIdno());
            visit.setContactTel(lastVisitExtra.getContactTel());
            visit.setRelationshipId(lastVisitExtra.getRelationshipId());
            visit.setWeightKg(lastVisitExtra.getWeightKg());
            visit.setHeightCm(lastVisitExtra.getHeightCm());
            // 门诊病例
            OutpatientEntity lastOp = outpatientService.getById(lastVisitExtra.getVisitId());
            if (lastOp != null) {
//                visit.setHpiDesc(lastOp.getHpiDesc());
                visit.setPastHistory(lastOp.getPastHistory());
                visit.setFamilyHistory(lastOp.getFamilyHistory());
                visit.setAllergicHistory(lastOp.getAllergicHistory());
            }
            // 获取过敏史列表
            List<VisitAllergyEntity> allergyLs = visitAllergyService.list(Wrappers.lambdaQuery(VisitAllergyEntity.class).eq(VisitAllergyEntity::getVisitId, lastVisitExtra.getVisitId()));
            if (ObjectUtil.isNotEmpty(allergyLs)) {
                visit.setVisitAllergyLs(allergyLs.stream().map(visitAllergy -> {
                    VisitAllergyDto visitAllergyDto = new VisitAllergyDto();
                    visitAllergyDto.setAllergyTypeId(visitAllergy.getAllergyTypeId());
                    return visitAllergyDto;
                }).collect(Collectors.toList()));
            }
            // 获取病史列表
            List<VisitIllnessEntity> illnessLs = visitIllnessService.list(Wrappers.lambdaQuery(VisitIllnessEntity.class).eq(VisitIllnessEntity::getVisitId, lastVisitExtra.getVisitId()));
            if (ObjectUtil.isNotEmpty(illnessLs)) {
                visit.setVisitIllnessLs(illnessLs.stream().map(visitIllness -> {
                    VisitIllnessDto visitIllnessDto = new VisitIllnessDto();
                    visitIllnessDto.setIllnessTypeId(visitIllness.getIllnessTypeId());
                    return visitIllnessDto;
                }).collect(Collectors.toList()));
            }
            return visit;
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> findVisitStats(Long orgId) {
        Map<String, Object> result = new HashMap<>();
        // 本月门诊数
        VisitReportDto visitReport = new VisitReportDto();
        QueryWrapper<VisitEntity> wrapper = new GQueryWrapper<VisitEntity>().getWrapper();
        wrapper.eq("v.Org_ID", orgId);
        wrapper.groupBy("v.Clinic_Type_ID");
        wrapper.apply("SUBSTR(v.Clinic_Date, 1, 6) = {0}", DateUtil.dateFormat("yyyyMM"));
        List<Map<String, Object>> list = baseMapper.findVisitStats(wrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            // 门诊
            List<Map<String, Object>> outLs = list.stream().filter(map -> Convert.toInt(map.get("clinicTypeId")) != null && (Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.outpatient.getValue())
                    || Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.emergency.getValue()))).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(outLs)) {
                Integer total = outLs.stream().mapToInt(map -> Convert.toInt(map.get("total"))).sum();
                Integer first_total = outLs.stream().mapToInt(map -> Convert.toInt(map.get("first_total"))).sum();
                Integer revisit_total = outLs.stream().mapToInt(map -> Convert.toInt(map.get("revisit_total"))).sum();
                Integer emergency_total = outLs.stream().mapToInt(map -> Convert.toInt(map.get("energency_total"))).sum();
                visitReport.setOutpatientTotal(total);
                visitReport.setOutpatientFirstTotal(first_total);
                visitReport.setOutpatientRevisitTotal(revisit_total);
                visitReport.setOutpatientEmergencyTotal(emergency_total);
            }
            // 住院
            List<Map<String, Object>> inLs = list.stream().filter(map -> Convert.toInt(map.get("clinicTypeId")) != null && Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.inpatient.getValue())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(inLs)) {
                Integer total = inLs.stream().mapToInt(map -> Convert.toInt(map.get("total"))).sum();
                visitReport.setInpatientInTotal(total);
            }
            // 其中
            List<Map<String, Object>> inOtherLs = inLs.stream().filter(map -> Convert.toInt(map.get("clinicTypeId")) == null ||
                    (Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.outpatient.getValue())
                            || Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.emergency.getValue())
                            || Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.inpatient.getValue()))).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(inOtherLs)) {
                Integer outTotal = inOtherLs.stream().mapToInt(map -> Convert.toInt(map.get("total"))).sum();
                visitReport.setOutpatientOtherTotal(outTotal);
            }
        }
        // 住院
        wrapper = new GQueryWrapper<VisitEntity>().getWrapper();
        wrapper.eq("v.Org_ID", orgId);
        wrapper.eq("v.Clinic_Type_ID", ClinicType.inpatient.getValue());
        wrapper.eq("v.Visit_Status", VisitStatus.outHospital.getValue());
        wrapper.apply("DATE_FORMAT(i.Time_Discharged, '%Y-%m') = {0}", DateUtil.dateFormat("yyyy-MM"));
        Map<String, Object> inMap = baseMapper.findDisVisitStats(wrapper);
        if (inMap != null) {
            visitReport.setInpatientOutTotal(Convert.toInt(inMap.get("total")));
            visitReport.setInpatientZyTotal(Convert.toInt(inMap.get("zy_total")));
            visitReport.setInpatientHzTotal(Convert.toInt(inMap.get("hz_total")));
            visitReport.setInpatientSwTotal(Convert.toInt(inMap.get("death_total")));
            visitReport.setInpatientWyTotal(Convert.toInt(inMap.get("wy_total")));
            visitReport.setInpatientOthTotal(Convert.toInt(inMap.get("other_total")));
        }
        // 处方数
        QueryWrapper<RecipeEntity> recipeWrapper = new GQueryWrapper<RecipeEntity>().getWrapper();
        recipeWrapper.eq("r.Org_ID", orgId);
        recipeWrapper.in("r.Recipe_Type_ID", RecipeType.xy.getValue(), RecipeType.zy.getValue());
        recipeWrapper.isNotNull("re.Time_Signed");
        recipeWrapper.apply("DATE_FORMAT(re.Time_Signed, '%Y-%m') = {0}", DateUtil.dateFormat("yyyy-MM"));
        Map<String, Object> recipeMap = baseMapper.findRecipeStats(recipeWrapper);
        if (recipeMap != null) {
            visitReport.setRecipeTotal(Convert.toInt(recipeMap.get("total")));
            visitReport.setXyTotal(Convert.toInt(recipeMap.get("west_total")));
            visitReport.setOutpatientXyTotal(Convert.toInt(recipeMap.get("out_west_total")));
            visitReport.setZcyTotal(Convert.toInt(recipeMap.get("zcy_total")));
            visitReport.setOutpatientZcyTotal(Convert.toInt(recipeMap.get("out_zcy_total")));
            visitReport.setTcmTotal(Convert.toInt(recipeMap.get("tcm_total")));
            visitReport.setOutpatientTcmTotal(Convert.toInt(recipeMap.get("out_tcm_total")));
            visitReport.setTcmTimes(Convert.toInt(recipeMap.get("tcm_times")));
        }

        Map<String, Object> map = JSONUtil.toBean(JSONUtil.toJsonStr(visitReport), Map.class);
        for(String key: map.keySet()) {
            result.put("month_" + key, map.get(key));
        }
        // 本年统计
        VisitReportDto yearStat = getYearStat(orgId);
        Map<String, Object> yesrMap = JSONUtil.toBean(JSONUtil.toJsonStr(yearStat), Map.class);
        for (String key: yesrMap.keySet()) {
            result.put("year_" + key, yesrMap.get(key));
        }
        OrgEntity org = orgService.getById(orgId);
        result.put("orgName", org.getOrgName());
        return ListUtil.of(result);
    }

    private VisitReportDto getYearStat(Long orgId) {
        // 本年统计
        VisitReportDto visitReport = new VisitReportDto();
        QueryWrapper<VisitEntity> wrapper = new GQueryWrapper<VisitEntity>().getWrapper();
        wrapper.eq("v.Org_ID", orgId);
        wrapper.groupBy("v.Clinic_Type_ID");
        List<Map<String, Object>> list = baseMapper.findVisitStats(wrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            // 门诊
            List<Map<String, Object>> outLs = list.stream().filter(map -> Convert.toInt(map.get("clinicTypeId")) != null && (Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.outpatient.getValue())
                    || Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.emergency.getValue()))).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(outLs)) {
                Integer total = outLs.stream().mapToInt(map -> Convert.toInt(map.get("total"))).sum();
                Integer first_total = outLs.stream().mapToInt(map -> Convert.toInt(map.get("first_total"))).sum();
                Integer revisit_total = outLs.stream().mapToInt(map -> Convert.toInt(map.get("revisit_total"))).sum();
                Integer emergency_total = outLs.stream().mapToInt(map -> Convert.toInt(map.get("energency_total"))).sum();
                visitReport.setOutpatientTotal(total);
                visitReport.setOutpatientFirstTotal(first_total);
                visitReport.setOutpatientRevisitTotal(revisit_total);
                visitReport.setOutpatientEmergencyTotal(emergency_total);
            }
            // 住院
            List<Map<String, Object>> inLs = list.stream().filter(map -> Convert.toInt(map.get("clinicTypeId")) != null && Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.inpatient.getValue())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(inLs)) {
                Integer total = inLs.stream().mapToInt(map -> Convert.toInt(map.get("total"))).sum();
                visitReport.setInpatientInTotal(total);
            }
            // 其中
            List<Map<String, Object>> inOtherLs = inLs.stream().filter(map -> Convert.toInt(map.get("clinicTypeId")) == null ||
                    (Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.outpatient.getValue())
                            || Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.emergency.getValue())
                            || Convert.toInt(map.get("clinicTypeId")).equals(ClinicType.inpatient.getValue()))).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(inOtherLs)) {
                Integer outTotal = inOtherLs.stream().mapToInt(map -> Convert.toInt(map.get("total"))).sum();
                visitReport.setOutpatientOtherTotal(outTotal);
            }
        }
        // 住院
        wrapper = new GQueryWrapper<VisitEntity>().getWrapper();
        wrapper.eq("v.Org_ID", orgId);
        wrapper.eq("v.Clinic_Type_ID", ClinicType.inpatient.getValue());
        wrapper.eq("v.Visit_Status", VisitStatus.outHospital.getValue());
        Map<String, Object> inMap = baseMapper.findDisVisitStats(wrapper);
        if (inMap != null) {
            visitReport.setInpatientOutTotal(Convert.toInt(inMap.get("total")));
            visitReport.setInpatientZyTotal(Convert.toInt(inMap.get("zy_total")));
            visitReport.setInpatientHzTotal(Convert.toInt(inMap.get("hz_total")));
            visitReport.setInpatientSwTotal(Convert.toInt(inMap.get("death_total")));
            visitReport.setInpatientWyTotal(Convert.toInt(inMap.get("wy_total")));
            visitReport.setInpatientOthTotal(Convert.toInt(inMap.get("other_total")));
        }
        // 处方数
        QueryWrapper<RecipeEntity> recipeWrapper = new GQueryWrapper<RecipeEntity>().getWrapper();
        recipeWrapper.eq("r.Org_ID", orgId);
        recipeWrapper.in("r.Recipe_Type_ID", RecipeType.xy.getValue(), RecipeType.zy.getValue());
        recipeWrapper.isNotNull("re.Time_Signed");
        Map<String, Object> recipeMap = baseMapper.findRecipeStats(recipeWrapper);
        if (recipeMap != null) {
            visitReport.setRecipeTotal(Convert.toInt(recipeMap.get("total")));
            visitReport.setXyTotal(Convert.toInt(recipeMap.get("west_total")));
            visitReport.setOutpatientXyTotal(Convert.toInt(recipeMap.get("out_west_total")));
            visitReport.setZcyTotal(Convert.toInt(recipeMap.get("zcy_total")));
            visitReport.setOutpatientZcyTotal(Convert.toInt(recipeMap.get("out_zcy_total")));
            visitReport.setTcmTotal(Convert.toInt(recipeMap.get("tcm_total")));
            visitReport.setOutpatientTcmTotal(Convert.toInt(recipeMap.get("out_tcm_total")));
            visitReport.setTcmTimes(Convert.toInt(recipeMap.get("tcm_times")));
        }
        return visitReport;
    }
}
