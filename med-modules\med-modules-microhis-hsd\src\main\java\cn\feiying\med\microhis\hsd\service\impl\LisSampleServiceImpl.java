package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.dto.ArtContainerDto;
import cn.feiying.med.hip.mdi.dto.SectionSamplePriceDto;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.his.moe.dto.SectionStockDto;
import cn.feiying.med.his.moe.service.SectionStockService;
import cn.feiying.med.microhis.hsd.dto.RecipeDetailDto;
import cn.feiying.med.microhis.hsd.entity.OrderEntity;
import cn.feiying.med.microhis.hsd.entity.RecipeEntity;
import cn.feiying.med.microhis.hsd.service.LisSampleService;
import cn.feiying.med.microhis.hsd.service.OrderService;
import cn.feiying.med.microhis.hsd.service.RecipeDetailService;
import cn.feiying.med.microhis.hsd.service.RecipeService;
import cn.feiying.med.microhis.hsd.vo.OrderVo;
import cn.feiying.med.microhis.hsd.vo.PendingSamplePriceVo;
import cn.feiying.med.microhis.hsd.vo.RecipeGroupVo;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LisSampleServiceImpl implements LisSampleService {

    @Resource
    private RecipeService recipeService;
    @Resource
    private RecipeDetailService recipeDetailService;
    @Resource
    private SectionItemLabService sectionItemLabService;
    @Resource
    private OrderService orderService;
    @Resource
    private ContainerTypeService containerTypeService;
    @Resource
    private ArtContainerService artContainerService;
    @Resource
    private ArticleService articleService;
    @Resource
    private DeptItemPriceService deptItemPriceService;
    @Resource
    private LaboratoryService laboratoryService;
    @Resource
    private SectionSamplePriceService sectionSamplePriceService;
    @Resource
    private MedicineService medicineService;
    @Resource
    private SectionStockService sectionStockService;
    @Resource
    private OrgDeptService orgDeptService;

    @Override
    public List<PendingSamplePriceVo> getPendingSamplePrice(Long orgId, List<Long> recipeIdLs) {
        List<PendingSamplePriceVo> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(recipeIdLs)) {
            List<RecipeDetailDto> originRecipeDetailLs = recipeDetailService.findLsByRecipeIdLs(recipeIdLs);
            List<OrderEntity> orderLs = orderService.list(new LambdaQueryWrapper<OrderEntity>().in(OrderEntity::getRecipeId, recipeIdLs));
            List<RecipeEntity> recipeLs = recipeService.list(new LambdaQueryWrapper<RecipeEntity>().in(RecipeEntity::getRecipeId, recipeIdLs));
            List<String> deptCodeLs = recipeLs.stream().map(RecipeEntity::getExceDeptcode).distinct().collect(Collectors.toList());
            List<OrgDeptEntity> orgDeptLs;
            if (ObjectUtil.isNotEmpty(deptCodeLs)) {
                orgDeptLs = orgDeptService.list(Wrappers.lambdaQuery(OrgDeptEntity.class).eq(OrgDeptEntity::getOrgId, orgId).in(OrgDeptEntity::getDeptCode, deptCodeLs));
                List<Integer> sectionIdLs = orgDeptLs.stream().map(OrgDeptEntity::getSectionId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
//                if (ObjectUtil.isEmpty(sectionIdLs)) {
//                    throw new SaveFailureException("检验项目执行科室未设置所属病区。");
//                }
            } else {
                throw new SaveFailureException("检验项目未选择执行科室。");
            }
            for (OrgDeptEntity orgDept : orgDeptLs) {
                List<Long> rIdLs = recipeLs.stream().filter(p -> p.getExceDeptcode().equals(orgDept.getDeptCode())).map(RecipeEntity::getRecipeId).distinct().collect(Collectors.toList());
                Integer sectionId = orgDept.getSectionId();
                if (sectionId == null) {
                    continue;
                }
                List<RecipeDetailDto> recipeDetailLs = originRecipeDetailLs.stream().filter(p -> rIdLs.contains(p.getRecipeId())).collect(Collectors.toList());
                List<Integer> cTypeIdLs = recipeDetailLs.stream().map(RecipeDetailDto::getContainerTypeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                List<ContainerTypeEntity> cTypeLs = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(cTypeIdLs)) {
                    cTypeLs = containerTypeService.list(new LambdaQueryWrapper<ContainerTypeEntity>().in(ContainerTypeEntity::getContainerTypeId, cTypeIdLs));
                }
                List<ContainerTypeEntity> finalCTypeLs = cTypeLs;
                recipeDetailLs.forEach(recipeDetail -> {
                    OrderEntity order = orderLs.stream().filter(p -> p.getRecipeId().equals(recipeDetail.getRecipeId())).findFirst().orElse(new OrderEntity());
                    RecipeEntity recipe = recipeLs.stream().filter(p -> p.getRecipeId().equals(recipeDetail.getRecipeId())).findFirst().orElse(new RecipeEntity());
                    ContainerTypeEntity cType = finalCTypeLs.stream().filter(p -> p.getContainerTypeId().equals(recipeDetail.getContainerTypeId())).findFirst().orElse(new ContainerTypeEntity());
                    // todo 待优化
                    if (recipe.getOrgId() != null) {
                        DeptItemPriceEntity deptItemPrice = deptItemPriceService.getOne(new LambdaQueryWrapper<DeptItemPriceEntity>().eq(DeptItemPriceEntity::getOrgId, recipe.getOrgId())
                                .eq(DeptItemPriceEntity::getArtId, recipeDetail.getArtId()).eq(DeptItemPriceEntity::getDeptCode, recipe.getExceDeptcode()));
                        recipeDetail.setLabId(deptItemPrice.getLabId());
                        if (deptItemPrice.getLabId() != null) {
                            LaboratoryEntity lab = laboratoryService.getById(deptItemPrice.getLabId());
                            recipeDetail.setLabCode(lab.getLabCode());
                            recipeDetail.setLabName(lab.getLabName());
                        }
                    }
                    recipeDetail.setOrderId(order.getOrderId());
                    recipeDetail.setVisitId(recipe.getVisitId());
                    recipeDetail.setContainerTypeCode(cType.getContainerTypeCode());
                    recipeDetail.setContainerTypeName(cType.getContainerTypeName());
                    recipeDetail.setContainerTypeChargesRequired(cType.getChargesRequired());
                    recipeDetail.setContainerTypeDisplayOrder(cType.getDisplayOrder());
                    recipeDetail.setContainerTypeMedId(cType.getMedId());
                });

                resetOrderItemSectionLab(sectionId, recipeDetailLs);
                setArtContainerLs(recipeDetailLs);
                List<ContainerTypeEntity> noneMedTypeLs = containerTypeService.list(new LambdaQueryWrapper<ContainerTypeEntity>().eq(ContainerTypeEntity::getChargesRequired, 1)
                        .isNull(ContainerTypeEntity::getMedId));
                List<Integer> specimenTypeIdLs = recipeDetailLs.stream().map(RecipeDetailDto::getSpecimenTypeId).distinct().collect(Collectors.toList());
                String noneLabItems = recipeDetailLs.stream().filter(p -> p.getLabId() == null).map(RecipeDetailDto::getArtName).collect(Collectors.joining(StrUtil.COMMA));
                String noneContainerTypeItems = recipeDetailLs.stream().filter(p -> p.getContainerTypeId() == null).map(RecipeDetailDto::getArtName).collect(Collectors.joining(StrUtil.COMMA));
                if (StrUtil.isNotBlank(noneLabItems)) {
                    throw new SaveFailureException("检验项目未设置实验室：" + noneLabItems);
                }
                if (StrUtil.isNotBlank(noneContainerTypeItems)) {
                    throw new SaveFailureException("检验项目未设置容器类型：" + noneContainerTypeItems);
                }
                List<Integer> containerTypeIdLs = new ArrayList<>();
                recipeDetailLs.forEach(p -> {
                    if (p.getContainerTypeId() != null) {
                        containerTypeIdLs.add(p.getContainerTypeId());
                    }
                    if (ObjectUtil.isNotEmpty(p.getArtContainerLs())) {
                        p.getArtContainerLs().forEach(c -> containerTypeIdLs.add(c.getContainerTypeId()));
                    }
                });
                if (ObjectUtil.isNotEmpty(noneMedTypeLs) && ObjectUtil.isNotEmpty(containerTypeIdLs)) {
                    String noneMedTypes = noneMedTypeLs.stream().filter(p -> containerTypeIdLs.contains(p.getContainerTypeId())).map(ContainerTypeEntity::getContainerTypeName)
                            .collect(Collectors.joining(StrUtil.COMMA));
                    if (StrUtil.isNotEmpty(noneMedTypes)) {
                        throw new SaveFailureException("容器类型：" + noneMedTypes + "未绑定耗材");
                    }
                }
                List<PendingSamplePriceVo.Specimen> visitSpecimenLs = new ArrayList<>();
                Map<Long, Integer> medCountMap = new HashMap<>();
                recipeDetailLs.stream().filter(p -> p.getLabId() != null && p.getContainerTypeId() != null).collect(Collectors.groupingBy(RecipeDetailDto::getLabId)).forEach((labId, labItemLs) -> {
                    Map<Integer, List<RecipeDetailDto>> lcItemLsMap = labItemLs.stream().collect(Collectors.groupingBy(RecipeDetailDto::getContainerTypeId));
                    List<Map.Entry<Integer, List<RecipeDetailDto>>> sortedEntries = new ArrayList<>(lcItemLsMap.entrySet());
                    sortedEntries.sort(Map.Entry.comparingByKey());
                    sortedEntries.forEach(entry -> {
                        List<RecipeDetailDto> lcItemLs = entry.getValue();
                        // 支持混管
                        List<RecipeDetailDto> mixItemLs = lcItemLs.stream().filter(p -> Convert.toInt(p.getIsSeparateContainer(), 0) == 0).sorted(Comparator.comparing(RecipeDetailDto::getOrderId))
                                .sorted(Comparator.comparing(RecipeDetailDto::getSpecimenTypeId).thenComparing(RecipeDetailDto::getArtId)).collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(mixItemLs)) {
                            RecipeDetailDto firstItem = mixItemLs.get(0);
                            addMedIdCount(medCountMap, firstItem.getContainerTypeChargesRequired(), firstItem.getContainerTypeMedId(), 1);
                            List<PendingSamplePriceVo.SpecimenItem> specimenItemLs = mixItemLs.stream().map(p -> BeanUtil.copyProperties(p, PendingSamplePriceVo.SpecimenItem.class)).collect(Collectors.toList());
                            String barCode = Convert.toStr(firstItem.getOrderId());
                            addSampleSpecimen(visitSpecimenLs, firstItem.getVisitId(), labId, firstItem.getLabCode(), firstItem.getLabName(), firstItem.getSpecimenTypeId(), firstItem.getSpecimenTypeName(), firstItem.getContainerTypeId(),
                                    firstItem.getContainerTypeCode(), firstItem.getContainerTypeName(), 0, barCode, specimenItemLs);
                        }
                        // 独立采样
                        List<RecipeDetailDto> separateItemLs = lcItemLs.stream().filter(p -> Convert.toInt(p.getIsSeparateContainer(), 0) == 1).collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(separateItemLs)) {
                            for (RecipeDetailDto child : separateItemLs) {
                                int separateIndex = 0;
                                for (ArtContainerDto artContainer : child.getArtContainerLs()) {
                                    addMedIdCount(medCountMap, artContainer.getChargesRequired(), artContainer.getMedId(), artContainer.getContainerCount());
                                    List<PendingSamplePriceVo.SpecimenItem> specimenItemLs = ListUtil.of(BeanUtil.copyProperties(child, PendingSamplePriceVo.SpecimenItem.class));
                                    for (int containerIndex = 0; containerIndex < Convert.toInt(artContainer.getContainerCount(), 1); containerIndex++) {
                                        String barCode = Convert.toStr(child.getOrderId()) + (separateIndex > 0 ? "_" + separateIndex : "");
                                        addSampleSpecimen(visitSpecimenLs, child.getVisitId(), labId, child.getLabCode(), child.getLabName(), child.getSpecimenTypeId(), child.getSpecimenTypeName(), artContainer.getContainerTypeId(),
                                                artContainer.getContainerTypeCode(), artContainer.getContainerTypeName(), 1, barCode, specimenItemLs);
                                        separateIndex++;
                                    }
                                }
                            }
                        }
                    });
                });
                List<PendingSamplePriceVo.MedCount> medCountLs = new ArrayList<>();
                medCountMap.forEach((medId, count) -> medCountLs.add(PendingSamplePriceVo.MedCount.builder().medId(medId).count(count).build()));

                List<SectionSamplePriceDto> priceLs = getSectionSamplePriceLs(sectionId, specimenTypeIdLs, medCountLs);
                PendingSamplePriceVo vo = new PendingSamplePriceVo();
                vo.setSectionId(sectionId);
                vo.setDeptCode(orgDept.getDeptCode());
                vo.setDeptName(orgDept.getDeptName());
                vo.setSpecimenLs(visitSpecimenLs);
                vo.setPriceLs(priceLs);
                list.add(vo);
            }
        }
        return list;
    }

    private void addSampleSpecimen(List<PendingSamplePriceVo.Specimen> specimenLs, Long visitId, Integer labId, String labCode, String labName, Integer specimenTypeId, String specimenTypeName,
                                   Integer containerTypeId, String containerTypeCode, String containerTypeName, Integer containerIndex, String barCode, List<PendingSamplePriceVo.SpecimenItem> specimenItemLs) {
        PendingSamplePriceVo.SpecimenItem specimenItem = specimenItemLs.get(0);
        PendingSamplePriceVo.Specimen specimen = new PendingSamplePriceVo.Specimen();
        specimen.setKeyStr(StrUtil.format("{}_{}_{}_{}_{}", labId, containerTypeId, specimenItem.getOrderId(), specimenItem.getLineNo(), containerIndex));
        specimen.setVisitId(visitId);
        specimen.setLabId(labId);
        specimen.setLabCode(labCode);
        specimen.setLabName(labName);
        specimen.setSpecimenTypeId(specimenTypeId);
        specimen.setSpecimenTypeName(specimenTypeName);
        specimen.setContainerTypeId(containerTypeId);
        specimen.setContainerTypeCode(containerTypeCode);
        specimen.setContainerTypeName(containerTypeName);
        specimen.setBarCode(barCode);
        specimen.setSpecimenItemLs(specimenItemLs);
        specimenLs.add(specimen);
    }

    private void resetOrderItemSectionLab(Integer sectionId, List<RecipeDetailDto> recipeDetailLs) {
        if (ObjectUtil.isNotEmpty(recipeDetailLs)) {
            List<Long> artIdLs = recipeDetailLs.stream().map(RecipeDetailDto::getArtId).distinct().collect(Collectors.toList());
            List<SectionItemLabEntity> sectionItemLabLs = sectionItemLabService.list(new LambdaQueryWrapper<SectionItemLabEntity>().eq(SectionItemLabEntity::getSectionId, sectionId)
                    .in(SectionItemLabEntity::getArtId, artIdLs));
            for (RecipeDetailDto recipeDetail : recipeDetailLs) {
                SectionItemLabEntity sectionItemLab = sectionItemLabLs.stream().filter(p -> p.getArtId().equals(recipeDetail.getArtId())).findFirst().orElse(null);
                if (sectionItemLab != null) {
                    recipeDetail.setLabId(sectionItemLab.getLabId());
                }
            }
        }
    }

    private void addMedIdCount(Map<Long, Integer> medCount, Integer containerTypeChargesRequired, Long medId, int count) {
        if (Convert.toInt(containerTypeChargesRequired, 0) == 1) {
            medCount.compute(medId, (k, medIdCount) -> Convert.toInt(medIdCount, 0) + count);
        }
    }

    private void setArtContainerLs(List<RecipeDetailDto> recipeDetailLs) {
        if (ObjectUtil.isNotEmpty(recipeDetailLs)) {
            List<Long> artIdLs = recipeDetailLs.stream().filter(p -> Convert.toInt(p.getIsSeparateContainer(), 0) == 1).map(RecipeDetailDto::getArtId).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(artIdLs)) {
                List<ArtContainerDto> artContainerLs = artContainerService.findArtContainerLsByArtIdLs(artIdLs);
                for (RecipeDetailDto orderItem : recipeDetailLs) {
                    if (Convert.toInt(orderItem.getIsSeparateContainer(), 0) == 1) {
                        List<ArtContainerDto> containerLs = artContainerLs.stream().filter(p -> p.getArtId().equals(orderItem.getArtId())).collect(Collectors.toList());
                        if (ObjectUtil.isEmpty(containerLs)) {
                            ArticleEntity article = articleService.getById(orderItem.getArtId());
                            throw new SaveFailureException(article.getArtName() + "设置为独立采样，未设置采样容器");
                        }
                        int separateContainerCount = containerLs.stream().mapToInt(p -> Convert.toInt(p.getContainerCount(), 0)).sum();
                        orderItem.setArtContainerLs(containerLs);
                        orderItem.setSeparateContainerCount(separateContainerCount);
                        ArtContainerDto container = containerLs.stream().min(Comparator.comparing((ArtContainerDto p) -> Convert.toInt(p.getDisplayOrder(), p.getContainerTypeId()))).orElse(new ArtContainerDto());
                        orderItem.setContainerTypeId(container.getContainerTypeId());
                        orderItem.setContainerTypeName(container.getContainerTypeName());
                    }
                }
            }
        }
    }

    private List<SectionSamplePriceDto> getSectionSamplePriceLs(Integer sectionId, List<Integer> specimenTypeIdLs, List<PendingSamplePriceVo.MedCount> medCountLs) {
        List<SectionSamplePriceDto> list = new ArrayList<>();
        if (ObjectUtil.isEmpty(specimenTypeIdLs)) {
            throw new SaveFailureException("请指定样本类型");
        }
        List<Long> medIdLs = medCountLs.stream().map(PendingSamplePriceVo.MedCount::getMedId).distinct().collect(Collectors.toList());
        if (ObjectUtil.isEmpty(medIdLs)) {
            return new ArrayList<>();
        }
        List<ArticleEntity> articleLs = articleService.list(new LambdaQueryWrapper<ArticleEntity>().in(ArticleEntity::getMedId, medIdLs));
        List<SectionSamplePriceDto> sectionSamplePriceLs = sectionSamplePriceService.findLsBySectionIdSpecTypeIdLs(sectionId, specimenTypeIdLs);
        for (PendingSamplePriceVo.MedCount medCount : medCountLs) {
            List<ArticleEntity> medArtLs = articleLs.stream().filter(item -> item.getMedId().equals(medCount.getMedId())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(medArtLs)) {
                BigDecimal reqCells = Convert.toBigDecimal(medCount.getCount(), BigDecimal.ONE);
                if (medArtLs.size() == 1) {
                    ArticleEntity medArt = medArtLs.get(0);
                    addMedCountArt(sectionId, medArt, reqCells, list);
                } else {
                    List<Long> artIdLs = medArtLs.stream().map(ArticleEntity::getArtId).collect(Collectors.toList());
                    List<SectionStockDto> sectionStockLs = sectionStockService.findLsBySectionIdArtIdLs(sectionId, artIdLs);
                    // 扣普通库存批次
                    for (SectionStockDto artSectionStock : sectionStockLs) {
                        ArticleEntity medArt = medArtLs.stream().filter(p -> p.getArtId().equals(artSectionStock.getArtId())).findFirst().orElse(new ArticleEntity());
                        BigDecimal packCells = Convert.toBigDecimal(medArt.getPackCells(), BigDecimal.ONE);
                        // 整包拆零的最大制剂数,允许负库存，最后一条记录totalPacks可能为负,会影响总金额计算
                        BigDecimal maxCells = (Convert.toBigDecimal(artSectionStock.getTotalPacks(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) < 0
                                ? BigDecimal.ZERO : Convert.toBigDecimal(artSectionStock.getTotalPacks(), BigDecimal.ZERO)).multiply(packCells)
                                .add(Convert.toBigDecimal(artSectionStock.getTotalCells(), BigDecimal.ZERO));
                        if (maxCells.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        if (reqCells.compareTo(maxCells) <= 0) {
                            addMedCountArt(sectionId, medArt, reqCells, list);
                            if (reqCells.compareTo(Convert.toBigDecimal(artSectionStock.getTotalCells(), BigDecimal.ZERO)) <= 0) {
                                // 现有拆零能满足需求，不拆整包
                                artSectionStock.setTotalCells(artSectionStock.getTotalCells().subtract(reqCells));
                            } else {
                                // 整包拆零+已有制剂数能满足前题下，拆最少的整包单位
                                // 先用完拆零
                                reqCells = reqCells.subtract(Convert.toBigDecimal(artSectionStock.getTotalCells(), BigDecimal.ZERO));
                                // 需求除包装制剂数向上取整得到待拆包装数
                                BigDecimal packs = reqCells.divide(packCells, 0, RoundingMode.CEILING);
                                // 拆出来的制剂数
                                BigDecimal residuePackCells = packs.multiply(packCells);
                                artSectionStock.setTotalPacks(artSectionStock.getTotalPacks() - packs.intValue());
                                artSectionStock.setTotalCells(residuePackCells.subtract(reqCells));
                            }
                            reqCells = BigDecimal.ZERO;
                            break;
                        } else {
                            addMedCountArt(sectionId, medArt, maxCells, list);
                            reqCells = reqCells.subtract(maxCells);
                            artSectionStock.setTotalPacks(artSectionStock.getTotalPacks() >= 0 ? 0 : artSectionStock.getTotalPacks());
                            artSectionStock.setTotalCells(BigDecimal.ZERO);
                        }
                    }
                    if (reqCells.compareTo(BigDecimal.ZERO) > 0) {
                        ArticleEntity medArt = medArtLs.get(0);
                        addMedCountArt(sectionId, medArt, reqCells, list);
                    }
                }
            } else {
                MedicineEntity med = medicineService.getById(medCount.getMedId());
                throw new SaveFailureException("通用名" + med.getChnName() + "未绑定条目");
            }
        }
        for (SectionSamplePriceDto sectionSamplePrice : sectionSamplePriceLs) {
            if (list.stream().noneMatch(item -> item.getArtId().equals(sectionSamplePrice.getArtId()))) {
                list.add(sectionSamplePrice);
            }
        }
        return list;
    }

    private static void addMedCountArt(Integer sectionId, ArticleEntity medArt, BigDecimal reqCells, List<SectionSamplePriceDto> list) {
        reqCells = Convert.toBigDecimal(reqCells, BigDecimal.ONE);
        SectionSamplePriceDto price = list.stream().filter(item -> item.getArtId().equals(medArt.getArtId())).findFirst().orElse(null);
        if (price == null) {
            price = new SectionSamplePriceDto();
            price.setMiCode(medArt.getMiCode());
            price.setArtCode(medArt.getArtCode());
            price.setArtName(medArt.getArtName());
            price.setArtSpec(medArt.getArtSpec());
            price.setProducer(medArt.getProducer());
            price.setPackUnit(medArt.getPackUnit());
            price.setCellUnit(medArt.getCellUnit());
            price.setDoseUnit(medArt.getDoseUnit());
            price.setPackCells(medArt.getPackCells());
            price.setSectionId(sectionId);
            price.setArtId(medArt.getArtId());
            price.setTotal(reqCells);
            price.setUnitType(UnitType.Cell_Unit.getValue());
            list.add(price);
        } else {
            price.setTotal(price.getTotal().add(reqCells));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> saveSampleRecipe(Long orgId, Long userId, Long visitId, Long clinicianId, String deptCode, List<PendingSamplePriceVo> samplePriceLs) {
        List<Long> orderIdLs = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(samplePriceLs)) {
            List<OrderVo> orderLs = new ArrayList<>();
            for (PendingSamplePriceVo priceVo : samplePriceLs) {
                List<SectionSamplePriceDto> sectionSamplePriceLs = priceVo.getPriceLs();
                if (ObjectUtil.isEmpty(sectionSamplePriceLs)) {
                    throw new SaveFailureException("未找到相关条目");
                }
                // 创建申请单
                OrderVo orderVo = new OrderVo();
                orderVo.setVisitId(visitId);
                orderVo.setOrgId(orgId);
                orderVo.setClinicianId(clinicianId);
                orderVo.setRecipeTypeId(RecipeType.zl.getValue());
                orderVo.setRecipeCatId(RecipeCat.commonRecipe.getValue());
                orderVo.setMedTypeId(MedicalType.normalOutpatient.getValue());
                orderVo.setApplyDeptcode(deptCode);
                orderVo.setExceDeptcode(priceVo.getDeptCode());
                orderVo.setSectionId(priceVo.getSectionId());
                orderVo.setDeliverType(1);
                // 处方组
                List<RecipeGroupVo> recipeGroupLs = new ArrayList<>();
                RecipeGroupVo recipeGroup = new RecipeGroupVo();
                recipeGroup.setGroupNo(1);
                recipeGroup.setOrderTypeId(MtaOrderType.nonSurgicalTreatment.getValue());
                // 处方明细
                List<RecipeDetailDto> recipeDetailLs = new ArrayList<>();
                for (SectionSamplePriceDto sectionSamplePrice : sectionSamplePriceLs) {
                    RecipeDetailDto recipeDetailDto = new RecipeDetailDto();
                    recipeDetailDto.setArtId(sectionSamplePrice.getArtId());
                    recipeDetailDto.setUnitType(sectionSamplePrice.getUnitType());
                    if (sectionSamplePrice.getUnitType().equals(UnitType.Cell_Unit.getValue())) {
                        recipeDetailDto.setUnit(sectionSamplePrice.getCellUnit());
                    } else if (sectionSamplePrice.getUnitType().equals(UnitType.Pack_Unit.getValue())) {
                        recipeDetailDto.setUnit(sectionSamplePrice.getPackUnit());
                    } else if (sectionSamplePrice.getUnitType().equals(UnitType.Dose_Unit.getValue())) {
                        recipeDetailDto.setUnit(sectionSamplePrice.getDoseUnit());
                    }
                    recipeDetailDto.setTotal(sectionSamplePrice.getTotal());
                    recipeDetailLs.add(recipeDetailDto);
                }
                if (ObjectUtil.isNotEmpty(recipeDetailLs)) {
                    recipeGroup.setRecipeDetailLs(recipeDetailLs);
                    recipeGroupLs.add(recipeGroup);
                    orderVo.setRecipeGroupLs(recipeGroupLs);
                    orderLs.add(orderVo);
                }
            }
            if (ObjectUtil.isNotEmpty(orderLs)) {
                List<OrderEntity> saveOrderLs = orderService.saveBatchOrder(userId, orderLs);
                orderIdLs = saveOrderLs.stream().map(OrderEntity::getOrderId).collect(Collectors.toList());
            }
        }
        return orderIdLs;
    }
}
