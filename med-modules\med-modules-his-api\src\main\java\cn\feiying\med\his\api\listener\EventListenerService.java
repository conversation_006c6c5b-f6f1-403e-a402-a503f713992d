package cn.feiying.med.his.api.listener;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.entity.ClinicianEntity;
import cn.feiying.med.hip.mdi.entity.MtBodypartEntity;
import cn.feiying.med.hip.mdi.service.ClinicianService;
import cn.feiying.med.hip.mdi.service.MtBodypartService;
import cn.feiying.med.hip.mpi.entity.*;
import cn.feiying.med.hip.mpi.service.*;
import cn.feiying.med.hip.vo.TreatmentVo;
import cn.feiying.med.his.moe.service.TreatmentService;
import cn.feiying.med.microhis.bcs.dto.BillDetailDto;
import cn.feiying.med.microhis.bcs.entity.BillEntity;
import cn.feiying.med.microhis.bcs.service.BillDetailService;
import cn.feiying.med.microhis.bcs.service.BillService;
import cn.feiying.med.microhis.hsd.dto.*;
import cn.feiying.med.microhis.hsd.entity.*;
import cn.feiying.med.microhis.hsd.service.*;
import cn.feiying.med.microhis.hsd.vo.RecipeEvent;
import cn.feiying.med.saas.api.service.*;
import cn.feiying.med.saas.api.vo.*;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class EventListenerService {

    private final RemoteAcService remoteAcService;
    private final RemoteImService remoteImService;
    private final RemoteMtaService remoteMtaService;
    private final RecipeService recipeService;
    private final OrderService orderService;
    private final VisitExtraService visitExtraService;
    private final PatientService patientService;
    private final MtBodypartService mtBodypartService;
    private final RecipeDetailService recipeDetailService;
    private final VisitService visitService;
    private final ClinicianService clinicianService;
    private final BillService billService;
    private final BillDetailService billDetailService;
    private final TreatmentService treatmentService;

    @EventListener(RecipeEvent.class)
    public void handleRecipeEvent(RecipeEvent recipeEvent) {
        log.info("监听处方事件获取到参数 {}", recipeEvent);
//        if (recipeEvent.getEventType().equals(RecipeEventType.PAY_SUCCESS.getCode())) { // 处方确认
//            remoteSpdService.confirmRecipe(recipeEvent.getOrgId(), recipeEvent.getRxNo());
//            afterRecipe(recipeEvent.getRecipeId(), ImMsgType.recipePaid);
//        } else if (recipeEvent.getEventType().equals(RecipeEventType.CANCEL.getCode())) { // 处方取消
//            remoteSpdService.cancelRecipe(recipeEvent.getOrgId(), recipeEvent.getRxNo());
//        } else if (recipeEvent.getEventType().equals(RecipeEventType.RSC_REJECT.getCode())) { // RSC处方驳回
//            afterRecipe(recipeEvent.getRecipeId(), ImMsgType.RecipeRejected);
//        } else if (recipeEvent.getEventType().equals(RecipeEventType.RSC_CANCEL.getCode())) { // RSC处方取消
//            afterRecipe(recipeEvent.getRecipeId(), ImMsgType.recipeCancel);
//        } else
        if (recipeEvent.getEventType().equals(RecipeEventType.PAY_INSPECTION.getCode())) { // 检查检验处方支付成功
            afterRecipe(recipeEvent.getRecipeId(), ImMsgType.recipePaid);
        }
    }

    private void afterRecipe(Long recipeId, ImMsgType imMsgType) {
        RecipeDto recipe = recipeService.findById(recipeId);
        if (recipe == null) {
            throw new SaveFailureException("处方编号[" + recipeId + "]未匹配到处方");
        }
        if (imMsgType.equals(ImMsgType.recipePaid)) {
            notifyMtaOrder(recipe);
        }
        // 发送消息给医生客户端
        try {
            String msg = "";
            if (imMsgType.getValue() == ImMsgType.RecipeRejected.getValue()) {
                msg = "REJECT-RECIPE_" + recipe.getRecipeId() + "_" + recipe.getRxNo() + "_" + recipe.getNotes() + "_" + recipe.getPatientId() + "_" + recipe.getVisitId() + "_" + recipe.getRecipeTypeId();
            } else if (imMsgType.getValue() == ImMsgType.recipeCancel.getValue()) {
                msg = "CANCEL-RECIPE_" + recipe.getRecipeId() + "_" + recipe.getRxNo() + "_" + recipe.getNotes() + "_" + recipe.getPatientId() + "_" + recipe.getVisitId() + "_" + recipe.getRecipeTypeId();
            } else if (imMsgType.getValue() == ImMsgType.recipePaid.getValue()) {
                msg = "PAID-RECIPE_" + recipe.getRecipeId() + "_" + recipe.getRxNo() + "_" + recipe.getPatientId() + "_" + recipe.getVisitId() + "_" + recipe.getRecipeTypeId();
            }
            this.remoteImService.sendToDoctor(recipe.getUserId(), recipe.getOrgId(), recipe.getClinicianId(), imMsgType, msg);
        } catch (Exception e) {
            log.error("发送消息给医生客户端失败", e);
        }
    }

    private void notifyMtaOrder(RecipeDto recipe) {
        log.debug("notifyMtaOrder recipe:{}", JSONUtil.toJsonStr(recipe));
        // 医技申请单
        if (recipe.getRecipeTypeId().equals(RecipeType.yj.getValue())) {
            boolean psyCtRecipe = false;
            List<RecipeDetailDto> recipeDetailLs = recipe.getRecipeDetailLs();
            if (ObjectUtil.isNotEmpty(recipeDetailLs)) {
                psyCtRecipe = recipeDetailLs.stream().allMatch(p -> p.getCatTypeId() != null && p.getCatTypeId().equals(ArtCatType.PsychologicalCT.getValue()));
            }
            if (psyCtRecipe) {
                List<String> artCodeLs = recipeDetailLs.stream().map(RecipeDetailDto::getArtCode).collect(Collectors.toList());
                Integer measureType = recipeDetailLs.get(0).getMeasureType();
                Integer siteId = null;
                Long acSvcId = null;
                Long clinicianId = remoteAcService.findOrgClinicianId(recipe.getOrgCode(), recipe.getClinicianNo());
                Long clinicianUId = null;
                if (StrUtil.isNotBlank(recipe.getSrcRegId())) {
                    JSONObject acRegObj = remoteAcService.findOcRegById(Convert.toLong(recipe.getSrcRegId()));
                    siteId = acRegObj.getInt("siteId");
                    acSvcId = acRegObj.getLong("svcId");
                    clinicianUId = acRegObj.getLong("checkerUid");
                    if (clinicianId == null) {
                        clinicianId = acRegObj.getLong("clinicianId");
                    }
                }
                List<OrderEntity> orderLs = orderService.list(new LambdaQueryWrapper<OrderEntity>().eq(OrderEntity::getRecipeId, recipe.getRecipeId()));
                OrderEntity order = orderLs.get(0);
                remoteMtaService.savePsyOrder(siteId, recipe.getOrgCode(), measureType, acSvcId, clinicianId, clinicianUId, recipe.getPatientId(), order.getIsSecret(),
                        Convert.toStr(recipe.getVisitId()), Convert.toStr(order.getOrderId()), artCodeLs);
            }
        } else if (recipe.getRecipeTypeId().equals(RecipeType.sys.getValue())) {
            notifyTreatment(recipe.getRecipeId());
        }
        // 推送申请单
        if (recipe.getRecipeTypeId().equals(RecipeType.yj.getValue()) || recipe.getRecipeTypeId().equals(RecipeType.sys.getValue())) {
            orderService.orderSend(Collections.singletonList(recipe.getRecipeId()));
        }
    }

    private void saveMtaOrder(Long recipeId) {
        log.debug("推送医技申请到MTA recipeId:{}", recipeId);
        if (recipeId != null) {
            RecipeDto recipe = recipeService.findById(recipeId);
            boolean psyCtRecipe = recipe.getRecipeDetailLs().stream().allMatch(p -> p.getCatTypeId() != null && p.getCatTypeId().equals(ArtCatType.PsychologicalCT.getValue()));
            if (recipe.getRecipeTypeId().equals(RecipeType.yj.getValue()) && !psyCtRecipe) {
                String age = "";
                if (recipe.getAgeOfYears() != null) {
                    age = recipe.getAgeOfYears() + "Y";
                }
                if (recipe.getAgeOfDays() != null) {
                    age = recipe.getAgeOfDays() + "D";
                }
                OrderEntity order = orderService.getOne(Wrappers.lambdaQuery(OrderEntity.class).eq(OrderEntity::getRecipeId, recipeId));
                //
                MtaOrderData mtaOrder = new MtaOrderData();
                mtaOrder.setHisOrderId(Convert.toStr(order.getOrderId()));
                mtaOrder.setApplyUid(recipe.getUserId());
                mtaOrder.setMedicalHistory(order.getMedicalHistory());
                mtaOrder.setPurposeDesc(order.getPurposeDesc());
                mtaOrder.setOrderTypeId(order.getOrderTypeId());
                mtaOrder.setVisitId(Convert.toStr(recipe.getVisitId()));
                mtaOrder.setClinicType(recipe.getClinicTypeId());
                mtaOrder.setSpecimenTypeId(order.getSpecimenTypeId());
                mtaOrder.setHospitalNo(Convert.toInt(recipe.getOrgCode()));
                mtaOrder.setPatientNo(Convert.toStr(recipe.getPatientId()));
                mtaOrder.setPatientName(recipe.getPatientName());
                mtaOrder.setGender(recipe.getPatientGenderName());
                mtaOrder.setAge(age);
                mtaOrder.setIsSecret(order.getIsSecret());
                mtaOrder.setApplyDeptcode(recipe.getApplyDeptcode());
                mtaOrder.setApplyDeptname(recipe.getApplyDeptname());
                mtaOrder.setExecDeptcode(recipe.getExceDeptcode());
                mtaOrder.setExecDeptname(recipe.getExceDeptname());
                mtaOrder.setApplierCode(recipe.getClinicianNo());
                mtaOrder.setApplierName(recipe.getClinicianName());
                VisitExtraEntity visitExtra = visitExtraService.getById(recipe.getVisitId());
                if (visitExtra!= null) {
                    mtaOrder.setContactTel(visitExtra.getContactTel());
                    mtaOrder.setCertTypeId(visitExtra.getCertTypeId());
                    mtaOrder.setCertIdno(visitExtra.getIdcertNo());
                }
                // 患者信息
                PatientEntity patientEntity = patientService.getById(recipe.getPatientId());
                if (patientEntity!= null) {
                    mtaOrder.setTelNo(patientEntity.getTelNo());
                }
                // 明细
                List<RecipeDetailDto> recipeDetailLs = recipe.getRecipeDetailLs();
                List<MtaOrderDetailData> mtaOrderDetailLs = new ArrayList<>();
                recipeDetailLs.forEach(detail -> {
                    MtaOrderDetailData mtaDetail = new MtaOrderDetailData();
                    mtaDetail.setItemId(detail.getArtId());
                    mtaDetail.setBodypartCount(detail.getBodypartCount());
                    // 部位
                    List<MtaOrderDetailBodypartData> detailBodypartDataLs = new ArrayList<>();
                    if (detail.getRecipeDetailPosLs() != null && !detail.getRecipeDetailPosLs().isEmpty()) {
                        List<Integer> bodypartIdLs = detail.getRecipeDetailPosLs().stream().map(RecipeDetailPosDto::getBodypartId).distinct().collect(Collectors.toList());
                        List<MtBodypartEntity> bodypartLs = mtBodypartService.listByIds(bodypartIdLs);
                        detail.getRecipeDetailPosLs().forEach(pos -> {
                            MtaOrderDetailBodypartData detailBodypartData = new MtaOrderDetailBodypartData();
                            Optional<MtBodypartEntity> bodypart = bodypartLs.stream().filter(p -> p.getBodypartId().equals(pos.getBodypartId())).findFirst();
                            bodypart.ifPresent(p -> {
                                detailBodypartData.setBodypartCode(p.getBodypartCode());
                                detailBodypartData.setBodypartName(p.getBodypartName());
                            });
                            detailBodypartDataLs.add(detailBodypartData);
                        });
                    }
                    mtaDetail.setBodypartLs(detailBodypartDataLs);
                    mtaOrderDetailLs.add(mtaDetail);
                });
                mtaOrder.setOrderDetailLs(mtaOrderDetailLs);
                // 诊断
                List<RecipeDiagDto> recipeDiagLs = recipe.getRecipeDiagLs();
                List<MtaOrderDiagData> mtaOrderDiagLs = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(recipeDiagLs)) {
                    recipeDiagLs.forEach(diag -> {
                        MtaOrderDiagData mtaDiag = new MtaOrderDiagData();
                        mtaDiag.setDiagCode(diag.getDiagCode());
                        mtaDiag.setDiagName(diag.getDiagName());
                        mtaOrderDiagLs.add(mtaDiag);
                    });
                }
                remoteMtaService.saveMtaOrder(mtaOrder, mtaOrderDiagLs);
            }
        }
    }

    /**
     * 发送到MOE处置
     */
    private void notifyTreatment(Long recipeId) {
        log.info("notifyTreatment recipeId:{}", recipeId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (recipeId == null) {
            throw new SaveFailureException("请指定处方编号");
        }
        List<TreatmentVo> treatmentLs = new ArrayList<>();
        List<OrderEntity> orderLs = new ArrayList<>();
        List<Integer> mtaRecipeTypeLs = ListUtil.of(RecipeType.sys.getValue());
        RecipeEntity recipe = recipeService.getById(recipeId);
        if (!recipe.getPaidStatus().equals(PaidStatus.paid.getValue())) {
            throw new SaveFailureException("处方：" + recipe.getRxNo() + "未处于支付完结状态");
        }
        List<RecipeDetailDto> detailLs = recipeDetailService.findLsByRecipeIdLs(ListUtil.of(recipeId));
        VisitEntity visit = visitService.getById(recipe.getVisitId());
        if (visit == null) {
            throw new SaveFailureException("处方：" + recipe.getRxNo() + "未关联就诊信息");
        }
        if (mtaRecipeTypeLs.contains(recipe.getRecipeTypeId())) {
            orderLs = orderService.list(new LambdaQueryWrapper<OrderEntity>().eq(OrderEntity::getRecipeId, recipe.getRecipeId()));
        }
        Long clinicianId = recipe.getClinicianId();
        if (clinicianId == null) {
            clinicianId = visit.getClinicianId();
        }
        OrderEntity order = orderLs.stream().filter(p -> p.getRecipeId().equals(recipe.getRecipeId())).findFirst().orElse(null);
        if (mtaRecipeTypeLs.contains(recipe.getRecipeTypeId()) && order == null) {
            throw new SaveFailureException("医技处方：" + recipe.getRxNo() + "未关联申请单");
        }
        ClinicianEntity clinician = clinicianService.getById(clinicianId);
        if (order != null && clinician == null) {
            throw new SaveFailureException("处方：" + recipe.getRxNo() + "未关联医生信息");
        }
        List<BillEntity> billLs = billService.list(new LambdaQueryWrapper<BillEntity>().eq(BillEntity::getRecipeId, recipe.getRecipeId()));
        BillEntity treatmentBill = billLs.stream().filter(p -> p.getBillTypeId().equals(BillType.treatmentBill.getValue()) && p.getRecipeId().equals(recipeId)).findFirst().orElse(null);
        List<TreatmentVo.TreatmentDetail> treatmentDetailLs = new ArrayList<>();
        for (RecipeDetailDto recipeDetail : detailLs) {
            if (order != null || (recipeDetail.getFeedMethod() != null && recipeDetail.getFeedMethod().equals(FeedMethod.injection.getValue()))) {
                TreatmentVo.TreatmentDetail treatmentDetail = new TreatmentVo.TreatmentDetail();
                treatmentDetail.setRecipeLineNo(recipeDetail.getLineNo());
                treatmentDetail.setArtId(recipeDetail.getArtId());
                treatmentDetail.setRouteId(recipeDetail.getRouteId());
                treatmentDetail.setFreqCode(recipeDetail.getFreqCode());
                treatmentDetail.setTotal(recipeDetail.getTotal());
                treatmentDetail.setUnit(recipeDetail.getUnit());
                treatmentDetail.setUnitType(recipeDetail.getUnitType());
                treatmentDetail.setStRequired(recipeDetail.getStRequired());
                treatmentDetail.setGroupNo(recipeDetail.getGroupNo());
                treatmentDetail.setGroupMark(recipeDetail.getGroupMark());
                treatmentDetail.setSpecimenTypeId(recipeDetail.getSpecimenTypeId());
                treatmentDetail.setBodypartDesc(recipeDetail.getBodypartDesc());
                treatmentDetail.setBodypartCount(recipeDetail.getBodypartCount());
                treatmentDetail.setLabId(order == null ? null : order.getLabId());
                treatmentDetail.setDpm(recipeDetail.getDpm());
                treatmentDetail.setNotice(recipeDetail.getNotice());
                treatmentDetail.setMealCells(recipeDetail.getMealCells());
                treatmentDetail.setMealDoses(recipeDetail.getMealDoses());
                treatmentDetail.setPaidCycles(recipeDetail.getPaidCycles());
                treatmentDetail.setHeadingTimes(recipeDetail.getHeadingTimes());
                treatmentDetail.setFeedMethod(recipeDetail.getFeedMethod());
                treatmentDetail.setFreqCycleBeats(recipeDetail.getFreqCycleBeats());
                treatmentDetail.setFreqCycleSeconds(recipeDetail.getFreqCycleSeconds());
                treatmentDetailLs.add(treatmentDetail);
            }
        }
        if (ObjectUtil.isNotEmpty(treatmentDetailLs)) {
            TreatmentVo treatment = new TreatmentVo();
            treatment.setRecipeId(recipe.getRecipeId());
            treatment.setRecipeTypeId(recipe.getRecipeTypeId());
            treatment.setOrgId(recipe.getOrgId());
            treatment.setVisitId(recipe.getVisitId());
            treatment.setApplyDeptcode(recipe.getApplyDeptcode());
            treatment.setExceDeptcode(recipe.getExceDeptcode());
            treatment.setTimeApplied(recipe.getTimeSubmitted());
            treatment.setSectionId(recipe.getSectionId());
            treatment.setClinicTypeId(visit.getClinicTypeId());
            treatment.setPatientName(visit.getPatientName());
            treatment.setAgeOfYears(visit.getAgeOfYears());
            treatment.setAgeOfDays(Convert.toInt(visit.getAgeOfDays()));
            treatment.setPatientId(visit.getPatientId());
            treatment.setGenderId(visit.getGenderId());
            if (order != null) {
                treatment.setOrderId(order.getOrderId());
                treatment.setOrderTypeId(order.getOrderTypeId());
                treatment.setPurposeDesc(order.getPurposeDesc());
                treatment.setMedicalHistory(order.getMedicalHistory());
            }
            treatment.setClinicianCode(clinician.getClinicianNo());
            treatment.setClinicianName(clinician.getClinicianName());
            treatment.setAccessionNo(recipe.getRxNo());
            treatment.setRecipeDate(Convert.toInt(sdf.format(recipe.getTimeCreated())));
            treatment.setSubjectId(recipe.getSubjectId());
            treatment.setTimes(recipe.getTimes());
            treatment.setPaidStatus(PaidStatus.paid.getValue());
            treatment.setBseqid(treatmentBill == null ? null : treatmentBill.getBseqid());
            treatment.setDetailLs(treatmentDetailLs);
            setConsumable(treatmentBill, treatment);
            treatmentLs.add(treatment);
            treatmentService.notifyTreatment(treatmentLs);
        }
    }

    public static void main(String[] args) {
        BigDecimal total = new BigDecimal("2.3");
        System.out.println(Convert.toInt(total));
    }

    private void setConsumable(BillEntity treatmentBill, TreatmentVo treatment) {
        if (treatmentBill != null) {
            List<TreatmentVo.TreatmentConsumable> consumableLs = new ArrayList<>();
            Map<String, Object> params = new HashMap<>(2);
            params.put("bSeqId", treatmentBill.getBseqid());
            params.put("stockReq", 1);
            //获取耗材
            List<BillDetailDto> stockArtLs = billDetailService.findLsByParams(params);
            if (ObjectUtil.isNotEmpty(stockArtLs)) {
                for (BillDetailDto stockArt : stockArtLs) {
                    TreatmentVo.TreatmentConsumable consumable = new TreatmentVo.TreatmentConsumable();
                    consumable.setArtId(stockArt.getArtId());
                    consumable.setArtName(stockArt.getArtName());
                    consumable.setArtSpec(stockArt.getArtSpec());
                    consumable.setPackCells(stockArt.getPackCells());
                    consumable.setPackTotal(stockArt.getPackTotal());
                    consumable.setCellTotal(stockArt.getCellTotal());
                    consumableLs.add(consumable);
                }
                treatment.setConsumableLs(consumableLs);
            }
        }
    }
}
