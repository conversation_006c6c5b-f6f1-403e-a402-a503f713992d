package cn.feiying.med.microhis.bcs.manager;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.hip.enums.*;
import cn.feiying.med.hip.mdi.dto.PackageDetailDto;
import cn.feiying.med.hip.mdi.entity.*;
import cn.feiying.med.hip.mdi.service.*;
import cn.feiying.med.hip.model.clinicsWm.WmBillDetailModel;
import cn.feiying.med.hip.model.clinicsWm.WmReqDetailModel;
import cn.feiying.med.hip.model.clinicsWm.WmReqModel;
import cn.feiying.med.hip.mpi.entity.PatientEntity;
import cn.feiying.med.hip.mpi.service.PatientService;
import cn.feiying.med.hip.vo.TreatmentVo;
import cn.feiying.med.his.moe.entity.MoeOrderEntity;
import cn.feiying.med.his.moe.service.MoeOrderService;
import cn.feiying.med.his.moe.service.TreatmentService;
import cn.feiying.med.his.moe.vo.OrderDetailVo;
import cn.feiying.med.his.moe.vo.OrderExecVo;
import cn.feiying.med.mcisp.entity.TransEntity;
import cn.feiying.med.mcisp.service.TransService;
import cn.feiying.med.microhis.bcs.dto.BeforePayIn;
import cn.feiying.med.microhis.bcs.dto.BillDetailDto;
import cn.feiying.med.microhis.bcs.entity.BillDetailEntity;
import cn.feiying.med.microhis.bcs.entity.BillEntity;
import cn.feiying.med.microhis.bcs.entity.CashEntity;
import cn.feiying.med.microhis.bcs.event.PayRefundFinishEvent;
import cn.feiying.med.microhis.bcs.service.BillDetailService;
import cn.feiying.med.microhis.bcs.service.BillService;
import cn.feiying.med.microhis.bcs.service.CashService;
import cn.feiying.med.microhis.bcs.service.OutPatientLisService;
import cn.feiying.med.microhis.hsd.dto.*;
import cn.feiying.med.microhis.hsd.entity.OrderEntity;
import cn.feiying.med.microhis.hsd.entity.RecipeEntity;
import cn.feiying.med.microhis.hsd.entity.VisitEntity;
import cn.feiying.med.microhis.hsd.entity.VisitExtraEntity;
import cn.feiying.med.microhis.hsd.service.*;
import cn.feiying.med.microhis.hsd.vo.RecipeEvent;
import cn.feiying.med.saas.api.service.*;
import cn.feiying.med.saas.api.vo.MtaOrderData;
import cn.feiying.med.saas.api.vo.MtaOrderDetailBodypartData;
import cn.feiying.med.saas.api.vo.MtaOrderDetailData;
import cn.feiying.med.saas.api.vo.MtaOrderDiagData;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class OutpatientManager implements PayInterceptor {
    private final PayManager payManager;
    private final RecipeService recipeService;
    private final BillService billService;
    private final BillDetailService billDetailService;
    private final RemoteSpdService remoteSpdService;
    private final RemoteImService remoteImService;
    private final RemoteAcService remoteAcService;
    private final RemoteMtaService remoteMtaService;
    private final RemoteClinicsWmService remoteClinicsWmService;
    private final OrderService orderService;
    private final VisitExtraService visitExtraService;
    private final PatientService patientService;
    private final MtBodypartService mtBodypartService;
    private final RecipeDetailService recipeDetailService;
    private final VisitService visitService;
    private final ClinicianService clinicianService;
    private final TreatmentService treatmentService;
    private final ArticleService articleService;
    private final OrgNonmedicalArtService orgNonmedicalArtService;
    private final OrgSettingService orgSettingService;
    private final OutPatientLisService outPatientLisService;
    private final MoeOrderService moeOrderService;
    private final HsdBaseService hsdBaseService;
    private final BillManager billManager;
    private final RemoteHsdService remoteHsdService;
    private final CashService cashService;
    private final RemoteInpatientHsdService remoteInpatientHsdService;
    private final TransService transService;

    @PostConstruct
    public void init() {
        payManager.addInterceptor(CashType.outpatient, this);
        payManager.addInterceptor(CashType.reg, this);
    }

    @Override
    public void beforePay(BeforePayIn in) {
        in.setNeedUserCode(true);
        Assert.notEmpty(in.getBillIds(), () -> new SaveFailureException("划价单不能为空"));
    }

    @Override
    public void afterPayFinish(PayFinishIn in) {
        CashEntity cash = cashService.get(in.cashId);
        if (cash.getCashTypeId() != CashType.outpatient.getValue()) {
            return;
        }
        TransEntity trans = transService.getFinishedBlueByCashId(cash.getCashId());
        if (trans == null) {
            return;
        }
        Long visitId = cash.getVisitId();
        try {
            remoteInpatientHsdService.visitTrans(visitId, trans.getFundPayAmt(), trans.getPatientPartAmt(), trans.getAcctPayAmt(), trans.getCashPayAmt(), trans.getMultiAidAmt());
        } catch (Exception e) {
            log.error("更新visitTrans异常", e);
        }
    }

    @Override
    public void afterPayCancel(PayCancelIn in) {
        BillEntity bill = billService.getFirstRegByCashId(in.getCashId());
        if (bill != null) {
            remoteHsdService.rejectedReg(bill.getRegId(), in.getNotes());
        }
    }

    @EventListener(PayRefundFinishEvent.JsCodePay.class)
    @Transactional(rollbackFor = Exception.class)
    public void appCallback(PayRefundFinishEvent.JsCodePay event) {
        if (event.succeed) {
            payManager.payFinish(event.cash.getCashId());
        }
    }

    @EventListener(RecipeEvent.class)
    public void handleRecipeEvent(RecipeEvent recipeEvent) {
        log.info("监听处方事件获取到参数 {}", recipeEvent);
        if (recipeEvent.getEventType().equals(RecipeEventType.PAY_SUCCESS.getCode())) { // 处方确认
            afterRecipe(recipeEvent.getRecipeIdLs(), ImMsgType.recipePaid);
        } else if (recipeEvent.getEventType().equals(RecipeEventType.CANCEL.getCode())) { // 处方取消
            RecipeDto recipe = recipeService.findById(recipeEvent.getRecipeId());
            recipePaidOrCancel(recipe, false, true);
        } else if (recipeEvent.getEventType().equals(RecipeEventType.PAY_INSPECTION.getCode())) { // 检查检验支付成功
            afterRecipe(recipeEvent.getRecipeIdLs(), ImMsgType.recipePaid);
        } else if (recipeEvent.getEventType().equals(RecipeEventType.PAY_DISPOSAL.getCode())) { // 处置费用支付成功
            notifyTreatment(recipeEvent.getRecipeIdLs());
        } else if (recipeEvent.getEventType().equals(RecipeEventType.REVOKE.getCode())) { // 处方撤销
            recipeWithdraw(recipeEvent.getRecipeId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRecipeTimes(Long bseqid, Integer times) {
        log.debug("更新处方付数，bseqid:{},times:{}", bseqid, times);
        if (times == null || times <= 0) {
            throw new SaveFailureException("请输入正确的付数。");
        }
        BillEntity bill = billService.getById(bseqid);
        if (bill == null) {
            throw new SaveFailureException("未找到该划价单信息。");
        }
        if (bill.getPaidStatus().equals(PaidStatus.paid.getValue()) || bill.getPaidStatus().equals(PaidStatus.refund.getValue())
                || bill.getPaidStatus().equals(PaidStatus.cancel.getValue())) {
            throw new SaveFailureException("该划价单是" + PaidStatus.getName(bill.getPaidStatus()) + "状态，不能修改。");
        }
        if (bill.getRecipeId() != null) {
            RecipeDto recipe = recipeService.findById(bill.getRecipeId());
            if (recipe != null && recipe.getRecipeTypeId().equals(RecipeType.zy.getValue())
                    && recipe.getDeliverType() != null
                    && recipe.getDeliverType().equals(DeliverType.self.getValue())
                    && !bill.getTimes().equals(times)) {
                // 先调取消
                //remoteClinicsWmService.recipeCancel(recipe.getRecipeId());
                // 修改库房预留预留
                selfWarehouseRecipe(bseqid, bill.getTimes(), times, recipe);
                // 修改划价单
                billService.updateTimes(bseqid, times);
            }
        }
    }

    private void afterRecipe(List<Long> recipeIdLs, ImMsgType imMsgType) {
        List<RecipeDto> recipeLs = recipeService.findLsByIdLs(recipeIdLs);
        if (ObjectUtil.isEmpty(recipeLs)) {
            throw new SaveFailureException("处方编号[" + recipeIdLs.stream().map(String::valueOf).collect(Collectors.joining(",")) + "]未匹配到处方");
        }
        for (Long recipeId : recipeIdLs) {
            if (!recipeLs.stream().map(RecipeDto::getRecipeId).collect(Collectors.toList()).contains(recipeId)) {
                throw new SaveFailureException("处方编号[" + recipeId + "]未匹配到处方");
            }
        }
        // 药品处方
        for (RecipeDto recipe : recipeLs) {
            // 支付成功，到SPD或自有库房确认处方
            if (imMsgType.equals(ImMsgType.recipePaid)
                    && (recipe.getRecipeTypeId().equals(RecipeType.xy.getValue())
                    || recipe.getRecipeTypeId().equals(RecipeType.zy.getValue())
                    || recipe.getRecipeTypeId().equals(RecipeType.zl.getValue()))) {
                recipePaidOrCancel(recipe, true, false);
            }
            // 发送消息给医生客户端
            try {
                String msg = getMsg(imMsgType, recipe);
                this.remoteImService.sendToDoctor(recipe.getUserId(), recipe.getOrgId(), recipe.getClinicianId(), imMsgType, msg);
            } catch (Exception e) {
                log.error("发送消息给医生客户端失败", e);
            }
        }
        // 检验检查
        if (imMsgType.equals(ImMsgType.recipePaid)) {
            notifyMtaOrder(recipeLs);
            OrgSettingEntity orgSetting = orgSettingService.getById(recipeLs.get(0).getOrgId());
            // consumableAppendToCure = 1,绑定耗材合并到治疗单时没有处置划价单 不会触发RecipeEventType.PAY_DISPOSAL处方划价单支付产生的MOE处置记录
            if (orgSetting != null && Convert.toInt(orgSetting.getConsumableAppendToCure(), 0) == 1) {
                List<RecipeDetailDto> recipeDetailLs = recipeDetailService.findLsByRecipeIdLs(recipeIdLs);
                List<Long> treatmentRecipeIdLs = new ArrayList<>();
                for (Long recipeId : recipeIdLs) {
                    List<RecipeDetailDto> detailLs = recipeDetailLs.stream().filter(p -> p.getRecipeId().equals(recipeId)).collect(Collectors.toList());
                    boolean hasInjection = detailLs.stream().anyMatch(p -> p.getFeedMethod() != null && p.getFeedMethod().equals(FeedMethod.injection.getValue()));
                    boolean stRequired = detailLs.stream().anyMatch(p -> Convert.toInt(p.getStRequired(), 0) == 1);
                    if (stRequired || hasInjection) {
                        treatmentRecipeIdLs.add(recipeId);
                    }
                }
                if (ObjectUtil.isNotEmpty(treatmentRecipeIdLs)) {
                    notifyTreatment(treatmentRecipeIdLs);
                }
            }
        }
    }

    private @NotNull String getMsg(ImMsgType imMsgType, RecipeDto recipe) {
        String msg = "";
        if (imMsgType.getValue() == ImMsgType.RecipeRejected.getValue()) {
            msg = "REJECT-RECIPE_" + recipe.getRecipeId() + "_" + recipe.getRxNo() + "_" + recipe.getNotes() + "_" + recipe.getPatientId() + "_" + recipe.getVisitId() + "_" + recipe.getRecipeTypeId();
        } else if (imMsgType.getValue() == ImMsgType.recipeCancel.getValue()) {
            msg = "CANCEL-RECIPE_" + recipe.getRecipeId() + "_" + recipe.getRxNo() + "_" + recipe.getNotes() + "_" + recipe.getPatientId() + "_" + recipe.getVisitId() + "_" + recipe.getRecipeTypeId();
        } else if (imMsgType.getValue() == ImMsgType.recipePaid.getValue() || imMsgType.getValue() == ImMsgType.RecipePass.getValue()) {
            msg = "PAID-RECIPE_" + recipe.getRecipeId() + "_" + recipe.getRxNo() + "_" + recipe.getPatientId() + "_" + recipe.getVisitId() + "_" + recipe.getRecipeTypeId();
        }
        return msg;
    }

    private void recipePaidOrCancel(RecipeDto recipe, boolean paidBool, boolean cancelBool) {
        // 如果是治疗处方，判断是否库存管理
        if (recipe.getRecipeTypeId().equals(RecipeType.zl.getValue())) {
            List<RecipeDetailDto> recipeDetailLs = recipeDetailService.findLsByRecipeIdLs(ListUtil.of(recipe.getRecipeId()));
            List<Long> artIdLs = new ArrayList<>();
            recipeDetailLs.forEach(recipeDetail -> {
                if (recipeDetail.getIsPackage() != null && recipeDetail.getIsPackage().equals(1)) {
                    artIdLs.addAll(recipeDetail.getPackageDetailLs().stream().map(PackageDetailDto::getArtId).collect(Collectors.toList()));
                } else {
                    artIdLs.add(recipeDetail.getArtId());
                }
            });
            boolean sectionWmMode = Convert.toInt(recipe.getExceWmmode(), DeptWmMode.WAREHOUSE_MANAGEMENT.getCode()).equals(DeptWmMode.WAREHOUSE_MANAGEMENT.getCode());
            if (ObjectUtil.isNotEmpty(artIdLs)) {
                List<ArticleEntity> articleLs = articleService.listByIds(artIdLs);
                List<Long> stockLs = articleLs.stream().filter(article -> article.getStockReq() != null && article.getStockReq().equals(1)).map(ArticleEntity::getArtId).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(stockLs) && sectionWmMode) {
                    if (paidBool) {
                        confirmRecipe(recipe);
                    } else if (cancelBool) {
                        cancelRecipe(recipe);
                    }
                }
            }
        } else {
            if (paidBool) {
                confirmRecipe(recipe);
            } else if (cancelBool) {
                cancelRecipe(recipe);
            }
        }
    }

    private void recipeWithdraw(Long recipeId) {
        RecipeDto recipe = recipeService.findById(recipeId);
        if (recipe == null) {
            throw new SaveFailureException("处方编号[" + recipeId + "]未匹配到处方");
        }
        // 处方撤销
        if (recipe.getRecipeTypeId().equals(RecipeType.xy.getValue()) || recipe.getRecipeTypeId().equals(RecipeType.zy.getValue())) {
            returnRecipe(recipe);
        } else if (recipe.getRecipeTypeId().equals(RecipeType.zl.getValue())) {
            List<RecipeDetailDto> recipeDetailLs = recipeDetailService.findLsByRecipeIdLs(ListUtil.of(recipe.getRecipeId()));
            List<Long> artIdLs = new ArrayList<>();
            recipeDetailLs.forEach(recipeDetail -> {
                if (recipeDetail.getIsPackage() != null && recipeDetail.getIsPackage().equals(1)) {
                    artIdLs.addAll(recipeDetail.getPackageDetailLs().stream().map(PackageDetailDto::getArtId).collect(Collectors.toList()));
                } else {
                    artIdLs.add(recipeDetail.getArtId());
                }
            });
            boolean sectionWmMode = Convert.toInt(recipe.getExceWmmode(), DeptWmMode.WAREHOUSE_MANAGEMENT.getCode()).equals(DeptWmMode.WAREHOUSE_MANAGEMENT.getCode());
            if (ObjectUtil.isNotEmpty(artIdLs)) {
                List<ArticleEntity> articleLs = articleService.listByIds(artIdLs);
                List<Long> stockLs = articleLs.stream().filter(article -> article.getStockReq() != null && article.getStockReq().equals(1)).map(ArticleEntity::getArtId).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(stockLs) && sectionWmMode) {
                    returnRecipe(recipe);
                }
            }
        }
    }

    private void confirmRecipe(RecipeDto recipe) {
        if (recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.spd.getValue())) {
            remoteSpdService.confirmRecipe(recipe.getOrgId(), recipe.getRxNo());
        } else if (recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.self.getValue())) {
            remoteClinicsWmService.recipeConfirm(recipe.getRecipeId());
        }
    }

    private void cancelRecipe(RecipeDto recipe) {
        if (recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.spd.getValue())) {
            remoteSpdService.cancelRecipe(recipe.getOrgId(), recipe.getRxNo());
        } else if (recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.self.getValue())) {
            remoteClinicsWmService.recipeCancel(recipe.getRecipeId());
        }
    }

    private void returnRecipe(RecipeDto recipe) {
        if (recipe.getDeliverType() != null && recipe.getDeliverType().equals(DeliverType.self.getValue())) {
            remoteClinicsWmService.recipeConfirmReturn(recipe.getRecipeId());
        }
    }

    private void notifyMtaOrder(List<RecipeDto> recipeLs) {
        log.debug("notifyMtaOrder recipe:{}", JSONUtil.toJsonStr(recipeLs));
        // 医技申请单
        List<RecipeDto> execRecipeLs = new ArrayList<>();
        List<Long> orderIdLs = new ArrayList<>();
        for (RecipeDto recipe : recipeLs) {
            List<OrderEntity> orderLs = orderService.list(new LambdaQueryWrapper<OrderEntity>().eq(OrderEntity::getRecipeId, recipe.getRecipeId()));
            if (ObjectUtil.isNotEmpty(orderLs)) {
                OrderEntity order = orderLs.get(0);
                long moeOrderCount = moeOrderService.count(Wrappers.lambdaQuery(MoeOrderEntity.class).eq(MoeOrderEntity::getOrderId, order.getOrderId()));
                if (moeOrderCount == 0) {
                    if (recipe.getRecipeTypeId().equals(RecipeType.yj.getValue())) {
                        boolean psyCtRecipe = false;
                        List<RecipeDetailDto> recipeDetailLs = recipe.getRecipeDetailLs();
                        if (ObjectUtil.isNotEmpty(recipeDetailLs)) {
                            psyCtRecipe = recipeDetailLs.stream().allMatch(p -> p.getCatTypeId() != null && p.getCatTypeId().equals(ArtCatType.PsychologicalCT.getValue()));
                        }
                        if (psyCtRecipe) {
                            List<String> artCodeLs = recipeDetailLs.stream().map(RecipeDetailDto::getArtCode).collect(Collectors.toList());
                            Integer measureType = recipeDetailLs.get(0).getMeasureType();
                            Integer siteId = null;
                            Long acSvcId = null;
                            Long clinicianId = remoteAcService.findOrgClinicianId(recipe.getOrgCode(), recipe.getClinicianNo());
                            Long clinicianUId = null;
                            if (StrUtil.isNotBlank(recipe.getSrcRegId())) {
                                JSONObject acRegObj = remoteAcService.findOcRegById(Convert.toLong(recipe.getSrcRegId()));
                                siteId = acRegObj.getInt("siteId");
                                acSvcId = acRegObj.getLong("svcId");
                                clinicianUId = acRegObj.getLong("checkerUid");
                                if (clinicianId == null) {
                                    clinicianId = acRegObj.getLong("clinicianId");
                                }
                            }
                            remoteMtaService.savePsyOrder(siteId, recipe.getOrgCode(), measureType, acSvcId, clinicianId, clinicianUId, recipe.getPatientId(), order.getIsSecret(), Convert.toStr(recipe.getVisitId()), Convert.toStr(order.getOrderId()), artCodeLs);
                        } else {
                            execRecipeLs.add(recipe);
                        }
                    } else if (recipe.getRecipeTypeId().equals(RecipeType.sys.getValue())) {
                        execRecipeLs.add(recipe);
                        orderIdLs.add(order.getOrderId());
                    } else if (recipe.getRecipeTypeId().equals(RecipeType.zl.getValue())) {
                        execRecipeLs.add(recipe);
                    }
                }
            }
        }
        if (ObjectUtil.isNotEmpty(execRecipeLs)) {
            saveOrderExec(execRecipeLs);
            // 检查
            List<RecipeDto> inspectionRecipeLs = execRecipeLs.stream().filter(p -> p.getRecipeTypeId().equals(RecipeType.yj.getValue())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(inspectionRecipeLs)) {
                List<Long> inspectionRecipeIdLs = inspectionRecipeLs.stream().map(RecipeDto::getRecipeId).distinct().collect(Collectors.toList());
                // 推送申请单
                orderService.orderSend(inspectionRecipeIdLs);
            }
            // 检验
            List<RecipeDto> clinicalRecipeLs = execRecipeLs.stream().filter(p -> p.getRecipeTypeId().equals(RecipeType.sys.getValue())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(clinicalRecipeLs)) {
                List<Long> clinicalRecipeIdLs = clinicalRecipeLs.stream().map(RecipeDto::getRecipeId).distinct().collect(Collectors.toList());
                notifyTreatment(clinicalRecipeIdLs);
            }
        }
        // 上报云Lis
        if (ObjectUtil.isNotEmpty(orderIdLs)) {
            outPatientLisService.uploadLisOrder(recipeLs.get(0).getOrgId(), orderIdLs);
        }
    }

    private void saveMtaOrder(Long recipeId) {
        log.debug("推送医技申请到MTA recipeId:{}", recipeId);
        if (recipeId != null) {
            RecipeDto recipe = recipeService.findById(recipeId);
            boolean psyCtRecipe = recipe.getRecipeDetailLs().stream().allMatch(p -> p.getCatTypeId() != null && p.getCatTypeId().equals(ArtCatType.PsychologicalCT.getValue()));
            if (recipe.getRecipeTypeId().equals(RecipeType.yj.getValue()) && !psyCtRecipe) {
                String age = "";
                if (recipe.getAgeOfYears() != null) {
                    age = recipe.getAgeOfYears() + "Y";
                }
                if (recipe.getAgeOfDays() != null) {
                    age = recipe.getAgeOfDays() + "D";
                }
                OrderEntity order = orderService.getOne(Wrappers.lambdaQuery(OrderEntity.class).eq(OrderEntity::getRecipeId, recipeId));
                //
                MtaOrderData mtaOrder = new MtaOrderData();
                mtaOrder.setHisOrderId(Convert.toStr(order.getOrderId()));
                mtaOrder.setApplyUid(recipe.getUserId());
                mtaOrder.setMedicalHistory(order.getMedicalHistory());
                mtaOrder.setPurposeDesc(order.getPurposeDesc());
                mtaOrder.setOrderTypeId(order.getOrderTypeId());
                mtaOrder.setVisitId(Convert.toStr(recipe.getVisitId()));
                mtaOrder.setClinicType(recipe.getClinicTypeId());
                mtaOrder.setSpecimenTypeId(order.getSpecimenTypeId());
                mtaOrder.setHospitalNo(Convert.toInt(recipe.getOrgCode()));
                mtaOrder.setPatientNo(Convert.toStr(recipe.getPatientId()));
                mtaOrder.setPatientName(recipe.getPatientName());
                mtaOrder.setGender(recipe.getPatientGenderName());
                mtaOrder.setAge(age);
                mtaOrder.setIsSecret(order.getIsSecret());
                mtaOrder.setApplyDeptcode(recipe.getApplyDeptcode());
                mtaOrder.setApplyDeptname(recipe.getApplyDeptname());
                mtaOrder.setExecDeptcode(recipe.getExceDeptcode());
                mtaOrder.setExecDeptname(recipe.getExceDeptname());
                mtaOrder.setApplierCode(recipe.getClinicianNo());
                mtaOrder.setApplierName(recipe.getClinicianName());
                VisitExtraEntity visitExtra = visitExtraService.getById(recipe.getVisitId());
                if (visitExtra != null) {
                    mtaOrder.setContactTel(visitExtra.getContactTel());
                    mtaOrder.setCertTypeId(visitExtra.getCertTypeId());
                    mtaOrder.setCertIdno(visitExtra.getIdcertNo());
                }
                // 患者信息
                PatientEntity patientEntity = patientService.getById(recipe.getPatientId());
                if (patientEntity != null) {
                    mtaOrder.setTelNo(patientEntity.getTelNo());
                }
                // 明细
                List<RecipeDetailDto> recipeDetailLs = recipe.getRecipeDetailLs();
                List<MtaOrderDetailData> mtaOrderDetailLs = new ArrayList<>();
                recipeDetailLs.forEach(detail -> {
                    MtaOrderDetailData mtaDetail = new MtaOrderDetailData();
                    mtaDetail.setItemId(detail.getArtId());
                    mtaDetail.setBodypartCount(detail.getBodypartCount());
                    // 部位
                    List<MtaOrderDetailBodypartData> detailBodypartDataLs = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(detail.getRecipeDetailPosLs())) {
                        List<Integer> bodypartIdLs = detail.getRecipeDetailPosLs().stream().map(RecipeDetailPosDto::getBodypartId).distinct().collect(Collectors.toList());
                        List<MtBodypartEntity> bodypartLs = mtBodypartService.listByIds(bodypartIdLs);
                        detail.getRecipeDetailPosLs().forEach(pos -> {
                            MtaOrderDetailBodypartData detailBodypartData = new MtaOrderDetailBodypartData();
                            Optional<MtBodypartEntity> bodypart = bodypartLs.stream().filter(p -> p.getBodypartId().equals(pos.getBodypartId())).findFirst();
                            bodypart.ifPresent(p -> {
                                detailBodypartData.setBodypartCode(p.getBodypartCode());
                                detailBodypartData.setBodypartName(p.getBodypartName());
                            });
                            detailBodypartDataLs.add(detailBodypartData);
                        });
                    }
                    mtaDetail.setBodypartLs(detailBodypartDataLs);
                    mtaOrderDetailLs.add(mtaDetail);
                });
                mtaOrder.setOrderDetailLs(mtaOrderDetailLs);
                // 诊断
                List<RecipeDiagDto> recipeDiagLs = recipe.getRecipeDiagLs();
                List<MtaOrderDiagData> mtaOrderDiagLs = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(recipeDiagLs)) {
                    recipeDiagLs.forEach(diag -> {
                        MtaOrderDiagData mtaDiag = new MtaOrderDiagData();
                        mtaDiag.setDiagCode(diag.getDiagCode());
                        mtaDiag.setDiagName(diag.getDiagName());
                        mtaOrderDiagLs.add(mtaDiag);
                    });
                }
                remoteMtaService.saveMtaOrder(mtaOrder, mtaOrderDiagLs);
            }
        }
    }

    /**
     * 发送到MOE处置
     */
    private void notifyTreatment(List<Long> recipeIdLs) {
        log.info("notifyTreatment recipeId:{}", JSONUtil.toJsonStr(recipeIdLs));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (ObjectUtil.isEmpty(recipeIdLs)) {
            throw new SaveFailureException("请指定处方编号");
        }
        List<TreatmentVo> treatmentLs = new ArrayList<>();
        List<OrderEntity> orderLs = new ArrayList<>();
        List<Integer> mtaRecipeTypeLs = ListUtil.of(RecipeType.sys.getValue());
        List<RecipeEntity> recipeLs = recipeService.listByIds(recipeIdLs);
        List<Long> visitIdLs = recipeLs.stream().map(RecipeEntity::getVisitId).distinct().collect(Collectors.toList());
        Long orgId = recipeLs.get(0).getOrgId();
        OrgSettingEntity orgSetting = orgSettingService.getById(orgId);
        // consumableAppendToCure = 1,绑定耗材合并到治疗单时没有处置划价单 不会触发RecipeEventType.PAY_DISPOSAL处方划价单支付产生的MOE处置记录
        boolean consumableAppendToCure = orgSetting != null && Convert.toInt(orgSetting.getConsumableAppendToCure(), 0) == 1;
//        if (!recipe.getPaidStatus().equals(PaidStatus.paid.getValue())) {
//            throw new SaveFailureException("处方：" + recipe.getRxNo() + "未处于支付完结状态");
//        }
        List<RecipeDetailDto> detailLs = recipeDetailService.findLsByRecipeIdLs(recipeIdLs);
        List<VisitEntity> visitLs = visitService.listByIds(visitIdLs);
        for (RecipeEntity recipe : recipeLs) {
            Long recipeId = recipe.getRecipeId();
            List<RecipeDetailDto> recipeDetailLs = detailLs.stream().filter(p -> p.getRecipeId().equals(recipeId)).collect(Collectors.toList());
            if (ObjectUtil.isEmpty(recipeDetailLs)) {
                throw new SaveFailureException("处方：" + recipe.getRxNo() + "未关联处方明细");
            }
            VisitEntity visit = visitLs.stream().filter(p -> p.getVisitId().equals(recipe.getVisitId())).findFirst().orElse(null);
            if (visit == null) {
                throw new SaveFailureException("处方：" + recipe.getRxNo() + "未关联就诊信息");
            }
            if (mtaRecipeTypeLs.contains(recipe.getRecipeTypeId())) {
                orderLs = orderService.list(new LambdaQueryWrapper<OrderEntity>().eq(OrderEntity::getRecipeId, recipe.getRecipeId()));
            }
            Long clinicianId = recipe.getClinicianId();
            if (clinicianId == null) {
                clinicianId = visit.getClinicianId();
            }
            OrderEntity order = orderLs.stream().filter(p -> p.getRecipeId().equals(recipe.getRecipeId())).findFirst().orElse(null);
            if (mtaRecipeTypeLs.contains(recipe.getRecipeTypeId()) && order == null) {
                throw new SaveFailureException("医技处方：" + recipe.getRxNo() + "未关联申请单");
            }
            ClinicianEntity clinician = clinicianService.getById(clinicianId);
            if (order != null && clinician == null) {
                throw new SaveFailureException("处方：" + recipe.getRxNo() + "未关联医生信息");
            }
            List<BillEntity> billLs = billService.list(new LambdaQueryWrapper<BillEntity>().eq(BillEntity::getRecipeId, recipe.getRecipeId()));
            BillEntity treatmentBill = billLs.stream().filter(p -> p.getBillTypeId().equals(BillType.treatmentBill.getValue()) && p.getRecipeId().equals(recipeId)).findFirst().orElse(null);
            List<TreatmentVo.TreatmentDetail> treatmentDetailLs = new ArrayList<>();
            for (RecipeDetailDto recipeDetail : recipeDetailLs) {
                if (order != null || (recipeDetail.getFeedMethod() != null && recipeDetail.getFeedMethod().equals(FeedMethod.injection.getValue()))) {
                    TreatmentVo.TreatmentDetail treatmentDetail = new TreatmentVo.TreatmentDetail();
                    treatmentDetail.setRecipeLineNo(recipeDetail.getLineNo());
                    treatmentDetail.setArtId(recipeDetail.getArtId());
                    treatmentDetail.setRouteId(recipeDetail.getRouteId());
                    treatmentDetail.setFreqCode(recipeDetail.getFreqCode());
                    treatmentDetail.setTotal(recipeDetail.getTotal());
                    treatmentDetail.setUnit(recipeDetail.getUnit());
                    treatmentDetail.setUnitType(recipeDetail.getUnitType());
                    treatmentDetail.setStRequired(recipeDetail.getStRequired());
                    treatmentDetail.setGroupNo(recipeDetail.getGroupNo());
                    treatmentDetail.setGroupMark(recipeDetail.getGroupMark());
                    treatmentDetail.setSpecimenTypeId(recipeDetail.getSpecimenTypeId());
                    treatmentDetail.setBodypartDesc(recipeDetail.getBodypartDesc());
                    treatmentDetail.setBodypartCount(recipeDetail.getBodypartCount());
                    treatmentDetail.setLabId(order == null ? null : order.getLabId());
                    treatmentDetail.setDpm(recipeDetail.getDpm());
                    treatmentDetail.setNotice(recipeDetail.getNotice());
                    treatmentDetail.setMealCells(recipeDetail.getMealCells());
                    treatmentDetail.setMealDoses(recipeDetail.getMealDoses());
                    treatmentDetail.setPaidCycles(recipeDetail.getPaidCycles());
                    treatmentDetail.setHeadingTimes(recipeDetail.getHeadingTimes());
                    treatmentDetail.setFeedMethod(recipeDetail.getFeedMethod());
                    treatmentDetail.setFreqCycleBeats(recipeDetail.getFreqCycleBeats());
                    treatmentDetail.setFreqCycleSeconds(recipeDetail.getFreqCycleSeconds());
                    treatmentDetailLs.add(treatmentDetail);
                }
            }
            if (ObjectUtil.isNotEmpty(treatmentDetailLs)) {
                TreatmentVo treatment = new TreatmentVo();
                treatment.setRecipeId(recipe.getRecipeId());
                treatment.setRecipeTypeId(recipe.getRecipeTypeId());
                treatment.setOrgId(recipe.getOrgId());
                treatment.setVisitId(recipe.getVisitId());
                treatment.setApplyDeptcode(recipe.getApplyDeptcode());
                treatment.setExceDeptcode(recipe.getExceDeptcode());
                treatment.setTimeApplied(recipe.getTimeSubmitted());
                treatment.setSectionId(recipe.getSectionId());
                treatment.setClinicTypeId(visit.getClinicTypeId());
                treatment.setPatientName(visit.getPatientName());
                treatment.setAgeOfYears(visit.getAgeOfYears());
                treatment.setAgeOfDays(Convert.toInt(visit.getAgeOfDays()));
                treatment.setPatientId(visit.getPatientId());
                treatment.setGenderId(visit.getGenderId());
                if (order != null) {
                    treatment.setOrderId(order.getOrderId());
                    treatment.setOrderTypeId(order.getOrderTypeId());
                    treatment.setPurposeDesc(order.getPurposeDesc());
                    treatment.setMedicalHistory(order.getMedicalHistory());
                }
                treatment.setClinicianCode(clinician.getClinicianNo());
                treatment.setClinicianName(clinician.getClinicianName());
                treatment.setAccessionNo(recipe.getRxNo());
                treatment.setRecipeDate(Convert.toInt(sdf.format(recipe.getTimeCreated())));
                treatment.setSubjectId(recipe.getSubjectId());
                treatment.setTimes(recipe.getTimes());
                treatment.setPaidStatus(PaidStatus.paid.getValue());
                treatment.setBseqid(treatmentBill == null ? null : treatmentBill.getBseqid());
                treatment.setDetailLs(treatmentDetailLs);
                if (!consumableAppendToCure) {
                    setConsumable(treatmentBill, treatment);
                }
                treatmentLs.add(treatment);
            }
        }
        if (ObjectUtil.isNotEmpty(treatmentLs)) {
            treatmentService.notifyTreatment(treatmentLs);
        }
    }

    /**
     * 保存执行科室申请单 治疗暂时不推送
     */
    private void saveOrderExec(List<RecipeDto> recipeLs) {
        List<OrderExecVo> orderExecLs = new ArrayList<>();
        for (RecipeDto recipe : recipeLs) {
            if (recipe != null && (recipe.getRecipeTypeId().equals(RecipeType.sys.getValue())
                    || recipe.getRecipeTypeId().equals(RecipeType.yj.getValue())
                    || recipe.getRecipeTypeId().equals(RecipeType.zl.getValue()))) {
                List<OrderEntity> orderLs = orderService.list(Wrappers.lambdaQuery(OrderEntity.class).eq(OrderEntity::getRecipeId, recipe.getRecipeId()));
                List<BillDetailEntity> billDetailLs = new ArrayList<>();
                if (recipe.getBseqid() != null) {
                    billDetailLs = billDetailService.list(Wrappers.lambdaQuery(BillDetailEntity.class).eq(BillDetailEntity::getBseqid, recipe.getBseqid()));
                }
                OrderEntity order = orderLs.stream().filter(o -> o.getRecipeId().equals(recipe.getRecipeId())).findFirst().orElse(new OrderEntity());
                List<RecipeDetailDto> recipeDetailLs = recipe.getRecipeDetailLs();
                List<Long> artIdLs = recipeDetailLs.stream().map(RecipeDetailDto::getArtId).collect(Collectors.toList());
                List<ArticleEntity> articleLs = articleService.list(Wrappers.lambdaQuery(ArticleEntity.class).in(ArticleEntity::getArtId, artIdLs));
                Integer artTypeId = articleLs.get(0).getArtTypeId();
                if (recipe.getRecipeTypeId().equals(RecipeType.zl.getValue()) || artTypeId.equals(ArtType.MoeLab.getValue()) || artTypeId.equals(ArtType.MoeExam.getValue())) {
                    OrderExecVo orderExec = OrderExecVo.builder()
                            .orderId(order.getOrderId())
                            .orgId(recipe.getOrgId())
                            .visitId(recipe.getVisitId())
                            .recipeId(recipe.getRecipeId())
                            .clinicTypeId(recipe.getClinicTypeId())
                            .orderTypeId(order.getOrderTypeId())
                            .applyDeptcode(recipe.getApplyDeptcode())
                            .exceDeptcode(recipe.getExceDeptcode())
                            .patientId(recipe.getPatientId())
                            .genderId(recipe.getPatientGenderId())
                            .patientName(recipe.getPatientName())
                            .ageOfYears(recipe.getAgeOfYears())
                            .ageOfDays(recipe.getAgeOfDays())
                            .creatorUid(recipe.getUserId())
                            .timeApplied(recipe.getTimeCreated())
                            .purposeDesc(order.getPurposeDesc())
                            .medicalHistory(order.getMedicalHistory())
                            .clinicianCode(recipe.getClinicianNo())
                            .clinicianName(recipe.getClinicianName())
                            .accessionNo(order.getAccessionNo())
                            .artTypeId(articleLs.get(0).getArtTypeId())
                            .sectionId(recipe.getSectionId())
                            .bseqid(recipe.getBseqid())
                            .build();
                    List<OrderDetailVo> orderDetailLs = new ArrayList<>();
                    for (RecipeDetailDto recipeDetail : recipeDetailLs) {
                        if (Convert.toInt(recipeDetail.getIsNhItem(), 0) == 0) {
                            List<Integer> bodypartIdLs = new ArrayList<>();
                            if (ObjectUtil.isNotEmpty(recipeDetail.getRecipeDetailPosLs())) {
                                bodypartIdLs = recipeDetail.getRecipeDetailPosLs().stream().map(RecipeDetailPosDto::getBodypartId).collect(Collectors.toList());
                            }
                            BigDecimal amount = BigDecimal.ZERO;
                            if (recipe.getBseqid() != null) {
                                List<BillDetailEntity> bdLs;
                                if (recipeDetail.getIsPackage() != null && recipeDetail.getIsPackage() == 1) {
                                    bdLs = billDetailLs.stream().filter(p -> p.getBseqid().equals(recipe.getBseqid()) && p.getPackageId() != null && p.getPackageId().equals(recipeDetail.getArtId())).collect(Collectors.toList());
                                } else {
                                    bdLs = billDetailLs.stream().filter(p -> p.getBseqid().equals(recipe.getBseqid()) && p.getArtId().equals(recipeDetail.getArtId())).collect(Collectors.toList());
                                }
                                if (ObjectUtil.isNotEmpty(bdLs)) {
                                    amount = bdLs.stream().map(BillDetailEntity::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                }
                            }
                            orderDetailLs.add(OrderDetailVo.builder()
                                    .times(1)
                                    .packageId(recipeDetail.getArtId())
                                    .total(Convert.toBigDecimal(recipeDetail.getTotal(), BigDecimal.ZERO))
                                    .specimenTypeId(recipeDetail.getSpecimenTypeId())
                                    .bodypartCount(recipeDetail.getBodypartCount())
                                    .bodypartDesc(recipeDetail.getBodypartDesc())
                                    .labId(order.getLabId())
                                    .amount(amount)
                                    .isUnchargeable(amount.compareTo(BigDecimal.ZERO) == 0 ? 1 : Convert.toInt(recipeDetail.getIsUnchargeable(), 0))
                                    .bodypartIdLs(bodypartIdLs)
                                    .build());
                        }
                    }
                    if (ObjectUtil.isNotEmpty(orderDetailLs)) {
                        orderExec.setOrderDetailLs(orderDetailLs);
                        orderExecLs.add(orderExec);
                    }
                }
            }
        }
        if (ObjectUtil.isNotEmpty(orderExecLs)) {
            moeOrderService.saveBatchOrderLs(orderExecLs);
        }
    }

    private void setConsumable(BillEntity treatmentBill, TreatmentVo treatment) {
        if (treatmentBill != null) {
            List<TreatmentVo.TreatmentConsumable> consumableLs = new ArrayList<>();
            Map<String, Object> params = new HashMap<>(2);
            params.put("bSeqId", treatmentBill.getBseqid());
            params.put("stockReq", 1);
            //获取耗材
            List<BillDetailDto> stockArtLs = billDetailService.findLsByParams(params);
            if (ObjectUtil.isNotEmpty(stockArtLs)) {
                for (BillDetailDto stockArt : stockArtLs) {
                    TreatmentVo.TreatmentConsumable consumable = new TreatmentVo.TreatmentConsumable();
                    consumable.setArtId(stockArt.getArtId());
                    consumable.setArtName(stockArt.getArtName());
                    consumable.setArtSpec(stockArt.getArtSpec());
                    consumable.setPackCells(stockArt.getPackCells());
                    consumable.setPackTotal(stockArt.getPackTotal());
                    consumable.setCellTotal(stockArt.getCellTotal());
                    consumableLs.add(consumable);
                }
                treatment.setConsumableLs(consumableLs);
            }
        }
    }

    /**
     * 推送到自有库房
     */
    private void selfWarehouseRecipe(Long bseqid, Integer originalTimes, Integer times, RecipeDto recipe) {
        // 划价明细
        List<BillDetailEntity> billDetailLs = new ArrayList<>();
        List<RecipeDetailDto> list = recipe.getRecipeDetailLs();
        List<Long> artIdLs = list.stream().map(RecipeDetailDto::getArtId).distinct().collect(Collectors.toList());
        List<ArticleEntity> articleLs = articleService.listByIds(artIdLs);
        List<ArtClassEntity> artClassLs = hsdBaseService.getArtClassLsFromArtLs(articleLs);
        List<OrgItemPriceEntity> orgItemPriceLs = hsdBaseService.getOrgItemPriceLs(recipe.getOrgId(), articleLs);
        List<WmBillDetailModel> wmBillDetailDataLs = wmRecipeReserve(times, recipe);
        for (WmBillDetailModel detail : wmBillDetailDataLs) {
            ArticleEntity article = articleLs.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(new ArticleEntity());
            // 构建划价明细
            RecipeDetailDto recipeDetail = list.stream().filter(p -> p.getArtId().equals(detail.getArtId())).findFirst().orElse(new RecipeDetailDto());
            BigDecimal packTotal = detail.getTotalPacks() != null ? BigDecimal.valueOf(detail.getTotalPacks()) : BigDecimal.ZERO;
            createBillDetailLs(recipe.getOrgId(), recipe.getRecipeTypeId(), article.getArtId(), detail.getPackPrice(), packTotal,
                    detail.getCellPrice(), detail.getTotalCells(), detail.getCost(), recipeDetail.getFreqCode(), article.getCellUnit(),
                    recipeDetail.getPeriodCycles(), recipeDetail.getIsUnrelative(), recipeDetail.getLimitedFlag(),
                    recipeDetail.getSelfpaidPct(), article, artClassLs, orgItemPriceLs, billDetailLs);
            // 包装
//            if (detail.getTotalPacks() != null && detail.getTotalPacks() != 0) {
//                BigDecimal total = BigDecimal.valueOf(detail.getTotalPacks());
//                BigDecimal amount = total.multiply(detail.getPackPrice());
//                createBillDetailLs(recipe.getOrgId(), article.getArtId(), recipe.getRecipeTypeId(), total,
//                        total, detail.getPackPrice(), amount, recipeDetail.getFreqCode(), article.getPackUnit(), 2,
//                        recipeDetail.getPeriodCycles(), recipeDetail.getIsUnrelative(), recipeDetail.getLimitedFlag(),
//                        recipeDetail.getSelfpaidPct(), article, artClassLs, billDetailLs);
//            }
            // 制剂
//            if (detail.getTotalCells() != null && detail.getTotalCells().compareTo(BigDecimal.ZERO) != 0) {
//                BigDecimal total = detail.getTotalCells();
//                BigDecimal amount = total.multiply(detail.getCellPrice());
//                createBillDetailLs(recipe.getOrgId(), article.getArtId(), recipe.getRecipeTypeId(), total,
//                        total, detail.getCellPrice(), amount, recipeDetail.getFreqCode(), article.getCellUnit(), 1,
//                        recipeDetail.getPeriodCycles(), recipeDetail.getIsUnrelative(), recipeDetail.getLimitedFlag(),
//                        recipeDetail.getSelfpaidPct(), article, artClassLs, billDetailLs);
//            }
        }
        try {
            // 先删除旧数据
            billDetailService.remove(Wrappers.lambdaQuery(BillDetailEntity.class).eq(BillDetailEntity::getBseqid, bseqid));
            // 保存划价明细
            BigDecimal amount = BigDecimal.ZERO;
            int lineNo = 1;
            for (BillDetailEntity billDetail : billDetailLs) {
                billDetail.setBseqid(bseqid);
                billDetail.setLineNo(lineNo);
                billDetail.setAmount(billDetail.getAmount() == null ? BigDecimal.ZERO : billDetail.getAmount().setScale(2, RoundingMode.HALF_UP));

                amount = amount.add(billDetail.getAmount() == null ? BigDecimal.ZERO : billDetail.getAmount());
            }
            billDetailService.saveBatch(billDetailLs);
            // 修改划价单金额
            LambdaUpdateWrapper<BillEntity> billWrapper = Wrappers.lambdaUpdate(BillEntity.class);
            billWrapper.eq(BillEntity::getBseqid, bseqid);
            billWrapper.set(BillEntity::getTimes, times);
            billWrapper.set(BillEntity::getAmount, amount);
            billWrapper.set(BillEntity::getTotalAmount, amount);
            billService.update(billWrapper);
        } catch (Exception e) {
            wmRecipeReserve(originalTimes, recipe);
            throw new SaveFailureException("生成划价单失败，原因：" + e.getMessage());
        }
    }

    private List<WmBillDetailModel> wmRecipeReserve(Integer times, RecipeDto recipe) {
        WmReqModel wmReq = new WmReqModel();
        wmReq.setOrgId(recipe.getOrgId());
        wmReq.setRecipeId(recipe.getRecipeId());
        wmReq.setApplyDeptcode(recipe.getApplyDeptcode());
        wmReq.setDeptCode("");
        wmReq.setTimes(times);
//        wmReq.setNotes(recipe.getNotes());
        wmReq.setRxNo(recipe.getRxNo());
        wmReq.setUserId(recipe.getSignUserId());
        wmReq.setClinicTypeId(recipe.getClinicTypeId());
        List<WmReqDetailModel> wmReqDetailLs = new ArrayList<>();
        for (RecipeGroupDto group : recipe.getRecipeGroupLs()) {
            for (RecipeDetailDto detail : group.getRecipeDetailLs()) {
                WmReqDetailModel wmReqDetail = new WmReqDetailModel();
                wmReqDetail.setLineNo(detail.getLineNo());
                wmReqDetail.setArtId(detail.getArtId());
                wmReqDetail.setFreqCode(group.getFreqCode());
                wmReqDetail.setRouteId(group.getRouteId());
                wmReqDetail.setMealCells(detail.getMealCells());
                wmReqDetail.setCellUnit(detail.getCellUnit());
                wmReqDetail.setMealDoses(detail.getMealDoses());
                wmReqDetail.setDoseUnit(detail.getDoseUnit());
                wmReqDetail.setNotice(group.getNotice());
                wmReqDetail.setVisitId(recipe.getVisitId());
                // 如果有小数这里做了向上取整
                if (Convert.toInt(detail.getUnitType(), UnitType.Pack_Unit.getValue()) == UnitType.Pack_Unit.getValue()) { // 包装
                    BigDecimal total = Convert.toBigDecimal(detail.getTotal(), BigDecimal.ONE).setScale(0, RoundingMode.CEILING);
                    wmReqDetail.setTotalPacks(total.intValue());
                    wmReqDetail.setTotalCells(BigDecimal.ZERO);
                    // 有小数
                    if (Convert.toBigDecimal(detail.getTotal(), BigDecimal.ONE).remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) != 0) {
                        ArticleEntity article = articleService.getById(detail.getArtId());
                        BigDecimal packCells = Convert.toBigDecimal(article.getPackCells(), BigDecimal.ONE);
                        if (NumberUtil.isGreater(packCells, BigDecimal.ONE)) {
                            // 包装单位有小数，且包装制剂数大于1换算成制剂数量做申请
                            BigDecimal totalCells = detail.getTotal().multiply(packCells).setScale(0, RoundingMode.CEILING);
                            wmReqDetail.setTotalPacks(0);
                            wmReqDetail.setTotalCells(totalCells);
                        }
                    }
                } else {
                    wmReqDetail.setTotalPacks(0);
                    wmReqDetail.setTotalCells(detail.getTotal());
                }
                wmReqDetailLs.add(wmReqDetail);
            }
        }
        wmReq.setDetails(wmReqDetailLs);
        return remoteClinicsWmService.recipeTimesChange(wmReq);
    }

    private void createBillDetailLs(Long orgId, Integer recipeTypeId, Long artId, BigDecimal packPrice, BigDecimal packTotal,
                                    BigDecimal cellPrice, BigDecimal cellTotal, BigDecimal cost, String freqCode, String unit, Integer periodCycles,
                                    Integer isUnrelative, Integer limitedFlag, BigDecimal selfpaidPct, ArticleEntity article,
                                    List<ArtClassEntity> artClassLs, List<OrgItemPriceEntity> itemPriceLs, List<BillDetailEntity> billDetailLs) {
        Integer feeTypeId = hsdBaseService.getRecipeFeeTypeId(recipeTypeId, article, artClassLs);
        Integer statsCatId = hsdBaseService.getStatsCatId(article.getArtClassId(), artClassLs);
        Integer udfTypeId = hsdBaseService.getUdfTypeId(orgId, artId, feeTypeId, itemPriceLs);
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal amount = BigDecimal.ZERO;
        int packCells = article.getPackCells() == null ? 1 : article.getPackCells();
        if (packTotal != null && packTotal.compareTo(BigDecimal.ZERO) != 0) {
            total = total.add(packTotal.multiply(BigDecimal.valueOf(packCells)));
            amount = amount.add(packTotal.multiply(packPrice).setScale(2, RoundingMode.HALF_UP));
        }
        if (cellTotal != null && cellTotal.compareTo(BigDecimal.ZERO) != 0) {
            total = total.add(cellTotal);
            amount = amount.add(cellTotal.multiply(cellPrice).setScale(2, RoundingMode.HALF_UP));
        }
        BigDecimal price = BigDecimal.ZERO;
        if (total.compareTo(BigDecimal.ZERO) != 0) {
            price = amount.divide(total, 6, RoundingMode.HALF_UP);
        }
        BillDetailEntity billDetail = BillDetailEntity.builder()
                .artId(artId)
                .baseTotal(total)
                .times(1)
                .freqCode(freqCode)
                .unit(unit)
                .total(total)
                .price(price)
                .amount(amount)
                .packTotal(packTotal)
                .packPrice(packPrice)
                .cellTotal(cellTotal)
                .cellPrice(cellPrice)
                .packCells(packCells)
                .feeTypeId(feeTypeId)
                .isEd(article.getIsEd() != null && article.getIsEd().equals(1))
                .nonMedicalFlag(orgNonmedicalArtService.isNonMedicalArt(orgId, artId))
                .isUnrelative(isUnrelative)
                .limitedFlag(limitedFlag)
                .selfpaidPct(selfpaidPct)
                .statsCatId(statsCatId)
                .cycleCount(periodCycles)
                .udfTypeId(udfTypeId)
                .cost(cost)
                .build();
        if (amount.compareTo(BigDecimal.ZERO) != 0) {
            billDetailLs.add(billDetail);
        }
    }
}
