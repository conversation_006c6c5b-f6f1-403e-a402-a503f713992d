package cn.feiying.med.microhis.hsd.service.impl;

import cn.feiying.med.common.exception.SaveFailureException;
import cn.feiying.med.common.utils.*;
import cn.feiying.med.hip.enums.EventStatus;
import cn.feiying.med.hip.mdi.entity.ClinicianEntity;
import cn.feiying.med.hip.mdi.entity.OrgDeptEntity;
import cn.feiying.med.hip.mdi.entity.OrgEntity;
import cn.feiying.med.hip.mdi.service.ClinicianService;
import cn.feiying.med.hip.mdi.service.GbZoneService;
import cn.feiying.med.hip.mdi.service.OrgDeptService;
import cn.feiying.med.hip.mdi.service.OrgService;
import cn.feiying.med.hip.mpi.dto.PatientDto;
import cn.feiying.med.hip.mpi.entity.PatientEntity;
import cn.feiying.med.hip.mpi.service.PatientService;
import cn.feiying.med.hip.vo.dmr.req.DiseaseCaseRequest;
import cn.feiying.med.hip.vo.dmr.req.InfectiousDiseaseReport;
import cn.feiying.med.hip.vo.dmr.req.UpdateDiseaseCaseRequest;
import cn.feiying.med.his.dmr.entity.DmrDiseaseEntity;
import cn.feiying.med.his.dmr.entity.DmrOrgParamEntity;
import cn.feiying.med.his.dmr.entity.DmrReportEntity;
import cn.feiying.med.his.dmr.enums.*;
import cn.feiying.med.his.dmr.service.DmrDiseaseService;
import cn.feiying.med.his.dmr.service.DmrOrgParamService;
import cn.feiying.med.his.dmr.service.DmrReportService;
import cn.feiying.med.his.dmr.util.EnumUtil;
import cn.feiying.med.microhis.hsd.dto.EventReportedDto;
import cn.feiying.med.microhis.hsd.entity.EventReportedEntity;
import cn.feiying.med.microhis.hsd.entity.VisitEntity;
import cn.feiying.med.microhis.hsd.service.*;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EventServiceImpl implements EventService {
    private static final List<Integer> qualityCaseDiseaseLs = ListUtil.of(DmrDiseaseName.HEPATITIS_B.getValue(), DmrDiseaseName.HEPATITIS_C.getValue(), DmrDiseaseName.SCHISTOSOMIASIS.getValue());

    @Value("${sys.aes.secret}")
    private String aesSecret;
    @Value("${dmr.url.saveReportCard:}")
    private String saveReportCardUrl;
    @Value("${dmr.url.putDiseaseCase:}")
    private String putDiseaseCaseUrl;
    @Value("${dmr.url.updateDiseaseCase:}")
    private String updateDiseaseCaseUrl;

    @Resource
    private VisitService visitService;
    @Resource
    private EventReportedService eventReportedService;
    @Resource
    private DmrReportService dmrReportService;
    @Resource
    private DmrOrgParamService dmrOrgParamService;
    @Resource
    private CommonService commonService;
    @Resource
    private PatientService patientService;
    @Resource
    private ClinicianService clinicianService;
    @Resource
    private OrgService orgService;
    @Resource
    private OrgDeptService orgDeptService;
    @Resource
    private GbZoneService gbZoneService;
    @Resource
    private IdService idService;
    @Resource
    private DmrDiseaseService dmrDiseaseService;
    private static final String DMR_RICH_PATH = "visit.dmr.";

    @Override
    public PageUtils<EventReportedDto> queryPage(Map<String, Object> params) {
        IPage<EventReportedDto> page = eventReportedService.queryPage(params);
        setDmrReportStatus(page.getRecords());
        return new PageUtils<>(page);
    }

    private void setDmrReportStatus(List<EventReportedDto> records) {
        if (ObjectUtil.isNotEmpty(records)) {
            List<Long> eventIdLs = records.stream().filter(p -> p.getEventStatus().equals(EventStatus.VERIFIED.getValue())).map(EventReportedDto::getEventId).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(eventIdLs)) {
                List<DmrReportEntity> dmrReportLs = dmrReportService.findPendingReportLsByEventIdLs(eventIdLs);
                for (EventReportedDto event : records) {
                    DmrReportEntity dmrReport = dmrReportLs.stream().filter(p -> p.getEventId().equals(event.getEventId())).findFirst().orElse(null);
                    event.setDmrId(dmrReport == null ? null : dmrReport.getDmrId());
                }
            }
        }
    }

    @Override
    public EventReportedDto findByEventId(Long eventId) {
        EventReportedDto eventReport = eventReportedService.findById(eventId);
        DmrReportEntity dmrReport = dmrReportService.findReportByEventId(eventId);
        if (dmrReport != null) {
            if (eventReport.getPatientId() != null) {
                PatientDto patient = patientService.getDtoById(eventReport.getPatientId());
                eventReport.setPatientName(patient.getPatientName());
                eventReport.setGenderName(patient.getGenderName());
                eventReport.setAgeOfYears(patient.getAgeOfYears());
                eventReport.setAgeOfDays(Convert.toInt(patient.getAgeOfDays()));
            }
            String reqPacketContent = commonService.getRichTextContent(dmrReport.getReqPacketUrl());
            if (StrUtil.isNotBlank(reqPacketContent) && JSONUtil.isTypeJSON(reqPacketContent)) {
                JSONObject jsonObj = JSONUtil.parseObj(reqPacketContent);
                Integer dmrTypeId = jsonObj.getInt("dmrTypeId");
                eventReport.setDmrTypeId(dmrTypeId);
                eventReport.setReqPacketContent(reqPacketContent);
                eventReport.setEventDate(dmrReport.getEventDate());
                eventReport.setSickDate(dmrReport.getSickDate());
                eventReport.setDiseaseId(dmrReport.getDiseaseId());
                eventReport.setReportType(dmrReport.getReportType());
                eventReport.setCaseType(dmrReport.getCaseType());
                if (dmrTypeId != null) {
                    if (dmrTypeId.equals(DmrDataType.INFECTIOUS_DISEASE.getValue())) {
                        InfectiousDiseaseReport reqPacket = JSONUtil.toBean(reqPacketContent, InfectiousDiseaseReport.class);
                        if (ObjectUtil.isNotEmpty(reqPacket.getAddressCounty())) {
                            String livingZoneCode = StrUtil.sub(reqPacket.getAddressCounty(), 0, 6);
                            List<String> livingZoneCodeList = gbZoneService.getZoneCodeList(livingZoneCode);
                            reqPacket.setLivingZonecode(livingZoneCode);
                            reqPacket.setLivingZoneCodeLs(livingZoneCodeList);
                        }
                        if (StrUtil.isNotBlank(reqPacket.getCloseHistory())) {
                            List<String> closeHistoryLs = StrUtil.split(reqPacket.getCloseHistory(), StrUtil.COMMA);
                            reqPacket.setCloseHistoryLs(closeHistoryLs);
                        }
                        eventReport.setReqPacketContent(JSONUtil.toJsonStr(reqPacket));
                    }
                }
            }
        }
        return eventReport;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveEventWithDmr(Long userId, Long patientId, Long visitId, Long recipeId, Long artId, String eventAbstract, Integer eventTypeId, String eventDesc, String treatmentDesc, Integer adverseTypeId,
                                 Integer adverseGradeId, Integer dmrTypeId, String reqPacketContent) {
        Integer eventDate = cn.feiying.med.common.utils.DateUtil.getTodayInt();
        Integer sickDate = cn.feiying.med.common.utils.DateUtil.getTodayInt();
        Integer diseaseId = null;
        Integer caseType = null;
        VisitEntity visit = visitService.getById(visitId);
        Long finalPatientId = Convert.toLong(visit.getPatientId(), patientId);
        if (finalPatientId == null) {
            throw new SaveFailureException("请指定建档患者");
        }
        EventReportedEntity eventReported = new EventReportedEntity();
        eventReported.setEventAbstract(eventAbstract);
        eventReported.setEventTypeId(eventTypeId);
        eventReported.setVisitId(visitId);
        eventReported.setRecipeId(recipeId);
        eventReported.setArtId(artId);
        eventReported.setEventDesc(eventDesc);
        eventReported.setTreatmentDesc(treatmentDesc);
        eventReported.setAdverseTypeId(adverseTypeId);
        eventReported.setAdverseGradeId(adverseGradeId);
        eventReportedService.saveEntity(visit.getOrgId(), userId, eventReported);
        if (dmrTypeId.equals(DmrDataType.INFECTIOUS_DISEASE.getValue())) {
            InfectiousDiseaseReport reqPacket = getInfectiousDiseaseReport(reqPacketContent, finalPatientId, visit.getOrgId(), eventReported.getEventId(), visit.getClinicianId(), visit.getDeptCode());

            if (StrUtil.isNotBlank(reqPacket.getDiseasesName())) {
                diseaseId = Convert.toInt(reqPacket.getDiseasesName());
            }
            if (StrUtil.isNotBlank(reqPacket.getCaseType())) {
                caseType = Convert.toInt(reqPacket.getCaseType());
            }
            if (StrUtil.isNotBlank(reqPacket.getMorbiditDate())) {
                Date morbiditDate = DateUtil.parse(reqPacket.getMorbiditDate(), "yyyy-MM-dd");
                sickDate = Convert.toInt(DateUtil.format(morbiditDate, "yyyyMMdd"));
            }
            if (StrUtil.isNotBlank(reqPacket.getDiagnoseDate())) {
                Date diagnoseDate = DateUtil.parse(reqPacket.getDiagnoseDate(), "yyyy-MM-dd HH:mm:ss");
                eventDate = Convert.toInt(DateUtil.format(diagnoseDate, "yyyyMMdd"));
            }
            reqPacketContent = JSONUtil.toJsonStr(reqPacket);
        } else if (dmrTypeId.equals(DmrDataType.CASE_INDIVIDUAL.getValue())) {
            DiseaseCaseRequest diseaseCaseRequest = getDiseaseCaseRequest(reqPacketContent, finalPatientId, visit.getOrgId(), eventReported.getEventId());
            Map<Object, Object> reqPacket = MapUtil.builder().put("diseaseCaseRequest", diseaseCaseRequest).build();
            if (StrUtil.isNotBlank(diseaseCaseRequest.getDoctorTime())) {
                Date doctorTime = DateUtil.parse(diseaseCaseRequest.getDoctorTime(), "yyyy-MM-dd HH:mm:ss");
                eventDate = Convert.toInt(DateUtil.format(doctorTime, "yyyyMMdd"));
                sickDate = eventDate;
            }
            reqPacketContent = JSONUtil.toJsonStr(reqPacket);
        }
        log.debug("reqPacketContent:{}", reqPacketContent);
        String reqPacketUrl = getReqPacketUrl(visit.getOrgId(), visitId, dmrTypeId, reqPacketContent);
        Integer defaultDate = visit.getTimeAdmission() == null ? cn.feiying.med.common.utils.DateUtil.getTodayInt() : Convert.toInt(DateUtil.format(visit.getTimeAdmission(), "yyyyMMdd"));
        dmrReportService.saveDmrReport(visit.getOrgId(), visitId, eventReported.getEventId(), reqPacketUrl, Convert.toInt(eventDate, defaultDate), Convert.toInt(sickDate, defaultDate), diseaseId, caseType);
    }

    private InfectiousDiseaseReport getInfectiousDiseaseReport(String reqPacketContent, Long finalPatientId, Long orgId, Long eventId, Long clinicianId, String deptCode) {
        PatientDto patient = patientService.getOrgDtoById(orgId, finalPatientId);
        OrgEntity org = orgService.getById(orgId);
        OrgDeptEntity orgDept = orgDeptService.findById(orgId, deptCode);
        DmrOrgParamEntity dmrOrgParam = dmrOrgParamService.getById(orgId);
        ClinicianEntity clinician = clinicianService.getById(clinicianId);
        if (dmrOrgParam == null) {
            throw new SaveFailureException("请配置机构传染病上报参数");
        }
        if (StrUtil.isBlank(dmrOrgParam.getAppId())) {
            throw new SaveFailureException("请配置机构传染病上报参数:appId");
        }
        if (StrUtil.isBlank(dmrOrgParam.getUserName())) {
            throw new SaveFailureException("请配置机构传染病上报参数:用户账号");
        }
        Integer sex = Gender.UNSPECIFIED.getValue();
        if (StrUtil.isNotBlank(Gender.getName(patient.getGenderId()))) {
            sex = patient.getGenderId();
        }
        String birthday = StrUtil.EMPTY;
        if (patient.getBirthDate() != null) {
            birthday = DateUtil.format(DateUtil.parse(Convert.toStr(patient.getBirthDate()), "yyyyMMdd"), "yyyy-MM-dd");
        }
        InfectiousDiseaseReport reqPacket = JSONUtil.toBean(reqPacketContent, InfectiousDiseaseReport.class);
        reqPacket.setAppId(dmrOrgParam.getAppId());
        reqPacket.setUserAccount(dmrOrgParam.getUserName());
        reqPacket.setDoctorNum(clinician.getClinicianNo());
        reqPacket.setUniqueIndexCode(eventId + StrUtil.UNDERLINE + idService.getSnowflakeId());
        reqPacket.setOutpaientNumber(Convert.toStr(patient.getOutpatientNo(), Convert.toStr(finalPatientId)));
        reqPacket.setName(patient.getPatientName());
        reqPacket.setCardId(patient.getIdcertNo());
        reqPacket.setSex(Convert.toStr(sex));
        reqPacket.setBirthday(birthday);
        if (patient.getAgeOfYears() > 0) {
            reqPacket.setAge(Convert.toStr(patient.getAgeOfYears()));
            reqPacket.setAgeUnit(Convert.toStr(AgeUnit.YEAR.getValue()));
        } else {
            reqPacket.setAge(Convert.toStr(patient.getAgeOfDays()));
            reqPacket.setAgeUnit(Convert.toStr(AgeUnit.DAY.getValue()));
        }
        reqPacket.setTel(patient.getTelNo());
        reqPacket.setReportOrg(org.getOrgName());
        reqPacket.setIsUpdate("0");
        reqPacket.setReportDate(DateUtil.format(DateUtil.date(), "yyyy-MM-dd"));
        reqPacket.setIsRole("3");
        reqPacket.setDeparthospital(orgDept == null ? null : orgDept.getDeptName());
        reqPacket.setReportType(Convert.toStr(ReportType.INITIAL.getValue()));
        reqPacket.setDiseasesType(StrUtil.isBlank(reqPacket.getDiseasesName()) || Convert.toInt(reqPacket.getDiseasesName()).equals(DmrDiseaseName.OTHER_DISEASES.getValue()) ? "其他" : "法定");
        reqPacket.setIsrevise(StrUtil.EMPTY);
        reqPacket.setPersonAlid(Convert.toStr(patient.getPatientId()));
        reqPacket.setReturnReviseTime(StrUtil.EMPTY);
        reqPacket.setCardId(patient.getIdcertNo());
        reqPacket.setCardType(DmrCardType.fromHipCertType(Convert.toStr(patient.getCertTypeId())));
        reqPacket.setCardBelong(Convert.toStr(CardBelong.PATIENT_SELF.getValue()));//            <!--      /**-->
        reqPacket.setReportType(Convert.toStr(DmrReportType.FIRST_REPORT.getValue()));
        reqPacket.setAddressTownName("不详");
        reqPacket.setAddressVillageName("不详");
        reqPacket.setAddressDetail(reqPacket.getAddress());
        if (ObjectUtil.isEmpty(reqPacket.getLivingZoneCodeLs()) || reqPacket.getLivingZoneCodeLs().size() != 3) {
            throw new SaveFailureException("请指定现居地");
        }
        if (StrUtil.isNotBlank(reqPacket.getCrowd()) && reqPacket.getCrowd().equalsIgnoreCase(CrowdClassification.OTHER.getValue()) && StrUtil.isBlank(reqPacket.getCrowdOther())) {
            throw new SaveFailureException("请录入人群分类其他信息");
        }
        if (StrUtil.isNotBlank(reqPacket.getDiseasesName()) && reqPacket.getDiseasesName().equalsIgnoreCase(Convert.toStr(DmrDiseaseName.OTHER_DISEASES.getValue())) && StrUtil.isBlank(reqPacket.getDiseasesOther())) {
            throw new SaveFailureException("请录入其他传染病名称");
        }
        if (StrUtil.isNotBlank(reqPacket.getDiseasesName())) {
            if (qualityCaseDiseaseLs.contains(Convert.toInt(reqPacket.getDiseasesName())) && StrUtil.isBlank(reqPacket.getCaseTypeQuality())) {
                throw new SaveFailureException("请录入病例类型");
            }
        }
        if (StrUtil.isNotBlank(reqPacket.getCommitteeName())) {
            reqPacket.setCommitteeName(reqPacket.getCommitteeName().trim());
            if (!Validator.hasChinese(reqPacket.getCommitteeName())) {
                throw new SaveFailureException("居委会名称请录入中文");
            }
        }
        if (StrUtil.isNotBlank(reqPacket.getStreetName())) {
            reqPacket.setStreetName(reqPacket.getStreetName().trim());
            if (!Validator.hasChinese(reqPacket.getStreetName())) {
                throw new SaveFailureException("街道名称请录入中文");
            }
        }
        Integer marriageStatusId = null;
        if (StrUtil.isNotBlank(reqPacket.getMarriage())) {
            if (!Convert.toInt(reqPacket.getMarriage()).equals(MaritalStatus.UNKNOWN.getValue())) {
                marriageStatusId = Convert.toInt(reqPacket.getMarriage());
            }
        }
        String nationalityCode = null;
        if (StrUtil.isNotBlank(reqPacket.getNation())) {
            nationalityCode = Convert.toStr(Convert.toInt(reqPacket.getNation()));
        }
        if (StrUtil.isNotBlank(reqPacket.getLivingZonecode()) || StrUtil.isNotBlank(nationalityCode) || marriageStatusId != null) {
            LambdaUpdateWrapper<PatientEntity> patientUpdateWrapper = new LambdaUpdateWrapper<>();
            patientUpdateWrapper.eq(PatientEntity::getPatientId, finalPatientId);
            patientUpdateWrapper.set(StrUtil.isNotBlank(reqPacket.getLivingZonecode()), PatientEntity::getLivingZonecode, reqPacket.getLivingZonecode());
            patientUpdateWrapper.set(StrUtil.isNotBlank(reqPacket.getAddress()), PatientEntity::getLivingAddr, reqPacket.getAddress());
            patientUpdateWrapper.set(StrUtil.isNotBlank(nationalityCode), PatientEntity::getNationalityCode, nationalityCode);
            patientUpdateWrapper.set(marriageStatusId != null, PatientEntity::getMarriageStatusId, marriageStatusId);
            patientService.update(patientUpdateWrapper);
        }
        if (patient.getAgeOfYears() <= 14 && StrUtil.isBlank(reqPacket.getParentName())) {
            throw new SaveFailureException("请录入家长姓名");
        }
        reqPacket.setAddressProvince(StrUtil.padAfter(reqPacket.getLivingZoneCodeLs().get(0), 12, '0'));
        reqPacket.setAddressCity(StrUtil.padAfter(reqPacket.getLivingZoneCodeLs().get(1), 12, '0'));
        reqPacket.setAddressCounty(StrUtil.padAfter(reqPacket.getLivingZoneCodeLs().get(2), 12, '0'));
        reqPacket.setProvince(reqPacket.getAddressProvince());
        reqPacket.setCity(reqPacket.getAddressCity());
        reqPacket.setCounty(reqPacket.getAddressCounty());
        if (!Validator.isMobile(reqPacket.getDepartmentTel())) {
            reqPacket.setDepartmentTel(clinician.getMobileNo());
        }
        if (ObjectUtil.isNotEmpty(reqPacket.getCloseHistoryLs())) {
            String closeHistory = reqPacket.getCloseHistoryLs().stream().map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA));
            reqPacket.setCloseHistory(closeHistory);
        }

        reqPacket.setLivingZonecode(null);
        reqPacket.setLivingZoneCodeLs(null);
        reqPacket.setCloseHistoryLs(null);
        return reqPacket;
    }

    private DiseaseCaseRequest getDiseaseCaseRequest(String reqPacketContent, Long finalPatientId, Long orgId, Long eventId) {
        PatientDto patient = patientService.getOrgDtoById(orgId, finalPatientId);
        DmrOrgParamEntity dmrOrgParam = dmrOrgParamService.getById(orgId);
        if (dmrOrgParam == null) {
            throw new SaveFailureException("请配置机构传染病上报参数");
        }
        if (StrUtil.isBlank(dmrOrgParam.getAppId())) {
            throw new SaveFailureException("请配置机构传染病上报参数:appId");
        }
        if (StrUtil.isBlank(dmrOrgParam.getUserName())) {
            throw new SaveFailureException("请配置机构传染病上报参数:用户账号");
        }
        Integer sex = Gender.UNSPECIFIED.getValue();
        if (StrUtil.isNotBlank(Gender.getName(patient.getGenderId()))) {
            sex = patient.getGenderId();
        }
        DiseaseCaseRequest reqPacket = JSONUtil.toBean(reqPacketContent, DiseaseCaseRequest.class);
        reqPacket.setObjId(StrUtil.format("{}{}", dmrOrgParam.getUserName(), eventId));
        reqPacket.setDiseaseName(patient.getPatientName());
        reqPacket.setDiseaseIdCard(patient.getIdcertNo());
        reqPacket.setDiseaseSex(Convert.toStr(sex));
        reqPacket.setDiseaseAge(AgeUtil.getAge(patient.getBirthDate()));
        reqPacket.setHospitalId(dmrOrgParam.getUserName());
        reqPacket.setTel(patient.getTelNo());
        reqPacket.setCountyId(StrUtil.sub(dmrOrgParam.getUserName(), 0, 6));
        if (StrUtil.isNotBlank(reqPacket.getAddress())) {
            LambdaUpdateWrapper<PatientEntity> patientUpdateWrapper = new LambdaUpdateWrapper<>();
            patientUpdateWrapper.eq(PatientEntity::getPatientId, finalPatientId);
            patientUpdateWrapper.set(StrUtil.isNotBlank(reqPacket.getAddress()), PatientEntity::getLivingAddr, reqPacket.getAddress());
            patientService.update(patientUpdateWrapper);
        }
        return reqPacket;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportedEvent(Long userId, Long eventId, String treatmentDesc) {
        DmrReportEntity dmrReport = dmrReportService.findPendingReportByEventId(eventId);
        if (dmrReport != null) {
            throw new SaveFailureException("事件存在待报卡数据，需调用报卡接口上报");
        }
        LambdaUpdateWrapper<EventReportedEntity> wrapper = Wrappers.lambdaUpdate(EventReportedEntity.class);
        wrapper.eq(EventReportedEntity::getEventId, eventId)
                .set(EventReportedEntity::getEventStatus, EventStatus.REPORTED)
                .set(EventReportedEntity::getTreatmentDesc, treatmentDesc)
                .set(EventReportedEntity::getReportorUid, userId)
                .set(EventReportedEntity::getTimeReported, new Date());
        eventReportedService.update(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAndReport(Long userId, Long eventId, String treatmentDesc, String reqPacketContent) {
        EventReportedEntity eventReported = eventReportedService.getById(eventId);
        VisitEntity visit = visitService.getById(eventReported.getVisitId());
        DmrReportEntity dmrReport = dmrReportService.findPendingReportByEventId(eventId);
        String reqPacketUrl;
        String respPacketUrl;
        if (dmrReport != null) {
            Date timeReq = new Date();
            JSONObject jsonObj = JSONUtil.parseObj(reqPacketContent);
            Integer dmrTypeId = jsonObj.getInt("dmrTypeId");
            if (dmrTypeId.equals(DmrDataType.INFECTIOUS_DISEASE.getValue())) {
                InfectiousDiseaseReport reqPacket = getInfectiousDiseaseReport(reqPacketContent, visit.getPatientId(), visit.getOrgId(), eventReported.getEventId(), visit.getClinicianId(), visit.getDeptCode());
                reqPacketContent = JSONUtil.toJsonStr(reqPacket);
                reqPacketUrl = getReqPacketUrl(visit.getOrgId(), visit.getVisitId(), dmrTypeId, reqPacketContent);
                if (StrUtil.isBlank(saveReportCardUrl)) {
                    throw new SaveFailureException("未配置传染病报卡对接路径");
                }
                ApiResult apiResult = MedApiUtil.postEncryptSignReturnResp(reqPacket, saveReportCardUrl, aesSecret);
                respPacketUrl = getReportUri(visit.getOrgId(), visit.getVisitId(), JSONUtil.toJsonStr(apiResult));
            } else {
                DiseaseCaseRequest diseaseCaseRequest = getDiseaseCaseRequest(reqPacketContent, visit.getPatientId(), visit.getOrgId(), eventReported.getEventId());
                Map<Object, Object> reqPacket = MapUtil.builder().put("diseaseCaseRequest", diseaseCaseRequest).build();
                reqPacketContent = JSONUtil.toJsonStr(reqPacket);
                reqPacketUrl = getReqPacketUrl(visit.getOrgId(), visit.getVisitId(), dmrTypeId, reqPacketContent);
                if (StrUtil.isBlank(putDiseaseCaseUrl)) {
                    throw new SaveFailureException("未配置病例个案新增对接路径");
                }
                ApiResult apiResult = MedApiUtil.postEncryptSignReturnResp(diseaseCaseRequest, putDiseaseCaseUrl, aesSecret);
                respPacketUrl = getReportUri(visit.getOrgId(), visit.getVisitId(), JSONUtil.toJsonStr(apiResult));
            }
            Date timeResp = new Date();
            dmrReportService.updateAndSubmitReport(userId, dmrReport.getDmrId(), dmrTypeId, timeReq, timeResp, reqPacketUrl, respPacketUrl, DmrReportType.FIRST_REPORT.getValue());
            eventReportedService.reported(eventId, userId, treatmentDesc);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dmrCaseCorrection(Long userId, Long eventId, String reqPacketContent) {
        EventReportedEntity eventReported = eventReportedService.getById(eventId);
        VisitEntity visit = visitService.getById(eventReported.getVisitId());
        DmrReportEntity dmrReport = dmrReportService.findReportByEventId(eventId);
        String reqPacketUrl;
        String respPacketUrl;
        if (dmrReport != null) {
            String sourceContent = commonService.getRichTextContent(dmrReport.getReqPacketUrl());
            if (StrUtil.isNotBlank(sourceContent) && JSONUtil.isTypeJSON(sourceContent)) {
                JSONObject jsonObj = JSONUtil.parseObj(sourceContent);
                Integer dmrTypeId = DmrDataType.CASE_INDIVIDUAL.getValue();
                DiseaseCaseRequest diseaseCaseRequest = jsonObj.getBean("diseaseCaseRequest", DiseaseCaseRequest.class);
                UpdateDiseaseCaseRequest updateDiseaseCaseRequest = JSONUtil.toBean(reqPacketContent, UpdateDiseaseCaseRequest.class);
                updateDiseaseCaseRequest.setObjId(diseaseCaseRequest.getObjId());
                jsonObj.set("updateDiseaseCaseRequest", updateDiseaseCaseRequest);
                reqPacketContent = JSONUtil.toJsonStr(jsonObj);
                reqPacketUrl = getReqPacketUrl(visit.getOrgId(), visit.getVisitId(), dmrTypeId, reqPacketContent);
                if (StrUtil.isBlank(updateDiseaseCaseUrl)) {
                    throw new SaveFailureException("未配置病例个案订正对接路径");
                }
                ApiResult apiResult = MedApiUtil.postEncryptSignReturnResp(updateDiseaseCaseRequest, updateDiseaseCaseUrl, aesSecret);
                respPacketUrl = getReportUri(visit.getOrgId(), visit.getVisitId(), JSONUtil.toJsonStr(apiResult));
                LambdaUpdateWrapper<DmrReportEntity> updateWrapper = new LambdaUpdateWrapper();
                updateWrapper.eq(DmrReportEntity::getDmrId, dmrReport.getDmrId());
                updateWrapper.set(StrUtil.isNotBlank(reqPacketUrl), DmrReportEntity::getReqPacketUrl, reqPacketUrl);
                updateWrapper.set(StrUtil.isNotBlank(respPacketUrl), DmrReportEntity::getRespPacketUrl, respPacketUrl);
                updateWrapper.set(DmrReportEntity::getReportState, ReportState.REPORT_SUCCESS.getCode());
                updateWrapper.set(DmrReportEntity::getTimeFinished, new Date());
                updateWrapper.set(DmrReportEntity::getReportType, DmrReportType.CORRECTION_REPORT.getValue());
                updateWrapper.setSql("Req_Count = ifnull(Req_Count, 0) + 1");
                dmrReportService.update(updateWrapper);
            }
        }
    }

    @Override
    public void syncDmrDisease() {
        List<Map<String, Object>> list = EnumUtil.enumToList(DmrDiseaseName.class, "getValue", "getName");
        if (ObjectUtil.isNotEmpty(list)) {
            list.forEach(map -> {
                DmrDiseaseEntity dmrDiseaseEntity = new DmrDiseaseEntity();
                dmrDiseaseEntity.setDiseaseId(Integer.parseInt(map.get("value").toString()));
                dmrDiseaseEntity.setDiseaseName(map.get("label").toString());
                dmrDiseaseEntity.setDiseaseCode(map.get("value").toString());
                dmrDiseaseService.save(dmrDiseaseEntity);
            });
        }
    }

    private String getReqPacketUrl(Long orgId, Long visitId, Integer dmrTypeId, String reqPacketContent) {
        dmrReportService.validatorDmrContent(dmrTypeId, reqPacketContent);
        JSONObject jsonObj = JSONUtil.parseObj(reqPacketContent);
        jsonObj.set("dmrTypeId", dmrTypeId);
        String reqPacketUrl = getReportUri(orgId, visitId, JSONUtil.toJsonStr(jsonObj));
        return reqPacketUrl;
    }

    private String getReportUri(Long orgId, Long visitId, String content) {
        if (StringUtil.isNotEmpty(content)) {
            try {
                return commonService.saveRichText(DMR_RICH_PATH + orgId + StrUtil.DOT + DateUtil.format(new Date(), "yyyyMMdd"),
                        visitId + StrUtil.UNDERLINE + idService.getSnowflakeId(), content, true);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

}
